{
  //   ╔═╗╔═╗╦  ╦╔╗╔╔╦╗┬─┐┌─┐  ┌─┐┬  ┬┌─┐┬─┐┬─┐┬┌┬┐┌─┐
  //   ║╣ ╚═╗║  ║║║║ ║ ├┬┘│    │ │└┐┌┘├┤ ├┬┘├┬┘│ ││├┤
  //  o╚═╝╚═╝╩═╝╩╝╚╝ ╩ ┴└─└─┘  └─┘ └┘ └─┘┴└─┴└─┴─┴┘└─┘
  //  ┌─  ┌─┐┌─┐┬─┐  ┌┐ ┬─┐┌─┐┬ ┬┌─┐┌─┐┬─┐   ┬┌─┐  ┌─┐┌─┐┌─┐┌─┐┌┬┐┌─┐  ─┐
  //  │   ├┤ │ │├┬┘  ├┴┐├┬┘│ ││││└─┐├┤ ├┬┘   │└─┐  ├─┤└─┐└─┐├┤  │ └─┐   │
  //  └─  └  └─┘┴└─  └─┘┴└─└─┘└┴┘└─┘└─┘┴└─  └┘└─┘  ┴ ┴└─┘└─┘└─┘ ┴ └─┘  ─┘
  // > An .eslintrc configuration override for use in the `assets/` directory.
  //
  // This extends the top-level .eslintrc file, primarily to change the set of
  // supported globals, as well as any other relevant settings.  (Since JavaScript
  // code in the `assets/` folder is intended for the browser habitat, a different
  // set of globals is supported.  For example, instead of Node.js/Sails globals
  // like `sails` and `process`, you have access to browser globals like `window`.)
  //
  // (See .eslintrc in the root directory of this Sails app for more context.)
  // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

  "extends": [
    "../.eslintrc"
  ],

  "env": {
    "browser": true,
    "node": false
  },

  "parserOptions": {
    "ecmaVersion": 8
    //^ If you are not using a transpiler like <PERSON>l, change this to `5`.
  },

  "globals": {

    // Allow any window globals you're relying on here; e.g.
    // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
    "SAILS_LOCALS": true,
    "io": true,
    "Cloud": true,
    "parasails": true,
    "$": true,
    "_": true,
    "bowser": true,
    "StripeCheckout": true,
    "Stripe": true,
    "Vue": true,
    "VueRouter": true,
    "moment": true,
    // "google": true,
    // ...etc.
    // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

    // Make sure backend globals aren't indadvertently tolerated in our client-side JS:
    // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
    "sails": false,
    "User": false
    // ...and any other backend globals (e.g. `"Organization": false`)
    // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
  }

}
