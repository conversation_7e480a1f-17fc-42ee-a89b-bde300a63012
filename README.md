# recipe-micro-service

[Sails v1](https://sailsjs.com) application

# Routes

/m2/logic/v2/recipe

m2 = monolith 2 for gateway
logic = service name
v2 = service version
recipe = resource



GET

/m2/logic/v2/recipe
 /              - add recipe of a site
 /rid/RID       - info of that specific recipe
 /


# TODO:

  search for `TODO` in the app and start doing them. they are explained


# Coding rules

* We only use actions(https://sailsjs.com/documentation/concepts/actions-and-controllers) to create a RESTFUL route and not controller.
* Folder structure should be known prior to any development.
* Service specific public file(service.public.js) can only be included by routeAction.js and nothing else can import this file(no other service, nothing).
* camelCase the variables.
* readibility over efficiency.
* all the service functions should return {problems: []} OR data OR throw Error in case 0f 500
* For the logic say does this component have Actuator installed or not. It should be in Component.hasActuator and DONOT write this function urself in any other controller.

* If no case exists when a raw data of a model is required, a service shouldnot manipulate data.

* When writing a task say 'getAvailableSlaveIds', we can have 3 possibilities:
  * Only 1 route uses this function
  * Multiple route in same controller uses this function
  * Some other service require this function.

  In cases, we have to go the following ways:
  * WHEN, Only 1 route uses this function
    * declare async code in route.js and sync code in `route.util.js`
  * WHEN,  Multiple route in same controller uses this function
    * declare async code in route.js and sync code in `controller/utils.js [command utils]`
  * When other service require this funciton:
    * Shift async + sync code to `private.js`, do param checking in public.js and use PRIVATE function to respond .

* A Service can be one of three kinds
  - Owns a database table and responsible for changes on the table.
  - Owns no database table and responsible for changes or queries on multiple tables.
  - Not a database service, instead a pipeline service like socket connections/ MQTT.


# Standard API responses
  * HTTP/REST:
    * 200 : all good with data required as response
    * 400 : Missing Keys required for this route
            {problems: [] }
            Not really showing these error to users
    * 403 : Anything Backend wanted user to see in front end, like wrong password etc
            {problems: []}
  * socket
      * everything is same as above except instead of status code frontEnd will get:
        * {statusCode: 200/4XX, body: X} where X can be data in case of 200 or {problems: []} in case of 4XX

# Error handling:
  * Route.js: ,
  ```
    try{
      response = await serviceName.doSomething();
      if(response.problems) return exits.badRequest({problems: res.problems});
    }catch(e){
      sails.log(e);
      return exits.serverError(e);
    }
  ```
  * serviceName.service.js
  ```
  async doSomething(){

    res = await doAsync()
    if(!res || someError){
      return {problems: ['problem detail']} // known issue
    }

    * all unknown issues will be caught by main calle function i.e, in route.js or .public.js
    * always whever call service functions check if {problems}.problems is sent or not

  }
  ```

# SAILS RESOURCES

## ORM resources

> What validations can be used in models.js file
* https://sailsjs.com/documentation/concepts/models-and-orm/query-language
* https://sailsjs.com/documentation/concepts/models-and-orm/validations


## Actions file

> validations possible in inputs same of models
https://sailsjs.com/documentation/concepts/models-and-orm/validations

### Directory structure & usage

## serviceName/

### serviceName/serviceName.service.js
* all the parameters passed in this function should be tested by parent calle function, this function does minimal check on is the input parameter is good

### serviceName/serviceName.public.js

* cannot contain file other than serviceName.service.js & globalHelper
* all parameter checking and validation should be done here not in service.js


### Links

* [Sails framework documentation](https://sailsjs.com/get-started)
* [Deployment tips](https://sailsjs.com/documentation/concepts/deployment)
* [Community support options](https://sailsjs.com/support)
* [Professional / enterprise options](https://sailsjs.com/enterprise)

### Comment Tags for migration

* #TODOSM: Sails migration todo help.
* #ToReviewAmit: Anything that specifically needs to be reviewed by Amit, ie, old logic confirmation, etc.
* #TodoEventService: Wherever eventService is being used, so that whenever it is integrated, it can be tested or modified if any change has been introduced.
* #IntegrationChanges: Changes made in API request OR response from the previous code.



### style guide
* https://github.com/airbnb/javascript/issues/1281 how to write multiline conditional operators.


### Testing
* https://hub.packtpub.com/how-add-unit-tests-sails-framework-application/
* https://sailsjs.com/documentation/concepts/testing



### Hygen

- `hygen controller new <service> --r <routeName>`:
 - Creates a route in: `/api/controllers/service/route-name.js`
 - Create an empty util file : `/api/utils/service/route-name.util.js`
- `hygen util new <service> --r <routeName>`:
 - Create an empty util file : `/api/utils/service/route-name.util.js`
- `hygen service database <service>`:
 - Creates a default database service template in `/api/services/<service>/<service>.private.js`
 - Creates a default database service template in`/api/services/<service>/<service>.public.js`
 - Creates a default database service template in`/api/services/<service>/<service>.servie.js`


