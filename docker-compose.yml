version: '3'

services:
    redis:
        image: redis
        network_mode: host
        restart: always
    rabbit-mq: 
        image: custom-rabitmq_url_on_ec2_local
        network_mode: host
        restart: always
        healthcheck:
            test: [ "CMD", "curl", "-f", "http://localhost:15672" ]
            interval: 10s
            timeout: 10s
            retries: 5
        logging:
            driver: "awslogs"
            options:
                awslogs-region: "ap-south-1"
                awslogs-group: "beta_env_log_group"
                awslogs-stream: "rabitmq_log_stream"
    jt-api-v2:
        image: 878252606197.dkr.ecr.us-west-2.amazonaws.com/smartjules-beta:jt_api_v2_latest
        network_mode: host
        environment:
            - REGION=ap-south-1
            - ACCESS_KEY=********************
            - SECRET_KEY=L6xvgq1V9DfgwOveaSRmd2yqkE31GoDlUOUc+5GI
            - NODE_ENV=development
            - RABBITMQ_SERVER_ADDRESS=localhost:5672
            - AWS_TIMESTREAM_REGION=us-west-2
            - TIMESERIES_DATABASE_NAME=dev_smartjoules
            - INFLUX_URL=http://************:8086/
            - INFLUX_TOKEN=AqG8nFbt3ahw1I3t8x8fM5HIWN-OrsScKHLNT7cRzNOvvZvy05fx0r3tMIKzVEsJyaHNxU-X4dSkvJdqX7htgQ==
            - INFLUX_ORG=smartjoules
            - INFLUX_ORG_ID=3636d80f25178a4d
            - SENTRY_DNS=https://<EMAIL>/5
            - SENTRY_TRACE_SAMPLE_RATE=0.3
            - SENTRY_DEBUG=true            
            #- RABBITMQ_USER=smartjoules
            #- RABBITMQ_PASS=smartjoules
            - REDIS_HOST=redis
        restart: always
        depends_on:
            redis:
                condition: service_started
            rabbit-mq:
                condition: service_healthy
        command: node app.js --port 1338
        #ports:
            #- "1338:1337"
        logging:
            driver: "awslogs"
            options:
                awslogs-region: "ap-south-1"
                awslogs-group: "beta_env_log_group"
                awslogs-stream: "jt_api_v2_log_stream"    
    jt-api:
        image: 878252606197.dkr.ecr.us-west-2.amazonaws.com/smartjules-beta:JouleTrack_API_latest
        network_mode: host
        restart: always
        depends_on:
            redis:
                condition: service_started
            rabbit-mq:
                condition: service_healthy
        environment:
            - REGION=ap-south-1
            - ACCESS_KEY=********************
            - SECRET_KEY=L6xvgq1V9DfgwOveaSRmd2yqkE31GoDlUOUc+5GI
            - NODE_ENV=development
            - CACHE_HOST=localhost
            - CACHE_PORT=6379
        #command: node app.js --port 1337     
        #ports:
            #- "1337:1337"
    jouletrack-mqtt:
        image: 878252606197.dkr.ecr.us-west-2.amazonaws.com/smartjules-beta:Jouletrack_mqtt_latest
        network_mode: host
        restart: always
        environment:
            - NODE_ENV=development
        depends_on:
            redis:
                condition: service_started
        command: node mqtt.js
        depends_on:
            redis:
                condition: service_started
            rabbit-mq:
                condition: service_healthy
        logging:
            driver: "awslogs"
            options:
                awslogs-region: "ap-south-1"
                awslogs-group: "beta_env_log_group"
                awslogs-stream: "jt_mqtt_log_stream"
