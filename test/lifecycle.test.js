var sails = require('sails');
var config = require('../config/env/testing');

global.chai = require('chai');
global.should = global.chai.should();

// Before running any tests...
/* eslint-disable no-undef */
before(function (done) {

  // Increase the Mocha timeout so that <PERSON><PERSON> has enough time to lift, even if you have a bunch of assets.
  this.timeout(10000);

  sails.lift({
    // Your Sails app's configuration files will be loaded automatically,
    // but you can also specify any other special overrides here for testing purposes.

    // For example, we might want to skip the Grunt hook,
    // and disable all logs except errors and warnings:
    hooks: { grunt: false },
    log: { level: config.log.level },
    'models': {
      'connection': config.models.connection,
      'migrate': config.models.migrate
    },
    'sockets': {
      'adapter': config.sockets.adapter
    },
    'cachePort': config.cachePort,
    // "cacheHost": '*************'
    'cacheHost': config.cacheHost

  }, (err) => {
    if (err) { return done(err); }

    // here you can load fixtures, etc.
    // (for example, you might want to create some records in the database)

    return done();
  });
});

// After all tests have finished...
after((done) => {

  // here you can clear fixtures, etc.
  // (e.g. you might want to destroy the records you created above)
  sails.lower(done);
  process.exit();


});
