const assert = require('chai').assert;
const globalHelpers = require('../../../api/utils/globalhelper');
const moment = require('moment');

/**
 * These should be very strict tests
 */
describe('Global helpers Test', () => {

  describe('toJson: parses data to valid json', () => {

    const validJson = { 'test': 'test', 'test1': { 'test2': 'test2' } };
    const validJsonString = JSON.stringify(validJson);
    const invalidJsonString = 'testCannotParseString';

    it('toJson: passing valid JSON', (done) => {
      let resValidJson = globalHelpers.toJson(validJson);
      assert.deepEqual(resValidJson, validJson, 'Objects should be deeply equal');
      done();
    });
    it('toJson: passing valid Stringified JSON', (done) => {
      let resvalidJsonString = globalHelpers.toJson(validJsonString);
      assert.isObject(resvalidJsonString, 'response should be valid json object');
      assert.deepEqual(resvalidJsonString, validJson,
        'Objects should be deeply equal');
      done();
    });
    it('toJson: passing invalid JSON', (done) => {
      let resinvalidJsonString = globalHelpers.toJson(invalidJsonString);
      assert.isUndefined(resinvalidJsonString, 'response should be undefined');
      done();
    });

  });

  describe('toArray: parses data to valid array', () => {
    const validArray = ['1', 2, { 'test': 'test' }];
    const validArrayString = JSON.stringify(validArray);
    const invalidArray = 'thisIsNotaRray';

    it('toArray: passing valid Array', (done) => {
      let resvalidArray = globalHelpers.toArray(validArray);
      assert.deepEqual(resvalidArray, validArray, 'Arrays should be deeply equal');
      done();
    });
    it('toArray: passing valid Stringified Array', (done) => {
      let resvalidArrayString = globalHelpers.toArray(validArrayString);
      assert.isArray(resvalidArrayString, 'response should be valid Array');
      assert.deepEqual(resvalidArrayString, validArray,
        'Arrays should be deeply equal');
      done();
    });
    it('toArray: passing invalid Array', (done) => {
      let resinvalidArray = globalHelpers.toArray(invalidArray);
      assert.isUndefined(resinvalidArray, 'response should be undefined');
      done();
    });

  });

  describe('toString: converts any object/array to string', () => {
    const inputString = 'any string';
    const inputArray = ['any value'];
    const inputObject = {
      key: 'value'
    };

    it('toString: passing alreay a string', (done) => {
      let ouputString = globalHelpers.toString(inputString);
      assert.isString(ouputString, 'should be a string');
      done();
    });
    it('toString: passing alreay an array', (done) => {
      let ouputArray = globalHelpers.toString(inputArray);
      assert.isString(ouputArray, 'should be a string');
      done();
    });
    it('toString: passing alreay a string', (done) => {
      let ouputObject = globalHelpers.toString(inputObject);
      assert.isString(ouputObject, 'should be a string');
      done();
    });
  });

  describe('toBoolean: converts any invalid string to boolean', () => {
    const inputBoolean = true;
    const inputStringTrue = 'true';
    const inputStringFalse = 'false';
    const inputStringNull = 'null';
    const inputStringUndefined = 'undefined';
    const inputStringRandom = 'random';
    const inputArray = ['random value'];

    it('toBoolean: passing a boolean', (done) => {
      let outputBoolean = globalHelpers.toBoolean(inputBoolean);
      assert.isBoolean(outputBoolean, 'should be a boolean');
      assert.isTrue(outputBoolean, 'should be true');
      done();
    });
    it('toBoolean: passing a stringified true', (done) => {
      let outputStringTrue = globalHelpers.toBoolean(inputStringTrue);
      assert.isBoolean(outputStringTrue, 'should be a boolean');
      assert.isTrue(outputStringTrue, 'should be true');
      done();
    });
    it('toBoolean: passing a stringified false', (done) => {
      let outputStringFalse = globalHelpers.toBoolean(inputStringFalse);
      assert.isBoolean(outputStringFalse, 'should be a boolean');
      assert.isFalse(outputStringFalse, 'should be false');
      done();
    });
    it('toBoolean: passing a stringified null', (done) => {
      let outputStringNull = globalHelpers.toBoolean(inputStringNull);
      assert.isBoolean(outputStringNull, 'should be a boolean');
      assert.isFalse(outputStringNull, 'should be false');
      done();
    });
    it('toBoolean: passing a stringified undefined', (done) => {
      let outputStringUndefined = globalHelpers.toBoolean(inputStringUndefined);
      assert.isBoolean(outputStringUndefined, 'should be a boolean');
      assert.isFalse(outputStringUndefined, 'should be false');
      done();
    });
    it('toBoolean: passing a random string', (done) => {
      let outputStringRandom = globalHelpers.toBoolean(inputStringRandom);
      assert.isBoolean(outputStringRandom, 'should be a boolean');
      assert.isTrue(outputStringRandom, 'should be true');
      done();
    });
    it('toBoolean: passing an array', (done) => {
      let outputArray = globalHelpers.toBoolean(inputArray);
      assert.isBoolean(outputArray, 'should be a boolean');
      assert.isTrue(outputArray, 'should be true');
      done();
    });
  });

  describe('bufferToString: converts a buffer to string', () => {
    const stringBuffer = Buffer.from('abc');
    const StringifiedObjectBuffer = Buffer.from('{"key": "value"}');

    it('bufferToString: passing a string buffer', (done) => {
      const outputStringBuffer = globalHelpers.bufferToString(stringBuffer);
      assert.isString(outputStringBuffer, 'should be a string');
      done();
    });

    it('bufferToString: passing a stringified object buffer', (done) => {
      const outputObjectBuffer = globalHelpers.bufferToString(StringifiedObjectBuffer);
      assert.isObject(outputObjectBuffer, 'should be an object');
      done();
    });
  });
  
  describe('stringToBoolean: converts stringified boolean to boolean', () => {
    const booleanValue = false;
    const validBooleanString = 'false';
    const inValidBooleanStr = 'something';

    it('stringToBoolean: passing valid boolean', (done) => {
      let resbooleanValue = globalHelpers.stringToBoolean(booleanValue);
      assert.isBoolean(resbooleanValue, 'Should be valid boolean returned');
      done();
    });
    it('stringToBoolean: passing valid boolean string', (done) => {
      let resValidBooleanStr = globalHelpers.stringToBoolean(validBooleanString);
      assert.isBoolean(resValidBooleanStr, 'Should be valid boolean returned');
      done();
    });
    it('stringToBoolean: passing Invalid boolean string', (done) => {
      let resinValidBooleanStr = globalHelpers.stringToBoolean(inValidBooleanStr);
      assert.isUndefined(resinValidBooleanStr, 'Should return undefined');
      done();
    });
  });

  describe('unique: remove duplicate values(fundamental datatypes) from array', () => {
    const arrayWithDuplicates = [1, 1, '0', '3', '0'];

    it('unique: passing array with duplicate values', (done) => {
      let resarrayWithDuplicates = arrayWithDuplicates.filter(globalHelpers.unique);
      assert.deepEqual(resarrayWithDuplicates, [1, '0', '3']);
      done();
    });
  });

  describe('isNullishArray: checks if array is null', () => {
    const inputString = 'value';
    const inputNormalArray = ['value1', 'value2'];
    const inputNullArray = ['', '', {}];

    it('isNullishArray: passing string value', (done) => {
      let outputString = globalHelpers.isNullishArray(inputString);
      assert.isFalse(outputString, 'should return false');
      done();
    });
    it('isNullishArray: passing normal array with value', (done) => {
      let outputNormalArray = globalHelpers.isNullishArray(inputNormalArray);
      assert.isFalse(outputNormalArray, 'should return false');
      done();
    });
    it('isNullishArray: passing array with null value', (done) => {
      let outputNullArray = globalHelpers.isNullishArray(inputNullArray);
      assert.isTrue(outputNullArray, 'should return true');
      done();
    });
  });
  
  describe('isNullish: Checks for falsie values', () => {
    const falsy1 = {}, falsy2 = '', falsy3 = undefined,
      falsy4 = NaN, falsy5 = [];
    const truth1 = 'true', truth2 = ['1'], truth3 = {'test': 'test'}, truth4 = 1337;

    it('isNullish: test for falsies', (done) => {
      assert.strictEqual(globalHelpers.isNullish(falsy1), true, 'falsy1 should return true');
      assert.strictEqual(globalHelpers.isNullish(falsy2), true, 'falsy2 should return true');
      assert.strictEqual(globalHelpers.isNullish(falsy3), true, 'falsy3 should return true');
      assert.strictEqual(globalHelpers.isNullish(falsy4), true, 'falsy4 should return true');
      assert.strictEqual(globalHelpers.isNullish(falsy5), true, 'falsy5 should return true');
      done();
    });
    it('isNullish: test for truthies', (done) => {
      assert.strictEqual(globalHelpers.isNullish(truth1), false, 'truthy1 should return false');
      assert.strictEqual(globalHelpers.isNullish(truth2), false, 'truthy2 should return false');
      assert.strictEqual(globalHelpers.isNullish(truth3), false, 'truthy3 should return false');
      assert.strictEqual(globalHelpers.isNullish(truth4), false, 'truthy4 should return false');
      done();
    });
  });

  describe('checkObjectKeys: check if object keys are nulllish', () => {
    const inputKeysArray = ['key1', 'key2'];
    const inputCorrectObject = {key1: 'value1', key2: 'value2'};
    const inputObjectWithNullValues = {key1: '', key2: ''};

    it('checkObjectKeys: passing object without null values', (done) => {
      let outputWithoutNull = globalHelpers.checkObjectKeys(inputCorrectObject, inputKeysArray);
      assert.isArray(outputWithoutNull, 'should be an array');
      assert.lengthOf(outputWithoutNull, 0, 'should be an empty array');
      done();
    });
    it('checkObjectKeys: passing object with null values', (done) => {
      let outputWithNull = globalHelpers.checkObjectKeys(inputObjectWithNullValues, inputKeysArray);
      assert.isArray(outputWithNull, 'should be an array');
      assert.lengthOf(outputWithNull, 2, 'should be an array with length 2');
      done();
    });
  });

  describe('convertStringArrayTOJSONArray', () => {
    const inputUndefined = undefined;
    const inputNonArray = 'random string';
    const stringifiedArray = '[{"key": "value"}]';

    it('convertStringArrayTOJSONArray: passing undefined', (done) => {
      let outputUndefined = globalHelpers.convertStringArrayToJSONArray(inputUndefined);
      assert.isUndefined(outputUndefined, 'should be undefined.');
      done(); 
    });
    it('convertStringArrayTOJSONArray: passing Non array - string', (done) => {
      let outputNonArray = globalHelpers.convertStringArrayToJSONArray(inputNonArray);
      assert.isNull(outputNonArray, 'should be Null.');
      done(); 
    });
    it('convertStringArrayTOJSONArray: passing valid stringified array', (done) => {
      let outputValidArray= globalHelpers.convertStringArrayToJSONArray(stringifiedArray);
      assert.isArray(outputValidArray, 'should be an array');
      assert.isObject(outputValidArray[0], 'array element should be object');
      done(); 
    });
  });

  describe('convertJSONArrayToStringArray', () => {
    const inputUndefined = undefined;
    const inputNonArray = 'random string';
    const validArray = [{key: 'value'}];

    it('convertJSONArrayToStringArray: passing undefined', (done) => {
      let outputUndefined = globalHelpers.convertJSONArrayToStringArray(inputUndefined);
      assert.isUndefined(outputUndefined, 'should be undefined.');
      done(); 
    });
    it('convertJSONArrayToStringArray: passing Non array - string', (done) => {
      let outputNonArray = globalHelpers.convertJSONArrayToStringArray(inputNonArray);
      assert.isUndefined(outputNonArray, 'should be Undefined');
      done(); 
    });
    it('convertJSONArrayToStringArray: passing valid stringified array', (done) => {
      let outputStringifiedArray= globalHelpers.convertJSONArrayToStringArray(validArray);
      assert.isArray(outputStringifiedArray, 'should be an array');
      assert.isString(outputStringifiedArray[0], 'array element should be of type string');
      done(); 
    });
  });

  describe('isEmail: check if the passed email is valid', () => {
    const validEmail = '<EMAIL>';
    const emailWithoutAt = 'xyzexample.com';
    const emailWithoutDomain = 'xyz@example';

    it('isEmail: passing valid email', (done) => {
      let outputValdEmail = globalHelpers.isEmail(validEmail);
      assert.isTrue(outputValdEmail, 'should be true');
      done();
    });
    it('isEmail: passing email without At symbol', (done) => {
      let outputEmailWithoutAt = globalHelpers.isEmail(emailWithoutAt);
      assert.isFalse(outputEmailWithoutAt, 'should be false');
      done();
    });
    it('isEmail: passing email without domain name', (done) => {
      let outputEmailWithoutDomain = globalHelpers.isEmail(emailWithoutDomain);
      assert.isFalse(outputEmailWithoutDomain, 'should be false');
      done();
    });
  });

  describe('getCurrentUnixTs: Returns current time in unix format', () => {
    it('getCurrentUnixTs: running the function', (done) => {
      let outputTs = globalHelpers.getCurrentUnixTs();
      assert.isNumber(outputTs, 'should be a number');
      done();
    });
  });

  describe('formatDateTime: Formats the Date time in YYYY-MM-DD HH:mm', () => {
    const datetime1 = Date.now();

    it('formatDateTime: running the function with Date object', (done) => {
      let outputFormat = globalHelpers.formatDateTime(datetime1);
      assert.isString(outputFormat, 'should be of type string');
      assert.isNotNull(outputFormat, 'should not be null');
      done();
    });
  });

  describe('removeNullValueFromObject: Removed undefined value from object', () => {
    const objectWithOneUndefinedValue = {
      key1: 'value',
      key2: undefined
    };
    const objectWithoutUndefinedValue = {
      key1: 'value1',
      key2: 'value2'
    };

    it('removeNullValueFromObject: passing object with one undefined value', (done) => {
      let outputObject = globalHelpers.removeNullValueFromObject(objectWithOneUndefinedValue);
      assert.isObject(outputObject, 'should be of type object');
      assert.lengthOf(Object.keys(outputObject), 1, 'should have only 1 key');
      done();
    });
    it('removeNullValueFromObject: passing object without undefined value', (done) => {
      let outputObject = globalHelpers.removeNullValueFromObject(objectWithoutUndefinedValue);
      assert.isObject(outputObject, 'should be of type object');
      assert.lengthOf(Object.keys(outputObject), 2, 'should have 2 keys');
      done();
    });
  });

  describe('generateRandomString: generates a random string of given length', () => {
    const inputLength6 = 6;
    const inputLength10 = 10;

    it('generateRandomString: passing length 6', (done) => {
      let ouputString = globalHelpers.generateRandomString(inputLength6);
      assert.isString(ouputString, 'should be of type string');
      assert.lengthOf(ouputString, 6, 'shoud be lngth 6');
      done();
    });
    it('generateRandomString: passing length 10', (done) => {
      let ouputString = globalHelpers.generateRandomString(inputLength10);
      assert.isString(ouputString, 'should be of type string');
      assert.lengthOf(ouputString, 10, 'shoud be lngth 10');
      done();
    });
  });

  describe('toMoment: Converts datetime to moment', () => {
    const inputDateTime = Date.now();

    it('toMoment: Passing current Date time', (done) => {
      let outputMoment = globalHelpers.toMoment(inputDateTime);
      assert.instanceOf(outputMoment, moment, 'should be of type moment');
      done();
    });
  });

  describe('removeAllItemsFromArray: Removes all instances of a item from the Array', () => {
    const nonArray = 'string';
    const validArray = ['x', 'a', 'x', 'b'];
    const value = 'x';

    it('removeAllItemsFromArray: Passing a string', (done) => {
      
      assert.throws(() => {
        globalHelpers.removeAllItemsFromArray(nonArray, value);
      }, 'Passed argument is not of type Array');
      done();
    });
    it('removeAllItemsFromArray: passing a valid array and value', (done) => {
      let outputArray = globalHelpers.removeAllItemsFromArray(validArray, value);
      assert.isArray(outputArray, 'should be an array');
      assert.lengthOf(validArray, 2, 'should be of length 2');
      assert.notInclude(outputArray, value, 'should not contain the value');
      done();
    });
  });

  describe('parseMQTTTopic: It parses the MQTT topic', () => {
    const nonString = [];
    const validTopic = 'ssh/recipe/1234/recipeconfig';

    it('parseMQTTTopic: passing a non string value', (done) => {
      let outputParsed = globalHelpers.parseMQTTTopic(nonString);
      assert.isObject(outputParsed, 'should be an object');
      assert.deepEqual(outputParsed, {}, 'should be an empty object');
      done();
    });
    it('parseMQTTTopic: passing avalid topic', (done) => {
      let outputParsed = globalHelpers.parseMQTTTopic(validTopic);
      assert.isObject(outputParsed, 'should be an object');
      assert.hasAllKeys(outputParsed, ['siteId', 'service', 'deviceId', 'event']);
      done();
    });
  });
});
