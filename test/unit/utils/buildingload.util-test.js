var assert = require('assert');
var mocha = require('mocha');
sails = require('sails');
sails.config = {IOT_CONFIG:{}};
const utils = require('../../../api/utils/datadevice/buildingload.util.js');
let describe;
let it;
if (mocha.describe) {
  describe = mocha.describe;
  it = mocha.it;
  before = mocha.before;
  after = mocha.after;
} else {
  describe = global.describe;
  it = global.it;
  before = global.before;
  after = global.after;
}

describe('getDatesFromWeekAndDays and isInputIntergerArray positive suit', async () => {

  it('isInputIntergerArray', async () => {
    try {
      let result = utils.isInputIntergerArray([1,2]);
      assert.equal(result, true);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in create recipe');
    }
  });

  it('getDatesFromWeekAndDays for max weeks and max days', async () => {
    try {
      let result = utils.getDatesFromWeekAndDays([0,1,2,3,4],[0,1,2,3,4,5,6]);
      assert.equal(result.length, 29);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in create recipe');
    }
  });

  it('getDatesFromWeekAndDays for 0 weeks and 0 days', async () => {
    try {
      let result = utils.getDatesFromWeekAndDays([],[]);
      assert.equal(result.length, 0);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in getDatesFromWeekAndDays  ');
    }
  });

  it('getDatesFromWeekAndDays for 1 weeks and 1 days', async () => {
    try {
      let result = utils.getDatesFromWeekAndDays([0],[0]);
      assert.equal(result.length, 0);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in create recipe');
    }
  });

  it('getDatesFromWeekAndDays for 1 weeks and 1 days', async () => {
    try {
      let result = utils.getDatesFromWeekAndDays([1],[3]);
      assert.equal(result.length, 1);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in create recipe');
    }
  });

  it('getDatesFromWeekAndDays for 1 weeks and 2 days', async () => {
    try {
      let result = utils.getDatesFromWeekAndDays([1],[1,3]);
      assert.equal(result.length, 2);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in create recipe');
    }
  });

  it('getDatesFromWeekAndDays for 2 weeks and 2 days', async () => {
    try {
      let result = utils.getDatesFromWeekAndDays([1,3],[1,3]);
      assert.equal(result.length, 4);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in create recipe');
    }
  });

});

describe(' getDatesFromWeekAndDays and isInputIntergerArray negative suit', async () => {
  it('unhandled exception in getDatesFromWeekAndDays for undefined weeks and undefined days', async () => {
    try {
      utils.getDatesFromWeekAndDays();
    } catch (err) {
      assert.equal(err.message,'weeks is not iterable');
    }
  });

  it('passing empty array isInputIntergerArray gives true', async () => {
    try {
      let result = utils.isInputIntergerArray([]);
      assert.equal(result, true);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in isInputIntergerArray');
    }
  });

  it(' exception on passing string array in isInputIntergerArray', async () => {
    try {
      let result = utils.isInputIntergerArray(['1']);
      assert.fail(`isInputIntergerArray returned ${result}`);
    } catch (err) {
      assert.equal(err.message,'Only integer values are allowed as value of weeks/days');
    }
  });

  it(' exception on passing null array in isInputIntergerArray', async () => {
    try {
      let result = utils.isInputIntergerArray([1,undefined,null]);
      assert.equal(result,true);
    } catch (err) {
      assert.equal(err.message,'Only integer values are allowed as value of weeks/days');
    }
  });

  it(' exception on passing mixed values array in isInputIntergerArray', async () => {
    try {
      let result = utils.isInputIntergerArray([1,2,9,null]);
      assert.fail(`isInputIntergerArray returned ${result}`);
    } catch (err) {
      assert.equal(err.message,'Only integer values are allowed as value of weeks/days');
    }
  });

  it(' exception on passing mixed values array in isInputIntergerArray', async () => {
    try {
      let result = utils.isInputIntergerArray([1,2,9,true]);
      assert.fail(`isInputIntergerArray returned ${result}`);
    } catch (err) {
      assert.equal(err.message,'Only integer values are allowed as value of weeks/days');
    }
  });

  it('exception on passing mixed values array in isInputIntergerArray', async () => {
    try {
      let result = utils.isInputIntergerArray([1,'2',9]);
      assert.fail(`isInputIntergerArray returned ${result}`);
    } catch (err) {
      assert.equal(err.message,'Only integer values are allowed as value of weeks/days');
    }
  });

  it('passing disallowed values in getDatesFromWeekAndDays', async () => {
    try {
      let result = utils.getDatesFromWeekAndDays([1,9,9],[8,9]);
      assert.equal(result.length, 6);
    } catch (err) {
      assert.fail(err.message,'Only integer values are allowed as value of weeks/days');
    }
  });

  it('passing negative values in getDatesFromWeekAndDays', async () => {
    try {
      let result = utils.getDatesFromWeekAndDays([-1,-9,-9],[-8,-9]);
      assert.equal(result.length, 0);
    } catch (err) {
      assert.fail(err.message,'Only integer values are allowed as value of weeks/days');
    }
  });

});

describe('getConsumptionUnitFromUnitPreference positive suit', async () => {
  it('getConsumptionUnitFromUnitPreference should return kvah for any site', async () => {
    try {
      let result = utils.getConsumptionUnitFromUnitPreference('123',{});
      assert.equal(result,'kvah');
    } catch (err) {
      assert.fail(err);
    }
  });

  it('getConsumptionUnitFromUnitPreference should return kvah if unitPreference is null', async () => {
    try {
      let result = utils.getConsumptionUnitFromUnitPreference('123',null);
      assert.equal(result,'kvah');
    } catch (err) {
      assert.fail(err);
    }
  });

  it('getConsumptionUnitFromUnitPreference should return kvah if unitPreference is kvah', async () => {
    try {
      let result = utils.getConsumptionUnitFromUnitPreference('123',{cons:'kvah'});
      assert.equal(result,'kvah');
    } catch (err) {
      assert.fail(err);
    }
  });

  it('getConsumptionUnitFromUnitPreference should return kvah if unitPreference is wats', async () => {
    try {
      let result = utils.getConsumptionUnitFromUnitPreference('123',{cons:'wats'});
      assert.equal(result,'kvah');
    } catch (err) {
      assert.fail(err);
    }
  }); 
});

describe('getConsumptionUnitFromUnitPreference negative suit', async () => {
  it('getConsumptionUnitFromUnitPreference should return kvah for any site', async () => {
    try {
      let result = utils.getConsumptionUnitFromUnitPreference(null,null);
      assert.equal(result,'kvah');
    } catch (err) {
      assert.fail(err);
    }
  });
});

describe('calculateBuildingConsumption positive suit', async () => {
  it('calculateBuildingConsumption should return consumption data', async () => {
    try {
      let result = utils.calculateBuildingConsumption([[{timestamp:'Sun Sep 06 2020 16:58:49 GMT+0530',data:{kw:'123'}}]],1);
      assert.notEqual(result,null);
    } catch (err) {
      assert.fail(err);
    }
  });

  it('calculateBuildingConsumption should return return consumption data', async () => {
    try {
      let result = utils.calculateBuildingConsumption([[{timestamp:'Sun Sep 06 2020 16:58:49 GMT+0530',data:{kw:'123'}}]],10);
      assert.notEqual(result,null);
    } catch (err) {
      assert.fail(err);
    }
  });

  it('calculateBuildingConsumption should return consumption data', async () => {
    try {
      let result = utils.calculateBuildingConsumption([[{timestamp:'Sun Sep 06 2020 16:58:49 GMT+0530',data:{kw:'123'}},{timestamp:'Sun Sep 06 2020 16:58:49 GMT+0530',data:{kw:'123'}}]],1);
      assert.notEqual(result,null);
    } catch (err) {
      assert.fail(err);
    }
  });
});

describe('calculateBuildingConsumption negative suit', async () => {
  it('calculateBuildingConsumption should return kvah for any site', async () => {
    try {
      let result = utils.calculateBuildingConsumption([[{timestamp:undefined,data:{kw:null}}]],1);
      assert.notEqual(result,null);
    } catch (err) {
      assert.fail(err);
    }
  });
});
