const assert = require('chai').assert;
const userUtil = require('../../../../api/utils/user/utils');

describe('User Utils Unit Tests', () => {
  describe('getDefaultPassword(): It returns the default user password', () => {
    it('getDefaultPassword()', (done) => {
      let output = userUtil.getDefaultPassword();
      assert.equal(output, 'smartjoules123');
      done();
    });
  });

  describe('addDefaultPreferences(): It adds the default preferences in the passed object', () => {
    it('addDefaultPreferences(): passing the object', (done) => {
      let preferences = {};
      userUtil.addDefaultPreferences(preferences);
      assert.isObject(preferences);
      assert.hasAllDeepKeys(preferences, ['dj', 'djNotif', 'mailConfig', 'msgConfig', 'unitPreference']);
      done();
    });
  });

  describe('turnEachKeyToFalse(); It turns the value of every key to false', () => {
    let inputObject = {
      key1: 'value1',
      key2: 'value2'
    };

    it('turnEachKeyToFalse(): passing a valid object', (done) => {
      let outputObject = userUtil.turnEachKeyToFalse(inputObject);
      assert.isObject(outputObject);
      assert.deepEqual(Object.values(outputObject), [false, false]);
      done();
    });

    it('turnEachKeyToFalse(): passing a stringified object', (done) => {
      let outputObject = userUtil.turnEachKeyToFalse(JSON.stringify(inputObject));
      assert.isObject(outputObject);
      assert.deepEqual(Object.values(outputObject), [false, false]);
      done();
    });
  });

  describe('flattenPolicies(): It flattens the policy.json file', () => {
    let policies = {
      'Recipe': {
        'displayName': 'Recipe',
        'pageView': false,
        'subHeadings': {
          'recipe': {
            'displayName': 'Recipe',
            'policies': {
              'create': {
                'displayName': 'Create',
                'routePolicyMap': {
                  'POST /m2/recipe/v2/recipe': 'recipe/createrecipe'
                },
                'hasAccess': false
              },
              'read': {
                'displayName': 'Read',
                'routePolicyMap': {
                  'GET /m2/recipe/v2/recipe/siteId/:siteId': 'recipe/find-recipe-by-siteId'
                },
                'hasAccess': false
              },
              
            }
          }
        }
      }
    };

    let policiesWithAccess = {
      'Recipe': {
        'displayName': 'Recipe',
        'pageView': false,
        'subHeadings': {
          'recipe': {
            'displayName': 'Recipe',
            'policies': {
              'create': {
                'displayName': 'Create',
                'routePolicyMap': {
                  'POST /m2/recipe/v2/recipe': 'recipe/createrecipe'
                },
                'hasAccess': false
              },
              'read': {
                'displayName': 'Read',
                'routePolicyMap': {
                  'GET /m2/recipe/v2/recipe/siteId/:siteId': 'recipe/find-recipe-by-siteId'
                },
                'hasAccess': true
              },
              
            }
          }
        }
      }
    };

    it('flattenPolicies(): Passing valid policy structure with no access', (done) => {
      let flattendPolicies = userUtil.flattenPolicies(policies);
      assert.isObject(flattendPolicies);
      assert.deepEqual(flattendPolicies, {
        'POST /m2/recipe/v2/recipe': false,
        'GET /m2/recipe/v2/recipe/:key/:value': false
      });
      done();
    });

    it('flattenPolicies(): Passing valid policy structure with one access', (done) => {
      let flattendPolicies = userUtil.flattenPolicies(policiesWithAccess);
      assert.isObject(flattendPolicies);
      assert.deepEqual(flattendPolicies, {
        'POST /m2/recipe/v2/recipe': false,
        'GET /m2/recipe/v2/recipe/siteId/:siteId': 'recipe/find-recipe-by-siteId'
      });
      done();
    });

    it('flattenPolicies(): Passing invalid policy strucutre', (done) => {
      let invalidPolicies = {'Recipe': {}};
      let flattendPolicies = userUtil.flattenPolicies(invalidPolicies);
      assert.deepEqual(flattendPolicies, {});
      done();
    });

    it('flattenPolicies(): Passing empty policy strucutre', (done) => {
      let emptyPolicy = {};
      let flattendPolicies = userUtil.flattenPolicies(emptyPolicy);
      assert.deepEqual(flattendPolicies, {});
      done();
    });
  });

  describe('checkForKeysInObject(): It checks for the existence of each key in the passed object', () => {
    let obj1 = {
      key1: true
    };
    let obj2 = {
      key1: true,
      key2: undefined
    };

    it('checkForKeysInObject(): passing valid object and keys', (done) => {
      let keysArray = ['key1'];
      let output = userUtil.checkForKeysInObject(obj1, keysArray);
      assert.isTrue(output);
      done();
    });

    it('checkForKeysInObject(): passing object with value undefined', (done) => {
      let keysArray = ['key1', 'key2'];
      let output = userUtil.checkForKeysInObject(obj2, keysArray);
      assert.isTrue(output);
      done();
    });

    it('checkForKeysInObject(): passing object not having key', (done) => {
      let keysArray = ['key1', 'key2'];
      let output = userUtil.checkForKeysInObject(obj1, keysArray);
      assert.isFalse(output);
      done();
    });
  });

  describe('validatePolicies(): It parses the policy.json file format and validates whether required keys are present or not', () => {
    it('validatePolicies(): Passing invalid policy data', (done) => {
      let policy = {
        'ACPlant': {
          'displayName': 'AC PLANT',
          'pageView': false,
        }
      };
      let output = userUtil.validatePolicies(policy);
      assert.isFalse(output);
      done();
    });

    it('validatePolicies(): Passing invalid subHeadings data', (done) => {
      let policy = {
        'ACPlant': {
          'displayName': 'AC PLANT',
          'pageView': false,
          'subHeadings': {
            'command': {
              'displayName': 'Command',
            }
          }
        }
      };
      let output = userUtil.validatePolicies(policy);
      assert.isFalse(output);
      done();
    });

    it('validatePolicies(): Passing invalid inner policies data', (done) => {
      let policy = {
        'ACPlant': {
          'displayName': 'AC PLANT',
          'pageView': false,
          'subHeadings': {
            'command': {
              'displayName': 'Command',
              'policies': {
                'write': {
                  'displayName': 'WRITE',
                  'routePolicyMap': {},
                },
                'read': {
                  'displayName': 'READ',
                  'routePolicyMap': {},
                  'hasAccess': false
                }
              }
            }
          }
        }
      };
      let output = userUtil.validatePolicies(policy);
      assert.isFalse(output);
      done();
    });

    it('validatePolicies(): Passing valid inner policies data', (done) => {
      let policy = {
        'ACPlant': {
          'displayName': 'AC PLANT',
          'pageView': false,
          'subHeadings': {
            'command': {
              'displayName': 'Command',
              'policies': {
                'write': {
                  'displayName': 'WRITE',
                  'routePolicyMap': {},
                  'hasAccess': false
                },
                'read': {
                  'displayName': 'READ',
                  'routePolicyMap': {},
                  'hasAccess': false
                }
              }
            }
          }
        }
      };
      let output = userUtil.validatePolicies(policy);
      assert.isTrue(output);
      done();
    });
  });
});
