const assert = require('chai').assert;
const createUserUtil = require('../../../../api/utils/user/create-user.util');

describe('Create User Util Unit Tests', () => {
  describe('createInitialUserInfoObject(): It creates the initial user info object', () => {
    let userInfo = {
      email: '<EMAIL>',
      name: 'sample name',
      designation: 'intern',
      phone: '9999999999',
      userOrganization: 'smart joules',
      policiesGroup: {
        'ssh': {},
        'mgch': {}
      },
    };
    it('createInitialUserInfoObject(): Passing invalid email', (done) => {
      let newUserInfoObject = {...userInfo};
      newUserInfoObject.email = 'invalid_email';
      let outputObject = createUserUtil.createInitialUserInfoObject(newUserInfoObject);
      assert.isObject(outputObject);
      assert.hasAllDeepKeys(outputObject, ['problems']);
      assert.equal(outputObject.problems, 'Invalid Email');
      done();
    });

    it('createInitialUserInfoObject(): Passing invalid policy group', (done) => {
      let newUserInfoObject = {...userInfo};
      newUserInfoObject.policiesGroup = {};
      let outputObject = createUserUtil.createInitialUserInfoObject(newUserInfoObject);
      assert.isObject(outputObject);
      assert.hasAllDeepKeys(outputObject, ['problems']);
      assert.equal(outputObject.problems, 'User doesn\'t have a policy.');
      done();
    });

    it('createInitialUserInfoObject(): Passing invalid user ID', (done) => {
      let newUserInfoObject = {...userInfo};
      newUserInfoObject.userId = 'something else';
      let outputObject = createUserUtil.createInitialUserInfoObject(newUserInfoObject);
      assert.isObject(outputObject);
      assert.hasAllDeepKeys(outputObject, ['problems']);
      assert.equal(outputObject.problems, 'User ID and Email did not match');
      done();
    });

    it('createInitialUserInfoObject(): Passing all valid info', (done) => {
      let newUserInfoObject = {...userInfo};
      let outputObject = createUserUtil.createInitialUserInfoObject(newUserInfoObject);
      assert.isObject(outputObject);
      assert.hasAllDeepKeys(outputObject, ['email', 'name', 'designation', 'phone', 'userId', 'userOrganization', 'policiesGroup',
        'password', 'defaultSite', 'notify', 'accountable']);
      done();
    });
  });
});
