const assert = require('chai').assert;
const changeSettingUtil = require('../../../../api/utils/user/change-setting.util');

describe('Change Setting Util Unit Tests', () => {
  describe('getPersonSiteRoleMapping(): It creates a nested DS', () => {
    let nonArray = {};
    it('Passing a non array', (done) => {
      let output = changeSettingUtil.getPersonSiteRoleMapping(nonArray);
      assert.hasAllKeys(output, ['problems']);
      assert.equal(output.problems[0], 'Data packet should be of type array');
      done();
    });

    it('Passing invalid info in array', (done) => {
      let newRoles = [{}];
      let output = changeSettingUtil.getPersonSiteRoleMapping(newRoles);
      assert.hasAllKeys(output, ['problems']);
      assert.equal(output.problems[0], 'Required keys are missing from the objects');
      done();
    });

    it('Positive suit, passing valid info', (done) => {
      let newRoles = [{userId: 'abc', siteId: 'ssh', role: 'admin'}];
      let output = changeSettingUtil.getPersonSiteRoleMapping(newRoles);
      assert.isObject(output);
      assert.hasAllDeepKeys(output, ['abc']);
      assert.isObject(output['abc']);
      assert.equal(output['abc']['ssh'], 'admin');
      done();
    });

    it('Positive suit, passing valid info of many sites', (done) => {
      let newRoles = [{userId: 'abc', siteId: 'ssh', role: 'admin'}, {userId: 'abc', siteId: 'mgch', role: 'admin'}];
      let output = changeSettingUtil.getPersonSiteRoleMapping(newRoles);
      assert.isObject(output);
      assert.hasAllDeepKeys(output, ['abc']);
      assert.isObject(output['abc']);
      assert.hasAllDeepKeys(output['abc'], ['ssh', 'mgch']);
      done();
    });

    it('Positive suit, passing valid info of many users', (done) => {
      let newRoles = [{userId: 'abc', siteId: 'ssh', role: 'admin'}, {userId: 'def', siteId: 'mgch', role: 'admin'}];
      let output = changeSettingUtil.getPersonSiteRoleMapping(newRoles);
      assert.isObject(output);
      assert.hasAllDeepKeys(output, ['abc', 'def']);
      assert.isObject(output['abc']);
      assert.isObject(output['def']);
      assert.hasAllDeepKeys(output['abc'], ['ssh']);
      assert.hasAllDeepKeys(output['def'], ['mgch']);
      done();
    });
  });
});
