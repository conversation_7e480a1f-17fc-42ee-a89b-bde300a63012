var assert = require('assert');
var mocha = require('mocha');
sails = require('sails');
const utils = require('../../../api/utils/datadevice/top-worst-ahus.util');
if (mocha.describe) {
  describe = mocha.describe;
  it = mocha.it;
  before = mocha.before;
  after = mocha.after;
} else {
  describe = global.describe;
  it = global.it;
  before = global.before;
  after = global.after;
}
describe('calculateStandardDeviation and getNBestAndWorstAhuIdsOnBasesofSD positive suit', async () => {

  
  it('calculateStandardDeviation passing 2,2', async () => {
    try {
      let result = utils.calculateStandardDeviation([{data:{kW:2}},{data:{kW:2}}],'kW');
      assert.strictEqual(result, 0);
    } catch (err) {
      console.log(err);
      assert.fail('Exception calculate Standard Deviation');
    }
  });
  it('calculateStandardDeviation passing 10,12,23,23,16,23,21,16', async () => {
    try {
      let result = utils.calculateStandardDeviation([{data:{kW:10}},{data:{kW:12}},{data:{kW:23}},{data:{kW:23}},{data:{kW:16}},{data:{kW:23}},{data:{kW:21}},{data:{kW:16}}],'kW');
      assert.strictEqual(result,4.8989794855664 );
    } catch (err) {
      console.log(err);
      assert.fail('Exception calculate Standard Deviation');
    }
  });
  it('calculateStandardDeviation passing 1,5', async () => {
    try {
      let result = utils.calculateStandardDeviation([{data:{kW:1}},{data:{kW:5}}],'kW');
      assert.strictEqual(result, 2.8284271247461903);
    } catch (err) {
      console.log(err);
      assert.fail('Exception calculate Standard Deviation');
    }
  });
  it('calculateStandardDeviation passing 1,null', async () => {
    try {
      let result = utils.calculateStandardDeviation([{data:{kW:1}},{data:{kW:null}}],'kW');
      assert.strictEqual(result, 0);
    } catch (err) {
      console.log(err);
      assert.fail('Exception calculate Standard Deviation');
    }
  });
  it('calculateStandardDeviation passing -1,5', async () => {
    try {
      let result = utils.calculateStandardDeviation([{data:{kW:-1}},{data:{kW:5}}],'kW');
      assert.strictEqual(result, 4.242640687119285);
    } catch (err) {
      console.log(err);
      assert.fail('Exception calculate Standard Deviation');
    }
  });
  it('calculateStandardDeviation passing 0,0', async () => {
    try {
      let result = utils.calculateStandardDeviation([{data:{kW:0}},{data:{kW:0}}],'kW');
      assert.strictEqual(result,0 );
    } catch (err) {
      console.log(err);
      assert.fail('Exception calculate Standard Deviation');
    }
  });
  it('getNBestAndWorstAhuIdsOnBasesOfSD passing N=2 where index less than or equal to  N  ', async () => {
    try {
      let result = utils.getNBestAndWorstAhuIdsOnBasesOfSD([{'deviceId':'2456','SD':0},{'deviceId':'2015','SD':2.8284271247461903},{'deviceId':'3125','SD':0},{'deviceId':'4156','SD': 4.242640687119285},{'deviceId':'5667','SD':0}],2);
      assert.deepStrictEqual(result,{bestAhuIds:['4156','2015'],worstAhuIds:['3125','2456']});
      
    } catch (err) {
      console.log(err);
      assert.fail('Exception calculate Standard Deviation');
    }
  });

  
  it('getNBestAndWorstAhuIdsOnBasesOfSD passing N = 4 where no.of best AHU greater than Worst AHU ', async () => {
    try {
      let result = utils.getNBestAndWorstAhuIdsOnBasesOfSD([{'deviceId':'2456','SD':0},{'deviceId':'2015','SD':2.8284271247461903},{'deviceId':'3125','SD':3.56789012},{'deviceId':'4156','SD': 4.242640687119285},{'deviceId':'5667','SD':0}],4);
      assert.deepStrictEqual(result,{bestAhuIds:['4156','3125','2015'],worstAhuIds:['5667','2456']});
      
    } catch (err) {
      console.log(err);
      assert.fail('Exception calculate Standard Deviation');
    }
  });
  it('getNBestAndWorstAhuIdsOnBasesOfSD passing N = 4 where no.of worst AHU greater than Best AHU  ', async () => {
    try {
      let result = utils.getNBestAndWorstAhuIdsOnBasesOfSD([{'deviceId':'2456','SD':0},{'deviceId':'2015','SD':2.8284271247461903},{'deviceId':'3125','SD':0},{'deviceId':'4156','SD': 4.242640687119285},{'deviceId':'5667','SD':0}],4);
      assert.deepStrictEqual(result,{bestAhuIds:['4156','2015'],worstAhuIds:['5667','3125','2456']});
      
    } catch (err) {
      console.log(err);
      assert.fail('Exception calculate Standard Deviation');
    }
  });
  it('getNBestAndWorstAhuIdsOnBasesOfSD passing N=5 having No worst AHU ', async () => {
    try {
      let result = utils.getNBestAndWorstAhuIdsOnBasesOfSD([{'deviceId':'2456','SD':1.2},{'deviceId':'2015','SD':2.8284271247461903},{'deviceId':'3125','SD':3.56789012},{'deviceId':'4156','SD': 4.242640687119285},{'deviceId':'5667','SD':6.5}],5);
      assert.deepStrictEqual(result,{bestAhuIds:['5667','4156','3125','2015','2456'],worstAhuIds:[]});
      
    } catch (err) {
      console.log(err);
      assert.fail('Exception calculate Standard Deviation');
    }
  });
  it('getNBestAndWorstAhuIdsOnBasesOfSD passing N=5 having SD 0   ', async () => {
    try {
      let result = utils.getNBestAndWorstAhuIdsOnBasesOfSD([{'deviceId':'2456','SD':0},{'deviceId':'2015','SD':0},{'deviceId':'3125','SD':0},{'deviceId':'4156','SD': 0},{'deviceId':'5667','SD':0}],5);
      assert.deepStrictEqual(result,{bestAhuIds:[],worstAhuIds:['5667','4156','3125','2015','2456']});
      
    } catch (err) {
      console.log(err);
      assert.fail('Exception calculate Standard Deviation');
    }
  });
  it('getNBestAndWorstAhuIdsOnBasesOfSD passing N=2 where index greater than N  ', async () => {
    try {
      let result = utils.getNBestAndWorstAhuIdsOnBasesOfSD([{'deviceId':'2456','SD':7.6},{'deviceId':'2015','SD':2.8284271247461903},{'deviceId':'3125','SD':0.7},{'deviceId':'4156','SD': 4.242640687119285},{'deviceId':'5667','SD':0.1}],2);
      assert.deepStrictEqual(result,{bestAhuIds:['2456','4156'],worstAhuIds:['3125','5667']});
      
    } catch (err) {
      console.log(err);
      assert.fail('Exception calculate Standard Deviation');
    }
  });
  it('getNBestAndWorstAhuIdsOnBasesOfSD passing N=7 where N greater than index  ', async () => {
    try {
      let result = utils.getNBestAndWorstAhuIdsOnBasesOfSD([{'deviceId':'2456','SD':7.6},{'deviceId':'2015','SD':2.8284271247461903},{'deviceId':'3125','SD':0},{'deviceId':'4156','SD': 4.242640687119285},{'deviceId':'5667','SD':0}],7);
      assert.deepStrictEqual(result,{bestAhuIds:['2456','4156','2015'],worstAhuIds:['5667','3125']});
      
    } catch (err) {
      console.log(err);
      assert.fail('Exception calculate Standard Deviation');
    }
  });
  it('getNBestAndWorstAhuIdsOnBasesOfSD passing N=7 where N greater than index  ', async () => {
    try {
      let result = utils.getNBestAndWorstAhuIdsOnBasesOfSD([{'deviceId':'2456','SD':7.6},{'deviceId':'2015','SD':2.8284271247461903},{'deviceId':'3125','SD':-7},{'deviceId':'4156','SD': 4.242640687119285},{'deviceId':'5667','SD':0}],7);
      assert.deepStrictEqual(result,{bestAhuIds:['2456','4156','2015'],worstAhuIds:['3125','5667']});
      
    } catch (err) {
      console.log(err);
      assert.fail('Exception calculate Standard Deviation');
    }
  });
  it('getNBestAndWorstAhuIdsOnBasesOfSD passing N=7 where N greater than index', async () => {
    try {
      let result = utils.getNBestAndWorstAhuIdsOnBasesOfSD([{'deviceId':'2456','SD':7.6},{'deviceId':'2015','SD':2.8284271247461903},{'deviceId':'3125','SD':0.1},{'deviceId':'4156','SD': 4.242640687119285},{'deviceId':'5667','SD':0.7}],7);
      assert.deepStrictEqual(result,{bestAhuIds:['2456','4156','2015','5667','3125'],worstAhuIds:[]});
      
    } catch (err) {
      console.log(err);
      assert.fail('Exception calculate Standard Deviation');
    }
  });
});

describe('calculateStandardDeviation and getNBestAndWorstAhuIdsOnBasesofSD negative suit', async () => {

  it('calculateStandardDeviation with devicedata length as 0', async () => {
    try {
      let result = utils.calculateStandardDeviation([]);
      assert.strictEqual(result,null);
    } catch (err) {
      console.log(err);
      assert.fail('Exception calculate Standard Deviation');
    }
  });
  it('calculateStandardDeviation passing null,null', async () => {
    try {
      let result = utils.calculateStandardDeviation([{data:{kW:null}},{data:{kW:null}}],'kW');
      assert.strictEqual(result, null);
    } catch (err) {
      console.log(err);
      assert.fail('Exception calculate Standard Deviation');
    }
  });
  it('calculateStandardDeviation passing 1,undefined', async () => {
    try {
      let result = utils.calculateStandardDeviation([{data:{kW:1}},{data:{kW:undefined}}],'kW');
      assert.strictEqual(result, undefined);
    } catch (err) {
      console.log(err);
      assert.fail('Exception calculate Standard Deviation');
    }
  });  
  it('getNBestAndWorstAhuIdsOnBasesOfSD passing N=2 and passing one of the SD as undefined  ', async () => {
    try {
      let result = utils.getNBestAndWorstAhuIdsOnBasesOfSD([{'deviceId':'2456','SD':7.6},{'deviceId':'2015','SD':2.8284271247461903},{'deviceId':'3125','SD':-7},{'deviceId':'4156','SD': 4.242640687119285},{'deviceId':'5667','SD':undefined}],2);
      assert.deepStrictEqual(result,{bestAhuIds:['2456','4156'],worstAhuIds:['3125','5667']});
      
    } catch (err) {
      console.log(err);
      assert.fail('Exception calculate Standard Deviation');
    }
  });
});

