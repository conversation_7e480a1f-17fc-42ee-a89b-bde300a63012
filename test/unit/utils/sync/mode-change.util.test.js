const assert = require('chai').assert;
const modeChangeUtils = require('../../../../api/utils/sync/mode-change.util');

describe('Mode Change Utils Unit Tests', () => {
  describe('removePausedRecipes(): It removes the paused recipes from a list of recipes', () => {
    let emptyArray = [];
    let recipesWithOnePaused = [{
      switchOff: '1'
    }, {
      switchOff: '0'
    }];
    let recipesWithoutSwitchOff = [{
      rid: 'anything'
    }, {
      rid: 'anything',
      switchOff: '1'
    }];
    it('removePausedRecipes(): passing an empty array', (done) => {
      let output = modeChangeUtils.removePausedRecipes(emptyArray);
      assert.isArray(output);
      assert.lengthOf(output, 0);
      done();
    });

    it('removePausedRecipes(): passing 2 recipes with 1 paused', (done) => {
      let output = modeChangeUtils.removePausedRecipes(recipesWithOnePaused);
      assert.isArray(output);
      assert.lengthOf(output, 1);
      done();
    });

    it('removePausedRecipes(): passing a recipe w/o switchOff', (done) => {
      let output = modeChangeUtils.removePausedRecipes(recipesWithoutSwitchOff);
      assert.isArray(output);
      assert.lengthOf(output, 0);
      done();
    });
  });

  describe('getAffectedRecipesMap(): It finds and returns the affected recipes', () => {
    let recipes = [{
      actionable: [{
        type: 'action',
        did: '111',
        command: 'start'
      }],
      runOn: '5555'
    }];

    let modes = {
      '111.start': 'jouletrack',
      '111.stop': 'jouletrack'
    };

    let recipesWithoutActionable = [{
      runOn: '5555'
    }];

    let recipesTwoSameCtrl = [{
      actionable: [{
        type: 'action',
        did: '111',
        command: 'start'
      }],
      runOn: '5555'
    }, {
      actionable: [{
        type: 'action',
        did: '111',
        command: 'stop'
      }],
      runOn: '5555'
    }];

    let recipesTwoDiffCtrl = [{
      actionable: [{
        type: 'action',
        did: '111',
        command: 'start'
      }],
      runOn: '5555'
    }, {
      actionable: [{
        type: 'action',
        did: '111',
        command: 'stop'
      }],
      runOn: '6666'
    }];

    it('getAffectedRecipesMap(): Passing valid recipe and mode', (done) => {
      let output = modeChangeUtils.getAffectedRecipesMap(recipes, modes);
      assert.isObject(output);
      assert.hasAllDeepKeys(output, ['5555']);
      assert.deepNestedInclude(output, {'5555[0].didDotParam': '111.start'});
      done();
    });

    it('getAffectedRecipesMap(): No actionable', (done) => {
      let output = modeChangeUtils.getAffectedRecipesMap(recipesWithoutActionable, modes);
      assert.isObject(output);
      assert.deepEqual(output, {});
      done();
    });

    it('getAffectedRecipesMap(): 2 recipes of same controller', (done) => {
      let output = modeChangeUtils.getAffectedRecipesMap(recipesTwoSameCtrl, modes);
      assert.isObject(output);
      assert.hasAllDeepKeys(output, ['5555']);
      assert.lengthOf(output['5555'], 2);
      done();
    });

    it('getAffectedRecipesMap(): 2 recipes of different controller', (done) => {
      let output = modeChangeUtils.getAffectedRecipesMap(recipesTwoDiffCtrl, modes);
      assert.isObject(output);
      assert.hasAllDeepKeys(output, ['5555', '6666']);
      assert.lengthOf(output['5555'], 1);
      assert.lengthOf(output['6666'], 1);
      done();
    });
  });

  describe('getRecipePacketForNewModes(): It gets the final packet based on modes', (done) => {
    let emptyMap = {};

    let controllerRecipeMap = {
      '5555': [{didDotParam: '111.start'}, {didDotParam: '111.stop'}],
      '6666': [{didDotParam: '111.setfrequency'}]
    };
    let modes = {
      '111.start': 'jouletrack',
      '111.stop': 'joulerecipe',
      '111.setfrequency': 'thermostat'
    };

    it('getRecipePacketForNewModes(): Passing empty controllerRecipeMap', (done) => {
      let output = modeChangeUtils.getRecipePacketForNewModes(emptyMap, modes);
      assert.isObject(output);
      assert.deepEqual(output, {});
      done();
    });

    it('getRecipePacketForNewModes(): Passing 3 different modes', (done) => {
      let output = modeChangeUtils.getRecipePacketForNewModes(controllerRecipeMap, modes);
      assert.isObject(output);
      assert.hasAllDeepKeys(output, ['5555', '6666']);
      assert.equal(output['5555'][0]['switchOff'], '2');
      assert.equal(output['5555'][1]['switchOff'], '0');
      assert.equal(output['6666'][0]['switchOff'], '2');
      done();
    });

    it('getRecipePacketForNewModes(): Passing 3 different modes with apptype thermal', (done) => {
      controllerRecipeMap['6666'][0].appType = 'thermal';
      controllerRecipeMap['5555'][1].appType = 'thermal';
      let output = modeChangeUtils.getRecipePacketForNewModes(controllerRecipeMap, modes);
      assert.isObject(output);
      assert.hasAllDeepKeys(output, ['5555', '6666']);
      assert.equal(output['5555'][0]['switchOff'], '2');
      assert.equal(output['5555'][1]['switchOff'], '2');
      assert.equal(output['6666'][0]['switchOff'], '0');
      done();
    });
  });
});
