const assert = require('chai').assert;
const syncRecipeUtils = require('../../../../api/utils/sync/sync-recipe.util');

describe('Sycn Recipe Unit tests', () => {
  describe('filterAndFormSyncPacket(): This forms the final packet for controller', () => {
    let syncData = [{
      type: 'recipe_a',
      switchOff: '1'
    }];

    let ridList = ['a', 'b'];

    let syncDataWithMode = [{
      type: 'modes',
      switchOff: '1'
    }];

    let syncDataMultRecipes = [{
      type: 'recipe_a',
      switchOff: '1'
    }, {
      type: 'recipe_b',
      switchOff: '1'
    }];

    it('filterAndFormSyncPacket(): positive suit', (done) => {
      let output = syncRecipeUtils.filterAndFormSyncPacket(syncData, ridList);
      assert.isObject(output);
      assert.hasAllDeepKeys(output, 'a');
      done();
    });

    it('filterAndFormSyncPacket(): positive suit with type having modes', (done) => {
      let output = syncRecipeUtils.filterAndFormSyncPacket(syncDataWithMode, ridList);
      assert.isObject(output);
      assert.deepEqual(output, {});
      done();
    });

    it('filterAndFormSyncPacket(): positive suit with multiple recipes', (done) => {
      let output = syncRecipeUtils.filterAndFormSyncPacket(syncDataMultRecipes, ridList);
      assert.isObject(output);
      assert.hasAllDeepKeys(output, ['a', 'b']);
      done();
    });
  });

});
