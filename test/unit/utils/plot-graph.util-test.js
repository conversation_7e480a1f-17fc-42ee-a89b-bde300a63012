var assert = require('assert');
var mocha = require('mocha');
sails = require('sails');
const utils = require('../../../api/utils/datadevice/plot-graph.util');
let describe;
let it;
if (mocha.describe) {
  describe = mocha.describe;
  it = mocha.it;
  before = mocha.before;
  after = mocha.after;
} else {
  describe = global.describe;
  it = global.it;
  before = global.before;
  after = global.after;
}
describe('filterDataForLineGraph,filterDataForHeatMap and filterDataForSpectralGraph positive suit', async () => {

  it('filterDataForLineGraph, give data for line between two timestamp', async () => {
    try {
      let deviceDatas =[{timestamp:'2020-08-09 14:30:45',data:{kwh:15}}];
      let group ='day';
      let params =['kwh'];
      let startTime='2020-08-09 06:45';
      let endTime ='2020-08-10 06:45';
      let result = utils.filterDataForLineGraph(deviceDatas,group,params,startTime,endTime);
      assert.deepStrictEqual(result,{kwh:[[1596935700000,15]]});
    } catch (err) {
      console.log(err);
      assert.fail('Exception in data for Line Graph ');
    }
  });
  it('filterDataForLineGraph, give data for line between two timestamp params is empty', async () => {
    try {
      let deviceDatas =[{timestamp:'2020-08-09 14:30:45',data:{kwh:15}}];
      let group ='day';
      let params =[];
      let startTime='2020-08-09 06:45';
      let endTime ='2020-08-10 06:45';
      let result = utils.filterDataForLineGraph(deviceDatas,group,params,startTime,endTime);
      assert.deepStrictEqual(result,{});
    } catch (err) {
      console.log(err);
      assert.fail('Exception in data for Line Graph');
    }
  });
  it('filterDataForLineGraph, give data for line when start time is not given or empty', async () => {
    try {
      let deviceDatas =[{timestamp:'2020-08-09 14:30:45',data:{kwh:16}}];
      let group ='day';
      let params =['kwh'];
      let startTime='';
      let endTime ='2020-08-10 06:45';
      let result = utils.filterDataForLineGraph(deviceDatas,group,params,startTime,endTime);
      assert.deepStrictEqual(result,{kwh:[]});
    } catch (err) {
      console.log(err);
      assert.fail('Exception in data for Line Graph');
    }
  });
  it('filterDataForLineGraph, give data for line when end time is not given or empty', async () => {
    try {
      let deviceDatas =[{timestamp:'2020-08-09 14:30:45',data:{kwh:16}}];
      let group ='day';
      let params =['kwh'];
      let startTime='2020-08-09 06:45';
      let endTime ='';
      let result = utils.filterDataForLineGraph(deviceDatas,group,params,startTime,endTime);
      assert.deepStrictEqual(result,{kwh:[]});
    } catch (err) {
      console.log(err);
      assert.fail('Exception in data for Line Graph');
    }
  });
  it('filterDataForHeatMap, give data for heatmap', async () => {
    try {
      let deviceDatas =[{timestamp:'2020-08-09 14:30:45',data:{kwh:16}}];
      let group ='day';
      let params =['kwh'];
      let startTime='2020-08-09 06:45';
      let endTime ='2020-08-10 06:45';
      let result = utils.filterDataForHeatMap(deviceDatas,group,params,startTime,endTime);
      assert.deepStrictEqual(result,{kwh:{maximum:16,minimum:16,plot:[[0,6,16]]}});
    } catch (err) {
      console.log(err);
      assert.fail('Exception in data for Heat Map');
    }
  });
  it('filterDataForHeatMap, give data for heatmap passing startime is not given or empty', async () => {
    try {
      let deviceDatas =[{timestamp:'2020-08-09 14:30:45',data:{kwh:13}}];
      let group ='day';
      let params =['kwh'];
      let startTime='';
      let endTime ='2020-08-10 06:45';
      let result = utils.filterDataForHeatMap(deviceDatas,group,params,startTime,endTime);
      assert.deepStrictEqual(result,{kwh:{maximum:-Infinity,minimum:Infinity,plot:[]}});
    } catch (err) {
      console.log(err);
      assert.fail('Exception in data for Heat Map');
    }
  });
  it('filterDataForHeatMap, give data for heatmap passing endtime is not given or empty', async () => {
    try {
      let deviceDatas =[{timestamp:'2020-08-09 14:30:45',data:{kwh:13}}];
      let group ='day';
      let params =['kwh'];
      let startTime='2020-08-09 06:45';
      let endTime ='';
      let result = utils.filterDataForHeatMap(deviceDatas,group,params,startTime,endTime);
      assert.deepStrictEqual(result,{kwh:{maximum:-Infinity,minimum:Infinity,plot:[]}});
    } catch (err) {
      console.log(err);
      assert.fail('Exception in data for Heat Map');
    }
  });
  it('filterDataForHeatMap, give data for heatmap passing params empty', async () => {
    try {
      let deviceDatas =[{timestamp:'2020-08-09 14:30:45',data:{kwh:13}}];
      let group ='day';
      let params =[];
      let startTime='2020-08-09 06:45';
      let endTime ='2020-08-10 06:45';
      let result = utils.filterDataForHeatMap(deviceDatas,group,params,startTime,endTime);
      assert.deepStrictEqual(result,{});
    } catch (err) {
      console.log(err);
      assert.fail('Exception in data for Heat Map');
    }
  });
  it('filterDataForSpectralGraph, give data for Spectral Graph ', async () => {
    try {
      let deviceDatas =[{timestamp:'2020-08-09 14:30:45',data:{kwh:16}}];
      let params =['kwh'];
      let result = utils.filterDataForSpectralGraph(deviceDatas,params);
      assert.deepStrictEqual(result,{kwh:{'16':1}});
    } catch (err) {
      console.log(err);
      assert.fail('Exception in data for Spectral Graph');
    }
  });
  it('filterDataForSpectralGraph, give data for Spectral Graph when param is empty ', async () => {
    try {
      let deviceDatas =[{timestamp:'2020-08-09 14:30:45',data:{kwh:16}}];
      let params =[];
      let result = utils.filterDataForSpectralGraph(deviceDatas,params);
      assert.deepStrictEqual(result,{});
    } catch (err) {
      console.log(err);
      assert.fail('Exception in data for Spectral Graph');
    }
  });
  it('filterDataForSpectralGraph, give data for Spectral Graph when data is empty ', async () => {
    try {
      let deviceDatas =[{timestamp:'2020-08-09 14:30:45',data:{}}];
      let params =['kwh'];
      let result = utils.filterDataForSpectralGraph(deviceDatas,params);
      assert.deepStrictEqual(result,{kwh:{}});
    } catch (err) {
      console.log(err);
      assert.fail('Exception in data for Spectral Graph');
    }
  });
});
describe('filterDataForLineGraph filterDataForHeatMap and filterDataForSpectralGraph negative  suit', async () => {

  it('filterDataForLineGraph, give data for line between two timestamp when Kwh is undefined', async () => {
    try {
      let deviceDatas =[{timestamp:'2020-08-09 14:30:45',data:{kwh:undefined}}];
      let group ='day';
      let params =['kwh'];
      let startTime='2020-08-09 06:45';
      let endTime ='2020-08-10 06:45';
      let result = utils.filterDataForLineGraph(deviceDatas,group,params,startTime,endTime);
      assert.deepStrictEqual(result,{kwh:[[1596935700000,NaN]]});
    } catch (err) {
      console.log(err);
      assert.fail('Exception in data for Line Graph');
    }
  });
  it('filterDataForLineGraph, give data for line between two timestamp with Kwh as null', async () => {
    try {
      let deviceDatas =[{timestamp:'2020-08-09 14:30:45',data:{kwh:null}}];
      let group ='day';
      let params =['kwh'];
      let startTime='2020-08-09 06:45';
      let endTime ='2020-08-10 06:45';
      let result = utils.filterDataForLineGraph(deviceDatas,group,params,startTime,endTime);
      assert.deepStrictEqual(result,{kwh:[[1596935700000,NaN]]});
    } catch (err) {
      console.log(err);
      assert.fail('Exception in data for Line Graph');
    }
  });
  it('filterDataForLineGraph, give data for line when startTime is undefined', async () => {
    try {
      let deviceDatas =[{timestamp:'2020-08-09 14:30:45',data:{kwh:15}}];
      let group ='day';
      let params =['kwh'];
      let startTime=undefined;
      let endTime ='2020-08-10 06:45';
      let result = utils.filterDataForLineGraph(deviceDatas,group,params,startTime,endTime);
      assert.deepStrictEqual(result,{kwh:[]});
    } catch (err) {
      console.log(err);
      assert.fail('Exception in data for Line Graph');
    }
  });
  it('filterDataForLineGraph, give data for line when timestamp is not between startime and endtime', async () => {
    try {
      let deviceDatas =[{timestamp:'2020-08-09 05:30:00',data:{kwh:15}}];
      let group ='day';
      let params =['kwh'];
      let startTime='2020-08-09 06:45';
      let endTime ='2020-08-10 06:45';
      let result = utils.filterDataForLineGraph(deviceDatas,group,params,startTime,endTime);
      assert.deepStrictEqual(result,{kwh:[[1596935700000,NaN]]});
    } catch (err) {
      console.log(err);
      assert.fail('Exception in data for Line Graph');
    }
  });
  it('filterDataForLineGraph, give error on data device problems', async () => {
    try {
      let deviceDatas =[{timestamp:'2020-08-09 05:30:00',problems:['we have issue in data']}];
      let group ='day';
      let params =['kwh'];
      let startTime='2020-08-09 06:45';
      let endTime ='2020-08-10 06:45';
      let result = utils.filterDataForLineGraph(deviceDatas,group,params,startTime,endTime);
      assert.deepStrictEqual(result,{kwh:[[1596935700000,NaN]]});
    } catch (err) {
      console.log(err);
      assert.fail('Exception in data for Line Graph');
    }
  });
  it('filterDataForHeatMap, give data for heatmap between two timestamp when Kwh is undefined', async () => {
    try {
      let deviceDatas =[{timestamp:'2020-08-09 14:30:45',data:{kwh:undefined}}];
      let group ='day';
      let params =['kwh'];
      let startTime='2020-08-09 06:45';
      let endTime ='2020-08-10 06:45';
      let result = utils.filterDataForHeatMap(deviceDatas,group,params,startTime,endTime);
      assert.deepStrictEqual(result,{kwh:{maximum:NaN,minimum:NaN,plot:[[0,6,NaN]]}});
    } catch (err) {
      console.log(err);
      assert.fail('Exception in data for Heat Map');
    }
  });
  it('filterDataForHeatMap, give data for heatmap between two timestamp with Kwh as null', async () => {
    try {
      let deviceDatas =[{timestamp:'2020-08-09 14:30:45',data:{kwh:null}}];
      let group ='day';
      let params =['kwh'];
      let startTime='2020-08-09 06:45';
      let endTime ='2020-08-10 06:45';
      let result = utils.filterDataForHeatMap(deviceDatas,group,params,startTime,endTime);
      assert.deepStrictEqual(result,{kwh:{maximum:NaN,minimum:NaN,plot:[[0,6,NaN]]}});
    } catch (err) {
      console.log(err);
      assert.fail('Exception in data for Heat Map');
    }
  });
  it('filterDataForHeatMap, give data for heatmpa when startTime is undefined', async () => {
    try {
      let deviceDatas =[{timestamp:'2020-08-09 14:30:45',data:{kwh:15}}];
      let group ='day';
      let params =['kwh'];
      let startTime=undefined;
      let endTime ='2020-08-10 06:45';
      let result = utils.filterDataForHeatMap(deviceDatas,group,params,startTime,endTime);
      assert.deepStrictEqual(result,{kwh:{maximum:-Infinity,minimum:Infinity,plot:[]}});
    } catch (err) {
      console.log(err);
      assert.fail('Exception in data for Heat Map');
    }
  });
  it('filterDataForHeatMap, give error on data device problems', async () => {
    try {
      let deviceDatas =[{timestamp:'2020-08-09 05:30',problems:['we have issue in data']}];
      let group ='day';
      let params =['kwh'];
      let startTime='2020-08-09 06:45';
      let endTime ='2020-08-10 06:45';
      let result = utils.filterDataForHeatMap(deviceDatas,group,params,startTime,endTime);
      assert.deepStrictEqual(result,{kwh:{maximum:NaN,minimum:NaN,plot:[[0,6,NaN]]}});
    } catch (err) {
      console.log(err);
      assert.fail('Exception in data for Heat Map');
    }
  });
  it('filterDataForSpectralGraph, give data for Spectral Graph when kwh is undefined in data param ', async () => {
    try {
      let deviceDatas =[{timestamp:'2020-08-09 14:30:45',data:{kwh:undefined}}];
      let params =['kwh'];
      let result = utils.filterDataForSpectralGraph(deviceDatas,params);
      assert.deepStrictEqual(result,{kwh:{}});
    } catch (err) {
      console.log(err);
      assert.fail('Exception in data for Spectral Graph');
    }
  });
  it('filterDataForSpectralGraph, give data for Spectral Graph when kwh is null in data param ', async () => {
    try {
      let deviceDatas =[{timestamp:'2020-08-09 14:30:45',data:{kwh:null}}];
      let params =['kwh'];
      let result = utils.filterDataForSpectralGraph(deviceDatas,params);
      assert.deepStrictEqual(result,{kwh:{}});
    } catch (err) {
      console.log(err);
      assert.fail('Exception in data for Spectral Graph');
    }
  });
  it('filterDataForSpectralGraph, give data for Spectral Graph when params is undefined', async () => {
    try {
      let deviceDatas =[{timestamp:'2020-08-09 14:30:45',data:{kwh:16}}];
      let params =undefined;
      let result = utils.filterDataForSpectralGraph(deviceDatas,params);
      assert.deepStrictEqual(result,{kwh:{}});
    } catch (err) {
      console.log(err);
      assert.fail('Exception in data for Spectral Graph');
    }
  });
});
