const assert = require('chai').assert;
const createRecipeUtils = require('../../../../api/utils/recipe/createrecipe.util');

describe('create-recipe Utils', () => {
  describe('buildInitialPacket(): It creates the initial recipe packet', () => {
    let inputPacket = {
      siteId: 'ssh',
      type: 'alert',
      neo: 'anything',
      isSchedule: 'false',
      label: '1',
      formula: '||#1||$3||1',
      params: '',
      operators: '',
      maxDataNeeded: '1',
      componentId: 'ssh_6',
      recipelabel: ['pidrecipe'],
      _userMeta: {
        id: 'ghost'
      },
      actionable: [{ 'title': 'test-1', 
        'description': 'none', 
        'notify': ['<EMAIL>'], 
        'accountable': ['<EMAIL>'], 
        'type': 'alert', 
        'priority': 0 
      }]
    };

    it('buildInitialPacket(): passing invalid recipe label', (done) => {
      let newInputPacket = {...inputPacket};
      newInputPacket.recipelabel = ['invalid label'];
      let outputInitialPacket = createRecipeUtils.buildInitialPacket(newInputPacket);
      assert.hasAllKeys(outputInitialPacket, ['problems']);
      assert.equal(outputInitialPacket.problems[0], 'Invalid Recipe Label');
      done();
    });

    it('buildInitialPacket(): passing appType=recipe and isScheule=false', (done) => {
      let outputInitialPacket = createRecipeUtils.buildInitialPacket(inputPacket);
      assert.isObject(outputInitialPacket, 'should be an object');
      assert.hasAllDeepKeys(outputInitialPacket, ['isSchedule', 'failsafe', 'isStage', 'isActive', 'notRun', 'switchOff', 'alwaysRun', 'scheduled',
        'maxDataNeeded', 'rid', 'siteId', 'type', 'label', 'user', 'neo', 'appType', 'actionable', 'recipelabel', 'formula', 'operators', 'params']);
      done();
    });

    it('buildInitialPacket(): passing appType=thermal', (done) => {
      let newInputPacket = {...inputPacket};
      newInputPacket.appType = 'thermal';
      let outputInitialPacket = createRecipeUtils.buildInitialPacket(newInputPacket);
      outputInitialPacket.appType = 'thermal';
      assert.isObject(outputInitialPacket, 'should be an object');
      assert.hasAllDeepKeys(outputInitialPacket, ['isSchedule', 'failsafe', 'isStage', 'isActive', 'notRun', 'switchOff', 'alwaysRun', 'scheduled',
        'maxDataNeeded', 'rid', 'siteId', 'type', 'label', 'user', 'neo', 'appType', 'actionable', 'recipelabel', 'formula', 'operators', 'params',
        'componentId', 'misc']);
      done();
    });
  });

  describe('isServerRecipe(): Checks if it should run onserver or not', () => {
    let runOnPreference = true;
    let deviceIdList = [];
    let formula = undefined;

    it ('isServerRecipe(): runOnPrefernece is true', (done) => {
      let output = createRecipeUtils.isServerRecipe(runOnPreference, deviceIdList, formula);
      assert.isTrue(output, 'should be true');
      done();
    });

    it ('isServerRecipe(): runOnPrefernece is false with no deviceidList', (done) => {
      runOnPreference = false;
      let output = createRecipeUtils.isServerRecipe(runOnPreference, deviceIdList, formula);
      assert.isTrue(output, 'should be true');
      done();
    });

    it ('isServerRecipe(): runOnPrefernece is false with no deviceidList but with formula', (done) => {
      runOnPreference = false;
      formula = 'valueAt|isNan';
      let output = createRecipeUtils.isServerRecipe(runOnPreference, deviceIdList, formula);
      assert.isTrue(output, 'should be true');
      done();
    });

    it ('isServerRecipe(): runOnPrefernece is false with no deviceidList but with formula', (done) => {
      runOnPreference = false;
      formula = 'invalid formula';
      let output = createRecipeUtils.isServerRecipe(runOnPreference, deviceIdList, formula);
      assert.isFalse(output, 'should be false');
      done();
    });
  });

  describe('getControllerIds(): Gets all controllerIds from list of devices', () => {
    let deviceList = [
      {deviceType: 'joulesense', deviceId: '1111'}, 
      {deviceType: 'joulebox', controllerId: '1234'},
      {deviceType: 'em', deviceId: '1122'}
    ];

    it('getControllerIds(): passing valid device list', (done) => {
      let controllerIdsList = createRecipeUtils.getControllerIds(deviceList);
      assert.isArray(controllerIdsList, 'should be an array');
      assert.lengthOf(controllerIdsList, 2, 'length should be 2');
      assert.equal(controllerIdsList[0], '1234', 'output is incorrect');
      assert.equal(controllerIdsList[1], '1122', 'output is incorrect');
      done();
    });

    it('getControllerIds(): passing stringified list', (done) => {
      let stringifiedList = JSON.stringify(deviceList);
      let controllerIdsList = createRecipeUtils.getControllerIds(stringifiedList);
      assert.isArray(controllerIdsList, 'should be an array');
      assert.lengthOf(controllerIdsList, 2, 'length should be 2');
      assert.equal(controllerIdsList[0], '1234', 'output is incorrect');
      assert.equal(controllerIdsList[1], '1122', 'output is incorrect');
      done();
    });
  });

  describe('getDeviceIdType(): It tells the type of deviceId', () => {
    const did = '1234';
    const componentId = 'ssh_6';

    it ('getDeviceIdType(): passing valid device id', (done) => {
      let deviceType = createRecipeUtils.getDeviceIdType(did);
      assert.equal(deviceType, 'deviceId', 'should be of type deviceId');
      done();
    });

    it ('getDeviceIdType(): passing valid componentId', (done) => {
      let deviceType = createRecipeUtils.getDeviceIdType(componentId);
      assert.equal(deviceType, 'compId', 'should be of type compId');
      done();
    });
  });

  describe('getParameterFromComponentsData(): Find device Id of param in data list of component', () => {
    const dataList = [{key: 'kwh'}];

    it('getParameterFromComponentsData(): Passing a valid data list and param', (done) => {
      let param = 'kwh';
      let output = createRecipeUtils.getParameterFromComponentsData(dataList, param);
      assert.deepEqual(output, {key: 'kwh'}, 'incorrect ouput');
      done();
    });

    it('getParameterFromComponentsData(): Passing a valid data list without param', (done) => {
      let param = 'kVah';
      let output = createRecipeUtils.getParameterFromComponentsData(dataList, param);
      assert.isUndefined(output, 'should be undefined');
      done();
    });
  });

  describe('getParameterFromProcessData(): Find device Id of param in rawParams list of process', () => {
    const dataList = [{abbr: 'kwh'}];

    it('getParameterFromProcessData(): Passing a valid data list and param', (done) => {
      let param = 'kwh';
      let output = createRecipeUtils.getParameterFromProcessData(dataList, param);
      assert.deepEqual(output, {abbr: 'kwh'}, 'incorrect ouput');
      done();
    });

    it('getParameterFromProcessData(): Passing a valid data list without param', (done) => {
      let param = 'kVah';
      let output = createRecipeUtils.getParameterFromProcessData(dataList, param);
      assert.isUndefined(output, 'should be undefined');
      done();
    });
  });

  describe('createFormula(): Create Formula from available data', () => {
    let deviceParamList = [['ssh_6', 'kvah', false]];
    let formula = '||#1||$3||1';
    let operators = { '$1': '-', '$2': '/', '$3': '>', '$4': '(', '$5': ')' };
    let params = { '#1': 'ssh_6.kvah' };
    let size = '2';

    it('createFormula(): passing valid arguments', (done) => {
      let outputObject = createRecipeUtils.createFormula(deviceParamList, params, formula, size, operators);
      assert.isObject(outputObject, 'should be an object');
      assert.hasAllKeys(outputObject, ['runOnServer', 'didArray', 'topicArray', 'formula']);
      assert.isFalse(outputObject.runOnServer, 'runOnServer should be false');
      assert.deepEqual(outputObject.didArray, [ 'ssh_6' ], 'should be an array with dids');
      assert.deepEqual(outputObject.topicArray, [ 'db/ssh_6/kvah/2' ], 'should be an array with topic');
      assert.deepEqual(outputObject.formula, '||ssh_6.kvah.2||>||1', 'formula is incorrecrt');
      done();
    });

    it('createFormula(): passing invalid arguments', (done) => {
      deviceParamList = JSON.stringify(deviceParamList);

      assert.throws(() => {
        createRecipeUtils.createFormula(deviceParamList, params, formula, size, operators);
      }, Error);
      done();
    });
  });
});
