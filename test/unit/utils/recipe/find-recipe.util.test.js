const assert = require('chai').assert;
const findRecipeUtils = require('../../../../api/utils/recipe/find-recipe.util');

describe('Find Recipe Utils Unit Tests', () => {
  describe('JSONifyRecipeObjectForFrontEnd(): It converts the stringified nested object to JSON', () => {
    const recipeObjectWithSchedule = {
      isSchedule: 'true',
      isActive: 0,
      recipelabel: JSON.stringify(['comfort']),
      actionable: '[]'
    };

    const recipeObjectWithoutSchedule = {
      isSchedule: 'false',
      isActive: 0,
      recipelabel: JSON.stringify(['comfort']),
      actionable: '[]',
      operator: JSON.stringify({'#1': '>'}),
      params: JSON.stringify({})
    };

    it('JSONifyRecipeObjectForFrontEnd(): passing object with isSchedule true', (done) => {
      let recipeOutput = findRecipeUtils.JSONifyRecipeObjectForFrontEnd(recipeObjectWithSchedule);
      assert.isTrue(recipeOutput.isSchedule, 'isSchedule should be true');
      assert.isArray(recipeOutput.recipelabel, 'recipelabel should be array');
      assert.isArray(recipeOutput.actionable, 'actionable should be array');
      done();
    });

    it('JSONifyRecipeObjectForFrontEnd(): passing object with isSchedule false', (done) => {
      let recipeOutput = findRecipeUtils.JSONifyRecipeObjectForFrontEnd(recipeObjectWithoutSchedule);
      assert.isFalse(recipeOutput.isSchedule, 'isSchedule should be false');
      assert.isArray(recipeOutput.recipelabel, 'recipelabel should be array');
      assert.isArray(recipeOutput.actionable, 'actionable should be array');
      assert.isObject(recipeOutput.operators, 'operators should be an Object');
      assert.isObject(recipeOutput.params, 'params should be an Object');
      assert.doesNotHaveAllDeepKeys(recipeOutput, ['operator'], 'operator key should not be present');
      done();
    });
  });
});
