const assert = require('chai').assert;
const findScheduleUtil = require('../../../../api/utils/recipe/find-schedule.util');

describe('Find Schedule Util Unit Tests', () => {
  describe('parseScheduleObjectForFrontEnd(): Parses the schedule object for front end', () => {
    let scheduleObjectDifferent = {
      ts: [['2020-02-20 11:11', '2020-02-20 23:00', '0 0 * * *']]
    };

    let scheduleObjectSame = {
      ts: [['2020-02-20 11:11', '2020-02-20 11:11', '0 0 * * *']]
    };

    it('parseScheduleObjectForFrontEnd(): passing different start and end time', (done) => {
      let parsedObject = findScheduleUtil.parseScheduleObjectForFrontEnd(scheduleObjectDifferent);
      assert.isObject(parsedObject, 'output should be an object');
      assert.hasAllDeepKeys(parsedObject, ['schedule', 'startDate', 'startTime', 'endDate', 'endTime']);
      assert.equal(parsedObject.startDate, '2020-02-20');
      assert.equal(parsedObject.endDate, '2020-02-20');
      assert.equal(parsedObject.startTime, '11:11');
      assert.equal(parsedObject.endTime, '23:00');
      assert.isArray(parsedObject.schedule);
      done();
    });

    it('parseScheduleObjectForFrontEnd(): passing same start and end time', (done) => {
      let parsedObject = findScheduleUtil.parseScheduleObjectForFrontEnd(scheduleObjectSame);
      assert.isObject(parsedObject, 'output should be an object');
      assert.hasAllDeepKeys(parsedObject, ['schedule', 'startDate', 'startTime', 'endDate', 'endTime']);
      assert.equal(parsedObject.startDate, '2020-02-20');
      assert.equal(parsedObject.endDate, '2020-02-20');
      assert.equal(parsedObject.startTime, '11:11');
      assert.equal(parsedObject.endTime, '11:41');
      assert.isArray(parsedObject.schedule);
      done();
    });
  });
});
