const assert = require('chai').assert;
const deployScheduleUtils = require('../../../../api/utils/recipe/deployschedule.util');

describe('Deploy Schedule Util Tests', () => {
  describe('getSendObj(): It forms the recipe and schedule object to send to controller', () => {
    it('getSendObj(): passing valid arguments', (done) => {
      let recipeObject = {
        everyMinuteTopics: '[]',
        runOn: '1122',
        formula: '#1 $2 #3',
        dependentOnOthers: '[]',
        rid: 'assdsv-w353geb-vwergrq3-va-4dv',
        notRun: '',
        alwaysRun: false
      };
      let formattedScheduleInfo = [];
      let formattedToSendAction = '[]';
      let outputObject = deployScheduleUtils.getSendObj(recipeObject, formattedScheduleInfo, formattedToSendAction);
      assert.isObject(outputObject, 'should be of type object');
      assert.hasAllDeepKeys(outputObject, ['recipeInfo', 'scheduleInfo', 'operation'], 'Incorrect keys');
      assert.equal(outputObject.operation, 'recipeInit', 'wrong value of operation key');
      done();
    });

    it('getSendObj(): passing valid arguments with misc key', (done) => {
      let recipeObject = {
        everyMinuteTopics: '[]',
        runOn: '1122',
        formula: '#1 $2 #3',
        dependentOnOthers: '[]',
        rid: 'assdsv-w353geb-vwergrq3-va-4dv',
        notRun: '',
        alwaysRun: false,
        appType: 'thermal',
        misc: {
          appSettings: {},
          controlSettings: {},
        }
      };
      let formattedScheduleInfo = [];
      let formattedToSendAction = '[]';
      let outputObject = deployScheduleUtils.getSendObj(recipeObject, formattedScheduleInfo, formattedToSendAction);
      assert.isObject(outputObject, 'should be of type object');
      assert.hasAllDeepKeys(outputObject, ['recipeInfo', 'scheduleInfo', 'operation'], 'Incorrect keys');
      assert.equal(outputObject.operation, 'recipeInit', 'wrong value of operation key');
      assert.deepEqual(outputObject.recipeInfo.misc, {appSettings: {}, controlSettings: {}}, 'invald misc info');
      done();
    });
  });
});
