const utils = require('../../../../api/utils/recipe/utils');
const assert = require('chai').assert;

describe('Recipe Utils', () => {
  describe('GetRecipeCategory()', () => {

    const validCategory = 'process';
    const unknownCategory = 'unknown';
    const pidRecipe = 'pidrecipe';
    const inputEmptyString = '';

    it('getRecipeCategory: passing valid Recipe category', (done) => {
      let outputValidRecipeCategory = utils.getRecipeCategory(validCategory);
      assert.typeOf(outputValidRecipeCategory, 'string');
      assert.equal(outputValidRecipeCategory, 'recipeprocess');
      done();
    });

    it('getRecipeCategory: passing unknown category', (done) => {
      let outputUnknownCategory = utils.getRecipeCategory(unknownCategory);
      assert.isUndefined(outputUnknownCategory, 'should return undefined');
      done();
    });

    it('getRecipeCategory: passing empty input string', (done) => {
      let outputEmptyString= utils.getRecipeCategory(inputEmptyString);
      assert.isUndefined(outputEmptyString, 'should return undefined');
      done();
    });

    it('getRecipeCategory: passing pidrecipe', (done) => {
      let outputPidRecipe = utils.getRecipeCategory(pidRecipe);
      assert.equal(outputPidRecipe, 'pidrecipe', 'output should be pidrecipe');
      done();
    });
  });

  describe('getDevicesInsideActionable()', () => {

    const invalidStringInput = 'any string';
    const validParam = [{'did':'1168','command':'changesetpoint','parent':'ssh_26',
      'value':'evaluate','title':'thermal_app','type':'action','notify':[],
      'accountable':[],'priority':1,'uniqId':'a5d5afd22a528b55717cc508bcb0d932',
      'category':['energydiagnosticrecipe']}];
    const stringValidParam = JSON.stringify(validParam);
    const invalidDeviceIdParam = JSON.stringify([{'didd':'1168','command':'changesetpoint','parent':'ssh_26',
      'value':'evaluate','title':'thermal_app','type':'action','notify':[],
      'accountable':[],'priority':1,'uniqId':'a5d5afd22a528b55717cc508bcb0d932',
      'category':['energydiagnosticrecipe']}]);

    it('If string is passed instead of array', (done) => {
      let outputForInvalidString = utils.getDevicesInsideActionable(invalidStringInput);
      assert.isEmpty(outputForInvalidString, 'Array should be empty');
      done();
    });

    it('Valid array is passed it should return dids', (done) => {
      let outputValidParam = utils.getDevicesInsideActionable(validParam);
      assert.deepEqual(outputValidParam, ['1168'], 'should return valid device id');
      done();
    });

    it('Stringified array is passed it should return dids', (done) => {
      let outputStringValidParam = utils.getDevicesInsideActionable(stringValidParam);
      assert.deepEqual(outputStringValidParam, ['1168'], 'should return valid device id');
      done();
    });

    it('Invalid did is passed', (done) => {
      let outputInvalidDidParam = utils.getDevicesInsideActionable(invalidDeviceIdParam);
      assert.isEmpty(outputInvalidDidParam, 'should return empty array');
      done();
    });
  });

  describe('isFormulaCorrect()', () => {
    const inputParam = {};

    it('should always return true for now, func not implemented', (done) => {
      let output = utils.isFormulaCorrect(inputParam);
      assert.isTrue(output);
      done();
    });
  });

  describe('stripFormula()', () => {

    const inputNoEndPipes = '||#1||$3||1';
    const inputWithEndPipes = '||#1||$3||1||';

    it('Replace pipes with space in formula, end pipes not present', (done) => {
      let outputNoEndPipes = utils.stripFormula(inputNoEndPipes);
      assert.equal(outputNoEndPipes, '#1 $3 1', 'should be a string seperated with spaces');
      done();
    });

    it('Replace pipes with space in formula, end pipes are present', (done) => {
      let outputWithEndPipes = utils.stripFormula(inputWithEndPipes);
      assert.equal(outputWithEndPipes, '#1 $3 1', 'should be a string seperated with spaces');
      done();
    });
  });

  describe('regexReplace()', () => {

    const findString = 'a';
    const replaceString = 'b';
    const targetString = 'aaaa';

    it('Replace all \'a\' with \'b\'', (done) => {
      let output = utils.regexReplace(findString, replaceString, targetString);
      assert.equal(output, 'bbbb', 'should be "bbbb"');
      done();
    });

    it('Replace all \'a\' with \'b\' should not return input string', (done) => {
      let output = utils.regexReplace(findString, replaceString, targetString);
      assert.notEqual(output, 'aaaa', 'should not be "aaaa"');
      done();
    });
  });

  describe('haveCalculatedParameter()', () => {

    const inputExpressionWithCalcParam = ['4323@feedback', '>', '0'];
    const inputExpressionWithoutCalcParam = ['2966@outputfrequency'];
    const inputStringExpression = JSON.stringify(inputExpressionWithoutCalcParam);

    it('should return true if calc param is present', (done) => {
      let outputWithCalcParam = utils.haveCalculatedParameter(inputExpressionWithCalcParam);
      assert.isTrue(outputWithCalcParam, 'should be true');
      done();
    });

    it('should return false if calc param is not present', (done) => {
      let outputWithoutCalcParam = utils.haveCalculatedParameter(inputExpressionWithoutCalcParam);
      assert.isFalse(outputWithoutCalcParam, 'should return false');
      done();
    });

    it('should return false, expression passed as string', (done) => {
      let outputForStringExpression = utils.haveCalculatedParameter(inputStringExpression);
      assert.isFalse(outputForStringExpression, 'should return false');
      done();
    });
  });

  describe('getMaxCountElement()', () => {

    const inputDidArray = ['1111', '1112', '1111'];
    const inputStringDidArray = JSON.stringify(inputDidArray);

    it('should return a did whose count is more', (done) => {
      let outputDid = utils.getMaxCountElement(inputDidArray);
      assert.equal(outputDid, '1111', 'should return 1111 in string');
      done();
    });

    it('should throw an error if type array is not passed', (done) => {
      assert.throws(() => {
        utils.getMaxCountElement(inputStringDidArray);
      }, Error);
      done();
    });
  });

  describe('parseTime()', () => {

  });

  describe('isScheduleDeployed()', () => {

    const inputScheduleIsDeployed = {isDeployed: '1'};
    const inputScheduleIsNotDeployed = {isDeployed: '0'};
    const undefinedInput = undefined;

    it('should return true if schedule\'s isDeployed is 1', (done) => {
      let outputIsDeployed = utils.isScheduleDeployed(inputScheduleIsDeployed);
      assert.isTrue(outputIsDeployed, 'should return true');
      done();
    });

    it('should return false if schedule\'s isDeployed is 0', (done) => {
      let outputNotDeployed = utils.isScheduleDeployed(inputScheduleIsNotDeployed);
      assert.isFalse(outputNotDeployed, 'should return false');
      done();
    });

    it('should return false if undefined is passed', (done) => {
      let outputForUndefined = utils.isScheduleDeployed(undefinedInput);
      assert.isFalse(outputForUndefined, 'should return false');
      done();
    });
  });

  describe('flattenSchedule()', () => {
    const inputEmptySchedule = '';
    const inputCorrectSchedue = {
      ts: [['10-07-2020', '15-07-2020', '* * 0/2 * *'], ['15-07-2020', '15-07-2020', '* 2 0/2 * *']],
      rid: '1256987poiu-8765thgtTg',
      sid: '45897daf7f5-d4efd48efe'
    };
    const inputStringifiedSchedule = JSON.stringify(inputCorrectSchedue);
    it('passing empty parameter instead of schedule, should throw error', (done) => {
      assert.throws(() => {
        utils.flattenSchedule(inputEmptySchedule);
      }, Error);
      done();
    });

    it('passing correct schedule object, should return flattend object', (done) => {
      let outputCorrectSchedule = utils.flattenSchedule(inputCorrectSchedue);
      assert.isArray(outputCorrectSchedule, 'should be an array off schedule objects');
      assert.lengthOf(outputCorrectSchedule, 2, 'should be 2 objects in the returned array');
      assert.hasAllDeepKeys(outputCorrectSchedule[0], ['rid', 'id', 'cron', 'startdate', 'enddate'], 'object structure is incorrect');
      assert.hasAllDeepKeys(outputCorrectSchedule[1], ['rid', 'id', 'cron', 'startdate', 'enddate'], 'object structure is incorrect');
      done();
    });

    it('passing correct strinigifed schedule object, should return flattend object', (done) => {
      let outputCorrectSchedule = utils.flattenSchedule(inputStringifiedSchedule);
      assert.isArray(outputCorrectSchedule, 'should be an array off schedule objects');
      assert.lengthOf(outputCorrectSchedule, 2, 'should be 2 objects in the returned array');
      assert.hasAllDeepKeys(outputCorrectSchedule[0], ['rid', 'id', 'cron', 'startdate', 'enddate'], 'object structure is incorrect');
      assert.hasAllDeepKeys(outputCorrectSchedule[1], ['rid', 'id', 'cron', 'startdate', 'enddate'], 'object structure is incorrect');
      done();
    });
  });

  describe('absCron()', () => {
    const inputUndefined = undefined;
    const inputCronArrayWithStarts = ['* * 0/2 * *'];
    const inputCronArrayWithoutStars = ['1 2 0/2 * *'];

    it('passing undefined cron array, should return null', (done) => {
      let outputForUndefinedCronArray = utils.absCron(inputUndefined);
      assert.isNull(outputForUndefinedCronArray, 'should return NULL');
      done();
    });

    it('passing cron array with first 2 stars', (done) => {
      let outputCronArrayWithStars = utils.absCron(inputCronArrayWithStarts);
      assert.isArray(outputCronArrayWithStars, 'should return an array');
      assert.deepEqual(outputCronArrayWithStars, inputCronArrayWithStarts);
      done();
    });

    it('passing cron array without first 2 stars', (done) => {
      let outputCronArrayWithoutStars = utils.absCron(inputCronArrayWithoutStars);
      assert.isArray(outputCronArrayWithoutStars, 'should return an array');
      assert.deepEqual(outputCronArrayWithoutStars, inputCronArrayWithStarts);
      done();
    });
  });

  describe('formatScheduleInfo()', () => {
    const inputCorrectScheduleInfo = [{
      ts: [['10-07-2020', '15-07-2020', '* * 0/2 * *'], ['15-07-2020', '15-07-2020', '* 2 0/2 * *']],
      rid: '1256987poiu-8765thgtTg',
      sid: '45897daf7f5-d4efd48efe'
    }];

    it('passing correct schedule info array', (done) => {
      let outputFormatScheduleInfo = utils.formatScheduleInfo(inputCorrectScheduleInfo);
      assert.isArray(outputFormatScheduleInfo, 'should be an array');
      assert.isArray(outputFormatScheduleInfo[0], 'should be an array');
      assert.lengthOf(outputFormatScheduleInfo[0], 2, 'length of inner array should be 2');
      assert.hasAllKeys(outputFormatScheduleInfo[0][0], ['rid', 'id', 'cron', 'startdate', 'enddate']);
      done();
    });
  });

  describe('getFormattedAction()', () => {
    const inputActionArrayTypeAction = [
      {
        notify: '<EMAIL>',
        type: 'action',
        uniqId: '7d8d9dvsdv965-dsvsd5v4-3w5rfesdgfe',
        did: '1234',
        command: '1234.setpoint',
        value: 22,
        delay: 10,
        category: 'Any category'
      }
    ];

    const inputActionArrayTypeAlert = [
      {
        notify: '<EMAIL>',
        type: 'alert',
        uniqId: '7d8d9dvsdv965-dsvsd5v4-3w5rfesdgfe',
        category: 'Any category'
      }
    ];

    it('passing action array with type action', (done) => {
      let outputTypeAction = utils.getFormattedAction(inputActionArrayTypeAction);
      assert.isArray(outputTypeAction, 'should be an array');
      assert.hasAllKeys(outputTypeAction[0], ['title', 'type', 'priority', 'uniqId', 'did', 'command', 'value', 'delay', 'category', 'notify', 'smslist',], 'Incorrect object is returned');
      done();
    });

    it('passing action array with type alert', (done) => {
      let outputTypeAlert = utils.getFormattedAction(inputActionArrayTypeAlert);
      assert.isArray(outputTypeAlert, 'should be an array');
      assert.hasAllKeys(outputTypeAlert[0], ['title', 'type', 'priority', 'uniqId', 'category', 'notify', 'smslist',], 'Incorrect object is returned');
      done();
    });
  });

  describe('removeDeployedSchedules()', () => {
    const inputSchedulesWithAllDeployed = [
      {
        sid: 'sdkljsdv3455-vdsv45-4332sdfsv',
        isDeployed: '1',
        repeat_type: 'daily',
        rid: '8a9c452d-e6c6-480b-b819-1f3903811aa1',
        runOn: '2826',
        siteId: 'ssh',
        ts: [['2020-07-28 00:01', '2020-07-28 00:01', '1-59/1 0 * * *']]
      }
    ];

    const inputSchedulesWithAllNotDeployed = [
      {
        sid: 'sdkljsdv3455-vdsv45-4332sdfsv',
        isDeployed: '0',
        repeat_type: 'daily',
        rid: '8a9c452d-e6c6-480b-b819-1f3903811aa1',
        runOn: '2826',
        siteId: 'ssh',
        ts: [['2020-07-28 00:01', '2020-07-28 00:01', '1-59/1 0 * * *']]
      }
    ];

    const inputSchedulesWithOneDeployed = [
      {
        sid: 'sdkljsdv3455-vdsv45-4332sdfsv',
        isDeployed: '0',
        repeat_type: 'daily',
        rid: '8a9c452d-e6c6-480b-b819-1f3903811aa1',
        runOn: '2826',
        siteId: 'ssh',
        ts: [['2020-07-28 00:01', '2020-07-28 00:01', '1-59/1 0 * * *']]
      },
      {
        sid: 'sdkljsdv3455-vdsv45-4332sdfsv',
        isDeployed: '1',
        repeat_type: 'daily',
        rid: '8a9c452d-e6c6-480b-b819-1f3903811aa1',
        runOn: '2826',
        siteId: 'ssh',
        ts: [['2020-07-28 00:01', '2020-07-28 00:01', '1-59/1 0 * * *']]
      }
    ];

    it('passing schedule array with all deployed schedules, shouled return empty array', (done) => {
      let outputAllDeployed = utils.removeDeployedSchedules(inputSchedulesWithAllDeployed);
      assert.isArray(outputAllDeployed);
      assert.isEmpty(outputAllDeployed);
      done();
    });

    it('passing schedule array with all not deployed schedules, shouled return as it is array', (done) => {
      let outputAllNotDeployed = utils.removeDeployedSchedules(inputSchedulesWithAllNotDeployed);
      assert.isArray(outputAllNotDeployed);
      assert.deepEqual(outputAllNotDeployed, inputSchedulesWithAllNotDeployed);
      done();
    });

    it('passing schedule array with one deployed schedule, shouled remove the deployed schedule', (done) => {
      let outputOneDeployed = utils.removeDeployedSchedules(inputSchedulesWithOneDeployed);
      assert.isArray(outputOneDeployed);
      assert.deepEqual(outputOneDeployed, inputSchedulesWithAllNotDeployed);
      done();
    });
  });

  describe('createUniqueIdForActionable()', () => {
    const rid = '8a9c452d-e6c6-480b-b819-1f3903811aa1';
    const undefinedRid = undefined;
    const actionable = [
      {
        notify: '<EMAIL>',
        type: 'action',
        uniqId: '7d8d9dvsdv965-dsvsd5v4-3w5rfesdgfe',
        did: '1234',
        command: '1234.setpoint',
        value: 22,
        delay: 10,
        category: 'Any category'
      }
    ];

    it('passing actionable and undefined rid', (done) => {
      assert.throws(() => {
        utils.createUniqueIdForActionable(actionable, undefinedRid);
      }, Error);
      done();
    });

    it('passing actionable and valid rid', (done) => {
      let outputUniqueId = utils.createUniqueIdForActionable(actionable, rid);
      assert.isString(outputUniqueId, 'should be a string');
      assert.deepEqual(outputUniqueId, '64895601dc9596feb170cc44fac37b20');
      done();
    });
  });

  describe('isRecipeActive()', () => {
    const currentTime = 1597060689000;
    const lastActiveTrue = 1597060688000;
    const lastActiveFalse = 1597050689000;

    it('passing params with time diff less than 10 mins', (done) => {
      const outputRecipeActiveTrue = utils.isRecipeActive(currentTime, lastActiveTrue);
      assert.isTrue(outputRecipeActiveTrue);
      done();
    });

    it('passing params with time diff more than 10 mins', (done) => {
      const outputRecipeActiveFalse = utils.isRecipeActive(currentTime, lastActiveFalse);
      assert.isFalse(outputRecipeActiveFalse);
      done();
    });
  });

  describe('getDeviceParamListFromThermostatExpression()', () => {
    const inputUndefinedDataExpression = undefined;
    const inputValidDataExpression = {'temp':'||5739@tmp||','ahuStatus':'||3044@inputpower||>||0.09||'};
    it('passing undefined data expression, should return empty array', (done) => {
      let outputUndefined = utils.getDeviceParamListFromThermostatExpression(inputUndefinedDataExpression);
      assert.isArray(outputUndefined, 'should be of type array');
      assert.isEmpty(outputUndefined, 'should be empty');
      done();
    });

    it('passing valid data expression', (done) => {
      let outputValid = utils.getDeviceParamListFromThermostatExpression(inputValidDataExpression);
      assert.isArray(outputValid, 'should be of type array');
      assert.lengthOf(outputValid, 2, 'length should be 2');
      assert.hasAllKeys(outputValid[0], ['deviceId', 'param', 'type'], 'output array has invalid object ');
      done();
    });
  });

  describe('getTopicArray()', () => {
    const inputEmptyArray = [];
    const inputValiddeviceParamArray = [
      { deviceId: '5739', param: 'tmp', type: 'deviceId' },
      { deviceId: '3044', param: 'inputpower', type: 'deviceId' }
    ];

    it('passing empty array should return empty array', (done) => {
      let outputEmptyArray = utils.getTopicsArray(inputEmptyArray);
      assert.isArray(outputEmptyArray, 'should be of type array');
      assert.isEmpty(outputEmptyArray, 'should be empty');
      done();
    });
    
    it('passing valid device param array should return array of topics', (done) => {
      let outputValidArray = utils.getTopicsArray(inputValiddeviceParamArray);
      assert.isArray(outputValidArray, 'should be of type array');
      assert.deepEqual(outputValidArray, ['db/5739/tmp/2', 'db/3044/inputpower/2'], 'output is not as expected');
      done();
    });
  });

  describe('parseRecipeObjectToSendToController()', () => {
    const recipeUndefined = undefined;
    const recipeObject = {
      everyMinuteTopics: ['db/5756/tmp/2','db/3032/inputpower/2'],
      runOn: '2818',
      formula: '',
      dependentOnOthers: ['5756', '3032'],
      actionable: [{'did':'3032','command':'setfrequency','parent':'gknmh_37','value':'evaluate','title':'thermal_app','type':'action','notify':[],'accountable':[],'priority':1,'uniqId':'9af0931283b1a38807f3d07737927e0e','category':['energydiagnosticrecipe']}],
      maxDataNeeded: '1',
      rid: '190507bd-91e1-4192-b43d-30f1e75707f8',
      notRun: [],
      appType: 'recipe',
      misc: {}
    };

    it('passing undefined recipe object should return false', (done) => {
      let outputUndefinedObject = utils.parseRecipeObjectToSendToController(recipeUndefined);
      assert.isFalse(outputUndefinedObject, 'should return false');
      done();
    });

    it('passing valid recipe object, should return parsed object', (done) => {
      let outputValidObject = utils.parseRecipeObjectToSendToController(recipeObject);
      assert.isObject(outputValidObject, 'should be of type object');
      assert.hasAllKeys(outputValidObject, ['failsafe', 'everyMinuteTopics', 'runOn', 'topics', 'recipe', 'syncAsyncAction', 'dependentOnOthers', 'controllers', 'switchOff',
        'actionAlert', 'startNow', 'maxDataNeeded', 'rid', 'notRun', 'appType', 'misc']);
      done();
    });
  });

  describe('getThermalObjectToSendToController()', () => {
    const undefinedRecipeObject = undefined;
    const validRecipeObject = {
      everyMinuteTopics: ['db/5756/tmp/2','db/3032/inputpower/2'],
      runOn: '2818',
      formula: '',
      dependentOnOthers: ['5756', '3032'],
      actionable: [{'did':'3032','command':'setfrequency','parent':'gknmh_37','value':'evaluate','title':'thermal_app','type':'action','notify':[],'accountable':[],'priority':1,'uniqId':'9af0931283b1a38807f3d07737927e0e','category':['energydiagnosticrecipe']}],
      maxDataNeeded: '1',
      rid: '190507bd-91e1-4192-b43d-30f1e75707f8',
      notRun: [],
      appType: 'thermal',
      misc: {'appSettings':{'setpoint':24,'minOffset':0.5,'maxOffset':0.2,'timeToAchieve':40,'appMode':'cooling'},
        'controlSettings':{'name':'vfd','emergencyPosition':70,'minimumPosition':50,'minModulation':56,'maxModulation':80,'modulationSpeed':5,'sampleTime':1},
        'dataExpression':{'temp':'||5756@tmp||','ahuStatus':'||3032@inputpower||>||0.3||'},
        'category':'other'}
    };

    it('passing undefined recipe object, should return false', (done) => {
      let outputUndefined = utils.parseThermalObjectToSendToController(undefinedRecipeObject);
      assert.isFalse(outputUndefined, 'should return false');
      done();
    });

    it('passing valid recipe object, should return parsed object', (done) => {
      let outputValidThermalObject = utils.parseThermalObjectToSendToController(validRecipeObject);
      assert.isObject(outputValidThermalObject, 'should be of type object');
      assert.hasAllKeys(outputValidThermalObject, ['failsafe', 'everyMinuteTopics', 'runOn', 'topics', 'recipe', 'syncAsyncAction', 'dependentOnOthers', 'controllers', 'switchOff',
        'actionAlert', 'startNow', 'maxDataNeeded', 'rid', 'notRun', 'appType', 'misc', 'maxLogsNeeded', 'alwaysRun']);
      assert.deepEqual(outputValidThermalObject.misc.dataExpression, { temp: '5756.tmp', ahuStatus: '3032.inputpower>0.3' });
      done();
    });
  });

  describe('stringifyRecipeToSaveInDB', () => {
    const validRecipeObject = {
      everyMinuteTopics: ['db/5756/tmp/2','db/3032/inputpower/2'],
      runOn: '2818',
      operators: '#1 #2',
      operator: '#1 #2',
      params: '$1 $2',
      formula: '',
      recipelabel: 'pidrecipe',
      scheduled: [],
      devicesIncluded: [],
      dependentOnOthers: ['5756', '3032'],
      actionable: [{'did':'3032','command':'setfrequency','parent':'gknmh_37','value':'evaluate','title':'thermal_app','type':'action','notify':[],'accountable':[],'priority':1,'uniqId':'9af0931283b1a38807f3d07737927e0e','category':['energydiagnosticrecipe']}],
      maxDataNeeded: '1',
      rid: '190507bd-91e1-4192-b43d-30f1e75707f8',
      notRun: [],
      appType: 'thermal',
      misc: {'appSettings':{'setpoint':24,'minOffset':0.5,'maxOffset':0.2,'timeToAchieve':40,'appMode':'cooling'},
        'controlSettings':{'name':'vfd','emergencyPosition':70,'minimumPosition':50,'minModulation':56,'maxModulation':80,'modulationSpeed':5,'sampleTime':1},
        'dataExpression':{'temp':'||5756@tmp||','ahuStatus':'||3032@inputpower||>||0.3||'},
        'category':'other'}
    };

    it('passing valid recipe object with few non string values', (done) => {
      utils.stringifyRecipeToSaveInDB(validRecipeObject);
      assert.isObject(validRecipeObject, 'should be of type object');
      assert.isString(validRecipeObject.scheduled, 'value of object should be of type string');
      assert.isString(validRecipeObject.everyMinuteTopics, 'value of object should be of type string');
      assert.isString(validRecipeObject.dependentOnOthers, 'value of object should be of type string');
      done();
    });
  });
});
