const assert = require('chai').assert;
let sinon = require('sinon');
const createScheduleUtils = require('../../../../api/utils/recipe/createschedule.util');
const utils = require('../../../../api/utils/recipe/utils');

sinon = sinon.createSandbox();

describe('Create Schedule Util Unit Tests', () => {
  describe('createDailyCron(): It will create the cron array for two time intervals', () => {
    let parseTimeMock;
    before(() => {
      parseTimeMock = sinon.stub(utils, 'parseTime');
    });
    after(() => {
      sinon.restore();
    });
    it('createDailyCron(): Passing valid start & end time', (done) => {
      const startTime = '00:00';
      const endTime = '00:00';
      parseTimeMock.returns(['0 0 * * *']);
      let outputCron = createScheduleUtils.createDailyCron(startTime, endTime);
      assert.isArray(outputCron, 'should be of type array');
      assert.lengthOf(outputCron, 1, 'length should be 1');
      assert.lengthOf(outputCron[0], 3, 'length of inner array should be 3');
      assert.equal(outputCron[0][2], '0 0 * * *', 'Invalid cron is generated');
      done();
    });

    it('createDailyCron(): Passing valid start & end time - 2', (done) => {
      const startTime = '00:00';
      const endTime = '06:00';
      parseTimeMock.returns(['* 0-5 * * *']);
      let outputCron = createScheduleUtils.createDailyCron(startTime, endTime);
      assert.isArray(outputCron, 'should be of type array');
      assert.lengthOf(outputCron, 1, 'length should be 1');
      assert.lengthOf(outputCron[0], 3, 'length of inner array should be 3');
      assert.equal(outputCron[0][2], '* 0-5 * * *', 'Invalid cron is generated');
      done();
    });

    it('createDailyCron(): Passing invalid start & end time', (done) => {
      const startTime = '30:00';
      const endTime = '50:00';
      parseTimeMock.throws(Error);
      assert.throws(() => {
        createScheduleUtils.createDailyCron(startTime, endTime);
      }, Error);
      done();
    });
  });

  describe('validTime(): Checks if the passed time is valid or not', () => {
    const validTime = '00:00';
    const validTime2 = '12:00';
    const invalidTime = '2300';
    const invalidTime2 = '12pm';

    it('validTime(): passing valid time 00:00', (done) => {
      let output1 = createScheduleUtils.validTime(validTime);
      let output2 = createScheduleUtils.validTime(validTime2);
      assert.isTrue(output1, 'output should be true');
      assert.isTrue(output2, 'output should be true');
      done();
    });

    it('validTime(): passing invalid time 24hrs format', (done) => {
      let output1 = createScheduleUtils.validTime(invalidTime);
      let output2 = createScheduleUtils.validTime(invalidTime2);
      assert.isFalse(output1, 'output should be False');
      assert.isFalse(output2, 'output should be False');
      done();
    });
  });

  describe('isValidDate(): Checks if the passed date is in valid format', () => {
    const inputUndefined = undefined;
    const invalidDate = '12012020';
    const validDate = '2019-12-24';
    it('isValidDate(): passing undefined', (done) => {
      let output1 = createScheduleUtils.isValidDate(inputUndefined);
      assert.isFalse(output1, 'output should be false');
      done();
    });

    it('isValidDate(): passing invalid date', (done) => {
      let output1 = createScheduleUtils.isValidDate(invalidDate);
      assert.isFalse(output1, 'output should be False');
      done();
    });

    it('isValidDate(): passing valid date', (done) => {
      let output1 = createScheduleUtils.isValidDate(validDate);
      assert.equal(output1, '2019-12-24', 'output is incorrect');
      done();
    });
  });

  describe('createNoIntervalCron(): It will create the cron array for two time & date intervals', () => {
    let parseTimeMock;
    before(() => {
      parseTimeMock = sinon.stub(utils, 'parseTime');
    });
    after(() => {
      sinon.restore();
    });
    it('createNoIntervalCron(): Passing valid start & end time', (done) => {
      const startTime = '00:00';
      const endTime = '00:00';
      const startDate = '2019-12-24';
      const endDate = '2022-12-24';
      parseTimeMock.returns(['0 0 * * *']);
      let outputCron = createScheduleUtils.createNoIntervalCron(startDate, endDate, startTime, endTime);
      assert.isArray(outputCron, 'should be of type array');
      assert.lengthOf(outputCron, 1, 'length should be 1');
      assert.lengthOf(outputCron[0], 3, 'length of inner array should be 3');
      assert.equal(outputCron[0][2], '0 0 * * *', 'Invalid cron is generated');
      done();
    });

    it('createNoIntervalCron(): Passing valid start & end time - 2', (done) => {
      const startTime = '00:00';
      const endTime = '06:00';
      const startDate = '2019-12-24';
      const endDate = '2022-12-24';
      parseTimeMock.returns(['* 0-5 * * *']);
      let outputCron = createScheduleUtils.createNoIntervalCron(startDate, endDate, startTime, endTime);
      assert.isArray(outputCron, 'should be of type array');
      assert.lengthOf(outputCron, 1, 'length should be 1');
      assert.lengthOf(outputCron[0], 3, 'length of inner array should be 3');
      assert.equal(outputCron[0][2], '* 0-5 * * *', 'Invalid cron is generated');
      done();
    });

    it('createNoIntervalCron(): Passing invalid start & end time', (done) => {
      const startTime = '30:00';
      const endTime = '50:00';
      const startDate = '2019-12-24';
      const endDate = '2022-12-24';
      parseTimeMock.throws(Error);
      assert.throws(() => {
        createScheduleUtils.createNoIntervalCron(startDate, endDate, startTime, endTime);
      }, Error);
      done();
    });
  });

  describe('addSampleTimeToCron(): It adds the sample time to cron expression', () => {
  
    it('addSampleTimeToCron(): passing valid recipe object and schedule', (done) => {
      let recipeObject = {
        misc: {
          controlSettings: {
            sampleTime: 5
          }
        }
      };
      let schedule = {
        sid: '3534tbfdb-wt4tvewtv',
        ts: [['startdate time', 'endDate time', '0 0 * * *']]
      };
      createScheduleUtils.addSampleTimeToCron(recipeObject, schedule);
      assert.isString(schedule.ts, 'ts key should be a string');
      assert.equal(JSON.parse(schedule.ts)[0][2], '0/5 0 * * *', 'incorrect cron expression');
      done();
    });

    it('addSampleTimeToCron(): passing valid recipe object and schedule - 2', (done) => {
      let recipeObject = {
        misc: {
          controlSettings: {
            sampleTime: 10
          }
        }
      };
      let schedule = {
        sid: '3534tbfdb-wt4tvewtv',
        ts: [['startdate time', 'endDate time', '0 0 * * *']]
      };
      createScheduleUtils.addSampleTimeToCron(recipeObject, schedule);
      assert.isString(schedule.ts, 'ts key should be a string');
      assert.equal(JSON.parse(schedule.ts)[0][2], '0/10 0 * * *', 'incorrect cron expression');
      done();
    });

    it('addSampleTimeToCron(): passing valid recipe object and schedule with sample time > 60', (done) => {
      let recipeObject = {
        misc: {
          controlSettings: {
            sampleTime: 500
          }
        }
      };
      let schedule = {
        sid: '3534tbfdb-wt4tvewtv',
        ts: [['startdate time', 'endDate time', '0 0 * * *']]
      };
      createScheduleUtils.addSampleTimeToCron(recipeObject, schedule);
      assert.isArray(schedule.ts, 'ts key should be an array');
      assert.equal(schedule.ts[0][2], '0 0 * * *', 'incorrect cron expression');
      done();
    });
  });
});
