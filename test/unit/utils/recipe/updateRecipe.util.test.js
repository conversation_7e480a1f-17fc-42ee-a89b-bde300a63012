const assert = require('chai').assert;
const updateRecipeUtil = require('../../../../api/utils/recipe/updateRecipe.util');
let sinon = require('sinon');

sinon = sinon.createSandbox();

describe('Update Recipe Util Unit Tests', () => {
  describe('buildInitialPacket(): It builds the initial recipe packet', () => {
    let validateMiscMock;
    before(() => {
      validateMiscMock = sinon.stub(updateRecipeUtil, 'validateMisc');
    });
    after(() => {
      sinon.restore();
    });
    let inputPacket = {
      siteId: 'ssh',
      type: 'alert',
      neo: 'anything',
      isSchedule: 'false',
      label: '1',
      formula: '||#1||$3||1',
      params: '',
      operators: '',
      maxDataNeeded: '1',
      componentId: 'ssh_6',
      recipelabel: ['pidrecipe'],
      rid: 'dvsdv-23rwd',
      _userMeta: {
        id: 'ghost'
      },
      actionable: [{ 'title': 'test-1', 
        'description': 'none', 
        'notify': ['<EMAIL>'], 
        'accountable': ['<EMAIL>'], 
        'type': 'alert', 
        'priority': 0 
      }]
    };

    it('buildInitialPacket(): Invalid recipelabel', (done) => {
      let newInputPacket = {...inputPacket};
      newInputPacket.recipelabel = ['invalid label'];
      let outputInitialPacket = updateRecipeUtil.buildInitialPacket(newInputPacket, {});
      assert.hasAllKeys(outputInitialPacket, ['problems']);
      assert.equal(outputInitialPacket.problems[0], 'Invalid Recipe Label');
      done();
    });

    it('buildInitialPacket(): Invalid misc', (done) => {
      let newInputPacket = {...inputPacket};
      let oldRecipe = {
        appType: 'thermal'
      };
      validateMiscMock.returns(false);
      let outputInitialPacket = updateRecipeUtil.buildInitialPacket(newInputPacket, oldRecipe);
      assert.hasAllKeys(outputInitialPacket, ['problems']);
      assert.equal(outputInitialPacket.problems[0], 'Invalid thermostat configuration');
      done();
    });

    it('buildInitialPacket(): passing all valid params', (done) => {
      let newInputPacket = {...inputPacket};
      let oldRecipe = {
        appType: 'recipe',
        componentId: 'ssh_6',
        scheduled: '[]',
        switchOff: '0',
        notRun: [],
        isStage: '0',
      };
      validateMiscMock.returns(false);
      let outputInitialPacket = updateRecipeUtil.buildInitialPacket(newInputPacket, oldRecipe);
      assert.isObject(outputInitialPacket, 'should be an object');
      assert.hasAllDeepKeys(outputInitialPacket, ['isSchedule', 'isStage', 'notRun', 'switchOff', 'scheduled',
        'maxDataNeeded', 'rid', 'siteId', 'type', 'label', 'user', 'neo', 'appType', 'actionable', 'recipelabel', 'formula', 'operators', 'params',
        'componentId', 'misc']);
      done();
    });
  });

  describe('validateMisc(): Validates the Misc keys', () => {
    let newMisc = {
      controlSettings: {sampleTime: 5},
      appSettings: {},
      dataExpression: 'random'
    };
    let oldMisc = {
      controlSettings: {sampleTime: 5},
      appSettings: {},
      dataExpression: 'random'
    };

    it('validateMisc(): Passing valid args', (done) => {
      let output = updateRecipeUtil.validateMisc(newMisc, oldMisc);
      assert.isTrue(output);
      done();
    });

    it('validateMisc(): Passing invalid args', (done) => {
      let newMisc2 = {...newMisc};
      delete newMisc2.controlSettings;
      let output = updateRecipeUtil.validateMisc(newMisc2, oldMisc);
      assert.isFalse(output);
      done();
    });

    it('validateMisc(): Passing invalid args with different sample times', (done) => {
      let newMisc2 = {...newMisc};
      newMisc2.controlSettings.sampleTime = 11;
      let output = updateRecipeUtil.validateMisc(newMisc2, oldMisc);
      assert.isFalse(output);
      done();
    });
  });

  describe('JSONifyRecipeObjectForController()', () => {
    let recipe = {
      params: JSON.stringify({}),
      everyMinuteTopics: JSON.stringify([]),
      dependentOnOthers: JSON.stringify([]),
      operator: JSON.stringify({}),
      actionable: JSON.stringify([])
    };

    it('JSONifyRecipeObjectForController(): passing stringifed keys of recipe', (done) => {
      let output = updateRecipeUtil.JSONifyRecipeObjectForController(recipe);
      assert.isObject(output);
      assert.isObject(output.params);
      assert.isObject(output.operator);
      assert.isArray(output.everyMinuteTopics);
      assert.isArray(output.dependentOnOthers);
      assert.isArray(output.actionable);
      done();
    });
  });
});
