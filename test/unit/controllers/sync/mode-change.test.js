const assert = require('chai').assert;
var mocha = require('mocha');
var sinon = require('sinon');
sails = require('sails');
sails.config = {IOT_CONFIG:{}};
const self = require('../../../../api/services/sync/sync.service');
const utils = require('../../../../api/utils/sync/mode-change.util');

sinon = sinon.createSandbox();
let describe;
let it;
let before;
let after;
if (mocha.describe) {
  describe = mocha.describe;
  it = mocha.it;
  before = mocha.before;
  after = mocha.after;
} else {
  describe = global.describe;
  it = global.it;
  before = global.before;
  after = global.after;
}
let modeChange = require('../../../../api/controllers/sync/mode-change');

describe('Mode Change API positive suit', () => {
  let getAllDeployedRecipesOfASiteMock, removePausedRecipesMock, getAffectedRecipesMapMock, handleServerRecipesMock;
  let getRecipePacketForNewModesMock, updateRecipeStateInSyncTableMock, sendStartStopToControllersMock;
  before(() => {
    getAllDeployedRecipesOfASiteMock = sinon.stub(self, 'getAllDeployedRecipesOfASite');
    removePausedRecipesMock = sinon.stub(utils, 'removePausedRecipes');
    getAffectedRecipesMapMock = sinon.stub(utils,'getAffectedRecipesMap');
    handleServerRecipesMock = sinon.stub(self,'handleServerRecipes');
    getRecipePacketForNewModesMock = sinon.stub(utils,'getRecipePacketForNewModes');
    updateRecipeStateInSyncTableMock = sinon.stub(self,'updateRecipeStateInSyncTable');
    sendStartStopToControllersMock = sinon.stub(self,'sendStartStopToControllers');
  });
  after(() => {
    sinon.restore();
  });

  it('passing no modes', async () => {
    let inputs = {
      siteId: 'ssh',
      modes: {},
      secret: '41f5e8d3dbae45b33f4ce040ff4ce13e23a5ddd4'
    };
    let exits = {};
    exits.success = sinon.spy();
    await modeChange.fn(inputs, exits);
    assert.equal(exits.success.callCount, 1);
  });

  it('No recipes found', async () => {
    let inputs = {
      siteId: 'ssh',
      modes: {'1111.start': 'joulerecipe'},
      secret: '41f5e8d3dbae45b33f4ce040ff4ce13e23a5ddd4'
    };
    let exits = {};
    getAllDeployedRecipesOfASiteMock.resolves([]);
    exits.success = sinon.spy();
    await modeChange.fn(inputs, exits);
    assert.equal(exits.success.callCount, 1);
    assert.equal(getAllDeployedRecipesOfASiteMock.callCount, 1);
  });

  it('No started recipe found', async () => {
    let inputs = {
      siteId: 'ssh',
      modes: {'1111.start': 'joulerecipe'},
      secret: '41f5e8d3dbae45b33f4ce040ff4ce13e23a5ddd4'
    };
    let exits = {};
    getAllDeployedRecipesOfASiteMock.resolves([{a:1}]);
    removePausedRecipesMock.returns([]);
    exits.success = sinon.spy();
    await modeChange.fn(inputs, exits);
    assert.equal(exits.success.callCount, 1);
    assert.equal(removePausedRecipesMock.callCount, 1);
  });

  it('Only server recipes', async () => {
    let inputs = {
      siteId: 'ssh',
      modes: {'1111.start': 'joulerecipe'},
      secret: '41f5e8d3dbae45b33f4ce040ff4ce13e23a5ddd4'
    };
    let exits = {};
    getAllDeployedRecipesOfASiteMock.resolves([{a:1}]);
    removePausedRecipesMock.returns([{a:1}]);
    getAffectedRecipesMapMock.returns({server: [{a:1}]});
    handleServerRecipesMock.resolves();
    exits.success = sinon.spy();
    await modeChange.fn(inputs, exits);
    assert.equal(exits.success.callCount, 1);
    assert.equal(getAffectedRecipesMapMock.callCount, 1);
    assert.equal(handleServerRecipesMock.callCount, 1);
  });

  it('All recipes passed', async () => {
    let inputs = {
      siteId: 'ssh',
      modes: {'1111.start': 'joulerecipe'},
      secret: '41f5e8d3dbae45b33f4ce040ff4ce13e23a5ddd4'
    };
    let exits = {};
    getAllDeployedRecipesOfASiteMock.resolves([{a:1}]);
    removePausedRecipesMock.returns([{a:1}]);
    getAffectedRecipesMapMock.returns({server: [], 1111: [{a: 1}]});
    handleServerRecipesMock.resolves();
    getRecipePacketForNewModesMock.returns({1111: [{a: 1}]});
    updateRecipeStateInSyncTableMock.resolves();
    sendStartStopToControllersMock.resolves();
    exits.success = sinon.spy();
    await modeChange.fn(inputs, exits);
    assert.equal(exits.success.callCount, 1);
    assert.equal(getRecipePacketForNewModesMock.callCount, 1);
    assert.equal(updateRecipeStateInSyncTableMock.callCount, 1);
    assert.equal(sendStartStopToControllersMock.callCount, 1);
  });
});

describe('Mode Change API Negative suit', () => {
  let getAllDeployedRecipesOfASiteMock, sailsMock;
  
  before(() => {
    getAllDeployedRecipesOfASiteMock = sinon.stub(self, 'getAllDeployedRecipesOfASite');
    sailsMock = sinon.stub(sails.log, 'error');
  });
  after(() => {
    sinon.restore();
  });
  it('passing no modes', async () => {
    let inputs = {
      siteId: 'ssh',
      modes: {'afd.af': 1},
      secret: '41f5e8d3dbae45b33f4ce040ff4ce13e23a5ddd4'
    };
    let exits = {};
    getAllDeployedRecipesOfASiteMock.rejects();
    sailsMock.returns();
    exits.serverError = sinon.spy();
    await modeChange.fn(inputs, exits);
    assert.equal(exits.serverError.callCount, 1);
    assert.equal(sailsMock.callCount, 1);
  });
});
