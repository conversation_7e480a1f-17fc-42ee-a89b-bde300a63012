const assert = require('chai').assert;
var mocha = require('mocha');
var sinon = require('sinon');
sails = require('sails');
sails.config = {IOT_CONFIG:{}};
const self = require('../../../../api/services/sync/sync.service');
const utils = require('../../../../api/utils/sync/sync-recipe.util');

sinon = sinon.createSandbox();
let describe;
let it;
let before;
let after;
if (mocha.describe) {
  describe = mocha.describe;
  it = mocha.it;
  before = mocha.before;
  after = mocha.after;
} else {
  describe = global.describe;
  it = global.it;
  before = global.before;
  after = global.after;
}
let syncRecipe = require('../../../../api/controllers/sync/sync-recipe');

describe('Sync Recipes API positive suit', () => {
  let findSyncMock, filterAndFormSyncPacketMock, sailsMock;
  before(() => {
    findSyncMock = sinon.stub(self, 'find');
    filterAndFormSyncPacketMock = sinon.stub(utils, 'filterAndFormSyncPacket');
    sailsMock = sinon.stub(sails.log, 'error');
  });
  after(() => {
    sinon.restore();
  });

  it('Positive suit', async () => {
    let inputs = {
      controllerId: '1111',
      ridList: ['a', 'b']
    };
    let exits = {};
    findSyncMock.resolves([{}, {}]);
    filterAndFormSyncPacketMock.returns({});
    exits.success = sinon.spy();
    await syncRecipe.fn(inputs, exits);
    assert.equal(exits.success.callCount, 1);
    assert.equal(findSyncMock.callCount, 1);
    assert.equal(filterAndFormSyncPacketMock.callCount, 1);
  });
});

describe('Mode Change API Negative suit', () => {
  let findSyncMock, sailsMock;
  before(() => {
    findSyncMock = sinon.stub(self, 'find');
    sailsMock = sinon.stub(sails.log, 'error');
  });
  after(() => {
    sinon.restore();
  });

  it('Negative suit', async () => {
    let inputs = {
      controllerId: '1111',
      ridList: ['a', 'b']
    };
    let exits = {};
    findSyncMock.rejects();
    sailsMock.returns();
    exits.serverError = sinon.spy();
    await syncRecipe.fn(inputs, exits);
    assert.equal(exits.serverError.callCount, 1);
    assert.equal(sailsMock.callCount, 1);
  });
});
