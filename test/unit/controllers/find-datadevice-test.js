var assert = require('assert');
var mocha = require('mocha');
var sinon = require('sinon');
sails = require('sails');
const datadeviceService = require('../../../api/services/datadevice/datadevice.service');
sinon = sinon.createSandbox();
// let describe;
// let it;
// let before;
// let after;
if (mocha.describe) {
  describe = mocha.describe;
  it = mocha.it;
  before = mocha.before;
  after = mocha.after;
} else {
  describe = global.describe;
  it = global.it;
  before = global.before;
  after = global.after;
}
let finddatadevice = require('../../../api/controllers/datadevice/find-datadevice');
describe('find data device positive suit', async () => {
  let getDataBetweenTwoTimeStampsMock;
  before(() => {
    getDataBetweenTwoTimeStampsMock = sinon.stub(
      datadeviceService,
      'getDataBetweenTwoTimeStamps'
    );
  });
  after(() => {
    sinon.restore();
  });

  it('should get device data ', async () => {
    getDataBetweenTwoTimeStampsMock.resolves([{deviceId : '2222' , timestamp: '2019-09-28 12:00:00', data: {feedback: '0'}}]);

    try {
      let inputs = {
        deviceId: '2345',
        startTime: '2019-09-28 12:00:00',
        endTime: '2019-09-30 13:18:00',
      };
      let exits = {};
      exits.success = sinon.spy();
      await finddatadevice.fn(inputs, exits);
      assert.equal(exits.success.callCount, 1);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in find data device');
    }
  });
  it('should get device data', async () => {
    getDataBetweenTwoTimeStampsMock.resolves([{deviceId : '2222' , timestamp: '2019-09-28 12:00:00', data: {feedback: '0'}}]);

    try {
      let inputs = {
        deviceId: '2345',
        startTime: '2019-09-28 12:00:00',
      };
      let exits = {};
      exits.success = sinon.spy();
      await finddatadevice.fn(inputs, exits);
      assert.equal(exits.success.callCount, 1);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in find data device');
    }
  });
});
describe('get device data negative suit', async () => {
  let getDataBetweenTwoTimeStampsMock;
  before(() => {
    getDataBetweenTwoTimeStampsMock = sinon.stub(
      datadeviceService,
      'getDataBetweenTwoTimeStamps'
    );
  });
  after(() => {
    sinon.restore();
  });

  it('should give bad Request error', async () => {
    getDataBetweenTwoTimeStampsMock.resolves({ problems: 'not valid' });

    try {
      let inputs = {
        deviceId: '2345',
        startTime: '2019-09-28 12:00:00',
        endTime: '2019-09-30 13:18:00',
      };
      let exits = {};
      exits.badRequest = sinon.spy();
      await finddatadevice.fn(inputs, exits);
      assert.equal(exits.badRequest.callCount, 1);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in find data device');
    }
  });
  it('should give error for undefined', async () => {
    getDataBetweenTwoTimeStampsMock.resolves([]);
    try {
      let inputs = {
        deviceId: '2345',
        startTime: '2019-09-28 12:00:00',
        endTime: '2019-09-30 13:18:00',
      };
      let exits = {};
      exits.success = sinon.spy();
      await finddatadevice.fn(inputs, exits);

      assert.equal(exits.badRequest.callCount, 1);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in find device data');
    }
  });
  it('should give error for undefined', async () => {
    getDataBetweenTwoTimeStampsMock.rejects();
    try {
      let inputs = {
        deviceId: '2345',
        startTime: '2019-09-28 12:00:00',
        endTime: '2019-09-30 13:18:00',
      };
      let exits = {};
      exits.serverError = sinon.spy();
      await finddatadevice.fn(inputs, exits);
      assert.equal(exits.serverError.callCount, 1);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in find data');
    }
  });
});
