var assert = require('assert');
var mocha = require('mocha');
var sinon = require('sinon');
sails = require('sails');
sails.config = {IOT_CONFIG:{}};
const datadeviceService = require('../../../api/services/datadevice/datadevice.service');
const utils = require('../../../api/utils/datadevice/buildingload.util.js');
const dynamoKeyStoreService = require('../../../api/services/dynamokeystore/dynamokeystore.public');

sinon = sinon.createSandbox();
let describe;
let it;
let before;
let after;
if (mocha.describe) {
  describe = mocha.describe;
  it = mocha.it;
  before = mocha.before;
  after = mocha.after;
} else {
  describe = global.describe;
  it = global.it;
  before = global.before;
  after = global.after;
}
let buildingload = require('../../../api/controllers/datadevice/buildingload');

describe('create buildingLoad positive suit', async () => {
  let findSitesMainMeterSetMock,getDeviceParamsDataBetween2TimestampInPreferredUnitMock,calculateBuildingConsumptionMock;
  before(() => {
    findSitesMainMeterSetMock = sinon.stub(dynamoKeyStoreService, 'findSitesMainMeterSet');
    getDeviceParamsDataBetween2TimestampInPreferredUnitMock = sinon.stub(datadeviceService, 'getDeviceParamsDataBetween2TimestampInPreferredUnit');
    calculateBuildingConsumptionMock = sinon.stub(utils,'calculateBuildingConsumption');
  });
  after(() => {
    sinon.restore();
  });

  it('should calculate building load if week is undefined', async () => {
    findSitesMainMeterSetMock.resolves(new Set([1,3]));
    calculateBuildingConsumptionMock.returns({message:'success'});
    getDeviceParamsDataBetween2TimestampInPreferredUnitMock.resolves([{timestamp:'2020-09-05T07:32:27+00:00', data:{kw:'jdj'}}]);
    try {
      let inputs = {_userMeta:{id:'123'},days:[1,2]};
      let exits = {};
      exits.success = sinon.spy();
      await buildingload.fn(inputs,exits);
      assert.equal(exits.success.callCount, 1);
      let args = exits.success.getCall(0).args;
      assert.equal(args[0].message, 'success');
    } catch (err) {
      console.log(err);
      assert.fail('Exception in create buildingLoad');
    }
  });

  it('should calculate building load if days is undefined', async () => {
    findSitesMainMeterSetMock.resolves(new Set([1,3]));
    calculateBuildingConsumptionMock.returns({message:'success'});
    getDeviceParamsDataBetween2TimestampInPreferredUnitMock.resolves([{timestamp:'2020-09-05T07:32:27+00:00', data:{kw:'jdj'}}]);
    try {
      let inputs = {_userMeta:{id:'123'},weeks:[1,2]};
      let exits = {};
      exits.success = sinon.spy();
      await buildingload.fn(inputs,exits);
      assert.equal(exits.success.callCount, 1);
      let args = exits.success.getCall(0).args;
      assert.equal(args[0].message, 'success');
    } catch (err) {
      console.log(err);
      assert.fail('Exception in create buildingLoad');
    }
  });

  it('should calculate building load if days and weeks are undefined', async () => {
    findSitesMainMeterSetMock.resolves(new Set([1,3]));
    calculateBuildingConsumptionMock.returns({message:'success'});
    getDeviceParamsDataBetween2TimestampInPreferredUnitMock.resolves([{timestamp:'2020-09-05T07:32:27+00:00', data:{kw:'jdj'}}]);
    try {
      let inputs = {_userMeta:{id:'123'},weeks:[1,2],days:[1,2]};
      let exits = {};
      exits.success = sinon.spy();
      await buildingload.fn(inputs,exits);
      assert.equal(exits.success.callCount, 1);
      let args = exits.success.getCall(0).args;
      assert.equal(args[0].message, 'success');
    } catch (err) {
      console.log(err);
      assert.fail('Exception in create buildingLoad');
    }
  });
});

describe('create buildingLoad negative suit', async () => {
  let findSitesMainMeterSetMock;
  before(() => {
    findSitesMainMeterSetMock = sinon.stub(dynamoKeyStoreService, 'findSitesMainMeterSet');
  });
  after(() => {
    sinon.restore();
  });

  it('should calculate building load if week is undefined', async () => {
    findSitesMainMeterSetMock.rejects({message:'custom error'});
    try {
      let inputs = {_userMeta:{id:'123'},days:[1,2]};
      let exits = {};
      exits.serverError = sinon.spy();
      await buildingload.fn(inputs,exits);
      assert.equal(exits.serverError.callCount, 1);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in create buildingLoad');
    }
  });
});
