var assert = require('assert');
var mocha = require('mocha');
var sinon = require('sinon');
sails = require('sails');
const self = require('../../../api/services/device/device.service');
const siteService = require('../../../api/services/site/site.public');
const dynamoKeyStoreService = require('../../../api/services/dynamokeystore/dynamokeystore.public');

sinon = sinon.createSandbox();
let describe;
let it;
let before;
let after;
if (mocha.describe) {
  describe = mocha.describe;
  it = mocha.it;
  before = mocha.before;
  after = mocha.after;
} else {
  describe = global.describe;
  it = global.it;
  // eslint-disable-next-line no-unused-vars
  before = global.before;
  // eslint-disable-next-line no-unused-vars
  after = global.after;
}
let createController = require('../../../api/controllers/device/create-controller');

describe('create controller positive suit', async () => {
  let findMockDevice, findMockService,dynamoKeyStoreMockService,updateMockService;
  let dynamoKeyMock1,dynamoKeyMock2,createMockDevice;
  before(() => {
    findMockDevice = sinon.stub(self, 'find');
    createMockDevice = sinon.stub(self, 'create');
    findMockService = sinon.stub(siteService, 'findOne');
    updateMockService = sinon.stub(siteService, 'update');
    dynamoKeyStoreMockService = sinon.stub(dynamoKeyStoreService, 'getDeviceIDFromTotalDeviceCount');
    dynamoKeyMock1 = sinon.stub(dynamoKeyStoreService, 'updateTotalDeviceCount');
    dynamoKeyMock2 = sinon.stub(dynamoKeyStoreService, 'updateConfigTs');
  });
  after(() => {
    sinon.restore();
  });

  it('should create a controller', async () => {
    findMockDevice.resolves([{deviceType: 'jouleiocontrol',name:'TestDevice-1'}]);
    findMockService.resolves({areas:{'smartjoules-network-0':'hd',
      nzmp:'nd',mqpb:'ieu'},regions:{'smartjoules-network-0':'hd',
      nzmp:{controller:[]},mqpb:'ieu'},networks:{'smartjoules-network-0':['hd'],
      nzmp:'nd',mqpb:'ieu'}});
    dynamoKeyStoreMockService.resolves(2);
    dynamoKeyMock1.resolves();
    dynamoKeyMock2.resolves();
    createMockDevice.resolves();
    updateMockService.resolves();
    try {
      let inputs = {'devicesInfo' :[ {'networkId' : 'smartjoules-network-0','regionId' : 'nzmp','softwareVer' : 'test','hardwareVer' : 'test','vendorId' : 'smartjoules','operationMode' : 'test','deviceType' : 'joulebox','areaId' : 'mqpb','siteId' : 'gknmh', 'name':'JT-1' }]};
      let exits = {};
      exits.success = sinon.spy();
      await createController.fn(inputs,exits);
      assert.equal(exits.success.callCount, 1);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in create controller');
    }
  });
});

describe('create controller negative suit', async () => {
  let findMockDevice, findMockService, sailsMock;
 
  before(() => {
    findMockDevice = sinon.stub(self, 'find');
    findMockService = sinon.stub(siteService, 'findOne');
    sails.log = {error:function(){}};
    sailsMock = sinon.stub(sails.log,'error');
  });
  after(() => {
    sinon.restore();
  });

  it('should give error if invalid response from dynamoDB', async () => {
    findMockDevice.resolves({clientId: 'ishita'});
    findMockService.resolves({areas:{},regions:{},networks:{}});
    sailsMock.returns();
    try {
      let inputs = {'devicesInfo' :[ {'networkId' : 'smartjoules-network-0','regionId' : 'nzmp','softwareVer' : 'test','hardwareVer' : 'test','vendorId' : 'smartjoules','operationMode' : 'test','deviceType' : 'joulebox','areaId' : 'mqpb','siteId' : 'gknmh', 'name':'JT-1' }]};
      let exits = {};
      exits.success = sinon.spy();
      createController.res = {serverError:sinon.spy()};
      await createController.fn(inputs,exits);
      assert.equal( createController.res.serverError.callCount, 1);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in create controller');
    }
  });

  it('should give error if deviceType is joulesense and vendorId is joulesense', async () => {
    try {
      let inputs = {
        'devicesInfo' :[ 
          {
            'networkId' : 'smartjoules-network-0',
            'regionId' : 'nzmp',
            'softwareVer' : 'test',
            'hardwareVer' : 'test',
            'vendorId' : 'joulesense',
            'operationMode' : 'test',
            'deviceType' : 'joulesense',
            'areaId' : 'mqpb',
            'siteId' : 'gknmh'
          }]};
      let exits = {inValidVendor:sinon.spy()};
      await createController.fn(inputs,exits);
      assert.equal( exits.inValidVendor.callCount, 1);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in create controller');
    }
  });

  it('should give error if site doesnot exists', async () => {
    try {
      findMockDevice.resolves(null);
      findMockService.resolves();
      let inputs = {'devicesInfo' :[ {'networkId' : 'smartjoules-network-0','regionId' : 'nzmp','softwareVer' : 'test','hardwareVer' : 'test','vendorId' : 'smartjoules','operationMode' : 'test','deviceType' : 'joulebox','areaId' : 'mqpb','siteId' : 'gknmh', 'name':'JT-1' }]};
      let exits = {siteDoesnotExist:sinon.spy()};
      await createController.fn(inputs,exits);
      assert.equal( exits.siteDoesnotExist.callCount, 1);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in create controller');
    }
  });
});
