var assert = require('assert');
var mocha = require('mocha');
var sinon = require('sinon');
sails = require('sails');
sails.config = {IOT_CONFIG:{}};
const datadeviceService = require('../../../api/services/datadevice/datadevice.service');

sinon = sinon.createSandbox();
let describe;
let it;
let before;
let after;
if (mocha.describe) {
  describe = mocha.describe;
  it = mocha.it;
  before = mocha.before;
  after = mocha.after;
} else {
  describe = global.describe;
  it = global.it;
  before = global.before;
  after = global.after;
}
let plotGraph = require('../../../api/controllers/datadevice/plot-graph');

describe('plot graph positive suit', async () => {
  let getGraphsMetaDataBetween2TimestampsMock,getDeviceParamsDataBetween2TimestampInPreferredUnitMock;
  before(() => {
    getGraphsMetaDataBetween2TimestampsMock = sinon.stub(datadeviceService, 'getGraphsMetaDataBetween2Timestamps');
    getDeviceParamsDataBetween2TimestampInPreferredUnitMock = sinon.stub(datadeviceService, 'getDeviceParamsDataBetween2TimestampInPreferredUnit');
  });
  after(() => {
    sinon.restore();
  });

  it('give graph data for type line', async () => {
    getGraphsMetaDataBetween2TimestampsMock.resolves(new Set([1,3]));
    getDeviceParamsDataBetween2TimestampInPreferredUnitMock.resolves([{timestamp:'2020-09-05T07:32:27+00:00', data:{kw:'jdj'}}]);
    try {
      let inputs = {_userMeta:{siteId:'123',unitPref:'kwah'},type:'line','startTime':'2020-08-05 06:54','endTime':'2020-08-06 06:54','deviceId':'296','group':'day','params':['KW']};
      let exits = {};
      exits.success = sinon.spy();
      await plotGraph.fn(inputs,exits);
      assert.equal(exits.success.callCount, 1);
      let args = exits.success.getCall(0).args;
      assert.equal('296' in args[0], true);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in create recipe');
    }
  });

  it('give graph data for type heatmap', async () => {
    getGraphsMetaDataBetween2TimestampsMock.resolves(new Set([1,3]));
    getDeviceParamsDataBetween2TimestampInPreferredUnitMock.resolves([{timestamp:'2020-09-05T07:32:27+00:00', data:{kw:'jdj'}}]);
    try {
      let inputs = {_userMeta:{siteId:'123',unitPref:'kwah'},type:'heatmap','startTime':'2020-08-05 06:54','endTime':'2020-08-06 06:54','deviceId':'296','group':'day','params':['KW']};
      let exits = {};
      exits.success = sinon.spy();
      await plotGraph.fn(inputs,exits);
      assert.equal(exits.success.callCount, 1);
      let args = exits.success.getCall(0).args;
      assert.equal('296' in args[0], true);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in create recipe');
    }
  });

});

describe('plot graph negative suit', async () => {
  let getGraphsMetaDataBetween2TimestampsMock,getDeviceParamsDataBetween2TimestampInPreferredUnitMock;
  before(() => {
    getGraphsMetaDataBetween2TimestampsMock = sinon.stub(datadeviceService, 'getGraphsMetaDataBetween2Timestamps');
    getDeviceParamsDataBetween2TimestampInPreferredUnitMock = sinon.stub(datadeviceService, 'getDeviceParamsDataBetween2TimestampInPreferredUnit');
  });
  after(() => {
    sinon.restore();
  });

  it('bad request', async () => {
    getGraphsMetaDataBetween2TimestampsMock.resolves(new Set([1,3]));
    getDeviceParamsDataBetween2TimestampInPreferredUnitMock.resolves([{timestamp:'2020-09-05T07:32:27+00:00', data:{kw:'jdj'}}]);
    try {
      let inputs = {_userMeta:{siteId:'123',unitPref:'kwah'},'startTime':'2020-08-05 06:54','endTime':'2020-08-06 06:54','deviceId':'296','group':'day','params':['KW']};
      let exits = {};
      exits.badRequest = sinon.spy();
      await plotGraph.fn(inputs,exits);
      assert.equal(exits.badRequest.callCount, 1);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in create recipe');
    }
  });

  it('give error on device data problems', async () => {
    getGraphsMetaDataBetween2TimestampsMock.resolves({problems:['we have issue']});
    getDeviceParamsDataBetween2TimestampInPreferredUnitMock.resolves([{timestamp:'2020-09-05T07:32:27+00:00', data:{kw:'jdj'}}]);
    try {
      let inputs = {_userMeta:{siteId:'123',unitPref:'kwah'},type:'heatmap','startTime':'2020-08-05 06:54','endTime':'2020-08-06 06:54','deviceId':'296','group':'day','params':['KW']};
      let exits = {};
      exits.badRequest = sinon.spy();
      await plotGraph.fn(inputs,exits);
      assert.equal(exits.badRequest.callCount, 1);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in create recipe');
    }
  });

  it('give error on device data problems', async () => {
    getGraphsMetaDataBetween2TimestampsMock.resolves(new Set([1,3]));
    getDeviceParamsDataBetween2TimestampInPreferredUnitMock.resolves({problems:['we have issue']});
    try {
      let inputs = {_userMeta:{siteId:'123',unitPref:'kwah'},type:'heatmap','startTime':'2020-08-05 06:54','endTime':'2020-08-06 06:54','deviceId':'296','group':'day','params':['KW']};
      let exits = {};
      exits.badRequest = sinon.spy();
      await plotGraph.fn(inputs,exits);
      assert.equal(exits.badRequest.callCount, 1);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in create recipe');
    }
  });

  it('main catch block', async () => {
    getGraphsMetaDataBetween2TimestampsMock.rejects(new Set([1,3]));
    getDeviceParamsDataBetween2TimestampInPreferredUnitMock.rejects({problems:['we have issue']});
    try {
      let inputs = {_userMeta:{siteId:'123',unitPref:'kwah'},type:'heatmap','startTime':'2020-08-05 06:54','endTime':'2020-08-06 06:54','deviceId':'296','group':'day','params':['KW']};
      let exits = {};
      exits.serverError = sinon.spy();
      await plotGraph.fn(inputs,exits);
      assert.equal(exits.serverError.callCount, 1);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in create recipe');
    }
  });
});
