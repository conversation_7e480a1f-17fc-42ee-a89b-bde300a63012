var assert = require('assert');
var mocha = require('mocha');
var sinon = require('sinon');
sails = require('sails');
const userService = require('../../../api/services/user/user.service');
sinon = sinon.createSandbox();

if (mocha.describe) {
  describe = mocha.describe;
  it = mocha.it;
  before = mocha.before;
  after = mocha.after;
} else {
  describe = global.describe;
  it = global.it;
  before = global.before;
  after = global.after;
}
let getuser = require('../../../api/controllers/user/get-user');

describe('get user positive suit', async () => {
  let findOneMockUser, getUserPreferenceOnAllSitesMock;
  before(() => {
    findOneMockUser = sinon.stub(userService, 'findOne');
    getUserPreferenceOnAllSitesMock = sinon.stub(userService, 'getUserPreferenceOnAllSites');
  });
  after(() => {
    sinon.restore();
  });

  it('should get user', async () => {
    findOneMockUser.resolves({});
    getUserPreferenceOnAllSitesMock.resolves([{ role: 'admin', siteId: 'gknmh' ,userId:'<EMAIL>'}]);

    try {
      let inputs = {
        _userMeta: {
          id: '<EMAIL>',
          _role: 'admin',
        },
      };
      let exits = {};
      exits.success = sinon.spy();
      await getuser.fn(inputs, exits);
      assert.equal(exits.success.callCount, 1);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in get user');
    }
  });
});
describe('get user negative suit', async () => {
  let findOneMockUser, getUserPreferenceOnAllSitesMock;
  before(() => {
    findOneMockUser = sinon.stub(userService, 'findOne');
    getUserPreferenceOnAllSitesMock= sinon.stub(userService, 'getUserPreferenceOnAllSites');
  });
  after(() => {
    sinon.restore();
  });

  it('should give bad Request error', async () => {
    findOneMockUser.resolves({ problems: '123' });
    getUserPreferenceOnAllSitesMock.resolves({ role: 'admin', siteId: 'gknmh' ,userId:'<EMAIL>'});

    try {
      let inputs = {
        _userMeta: {
          id: '<EMAIL>',
          _role: 'admin',
        },
      };
      let exits = {};
      exits.badRequest = sinon.spy();
      await getuser.fn(inputs, exits);

      assert.equal(exits.badRequest.callCount, 1);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in get user');
    }
  });
  it('should give error for undefined', async () => {
    findOneMockUser.resolves(undefined);
    getUserPreferenceOnAllSitesMock.resolves({ role: 'admin', siteId: 'gknmh',userId:'<EMAIL>' });

    try {
      let inputs = {
        _userMeta: {
          id: '<EMAIL>',
          _role: 'admin',
        },
      };
      let exits = {};
      exits.badRequest = sinon.spy();
      await getuser.fn(inputs, exits);

      assert.equal(exits.badRequest.callCount, 1);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in create controller');
    }
  });
  it('should give error for undefined', async () => {
    findOneMockUser.rejects();
    getUserPreferenceOnAllSitesMock.resolves({ role: 'admin', siteId: 'gknmh' ,userId:'<EMAIL>'});

    try {
      let inputs = {
        _userMeta: {
          id: '<EMAIL>',
          _role: 'admin',
        },
      };
      let exits = {};
      exits.serverError = sinon.spy();
      await getuser.fn(inputs, exits);

      assert.equal(exits.serverError.callCount, 1);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in create controller');
    }
  });
});
