var assert = require('assert');
var mocha = require('mocha');
var sinon = require('sinon');
sails = require('sails');
const self = require('../../../api/services/device/device.service');

sinon = sinon.createSandbox();
// let describe;
// let it;
// let before;
// let after;
if (mocha.describe) {
  describe = mocha.describe;
  it = mocha.it;
  before = mocha.before;
  after = mocha.after;
} else {
  describe = global.describe;
  it = global.it;
  before = global.before;
  after = global.after;
}
let finddevice = require('../../../api/controllers/device/finddevice');
describe('find device positive suit', async () => {
  let findMockDevice;
  before(() => {
    findMockDevice = sinon.stub(self, 'find');
  });
  after(() => {
    sinon.restore();
  });

  it('should get device', async () => {
    findMockDevice.resolves([{ deviceId: '2579', siteId: 'mgch' }]);

    try {
      let inputs = {
        deviceId: '2345',
        siteId: 'ssh',
      };
      let exits = {};
      exits.success = sinon.spy();
      await finddevice.fn(inputs, exits);
      assert.equal(exits.success.callCount, 1);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in find device');
    }
  });
});
describe('find device negative suit', async () => {
  let findMockDevice;
  before(() => {
    findMockDevice = sinon.stub(self, 'find');
  });
  after(() => {
    sinon.restore();
  });
  it('should give error for if device not found', async () => {
    findMockDevice.rejects([{ deviceId: '2579', siteId: 'mgch' }]);
    try {
      let inputs = {
        deviceId: '2345',
        siteId: 'ssh',
      };

      let exits = {};
      finddevice.res = { serverError: sinon.spy() };
      await finddevice.fn(inputs, exits);
      assert.equal(finddevice.res.serverError.callCount, 1);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in find device');
    }
  });
});
