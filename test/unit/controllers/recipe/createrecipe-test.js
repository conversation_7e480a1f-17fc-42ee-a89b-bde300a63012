var assert = require('assert');
var mocha = require('mocha');
var sinon = require('sinon');
sails = require('sails');
sails.config = {IOT_CONFIG:{}};
const self = require('../../../../api/services/recipe/recipe.service');
const utils = require('../../../../api/utils/recipe/utils');

sinon = sinon.createSandbox();
let describe;
let it;
let before;
let after;
if (mocha.describe) {
  describe = mocha.describe;
  it = mocha.it;
  before = mocha.before;
  after = mocha.after;
} else {
  describe = global.describe;
  it = global.it;
  before = global.before;
  after = global.after;
}
let createRecipe = require('../../../../api/controllers/recipe/createrecipe');

describe('create recipe positive suit', async () => {
  let getRunOnMock, createMockDevice,v1ParseFormulaMock;
  before(() => {
    getRunOnMock = sinon.stub(self, 'getRunOn');
    createMockDevice = sinon.stub(self, 'create');
    v1ParseFormulaMock = sinon.stub(self,'v1ParseFormula');
  });
  after(() => {
    sinon.restore();
  });

  it('should create a recipe if isSchedule is true', async () => {
    getRunOnMock.resolves({});
    createMockDevice.resolves();
    try {
      let inputs = {_userMeta:{id:'123'},'siteId':'123','recipelabel':['reciperoutine'],'label':'1','maxDataNeeded':'2','actionable':[{ 'title': 'test-1', 'description': 'none', 'notify': ['<EMAIL>'], 'accountable': ['<EMAIL>'], 'type': 'alert', 'priority': 0 }], 'neo':'wowoa', 'isSchedule':'true', 'type':'routine'};
      let exits = {};
      exits.success = sinon.spy();
      await createRecipe.fn(inputs,exits);
      assert.equal(exits.success.callCount, 1);
      let args = exits.success.getCall(0).args;
      assert.equal(args[0].siteId, '123');
    } catch (err) {
      console.log(err);
      assert.fail('Exception in create recipe');
    }
  });

  it('should create a recipe if isSchedule is false', async () => {
    getRunOnMock.resolves({});
    createMockDevice.resolves();
    v1ParseFormulaMock.resolves({ 
      dids:[], 
      devicesToSubscribe:'', 
      parsedFormula:'', 
      runOnServer:''
    });
    try {
      let inputs = {_userMeta:{id:'123'},'formula': '||#1||$3||1',
        'label': 'test', 
        'type': 'nonRoutine', 
        'recipelabel': ['comfort'], 
        'neo': 'simple', 
        'isSchedule': 'false',
        'operators': { '$1': '-', '$2': '/', '$3': '>', '$4': '(', '$5': ')' }, 
        'params': { '#1': 'ssh_6.kvah' }, 
        'siteId': 'ssh', 
        'startNow': true, 
        'maxDataNeeded': 1, 
        'actionable': [
          { 'title': 'e', 'description': 'a',
            'notify': ['<EMAIL>'], 
            'accountable': ['<EMAIL>'], 
            'type': 'alert', 'priority': 0 }]};
      let exits = {};
      exits.success = sinon.spy();
      await createRecipe.fn(inputs,exits);
      assert.equal(exits.success.callCount, 1);
      let args = exits.success.getCall(0).args;
      assert.equal(args[0].siteId, 'ssh');
    } catch (err) {
      console.log(err);
      assert.fail('Exception in create recipe');
    }
  });

  it('should create a recipe if isSchedule is false, appType is thermal', async () => {
    getRunOnMock.resolves({});
    createMockDevice.resolves();
    v1ParseFormulaMock.resolves({ 
      dids:[], 
      devicesToSubscribe:'', 
      parsedFormula:'', 
      runOnServer:''
    });
    try {
      let inputs = {_userMeta:{id:'123'},'formula': '||#1||$3||1',
        'appType':'thermal',
        'label': 'test', 
        'misc':{ 'appSettings': {}, 'controlSettings': {}, 'dataExpression': {} },
        'type': 'nonRoutine', 
        'recipelabel': ['comfort'], 
        'neo': 'simple', 
        'isSchedule': 'false',
        'operators': { '$1': '-', '$2': '/', '$3': '>', '$4': '(', '$5': ')' }, 
        'params': { '#1': 'ssh_6.kvah' }, 
        'siteId': 'ssh', 
        'startNow': true, 
        'componentId':'123',
        'maxDataNeeded': 1, 
        'actionable': [
          { 'title': 'e', 'description': 'a',
            'notify': ['<EMAIL>'], 
            'accountable': ['<EMAIL>'], 
            'type': 'alert', 'priority': 0 }]};
      let exits = {};
      exits.success = sinon.spy();
      await createRecipe.fn(inputs,exits);
      assert.equal(exits.success.callCount, 1);
      let args = exits.success.getCall(0).args;
      assert.equal(args[0].componentId, '123');
    } catch (err) {
      console.log(err);
      assert.fail('Exception in create recipe');
    }
  });
});

describe('create recipe negative suit', async () => {
  let getRunOnMock, createMockDevice,sailsMock,utilsMock,v1ParseFormulaMock;
  before(() => {
    getRunOnMock = sinon.stub(self, 'getRunOn');
    createMockDevice = sinon.stub(self, 'create');
    sailsMock = sinon.stub(sails.log,'error');
    utilsMock = sinon.stub(utils,'isFormulaCorrect');
    v1ParseFormulaMock = sinon.stub(self,'v1ParseFormula');

  });
  after(() => {
    sinon.restore();
  });

  it('should give error if userMetadata is not provided', async () => {
    getRunOnMock.resolves({});
    createMockDevice.resolves();
    sailsMock.returns();
    try {
      let inputs = {'siteId':'123','recipelabel':['reciperoutine'],'label':'1','maxDataNeeded':'2','actionable':[{ 'title': 'test-1', 'description': 'none', 'notify': ['<EMAIL>'], 'accountable': ['<EMAIL>'], 'type': 'alert', 'priority': 0 }], 'neo':'wowoa', 'isSchedule':'true', 'type':'routine'};
      let exits = {};
      exits.serverError = sinon.spy();
      await createRecipe.fn(inputs,exits);
      assert.equal(exits.serverError.callCount, 1);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in create recipe');
    }
  });

  it('should give error for bad request', async () => {
    getRunOnMock.resolves({});
    createMockDevice.resolves();
    sailsMock.returns();
    try {
      let inputs = {_userMeta:{id:'123'},'siteId':'123','recipelabel':['someroutine'],'label':'1','maxDataNeeded':'2','actionable':[{ 'title': 'test-1', 'description': 'none', 'notify': ['<EMAIL>'], 'accountable': ['<EMAIL>'], 'type': 'alert', 'priority': 0 }], 'neo':'wowoa', 'isSchedule':'true', 'type':'routine'};
      let exits = {};
      exits.badRequest = sinon.spy();
      await createRecipe.fn(inputs,exits);
      assert.equal(exits.badRequest.callCount, 1);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in create recipe');
    }
  });

  it('should give error for bad formula, isSchedule=false', async () => {
    getRunOnMock.resolves({});
    createMockDevice.resolves();
    sailsMock.returns();
    utilsMock.returns(false);
    v1ParseFormulaMock.resolves({ 
      dids:[], 
      devicesToSubscribe:'', 
      parsedFormula:'', 
      runOnServer:''
    });
    try {
      let inputs = {_userMeta:{id:'123'},'formula': '||#1||$3||1','siteId':'123','recipelabel':['reciperoutine'],'label':'1','maxDataNeeded':'2','actionable':[{ 'title': 'test-1', 'description': 'none', 'notify': ['<EMAIL>'], 'accountable': ['<EMAIL>'], 'type': 'alert', 'priority': 0 }], 'neo':'wowoa', 'isSchedule':'false', 'type':'routine'};
      let exits = {};
      exits.forbidden = sinon.spy();
      await createRecipe.fn(inputs,exits);
      assert.equal(exits.forbidden.callCount, 1);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in create recipe');
    }
  });

  it('should give error for bad request for thermal and no misc object', async () => {
    getRunOnMock.resolves({});
    createMockDevice.resolves();
    sailsMock.returns();
    utilsMock.returns(false);
    v1ParseFormulaMock.resolves({ 
      dids:[], 
      devicesToSubscribe:'', 
      parsedFormula:'', 
      runOnServer:''
    });
    try {
      let inputs = {_userMeta:{id:'123'},'formula': '||#1||$3||1',
        'appType':'thermal',
        'label': 'test', 
        'type': 'nonRoutine', 
        'recipelabel': ['comfort'], 
        'neo': 'simple', 
        'isSchedule': 'false',
        'operators': { '$1': '-', '$2': '/', '$3': '>', '$4': '(', '$5': ')' }, 
        'params': { '#1': 'ssh_6.kvah' }, 
        'siteId': 'ssh', 
        'startNow': true, 
        'componentId':'123',
        'maxDataNeeded': 1, 
        'actionable': [
          { 'title': 'e', 'description': 'a',
            'notify': ['<EMAIL>'], 
            'accountable': ['<EMAIL>'], 
            'type': 'alert', 'priority': 0 }]};
      let exits = {};
      exits.badRequest = sinon.spy();
      await createRecipe.fn(inputs,exits);
      assert.equal(exits.badRequest.callCount, 1);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in create recipe');
    }
  });

  it('should give error for invalid app type', async () => {
    getRunOnMock.resolves({});
    createMockDevice.resolves();
    sailsMock.returns();
    utilsMock.returns(false);
    v1ParseFormulaMock.resolves({ 
      dids:[], 
      devicesToSubscribe:'', 
      parsedFormula:'', 
      runOnServer:''
    });
    try {
      let inputs = {_userMeta:{id:'123'},'formula': '||#1||$3||1',
        'appType':'invalid',
        'label': 'test', 
        'type': 'nonRoutine', 
        'recipelabel': ['comfort'], 
        'neo': 'simple', 
        'isSchedule': 'false',
        'operators': { '$1': '-', '$2': '/', '$3': '>', '$4': '(', '$5': ')' }, 
        'params': { '#1': 'ssh_6.kvah' }, 
        'siteId': 'ssh', 
        'startNow': true, 
        'componentId':'123',
        'maxDataNeeded': 1, 
        'actionable': [
          { 'title': 'e', 'description': 'a',
            'notify': ['<EMAIL>'], 
            'accountable': ['<EMAIL>'], 
            'type': 'alert', 'priority': 0 }]};
      let exits = {};
      exits.badRequest = sinon.spy();
      await createRecipe.fn(inputs,exits);
      assert.equal(exits.badRequest.callCount, 1);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in create recipe');
    }
  });
});
