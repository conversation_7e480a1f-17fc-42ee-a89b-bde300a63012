const assert = require('chai').assert;
let userSiteMapService = require('../../../../api/services/userSiteMap/userSiteMap.public');
var sinon = require('sinon');
sails = require('sails');
sails.config = {IOT_CONFIG:{}, cachePort:''};
sinon = sinon.createSandbox();

let getUserPref = require('../../../../api/controllers/user/get-user-pref');

describe('Get User Preferences Action Unit Tests', () => {
  let userSiteMapFindOneMock, sailsMock;
  before(() => {
    userSiteMapFindOneMock = sinon.stub(userSiteMapService, 'findOne');
    sailsMock = sinon.stub(sails.log, 'error');
  });
  after(() => {
    sinon.restore();
  });

  it('No user id passed', async () => {
    let inputs = {
      _userMeta: {}
    };
    let exits = {};
    exits.badRequest = sinon.spy();
    await getUserPref.fn(inputs, exits);
    assert.strictEqual(exits.badRequest.callCount, 1);
    let args = exits.badRequest.getCall(0).args;
    assert.strictEqual(args[0].problems[0], 'Invalid request parameters.');
  });
  
  it('Catching exception', async () => {
    let inputs = {};
    let exits = {};
    sailsMock.returns();
    exits.serverError = sinon.spy();
    await getUserPref.fn(inputs, exits);
    assert.strictEqual(exits.serverError.callCount, 1);
  });

  it('No user info found', async () => {
    let inputs = {
      _userMeta: {
        id: '<EMAIL>',
        _site: 'ssh'
      }
    };
    let exits = {};
    exits.forbidden = sinon.spy();
    userSiteMapFindOneMock.resolves({});
    await getUserPref.fn(inputs, exits);
    assert.strictEqual(exits.forbidden.callCount, 1);
    let args = exits.forbidden.getCall(0).args;
    assert.strictEqual(args[0].problems[0], 'User Info for this site not found.');
  });

  it('Positive Suit', async () => {
    let inputs = {
      _userMeta: {
        id: '<EMAIL>',
        _site: 'ssh'
      }
    };
    let exits = {};
    exits.success = sinon.spy();
    userSiteMapFindOneMock.resolves({
      dj: {},
      djNotif: {},
      mailConfig: {},
      msgConfig: {},
      unitPreference: {}
    });
    await getUserPref.fn(inputs, exits);
    assert.strictEqual(exits.success.callCount, 1);
    let args = exits.success.getCall(0).args[0];
    assert.hasAllDeepKeys(args, ['dj', 'djNotif', 'mailConfig', 'msgConfig', 'unitPreference']);
  });
});
