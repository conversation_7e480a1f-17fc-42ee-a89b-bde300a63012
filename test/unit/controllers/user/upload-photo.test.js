const assert = require('chai').assert;
var sinon = require('sinon');
sails = require('sails');
sails.config = {IOT_CONFIG:{}, cachePort:''};
const self = require('../../../../api/services/user/user.service');
sinon = sinon.createSandbox();

let uploadPhoto = require('../../../../api/controllers/user/upload-photo');

describe('Edit password action unit tests', () => {
  let updateUserMock, sailsMock;
  before(() => {
    updateUserMock = sinon.stub(self, 'update');
    sailsMock = sinon.stub(sails.log, 'error');
  });
  after(() => {
    sinon.restore();
  });

  it('Invalid userId', async () => {
    let inputs = {};
    let exits = {};
    exits.badRequest = sinon.spy();
    uploadPhoto.req = {
      _userMeta: {

      }
    };
    await uploadPhoto.fn(inputs, exits);
    assert.strictEqual(exits.badRequest.callCount, 1);
    let args = exits.badRequest.getCall(0).args;
    assert.strictEqual(args[0].problems[0], 'User ID is not present.');
  });

  it('Server error', async () => {
    let inputs = {};
    let exits = {};
    exits.serverError = sinon.spy();
    uploadPhoto.req = {
      _userMeta: {
        id: 'abc'
      }
    };
    updateUserMock.rejects();
    sailsMock.returns();
    await uploadPhoto.fn(inputs, exits);
    assert.strictEqual(exits.serverError.callCount, 1);
  });

  it('Positive Suit', async () => {
    let inputs = {};
    let exits = {};
    exits.success = sinon.spy();
    uploadPhoto.req = {
      _userMeta: {
        id: 'abc'
      }
    };
    updateUserMock.resolves();
    sailsMock.returns();
    await uploadPhoto.fn(inputs, exits);
    assert.strictEqual(exits.success.callCount, 1);
  });
});
