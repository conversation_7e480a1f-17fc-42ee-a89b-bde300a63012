const assert = require('chai').assert;
var sinon = require('sinon');
sails = require('sails');
sails.config = {IOT_CONFIG:{}, cachePort:''};
const self = require('../../../../api/services/user/user.service');
const jwtService = require('../../../../api/services/auth/auth.public');
sinon = sinon.createSandbox();

let resetPassword = require('../../../../api/controllers/user/reset-password');

describe('Reset password action unit tests', () => {
  let userFindMock, updateUserMock, sailsMock, verifyJwtMock, md5Mock;
  before(() => {
    userFindMock = sinon.stub(self, 'findOne');
    verifyJwtMock = sinon.stub(jwtService, 'verifyJWTToken');
    updateUserMock = sinon.stub(self, 'update');
    sailsMock = sinon.stub(sails.log, 'error');
  });
  after(() => {
    sinon.restore();
  });

  it('Invalid token', async () => {
    let inputs = {};
    let exits = {};
    exits.forbidden = sinon.spy();
    verifyJwtMock.throws();
    await resetPassword.fn(inputs, exits);
    assert.strictEqual(exits.forbidden.callCount, 1);
    let args = exits.forbidden.getCall(0).args;
    assert.strictEqual(args[0].problems[0], 'Invalid token');
  });

  it('User Not Found', async () => {
    let inputs = {};
    let exits = {};
    exits.forbidden = sinon.spy();
    verifyJwtMock.returns({secret: 'abc', id: 'asd'});
    userFindMock.resolves(undefined);
    await resetPassword.fn(inputs, exits);
    assert.strictEqual(exits.forbidden.callCount, 1);
    let args = exits.forbidden.getCall(0).args;
    assert.strictEqual(args[0].problems[0], 'This user does not exist');
  });

  it('Password already changed', async () => {
    let inputs = {};
    let exits = {};
    exits.forbidden = sinon.spy();
    verifyJwtMock.returns({secret: 'abc', id: 'asd'});
    userFindMock.resolves({userId: 'asd', password: '12345'});
    await resetPassword.fn(inputs, exits);
    assert.strictEqual(exits.forbidden.callCount, 1);
    let args = exits.forbidden.getCall(0).args;
    assert.strictEqual(args[0].problems[0], 'Password already changed using this link');
  });


  it('Server error while updating', async () => {
    let inputs = {};
    let exits = {};
    exits.serverError = sinon.spy();
    verifyJwtMock.returns({secret: '7f31c778d8785a05d2de7f3e22c9468a', id: 'asd'});
    userFindMock.resolves({userId: 'asd', password: '12345'});
    updateUserMock.rejects();
    sailsMock.returns();
    await resetPassword.fn(inputs, exits);
    assert.strictEqual(exits.serverError.callCount, 1);
  });

  it('Positive suit', async () => {
    let inputs = {};
    let exits = {};
    exits.success = sinon.spy();
    verifyJwtMock.returns({secret: '7f31c778d8785a05d2de7f3e22c9468a', id: 'asd'});
    userFindMock.resolves({userId: 'asd', password: '12345'});
    updateUserMock.resolves();
    await resetPassword.fn(inputs, exits);
    assert.strictEqual(exits.success.callCount, 1);
  });
});
