const assert = require('chai').assert;
var sinon = require('sinon');
sails = require('sails');
sails.config = {IOT_CONFIG:{}, cachePort:''};
const self = require('../../../../api/services/user/user.service');
sinon = sinon.createSandbox();

let userAvailable = require('../../../../api/controllers/user/user-available');

describe('Edit password action unit tests', () => {
  let userFindMock, sailsMock;
  before(() => {
    userFindMock = sinon.stub(self, 'findOne');
    sailsMock = sinon.stub(sails.log, 'error');
  });
  after(() => {
    sinon.restore();
  });

  it('User ID is present', async () => {
    let inputs = {};
    let exits = {};
    exits.forbidden = sinon.spy();
    userFindMock.returns({id: 'abc'});
    await userAvailable.fn(inputs, exits);
    assert.strictEqual(exits.forbidden.callCount, 1);
    let args = exits.forbidden.getCall(0).args;
    assert.strictEqual(args[0].problems[0], 'User with this ID exists');
  });

  it('User ID is not present, positive suit', async () => {
    let inputs = {};
    let exits = {};
    exits.success = sinon.spy();
    userFindMock.returns(undefined);
    await userAvailable.fn(inputs, exits);
    assert.strictEqual(exits.success.callCount, 1);
  });

  it('Server error', async () => {
    let inputs = {};
    let exits = {};
    exits.serverError = sinon.spy();
    userFindMock.rejects();
    sailsMock.returns();
    await userAvailable.fn(inputs, exits);
    assert.strictEqual(exits.serverError.callCount, 1);
  });
});
