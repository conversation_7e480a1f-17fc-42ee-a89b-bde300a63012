const assert = require('chai').assert;
let userService = require('../../../../api/services/user/user.service');
let userSiteMapService = require('../../../../api/services/userSiteMap/userSiteMap.public');
var sinon = require('sinon');
sails = require('sails');
sails.config = {IOT_CONFIG:{}, cachePort:''};
sinon = sinon.createSandbox();

let getUserPolicies = require('../../../../api/controllers/user/get-user-policies');

describe('Get User Policies Action Unit Tests', () => {
  let userSiteMapFindMock, sailsMock, getRolesInfoMock;
  before(() => {
    userSiteMapFindMock = sinon.stub(userSiteMapService, 'find');
    getRolesInfoMock = sinon.stub(userService, 'getRolesInfo');
    sailsMock = sinon.stub(sails.log, 'error');
  });
  after(() => {
    sinon.restore();
  });

  it('No user id passed', async () => {
    let inputs = {
      _userMeta: {}
    };
    let exits = {};
    exits.forbidden = sinon.spy();
    await getUserPolicies.fn(inputs, exits);
    assert.strictEqual(exits.forbidden.callCount, 1);
    let args = exits.forbidden.getCall(0).args;
    assert.strictEqual(args[0].problems[0], 'No valid user id is present in the token');
  });

  it('Catching exception', async () => {
    let inputs = {};
    let exits = {};
    sailsMock.returns();
    exits.serverError = sinon.spy();
    await getUserPolicies.fn(inputs, exits);
    assert.strictEqual(exits.serverError.callCount, 1);
  });

  it('No roles found for a user', async () => {
    let inputs = {
      _userMeta: {id: '<EMAIL>'}
    };
    let exits = {};
    userSiteMapFindMock.resolves([]);
    exits.forbidden = sinon.spy();
    await getUserPolicies.fn(inputs, exits);
    assert.strictEqual(exits.forbidden.callCount, 1);
    let args = exits.forbidden.getCall(0).args;
    assert.strictEqual(args[0].problems[0], 'Unable to get any roles for this user.');
  });

  it('No roles info found', async () => {
    let inputs = {
      _userMeta: {id: '<EMAIL>'}
    };
    let exits = {};
    userSiteMapFindMock.resolves([{
      siteId: 'ssh',
      role: 'admin'
    }]);
    getRolesInfoMock.resolves({ problems: ['Invalid Role.'] });
    exits.forbidden = sinon.spy();
    await getUserPolicies.fn(inputs, exits);
    assert.strictEqual(exits.forbidden.callCount, 1);
    let args = exits.forbidden.getCall(0).args;
    assert.strictEqual(args[0].problems[0], 'Invalid Role.');
  });

  it('Positive Suit', async () => {
    let inputs = {
      _userMeta: {id: '<EMAIL>'}
    };
    let exits = {};
    userSiteMapFindMock.resolves([{
      siteId: 'ssh',
      role: 'admin'
    }]);
    getRolesInfoMock.resolves({
      admin: {
        policies: {}
      }
    });
    exits.success = sinon.spy();
    await getUserPolicies.fn(inputs, exits);
    assert.strictEqual(exits.success.callCount, 1);
    let args = exits.success.getCall(0).args[0];
    assert.hasAllDeepKeys(args[0], ['roleName', 'policies']);
  });
  
});
