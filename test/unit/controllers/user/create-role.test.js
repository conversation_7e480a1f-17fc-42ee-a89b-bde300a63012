const assert = require('chai').assert;
var sinon = require('sinon');
sails = require('sails');
sails.config = {IOT_CONFIG:{}, cachePort:''};
const roleService = require('../../../../api/services/role/role.public');
sinon = sinon.createSandbox();

let createRole = require('../../../../api/controllers/user/create-role');

describe('Create Role Action Unit Tests', () => {
  let findRoleMock, createRoleMock, sailsMock;
  before(() => {
    findRoleMock = sinon.stub(roleService, 'findOne');
    createRoleMock = sinon.stub(roleService, 'create');
    sailsMock = sinon.stub(sails.log, 'error');
  });
  after(() => {
    sinon.restore();
  });

  it('Role already exists', async () => {
    let inputs = {
      roleName: 'admin',
      pref: {},
      policies: {}
    };
    let exits = {};
    exits.forbidden = sinon.spy();
    findRoleMock.resolves({roleName: 'admin'});
    await createRole.fn(inputs, exits);
    assert.strictEqual(exits.forbidden.callCount, 1);
    let args = exits.forbidden.getCall(0).args;
    assert.strictEqual(args[0].problems[0], 'This role already exists.');
  });

  it('Invalid policy from FE', async () => {
    let inputs = {
      roleName: 'admin',
      pref: {},
      policies: {Recipe: {}}
    };
    let exits = {};
    sailsMock.returns();
    exits.serverError = sinon.spy();
    findRoleMock.resolves(null);
    await createRole.fn(inputs, exits);
    assert.strictEqual(exits.serverError.callCount, 1);
  });

  it('Positive suit, all valid input', async () => {
    let inputs = {
      roleName: 'admin',
      pref: {},
      policies: {
        'Recipe': {
          'displayName': 'Recipe',
          'pageView': false,
          'subHeadingsList': [
            {
              'displayName': 'Recipe',
              'policyList': [
                {
                  'displayName': 'Create',
                  'routePolicyMap': {
                    'POST /m2/recipe/v2/recipe': 'recipe/createrecipe'
                  },
                  'hasAccess': false
                },
                {
                  'displayName': 'Read',
                  'routePolicyMap': {
                    'GET /m2/recipe/v2/recipe/siteId/:siteId': 'recipe/find-recipe-by-siteId'
                  },
                  'hasAccess': true
                },
                
              ]
            }
          ]
        }
      }
    };
    let exits = {};
    exits.success = sinon.spy();
    findRoleMock.resolves(null);
    createRoleMock.resolves();
    await createRole.fn(inputs, exits);
    assert.strictEqual(exits.success.callCount, 1);
    let args = exits.success.getCall(0).args;
    assert.hasAllDeepKeys(args[0], ['roleName', 'policies', 'policiesBE', 'defpref', 'isDeleted']);
  });
});
