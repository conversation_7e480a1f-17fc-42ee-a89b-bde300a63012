const assert = require('assert');
var sinon = require('sinon');
sails = require('sails');
sails.config = {IOT_CONFIG:{}, cachePort:''};
const self = require('../../../../api/services/user/user.service');
sinon = sinon.createSandbox();

let updateUser = require('../../../../api/controllers/user/update-user');

describe('Update User Action Unit Tests', () => {
  let sailsMock, updateUserMock;
  before(() => {
    sailsMock = sinon.stub(sails.log, 'error');
    updateUserMock = sinon.stub(self, 'update');
  });
  after(() => {
    sinon.restore();
  });
  it('Update object is nullish', async () => {
    let inputs = {_userMeta: {id: 'ghost'}};
    let exits = {};
    exits.success = sinon.spy();
    await updateUser.fn(inputs, exits);
    assert.strictEqual(exits.success.callCount, 1);
  });

  it('Unable to update the object', async () => {
    let inputs = {_userMeta: {id: 'ghost'}, name: 'priyam'};
    let exits = {};
    exits.serverError = sinon.spy();
    sailsMock.returns();
    updateUserMock.rejects();
    await updateUser.fn(inputs, exits);
    assert.strictEqual(exits.serverError.callCount, 1);
  });

  it('Positive suit, everything is ok', async () => {
    let inputs = {_userMeta: {id: 'ghost'}, name: 'priyam'};
    let exits = {};
    exits.success = sinon.spy();
    sailsMock.returns();
    updateUserMock.resolves();
    await updateUser.fn(inputs, exits);
    assert.strictEqual(exits.success.callCount, 1);
  });
});
