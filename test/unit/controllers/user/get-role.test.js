const assert = require('chai').assert;
let roleService = require('../../../../api/services/role/role.public');
var sinon = require('sinon');
sails = require('sails');
sails.config = {IOT_CONFIG:{}, cachePort:''};
sinon = sinon.createSandbox();

let getRole = require('../../../../api/controllers/user/get-role');

describe('Get Role Action Unit Tests', () => {
  let findRoleMock, sailsMock;
  before(() => {
    findRoleMock = sinon.stub(roleService, 'find');
    sailsMock = sinon.stub(sails.log, 'error');
  });
  after(() => {
    sinon.restore();
  });

  let multipleRoles = [
    {
      'defpref': '{"dj":{"JouleRecipeMode":"1","JouleRecipeThermostatMode":"1","MaintenanceMode":"1","OverrideMode":"1","UIMode":"1","DiagnosticGraph":"1"},"djNotif":{"JouleRecipe":{"recipeprocess":"1","recipecomfort":"0","reciperoutine":"0","recipefailsafe":"0","recipediagnostics":"0"},"MaintenanceAction":{"maintainancecritical":"0","maintainancehigh":"0","maintainancelow":"0","maintainancemedium":"0"},"hvacNotif":{"command":"0","mode":"0","config":"0"}},"mailConfig":{"JouleRecipe":{"recipeprocess":"1","recipecomfort":"0","reciperoutine":"0","recipefailsafe":"0","recipediagnostics":"0"},"MaintenanceAction":{"maintainancecritical":"0","maintainancehigh":"0","maintainancelow":"0","maintainancemedium":"0"},"hvacNotif":{"command":"0","mode":"0","config":"0"}},"msgConfig":{"JouleRecipe":{"recipeprocess":"1","recipecomfort":"1","reciperoutine":"1","recipefailsafe":"1","recipediagnostics":"1"},"MaintenanceAction":{"maintainancecritical":"0","maintainancehigh":"0","maintainancelow":"0","maintainancemedium":"0"},"hvacNotif":{"command":"0","mode":"0","config":"0"}},"unitPreference":{"temperature":"degC","delTemperature":"delC","pressure":"kPa","length":"m","cons":"kvah"}}',
      'isDeleted': '0',
      'policies': '{"ACPlant":{"displayName":"AC PLANT","pageView":true,"subHeadings":{"command":{"displayName":"Command","policies":{"write":{"displayName":"WRITE","routePolicyMap":{},"hasAccess":false},"read":{"displayName":"READ","routePolicyMap":{},"hasAccess":true}}}}},"Recipe":{"displayName":"Recipe","pageView":false,"subHeadings":{"recipe":{"displayName":"Recipe","policies":{"create":{"displayName":"Create","routePolicyMap":{"POST /m2/recipe/v2/recipe":false},"hasAccess":false},"read":{"displayName":"Read","routePolicyMap":{"GET /m2/recipe/v2/recipe/:key/:value":false},"hasAccess":false},"update":{"displayName":"Update","routePolicyMap":{"PUT /m2/recipe/v2/recipe":false},"hasAccess":false},"delete":{"displayName":"Delete","routePolicyMap":{"DELETE /m2/recipe/v2/recipe":false},"hasAccess":false},"deploy":{"displayName":"Deploy","routePolicyMap":{"POST /m2/recipe/v2/deploy":false},"hasAccess":false},"pause":{"displayName":"Pause","routePolicyMap":{"POST /m2/recipe/v2/pause":false},"hasAccess":false}}},"schedule":{"displayName":"Schedule","policies":{"create":{"displayName":"Create","routePolicyMap":{"POST /m2/recipe/v2/schedule":false},"hasAccess":false},"delete":{"displayName":"Delete","routePolicyMap":{"DELETE /m2/recipe/v2/schedule":false},"hasAccess":false},"find":{"displayName":"Find","routePolicyMap":{"GET /m2/recipe/v2/schedule/:key/:value":false},"hasAccess":false},"deploy":{"displayName":"Deploy","routePolicyMap":{"POST /m2/recipe/v2/deploySchedule":false},"hasAccess":false}}}}}}',
      'policiesBE': '{"POST /m2/recipe/v2/recipe":false,"GET /m2/recipe/v2/recipe/:key/:value":false,"PUT /m2/recipe/v2/recipe":false,"DELETE /m2/recipe/v2/recipe":false,"POST /m2/recipe/v2/deploy":false,"POST /m2/recipe/v2/pause":false,"POST /m2/recipe/v2/schedule":false,"DELETE /m2/recipe/v2/schedule":false,"GET /m2/recipe/v2/schedule/:key/:value":false,"POST /m2/recipe/v2/deploySchedule":false}',
      'roleName': 'test1',
    },
    {
      'defpref': '{"dj":{"JouleRecipeMode":"1","JouleRecipeThermostatMode":"1","MaintenanceMode":"1","OverrideMode":"1","UIMode":"1","DiagnosticGraph":"1"},"djNotif":{"JouleRecipe":{"recipeprocess":"1","recipecomfort":"0","reciperoutine":"0","recipefailsafe":"0","recipediagnostics":"0"},"MaintenanceAction":{"maintainancecritical":"0","maintainancehigh":"0","maintainancelow":"0","maintainancemedium":"0"},"hvacNotif":{"command":"0","mode":"0","config":"0"}},"mailConfig":{"JouleRecipe":{"recipeprocess":"1","recipecomfort":"0","reciperoutine":"0","recipefailsafe":"0","recipediagnostics":"0"},"MaintenanceAction":{"maintainancecritical":"0","maintainancehigh":"0","maintainancelow":"0","maintainancemedium":"0"},"hvacNotif":{"command":"0","mode":"0","config":"0"}},"msgConfig":{"JouleRecipe":{"recipeprocess":"1","recipecomfort":"1","reciperoutine":"1","recipefailsafe":"1","recipediagnostics":"1"},"MaintenanceAction":{"maintainancecritical":"0","maintainancehigh":"0","maintainancelow":"0","maintainancemedium":"0"},"hvacNotif":{"command":"0","mode":"0","config":"0"}},"unitPreference":{"temperature":"degC","delTemperature":"delC","pressure":"kPa","length":"m","cons":"kvah"}}',
      'isDeleted': '0',
      'policies': '{"ACPlant":{"displayName":"AC PLANT","pageView":true,"subHeadings":{"command":{"displayName":"Command","policies":{"write":{"displayName":"WRITE","routePolicyMap":{},"hasAccess":false},"read":{"displayName":"READ","routePolicyMap":{},"hasAccess":true}}}}},"Recipe":{"displayName":"Recipe","pageView":false,"subHeadings":{"recipe":{"displayName":"Recipe","policies":{"create":{"displayName":"Create","routePolicyMap":{"POST /m2/recipe/v2/recipe":false},"hasAccess":false},"read":{"displayName":"Read","routePolicyMap":{"GET /m2/recipe/v2/recipe/:key/:value":false},"hasAccess":false},"update":{"displayName":"Update","routePolicyMap":{"PUT /m2/recipe/v2/recipe":false},"hasAccess":false},"delete":{"displayName":"Delete","routePolicyMap":{"DELETE /m2/recipe/v2/recipe":false},"hasAccess":false},"deploy":{"displayName":"Deploy","routePolicyMap":{"POST /m2/recipe/v2/deploy":false},"hasAccess":false},"pause":{"displayName":"Pause","routePolicyMap":{"POST /m2/recipe/v2/pause":false},"hasAccess":false}}},"schedule":{"displayName":"Schedule","policies":{"create":{"displayName":"Create","routePolicyMap":{"POST /m2/recipe/v2/schedule":false},"hasAccess":false},"delete":{"displayName":"Delete","routePolicyMap":{"DELETE /m2/recipe/v2/schedule":false},"hasAccess":false},"find":{"displayName":"Find","routePolicyMap":{"GET /m2/recipe/v2/schedule/:key/:value":false},"hasAccess":false},"deploy":{"displayName":"Deploy","routePolicyMap":{"POST /m2/recipe/v2/deploySchedule":false},"hasAccess":false}}}}}}',
      'policiesBE': '{"POST /m2/recipe/v2/recipe":false,"GET /m2/recipe/v2/recipe/:key/:value":false,"PUT /m2/recipe/v2/recipe":false,"DELETE /m2/recipe/v2/recipe":false,"POST /m2/recipe/v2/deploy":false,"POST /m2/recipe/v2/pause":false,"POST /m2/recipe/v2/schedule":false,"DELETE /m2/recipe/v2/schedule":false,"GET /m2/recipe/v2/schedule/:key/:value":false,"POST /m2/recipe/v2/deploySchedule":false}',
      'roleName': 'test2',
    }
  ];

  

  it('No roles found', async () => {
    let inputs = {};
    let exits = {};
    exits.forbidden = sinon.spy();
    findRoleMock.resolves([]);
    await getRole.fn(inputs, exits);
    assert.strictEqual(exits.forbidden.callCount, 1);
    let args = exits.forbidden.getCall(0).args;
    assert.strictEqual(args[0].problems[0], 'Role not found!');
  });

  it('Returning all rolenames', async () => {
    let inputs = {};
    let exits = {};
    exits.success = sinon.spy();
    findRoleMock.resolves(multipleRoles);
    await getRole.fn(inputs, exits);
    assert.strictEqual(exits.success.callCount, 1);
    let args = exits.success.getCall(0).args;
    assert.isArray(args[0]);
    assert.deepEqual(args[0], ['test1', 'test2']);
  });

  it('Returning detailed info of 1 role', async () => {
    let inputs = {roleName: 'test1'};
    let exits = {};
    exits.success = sinon.spy();
    findRoleMock.resolves([multipleRoles[0]]);
    await getRole.fn(inputs, exits);
    assert.strictEqual(exits.success.callCount, 1);
    let args = exits.success.getCall(0).args;
    assert.isObject(args[0]);
  });

  it('Error while querying database', async () => {
    let inputs = {roleName: 'test1'};
    let exits = {};
    exits.serverError = sinon.spy();
    findRoleMock.rejects();
    sailsMock.returns();
    await getRole.fn(inputs, exits);
    assert.strictEqual(exits.serverError.callCount, 1);
  });
});
