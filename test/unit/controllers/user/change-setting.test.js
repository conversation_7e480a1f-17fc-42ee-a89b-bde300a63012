const assert = require('chai').assert;
var sinon = require('sinon');
sails = require('sails');
sails.config = {IOT_CONFIG:{}, cachePort:''};
const self = require('../../../../api/services/user/user.service');
const userSiteMapService = require('../../../../api/services/userSiteMap/userSiteMap.public');
const changeSettingUtils = require('../../../../api/utils/user/change-setting.util');
sinon = sinon.createSandbox();

let changeSetting = require('../../../../api/controllers/user/change-setting');

describe('Change Setting action unit tests', () => {
  let userFindMock, sailsMock, getRolesInfoMock, userUpdateMock, userSiteMapUpdateMock, getPersonSiteRoleMappingMock;
  before(() => {
    userFindMock = sinon.stub(self, 'findOne');
    sailsMock = sinon.stub(sails.log, 'error');
    getRolesInfoMock = sinon.stub(self, 'getRolesInfo');
    userUpdateMock = sinon.stub(self, 'update');
    userSiteMapUpdateMock = sinon.stub(userSiteMapService, 'update');
    getPersonSiteRoleMappingMock = sinon.stub(changeSettingUtils, 'getPersonSiteRoleMapping');
  });
  after(() => {
    sinon.restore();
  });

  it('Invalid inputs', async () => {
    let inputs = {};
    let exits = {};
    exits.badRequest = sinon.spy();
    getPersonSiteRoleMappingMock.returns({problems: ['error']});
    await changeSetting.fn(inputs, exits);
    assert.strictEqual(exits.badRequest.callCount, 1);
    let args = exits.badRequest.getCall(0).args;
    assert.strictEqual(args[0].problems[0], 'error');
  });

  it('Invalid roles', async () => {
    let inputs = {};
    let exits = {};
    exits.forbidden = sinon.spy();
    getPersonSiteRoleMappingMock.returns({abc: {ssh: 'admin'}});
    getRolesInfoMock.resolves({problems: ['Invalid role']});
    await changeSetting.fn(inputs, exits);
    assert.strictEqual(exits.forbidden.callCount, 1);
    let args = exits.forbidden.getCall(0).args;
    assert.strictEqual(args[0].problems[0], 'Invalid role');
  });

  it('User not found', async () => {
    let inputs = {};
    let exits = {};
    exits.forbidden = sinon.spy();
    getPersonSiteRoleMappingMock.returns({abc: {ssh: 'admin'}});
    getRolesInfoMock.resolves({});
    userFindMock.resolves(undefined);
    await changeSetting.fn(inputs, exits);
    assert.strictEqual(exits.forbidden.callCount, 1);
    let args = exits.forbidden.getCall(0).args;
    assert.strictEqual(args[0].problems[0], 'User not found');
  });

  it('Server error while updating user policy group', async () => {
    let inputs = {};
    let exits = {};
    exits.serverError = sinon.spy();
    getPersonSiteRoleMappingMock.returns({abc: {ssh: 'admin'}});
    getRolesInfoMock.resolves({});
    userFindMock.resolves({policiesGroup: '{}'});
    userUpdateMock.rejects();
    sailsMock.returns();
    await changeSetting.fn(inputs, exits);
    assert.strictEqual(exits.serverError.callCount, 1);
  });

  it('Positive suit, all good', async () => {
    let inputs = {};
    let exits = {};
    exits.success = sinon.spy();
    getPersonSiteRoleMappingMock.returns({abc: {ssh: 'admin'}});
    getRolesInfoMock.resolves({});
    userFindMock.resolves({policiesGroup: '{}'});
    userUpdateMock.resolves();
    userSiteMapUpdateMock.resolves();
    await changeSetting.fn(inputs, exits);
    assert.strictEqual(exits.success.callCount, 1);
  });
});
