const assert = require('chai').assert;
var sinon = require('sinon');
sails = require('sails');
sails.config = {IOT_CONFIG:{}, cachePort:''};
const self = require('../../../../api/services/user/user.service');
const createUserUtils = require('../../../../api/utils/user/create-user.util');
const userSiteMapService = require('../../../../api/services/userSiteMap/userSiteMap.public');
sinon = sinon.createSandbox();

let createUser = require('../../../../api/controllers/user/create-user');

describe('Create User Action Unit Test', () => {
  let getRolesInfoMock, deleteUserMock, sailsMock, userFindOneMock, userCreateMock, createInitialUserInfoMock, userSiteMapCreateMock;
  before(() => {
    getRolesInfoMock = sinon.stub(self, 'getRolesInfo');
    userFindOneMock = sinon.stub(self, 'findOne');
    userCreateMock = sinon.stub(self, 'create');
    createInitialUserInfoMock = sinon.stub(createUserUtils, 'createInitialUserInfoObject');
    sailsMock = sinon.stub(sails.log, 'error');
    userSiteMapCreateMock = sinon.stub(userSiteMapService, 'create');
    deleteUserMock = sinon.stub(self, 'delete');
  });
  after(() => {
    sinon.restore();
  });

  it('Incorrect input, invalid email', async () => {
    let inputs = {};
    let exits = {};
    exits.badRequest = sinon.spy();
    createInitialUserInfoMock.returns({problems: ['Invalid Email']});
    await createUser.fn(inputs, exits);
    assert.strictEqual(exits.badRequest.callCount, 1);
    let args = exits.badRequest.getCall(0).args;
    assert.strictEqual(args[0].problems[0], 'Invalid Email');
  });

  it('Incorrect input, invalid role', async () => {
    let inputs = {};
    let exits = {};
    exits.badRequest = sinon.spy();
    createInitialUserInfoMock.returns({defaultSite: 'ssh', policiesGroup: {ssh: 'admin'}});
    getRolesInfoMock.resolves({problems: ['Invalid Role.']});
    await createUser.fn(inputs, exits);
    assert.strictEqual(exits.badRequest.callCount, 1);
    let args = exits.badRequest.getCall(0).args;
    assert.strictEqual(args[0].problems[0], 'Invalid Role.');
  });

  it('User already exists', async () => {
    let inputs = {};
    let exits = {};
    exits.forbidden = sinon.spy();
    createInitialUserInfoMock.returns({defaultSite: 'ssh', policiesGroup: {ssh: 'admin'}});
    getRolesInfoMock.resolves({});
    userFindOneMock.resolves({user:{}});
    await createUser.fn(inputs, exits);
    assert.strictEqual(exits.forbidden.callCount, 1);
    let args = exits.forbidden.getCall(0).args;
    assert.strictEqual(args[0].problems[0], 'User with this ID already exists');
  });

  it('User not created, server error', async () => {
    let inputs = {};
    let exits = {};
    exits.serverError = sinon.spy();
    createInitialUserInfoMock.returns({defaultSite: 'ssh', policiesGroup: {ssh: 'admin'}});
    getRolesInfoMock.resolves({});
    userFindOneMock.resolves(null);
    userCreateMock.rejects();
    sailsMock.returns();
    await createUser.fn(inputs, exits);
    assert.strictEqual(exits.serverError.callCount, 1);
  });

  it('User Site Map error, server error', async () => {
    let inputs = {};
    let exits = {};
    exits.serverError = sinon.spy();
    createInitialUserInfoMock.returns({defaultSite: 'ssh', policiesGroup: {ssh: 'admin'}});
    getRolesInfoMock.resolves({admin: {defpref: {}}});
    userFindOneMock.resolves(null);
    userCreateMock.resolves();
    sailsMock.returns();
    userSiteMapCreateMock.rejects();
    deleteUserMock.resolves();
    await createUser.fn(inputs, exits);
    assert.strictEqual(exits.serverError.callCount, 1);
  });

  it('Positive suit, everything goes right', async () => {
    let inputs = {};
    let exits = {};
    exits.success = sinon.spy();
    createInitialUserInfoMock.returns({defaultSite: 'ssh', policiesGroup: {ssh: 'admin'}, phone: '88'});
    getRolesInfoMock.resolves({admin: {defpref: {}}});
    userFindOneMock.resolves(null);
    userCreateMock.resolves();
    userSiteMapCreateMock.resolves();
    sailsMock.returns();
    await createUser.fn(inputs, exits);
    assert.strictEqual(exits.success.callCount, 1);
    let args = exits.success.getCall(0).args;
    assert.hasAllDeepKeys(args[0], ['user', 'token', 'siteId']);
  });
});
