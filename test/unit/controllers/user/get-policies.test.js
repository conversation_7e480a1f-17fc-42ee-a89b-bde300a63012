const assert = require('chai').assert;
let getPolicies = require('../../../../api/controllers/user/get-policies');
var sinon = require('sinon');
sails = require('sails');
sails.config = {IOT_CONFIG:{}, cachePort:''};
sinon = sinon.createSandbox();

describe('Get Policies Action Unit Tests', () => {

  it('Positive suit, should return the policies', async () => {
    let inputs = {};
    let exits = {};
    exits.success = sinon.spy();
    await getPolicies.fn(inputs, exits);
    assert.strictEqual(exits.success.callCount, 1);
  });

  it('Positive suit, validating policies', async () => {
    let inputs = {};
    let exits = {};
    exits.success = sinon.spy();
    await getPolicies.fn(inputs, exits);
    assert.strictEqual(exits.success.callCount, 1);
    let args = exits.success.getCall(0).args;
    assert.isObject(args[0]);
    assert.hasAllDeepKeys(args[0]['ACPlant'], ['displayName', 'pageView', 'subHeadings']);
  });

  it('Throws server error', async () => {
    let inputs = {};
    let exits = {
      success: () => {
        throw Error;
      },
      serverError: sinon.spy()
    };
    await getPolicies.fn(inputs, exits);
    assert.strictEqual(exits.serverError.callCount, 1);
  });
});
