const assert = require('chai').assert;
var sinon = require('sinon');
sails = require('sails');
sails.config = {IOT_CONFIG:{}, cachePort:''};
const self = require('../../../../api/services/user/user.service');
sinon = sinon.createSandbox();

let editPassword = require('../../../../api/controllers/user/edit-password');

describe('Edit password action unit tests', () => {
  let userFindMock, comparePassMock, updateUserMock, sailsMock;
  before(() => {
    userFindMock = sinon.stub(self, 'findOne');
    comparePassMock = sinon.stub(self, 'comparePassword');
    updateUserMock = sinon.stub(self, 'update');
    sailsMock = sinon.stub(sails.log, 'error');
  });
  after(() => {
    sinon.restore();
  });

  it('Invalid userId', async () => {
    let inputs = {};
    let exits = {};
    exits.badRequest = sinon.spy();
    editPassword.req = {
      _userMeta: {

      }
    };
    await editPassword.fn(inputs, exits);
    assert.strictEqual(exits.badRequest.callCount, 1);
    let args = exits.badRequest.getCall(0).args;
    assert.strictEqual(args[0].problems[0], 'User ID is not present');
  });

  it('User Not Found', async () => {
    let inputs = {};
    let exits = {};
    exits.forbidden = sinon.spy();
    editPassword.req = {
      _userMeta: {
        id: '<EMAIL>'
      }
    };
    userFindMock.resolves(undefined);
    await editPassword.fn(inputs, exits);
    assert.strictEqual(exits.forbidden.callCount, 1);
    let args = exits.forbidden.getCall(0).args;
    assert.strictEqual(args[0].problems[0], 'User not found');
  });

  it('Invalid old password', async () => {
    let inputs = {};
    let exits = {};
    exits.forbidden = sinon.spy();
    editPassword.req = {
      _userMeta: {
        id: '<EMAIL>'
      }
    };
    userFindMock.resolves({userId: 'abc'});
    comparePassMock.resolves(false);
    await editPassword.fn(inputs, exits);
    assert.strictEqual(exits.forbidden.callCount, 1);
    let args = exits.forbidden.getCall(0).args;
    assert.strictEqual(args[0].problems[0], 'Old password did not match');
  });

  it('Invalid old password, server error', async () => {
    let inputs = {};
    let exits = {};
    exits.serverError = sinon.spy();
    editPassword.req = {
      _userMeta: {
        id: '<EMAIL>'
      }
    };
    userFindMock.resolves({userId: 'abc'});
    comparePassMock.rejects(false);
    sailsMock.returns();
    await editPassword.fn(inputs, exits);
    assert.strictEqual(exits.serverError.callCount, 1);
  });

  it('Positive suit', async () => {
    let inputs = {};
    let exits = {};
    exits.success = sinon.spy();
    editPassword.req = {
      _userMeta: {
        id: '<EMAIL>'
      }
    };
    userFindMock.resolves({userId: 'abc'});
    comparePassMock.resolves(true);
    updateUserMock.resolves();
    await editPassword.fn(inputs, exits);
    assert.strictEqual(exits.success.callCount, 1);
  });
});
