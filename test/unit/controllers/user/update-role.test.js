const assert = require('chai').assert;
var sinon = require('sinon');
sails = require('sails');
sails.config = {IOT_CONFIG:{}, cachePort:''};
const roleService = require('../../../../api/services/role/role.public');
sinon = sinon.createSandbox();

let updateRole = require('../../../../api/controllers/user/update-role');

describe('Update Role Action Unit Tests', () => {
  let findRoleMock, updateRoleMock, sailsMock;
  before(() => {
    findRoleMock = sinon.stub(roleService, 'findOne');
    updateRoleMock = sinon.stub(roleService, 'update');
    sailsMock = sinon.stub(sails.log, 'error');
  });
  after(() => {
    sinon.restore();
  });

  it('Role does not exists', async () => {
    let inputs = {
      roleName: 'admin',
      pref: {},
      policies: {}
    };
    let exits = {};
    exits.forbidden = sinon.spy();
    findRoleMock.resolves(null);
    await updateRole.fn(inputs, exits);
    assert.strictEqual(exits.forbidden.callCount, 1);
    let args = exits.forbidden.getCall(0).args;
    assert.strictEqual(args[0].problems[0], 'This role does not exists.');
  });

  it('Role exists but it is deleted', async () => {
    let inputs = {
      roleName: 'admin',
      pref: {},
      policies: {}
    };
    let exits = {};
    exits.forbidden = sinon.spy();
    findRoleMock.resolves({roleName: 'admin', isDeleted: '1'});
    await updateRole.fn(inputs, exits);
    assert.strictEqual(exits.forbidden.callCount, 1);
    let args = exits.forbidden.getCall(0).args;
    assert.strictEqual(args[0].problems[0], 'This role is deleted');
  });

  it('Invalid policy from FE', async () => {
    let inputs = {
      roleName: 'admin',
      pref: {},
      policies: {Recipe: {}}
    };
    let exits = {};
    sailsMock.returns();
    exits.serverError = sinon.spy();
    findRoleMock.resolves({roleName: 'admin', isDeleted: '0'});
    await updateRole.fn(inputs, exits);
    assert.strictEqual(exits.serverError.callCount, 1);
  });

  it('Positive suit, all valid input', async () => {
    let inputs = {
      roleName: 'admin',
      pref: {},
      policies: {
        'Recipe': {
          'displayName': 'Recipe',
          'pageView': false,
          'subHeadingsList': [
            {
              'displayName': 'Recipe',
              'policyList': [
                {
                  'displayName': 'Create',
                  'routePolicyMap': {
                    'POST /m2/recipe/v2/recipe': 'recipe/createrecipe'
                  },
                  'hasAccess': false
                },
                {
                  'displayName': 'Read',
                  'routePolicyMap': {
                    'GET /m2/recipe/v2/recipe/siteId/:siteId': 'recipe/find-recipe-by-siteId'
                  },
                  'hasAccess': true
                },
                
              ]
            }
          ]
        }
      }
    };
    let exits = {};
    exits.success = sinon.spy();
    findRoleMock.resolves({roleName: 'admin', isDeleted: '0'});
    updateRoleMock.resolves();
    await updateRole.fn(inputs, exits);
    assert.strictEqual(exits.success.callCount, 1);
    let args = exits.success.getCall(0).args;
    assert.hasAllDeepKeys(args[0], ['policies', 'policiesBE', 'defpref']);
  });
});
