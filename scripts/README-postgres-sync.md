# Simple PostgreSQL Sync Tool

This tool provides a simple way to sync PostgreSQL table data from us-west-2 (master) to any specified region based on any column filter.

## Overview

The sync tool copies data filtered by any column from us-west-2 (master) to any destination region. It supports both the main PostgreSQL database (`postgres` datastore) and the alert system database (`vigilante` datastore).

## Supported Tables

### Main Database Tables (postgres datastore)
- **configurator_graphs** - Graph configurations
- **sub_system_pages** - Subsystem page definitions  
- **configurator_table** - Table configurations
- **systems** - System definitions
- **configurator_page_svg** - SVG page configurations
- **site_system_mapping** - Site to system mappings
- **recipe_schedule** - Recipe scheduling data
- **recipe_info** - Recipe information
- **system_nodes** - System node hierarchy
- **configurator_table_group** - Table group configurations
- **nodes** - Node definitions
- **configurator_table_row** - Table row configurations

### Alert System Tables (vigilante datastore)
- **alert_inventory** - Alert inventory and configurations
- **alert_subscribers** - Alert subscription data
- **alert_message_template_mapping** - Alert message template mappings
- **alert_template** - Alert template definitions
- **notification_message_template** - Notification message templates
- **message_template** - Message template definitions
- **alert_incident_history** - Alert incident history
- **message** - Message data
- **message_placeholder** - Message placeholder definitions
- **subscriber_channel_mapping** - Subscriber channel mappings

## Usage

### Basic Syntax
```bash
node scripts/sync-postgres.js <filterField> <filterValue> <tableName> <destinationRegion>
```

### Parameters

- `filterField` - The column name to filter by (e.g., site_id, id, created_by)
- `filterValue` - The value to filter by
- `tableName` - The PostgreSQL table name to sync
- `destinationRegion` - The AWS region to sync data to

### Examples

#### Sync system configurations by site
```bash
npm run sync-postgres site_id ssh_site systems us-west-1
# or
node scripts/sync-postgres.js site_id ssh_site systems us-west-1
```

#### Sync specific configurator graph
```bash
npm run sync-postgres id 123 configurator_graphs us-west-1
```

#### Sync recipe info by creator
```bash
npm run sync-postgres created_by <EMAIL> recipe_info ap-south-1
```

#### Sync alert templates by site
```bash
npm run sync-postgres site_id ssh_site alert_template us-west-1
```

### Flexible Filtering Examples
```bash
# Sync all systems for a site
npm run sync-postgres site_id ssh_site systems us-west-1

# Sync specific configurator table
npm run sync-postgres id 456 configurator_table us-west-1

# Sync all alerts for a site
npm run sync-postgres site_id ssh_site alert_inventory us-west-1

# Sync message templates by creator
npm run sync-postgres created_by <EMAIL> message_template us-west-1
```

## How It Works

1. **Data Retrieval**: Fetches all data for the specified filter from us-west-2 (master)
2. **Confirmation**: Asks for user confirmation before proceeding with the sync
3. **Data Clearing**: Removes existing filtered data in the destination region
4. **Data Writing**: Writes the source data to the destination region using UPSERT operations

## Safety Features

- **Confirmation Prompt**: Always asks for confirmation before making changes
- **Batch Processing**: Uses batch operations for efficient data transfer
- **UPSERT Operations**: Uses INSERT...ON CONFLICT for safe data updates
- **Connection Management**: Properly manages database connections and cleanup
- **Error Handling**: Comprehensive error handling with detailed messages

## Environment Configuration

The tool requires the following environment variables:

### Required Variables
- `DATABASE_USER` - PostgreSQL username
- `DATABASE_PASSWORD` - PostgreSQL password  
- `DATABASE_HOST` - PostgreSQL host for source (us-west-2)
- `DATABASE_PORT` - PostgreSQL port
- `DATABASE_NAME` - Main database name
- `SMART_ALERT_DB_NAME` - Alert system database name

### Optional Variables (for destination regions)
- `DATABASE_HOST_US_WEST_1` - PostgreSQL host for us-west-1
- `DATABASE_HOST_AP_SOUTH_1` - PostgreSQL host for ap-south-1
- etc.

If region-specific host variables are not set, it will use the default `DATABASE_HOST`.

## Important Notes

⚠️ **Warning**: This tool will **replace** existing data in the destination region for the specified filter. Always backup important data before running sync operations.

### Database Connections
- The script automatically determines which database to connect to based on the table name
- Main tables use the `DATABASE_NAME` database
- Alert tables use the `SMART_ALERT_DB_NAME` database

### Performance
- Uses batch processing for large datasets
- Implements proper connection pooling
- Uses parameterized queries for security

## Troubleshooting

### Common Issues

1. **Connection Errors**: Ensure database credentials and host information are correct
2. **Permission Errors**: Verify the database user has SELECT, INSERT, UPDATE, DELETE permissions
3. **Table Not Found**: Check that the table name is correct and exists in both regions

### Error Messages

- `Unknown table: <tableName>` - The specified table is not in the supported list
- `No data found to sync` - No data exists for the specified filter in the source region
- `Sync cancelled` - User cancelled the operation at the confirmation prompt

## Dependencies

The script requires the `pg` PostgreSQL client library:

```bash
npm install pg
```

This dependency has been added to the project's package.json.

## Support

For issues or questions about the PostgreSQL sync tool:
1. Check this README for common solutions
2. Review error messages for specific guidance
3. Verify database connectivity and permissions
