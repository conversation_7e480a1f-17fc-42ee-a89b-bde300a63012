#!/usr/bin/env node

/**
 * Simple PostgreSQL Sync Script
 *
 * This script syncs a single table from us-west-2 (master) to any specified region
 * Usage: node scripts/sync-postgres.js <filterField> <filterValue> <tableName> <destinationRegion>
 *
 * Parameters:
 * - filterField: The column name to filter by (e.g., siteId, id, created_by)
 * - filterValue: The value to filter by
 * - tableName: The PostgreSQL table name to sync
 * - destinationRegion: The AWS region to sync data to (e.g., us-west-1, ap-south-1)
 *
 * Examples:
 * node scripts/sync-postgres.js site_id ssh_site systems us-west-1
 * node scripts/sync-postgres.js id 123 configurator_graphs us-west-1
 * node scripts/sync-postgres.js created_by <EMAIL> recipe_info ap-south-1
 */

const { Pool } = require('pg');
const readline = require('readline');
require('dotenv').config();

// Source region is always us-west-2 (master)
const SOURCE_REGION = 'us-west-2';

// PostgreSQL table configurations with their primary keys
const TABLE_CONFIGS = {
  // Core configurator tables (postgres datastore)
  'configurator_graphs': { primaryKey: 'id', datastore: 'postgres' },
  'sub_system_pages': { primaryKey: 'id', datastore: 'postgres' },
  'configurator_table': { primaryKey: 'id', datastore: 'postgres' },
  'systems': { primaryKey: 'id', datastore: 'postgres' },
  'configurator_page_svg': { primaryKey: 'id', datastore: 'postgres' },
  'site_system_mapping': { primaryKey: 'id', datastore: 'postgres' },
  'recipe_schedule': { primaryKey: 'id', datastore: 'postgres' },
  'recipe_info': { primaryKey: 'id', datastore: 'postgres' },
  'system_nodes': { primaryKey: 'id', datastore: 'postgres' },
  'configurator_table_group': { primaryKey: 'id', datastore: 'postgres' },
  'nodes': { primaryKey: 'id', datastore: 'postgres' },
  'configurator_table_row': { primaryKey: 'id', datastore: 'postgres' },

  // Alert system tables (vigilante datastore)
  'alert_inventory': { primaryKey: 'id', datastore: 'vigilante' },
  'alert_subscribers': { primaryKey: 'id', datastore: 'vigilante' },
  'alert_message_template_mapping': { primaryKey: 'id', datastore: 'vigilante' },
  'alert_template': { primaryKey: 'id', datastore: 'vigilante' },
  'notification_message_template': { primaryKey: 'id', datastore: 'vigilante' },
  'message_template': { primaryKey: 'id', datastore: 'vigilante' },
  'alert_incident_history': { primaryKey: 'id', datastore: 'vigilante' },
  'message': { primaryKey: 'id', datastore: 'vigilante' },
  'message_placeholder': { primaryKey: 'id', datastore: 'vigilante' },
  'subscriber_channel_mapping': { primaryKey: 'id', datastore: 'vigilante' },
};

class PostgreSQLSyncTool {
  constructor(destinationRegion) {
    this.destinationRegion = destinationRegion;
    this.sourcePool = null;
    this.destPool = null;
  }

  /**
   * Create database connection pools
   */
  createConnections(tableName) {
    const config = TABLE_CONFIGS[tableName];
    if (!config) {
      throw new Error(`Unknown table: ${tableName}`);
    }

    const dbName = config.datastore === 'vigilante' 
      ? process.env.SMART_ALERT_DB_NAME 
      : process.env.DATABASE_NAME;

    // Source connection (us-west-2)
    this.sourcePool = new Pool({
      user: process.env.DATABASE_USER,
      password: process.env.DATABASE_PASSWORD,
      host: process.env.DATABASE_HOST,
      port: process.env.DATABASE_PORT,
      database: dbName,
      ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
    });

    // Destination connection (specified region)
    // Note: You'll need region-specific environment variables for destination
    const destHost = process.env[`DATABASE_HOST_${this.destinationRegion.toUpperCase().replace('-', '_')}`] 
      || process.env.DATABASE_HOST;
    
    this.destPool = new Pool({
      user: process.env.DATABASE_USER,
      password: process.env.DATABASE_PASSWORD,
      host: destHost,
      port: process.env.DATABASE_PORT,
      database: dbName,
      ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
    });
  }

  /**
   * Get all items from a table filtered by the given field and value
   */
  async getTableData(pool, tableName, filterField, filterValue) {
    const query = `
      SELECT * FROM ${tableName} 
      WHERE ${filterField} = $1
      ORDER BY ${TABLE_CONFIGS[tableName].primaryKey}
    `;
    
    try {
      const result = await pool.query(query, [filterValue]);
      return result.rows;
    } catch (error) {
      throw new Error(`Failed to fetch data from ${tableName}: ${error.message}`);
    }
  }

  /**
   * Clear existing data for filter field and value in destination table
   */
  async clearTableData(tableName, filterField, filterValue) {
    console.log(
      `Clearing existing data for ${filterField} = ${filterValue} in ${tableName} (${this.destinationRegion})...`
    );

    const existingItems = await this.getTableData(this.destPool, tableName, filterField, filterValue);
    if (existingItems.length === 0) {
      console.log('No existing data to clear.');
      return;
    }

    const config = TABLE_CONFIGS[tableName];
    const primaryKey = config.primaryKey;
    
    // Delete in batches
    const batchSize = 100;
    for (let i = 0; i < existingItems.length; i += batchSize) {
      const batch = existingItems.slice(i, i + batchSize);
      const ids = batch.map(item => item[primaryKey]);
      
      const placeholders = ids.map((_, index) => `$${index + 1}`).join(',');
      const deleteQuery = `DELETE FROM ${tableName} WHERE ${primaryKey} IN (${placeholders})`;
      
      try {
        await this.destPool.query(deleteQuery, ids);
        console.log(`Deleted batch ${Math.floor(i / batchSize) + 1}`);
      } catch (error) {
        console.error(`Error deleting batch:`, error);
        throw error;
      }
    }
  }

  /**
   * Batch write items to destination table
   */
  async writeTableData(tableName, items) {
    if (items.length === 0) {
      console.log('No items to sync.');
      return;
    }

    console.log(`Writing ${items.length} items to ${tableName} in ${this.destinationRegion}...`);

    if (items.length === 0) return;

    // Get column names from first item
    const columns = Object.keys(items[0]);
    const columnList = columns.join(', ');
    
    // Create placeholders for values
    const batchSize = 50; // Smaller batch size for PostgreSQL
    
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      
      // Create value placeholders
      const valuePlaceholders = batch.map((_, rowIndex) => {
        const rowPlaceholders = columns.map((_, colIndex) => 
          `$${rowIndex * columns.length + colIndex + 1}`
        ).join(', ');
        return `(${rowPlaceholders})`;
      }).join(', ');
      
      // Flatten all values
      const values = batch.flatMap(item => columns.map(col => item[col]));
      
      const insertQuery = `
        INSERT INTO ${tableName} (${columnList}) 
        VALUES ${valuePlaceholders}
        ON CONFLICT (${TABLE_CONFIGS[tableName].primaryKey}) 
        DO UPDATE SET ${columns.map(col => `${col} = EXCLUDED.${col}`).join(', ')}
      `;
      
      try {
        await this.destPool.query(insertQuery, values);
        console.log(`Batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(items.length / batchSize)} completed`);
      } catch (error) {
        console.error(`Error writing batch ${Math.floor(i / batchSize) + 1}:`, error);
        throw error;
      }
    }
  }

  /**
   * Ask for user confirmation
   */
  async confirmSync(filterField, filterValue, tableName, itemCount) {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });

    return new Promise((resolve) => {
      rl.question(
        `\n⚠️  WARNING: This will replace ${itemCount} items in ${this.destinationRegion} ${tableName} table for ${filterField} = "${filterValue}".\n` +
          `Source: ${SOURCE_REGION} (master)\n` +
          `Destination: ${this.destinationRegion}\n` +
          `Are you sure you want to continue? (yes/no): `,
        (answer) => {
          rl.close();
          resolve(answer.toLowerCase() === 'yes' || answer.toLowerCase() === 'y');
        }
      );
    });
  }

  /**
   * Main sync function
   */
  async sync(filterField, filterValue, tableName) {
    console.log(`\n=== Simple PostgreSQL Sync Tool ===`);
    console.log(`${filterField}: ${filterValue}`);
    console.log(`Table: ${tableName}`);
    console.log(`Source: ${SOURCE_REGION} (master)`);
    console.log(`Destination: ${this.destinationRegion}\n`);

    if (!TABLE_CONFIGS[tableName]) {
      throw new Error(
        `Unknown table: ${tableName}. Available tables: ${Object.keys(TABLE_CONFIGS).join(', ')}`
      );
    }

    try {
      // Create database connections
      this.createConnections(tableName);

      // Get data from source (master)
      console.log(`Fetching data from ${SOURCE_REGION} (master)...`);
      const sourceData = await this.getTableData(this.sourcePool, tableName, filterField, filterValue);
      console.log(`Found ${sourceData.length} items in source.`);

      if (sourceData.length === 0) {
        console.log('No data found to sync.');
        return;
      }

      // Confirm before proceeding
      const confirmed = await this.confirmSync(filterField, filterValue, tableName, sourceData.length);
      if (!confirmed) {
        console.log('Sync cancelled.');
        return;
      }

      // Clear existing data in destination
      await this.clearTableData(tableName, filterField, filterValue);

      // Write data to destination
      await this.writeTableData(tableName, sourceData);

      console.log(`\n✅ Sync completed successfully!`);
      console.log(
        `Synced ${sourceData.length} items from ${SOURCE_REGION} to ${this.destinationRegion}`
      );
    } catch (error) {
      console.error('\n❌ Sync failed:', error.message);
      throw error;
    } finally {
      // Close connections
      if (this.sourcePool) await this.sourcePool.end();
      if (this.destPool) await this.destPool.end();
    }
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  if (args.length !== 4) {
    console.log(`
Usage: node scripts/sync-postgres.js <filterField> <filterValue> <tableName> <destinationRegion>

Examples:
  node scripts/sync-postgres.js site_id ssh_site systems us-west-1
  node scripts/sync-postgres.js id 123 configurator_graphs us-west-1
  node scripts/sync-postgres.js created_by <EMAIL> recipe_info ap-south-1
    `);
    process.exit(1);
  }

  const [filterField, filterValue, tableName, destinationRegion] = args;

  const syncTool = new PostgreSQLSyncTool(destinationRegion);

  try {
    await syncTool.sync(filterField, filterValue, tableName);
  } catch (error) {
    console.error('Sync failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = PostgreSQLSyncTool;
