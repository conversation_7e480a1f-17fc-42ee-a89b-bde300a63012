<!DOCTYPE html>
<html>
<head>
    <title></title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <style type="text/css">
        body {
            height: 100% !important;
            margin: 0 !important;
            padding: 0 !important;
            width: 100% !important;
        }
        .alert-content {
            margin: 25px 20% 0 20%;
        }
        .content-heading {
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .content-heading .text {
            font-size: 2vw;
            color: #F4A627;
            font-family: sans-serif;
        }
        .content-heading .logo img {
            width: 12vw;
            height: 8vw;
        }
        .alert-header {
            margin: 10px 2%;
            display: flex;
            align-items: center;
            background: #F7F7F7;
            font-family: sans-serif;
        }
        .smart-joules-logo {
            margin: 6px 2%;
        }
        .smart-joules-logo img {
            height: 5vw;
            width: 5vw;
        }
        .site-Name {
            color: #868686;
            font-weight: 600;
            font-size: 2.1vw;
            font-family: "Gill Sans", sans-serif;
        }
        .alert-title {
            text-align: center;
            font-size: 2vw;
            color: gray;
            font-family: "Gill Sans", sans-serif;
            margin-bottom: 10px;
        }
        .footer-buttons {
            margin: 30px 2% 30px 2%;
            display: flex;
            align-items: flex-end;
            justify-content: space-between;
        }
        .footer-buttons a {
            color: orange;
            text-decoration: none;
        }
        .alert-normal-font {
            color: gray;
            font-family: sans-serif;
        }
        .footer-buttons button {
            color: orange;
            border: 1px solid orange;
            background: white;
            height: 40px;
            padding: 11px;
            border-radius: 2px;
            margin-left: 0.5em;
        }
        .footer-buttons button:hover {
            color: darkorange;
            border-color: darkorange;
            cursor: pointer;
            background: whitesmoke;
        }
        .footer-buttons a:hover {
            color: darkorange;
        }
        .suggetion {
            width: 100%;
            display: flex;
            justify-content: center;
            flex-direction: column;
            align-items: center;
            margin-top: 1.3em;
        }
        /* MOBILE STYLES */
        @media screen and (max-width:600px) {
            h1 {
                font-size: 32px !important;
                line-height: 32px !important;
            }
            .alert-mid-content table {
                font-size: 8px;
            }
            .alert-mid-content tr {
                height: 20px;
            }
            .alert-normal-font {
                font-size: 8px;
            }
            .table-heading {
                font-size: 8px;
                height: 12px;
            }
            .real-maintenance-table th {
                font-size: 8px;
            }
            .real-maintenance-table td {
                font-size: 8px;
            }
            .real-maintenance-table tr {
                height: 15px;
            }
            .footer-buttons button {
                height: 21px;
                font-size: 4px;
                padding: 7px;
                margin-top: 5px;
            }
            .footer-buttons {
                align-items: baseline;
            }
        }
        /* ANDROID CENTER FIX */
        div[style*="margin: 16px 0;"] {
            margin: 0 !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="alert-header">
            <div class="smart-joules-logo">
                <img src="cid:logo_sm" />
            </div>
            <div class="site-Name">
                {{siteId}}
            </div>
        </div>
        <div class="alert-content">
            <div class="content-heading">
                <div class="logo">
                    <img src="cid:jr" />
                </div>
                <div class="text">{{siteId}}: Nudge Alert!</div>
            </div>

            <div class="alert-normal-font" style="text-align: center;font-size: 1.5vw;">Title: {{title}}</div>
            <div class="alert-normal-font" style="text-align: center;font-size: 1.5vw;">Suggestion: {{description}}</div>
            <!-- <div class="suggetion">
                <div style="font-family: sans-serif;color: orange;font-weight: 600;font-size: 1.2vw;">		Observation
               	</div>
                <div
                    style="padding: 1em;background: #F7F7F7;margin: 6px;color: dimgray;font-size: 1vw;font-family: sans-serif;">
                    {{suggetion}}
                </div>
            </div> -->
        </div>
        <div class="footer-buttons">
            <div class="alert-normal-font" style="width: 30%;">
                Created At: {{timestamp}}
            </div>
            <div>
                <button>AC Plant</button>
                <button>Joule Recipe</button>
                <button><a href="http://smartjoules.org/devices">Device Data</a></button>
                <button>Detail Analytics</button>
            </div>
        </div>
    </div>
</body>
</html>