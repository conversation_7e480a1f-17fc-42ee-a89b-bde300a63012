################################################
#   ╔═╗╔╦╗╦╔╦╗╔═╗╦═╗┌─┐┌─┐┌┐┌┌─┐┬┌─┐
#   ║╣  ║║║ ║ ║ ║╠╦╝│  │ ││││├┤ ││ ┬
#  o╚═╝═╩╝╩ ╩ ╚═╝╩╚═└─┘└─┘┘└┘└  ┴└─┘
#
# > Formatting conventions for your Sails app.
#
# This file (`.editorconfig`) exists to help
# maintain consistent formatting throughout the
# files in your Sails app.
#
# For the sake of convention, the Sails team's
# preferred settings are included here out of the
# box.  You can also change this file to fit your
# team's preferences (for example, if all of the
# developers on your team have a strong preference
# for tabs over spaces),
#
# To review what each of these options mean, see:
# http://editorconfig.org/
#
################################################
root = true

[*]
indent_style = space
indent_size = 2
end_of_line = lf
charset = utf-8
trim_trailing_whitespace = true
insert_final_newline = true
# editorconfig-tools is unable to eignore longs strings or urls
max_line_length = 100
