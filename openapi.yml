openapi: 3.0.3
info:
  title: jt-api-v2
  version: '1.0'
  description: Refactored backend codebase for Jouletrack APIs
servers:
  - url: 'https://services.smartjoules.org/'
paths:
  /m2/data/v2/recentdata:
    post:
      tags:
        - DataDevice
      summary: Query last minute's data for a list of deviceIds
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - deviceList
              properties:
                deviceList:
                  type: array
                  description: Array of deviceIds
                  items:
                    type: string
            example:
              deviceList:
                - '2579'
                - '2127'
                - '290'
      responses:
        '200':
          description: >-
            Array of objects containing data parameter values of the last
            minute. If data is not present, only deviceId will be present in the
            object.
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    deviceId:
                      type: string
                    parameterN:
                      type: number
              examples:
                Example 1:
                  value:
                    - deviceId: '290'
                      kwh: 170328.14
                      kva: 1.71
                      kw: 1.62
                      pf: 1.94
                      ib: 2.63
                      a_vln: 232.58
                      iy: 2.57
                      a_vll: 402.82
                      kvar: 0.57
                      kvah: 220528.96
                      ir: 2.89
                      a_amp: 2.7
                    - deviceId: '2127'
                    - deviceId: '2579'
                      tmp: '23.64'
  '/m2/user/v2/componentcard/{id}':
    get:
      tags:
        - DataDevice
      summary: Fetches Component Card configuration of order of parameters.
      parameters:
        - name: id
          in: path
          description: Component OR Process ID.
          required: true
          schema:
            type: string
          example: mgch_23
      responses:
        '200':
          description: Array of parameter keys in the order to be displayed.
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
                example:
                  
              examples:
                Example 1:
                  description: If no configuration present
                  value:
                    -
                Example 2:
                  description: If configuration present
                  value:
                    - kva
                    - approach
                    - outwatertemp
                    - inwatertemp
                    - range
                    - efficiency
                    - outputfrequency
                    - status
                    - ikwtr
  /m2/user/v2/componentcard:
    post:
      tags:
        - DataDevice
      summary: Saves Component Card Configuration of order of parameters.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - id
                - config
              properties:
                id:
                  type: string
                  description: componentId OR processID
                  example: mgch_23
                config:
                  type: array
                  description: Array of Parameter keys in order needed to be displayed.
                  items:
                    type: string
            example:
              id: mgch_23
              config:
                - kva
                - approach
                - outwatertemp
                - inwatertemp
                - range
                - efficiency
                - outputfrequency
                - status
                - ikwtr
      responses:
        '200':
          description: On successful saving of configuration
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                  message:
                    type: string
              example:
                status: true
                message: Configuration successfuly stored.
        '500':
          description: If saving to Database fails
          content:
            application/json:
              schema:
                type: object
                properties:
                  problems:
                    type: array
                    items:
                      type: string
                example:
                  problems:
                    - Error creating entry in userComponentCards table while saving configuration