{"info": {"_postman_id": "7f4fa77d-1719-4d67-a5ce-5a0f14426a69", "name": "SmartJoules JouleTrack Web apis", "description": "Joule track web clients will be the major consumers of these apis", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "auth", "item": [{"name": "login", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"userId\": \"<string>\",\n    \"password\": \"<string>\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/v2/login", "host": ["{{baseUrl}}"], "path": ["auth", "v2", "login"]}, "description": "<PERSON><PERSON>"}, "response": [{"name": "Untitled Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"userId\": \"<string>\",\n    \"password\": \"<string>\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/v2/login", "host": ["{{baseUrl}}"], "path": ["auth", "v2", "login"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"token\": \"veli\"\n}"}, {"name": "Internal server error", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"userId\": \"<string>\",\n    \"password\": \"<string>\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/v2/login", "host": ["{{baseUrl}}"], "path": ["auth", "v2", "login"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "issue token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"siteId\": \"<string>\",\n    \"secret\": \"<string>\",\n    \"prevSiteId\": \"<string>\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/v2/issueToken", "host": ["{{baseUrl}}"], "path": ["auth", "v2", "issueToken"]}, "description": "issueToken"}, "response": [{"name": "Internal server error", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"siteId\": \"<string>\",\n    \"secret\": \"<string>\",\n    \"prevSiteId\": \"<string>\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/v2/issueToken", "host": ["{{baseUrl}}"], "path": ["auth", "v2", "issueToken"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}, {"name": "Untitled Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"siteId\": \"<string>\",\n    \"secret\": \"<string>\",\n    \"prevSiteId\": \"<string>\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/v2/issueToken", "host": ["{{baseUrl}}"], "path": ["auth", "v2", "issueToken"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"token\": \"veli\"\n}"}]}], "protocolProfileBehavior": {}}, {"name": "analytics", "item": [{"name": "To get building load", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"weeks\": [\n        \"<number>\",\n        \"<number>\"\n    ],\n    \"days\": [\n        \"<number>\",\n        \"<number>\"\n    ],\n    \"prevSiteId\": \"<string>\"\n}"}, "url": {"raw": "{{baseUrl}}/m2/analytic/v2/presets/buildingload", "host": ["{{baseUrl}}"], "path": ["m2", "analytic", "v2", "presets", "buildingload"]}, "description": "buildingLoad"}, "response": [{"name": "Untitled Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"weeks\": [\n        \"<number>\",\n        \"<number>\"\n    ],\n    \"days\": [\n        \"<number>\",\n        \"<number>\"\n    ],\n    \"prevSiteId\": \"<string>\"\n}"}, "url": {"raw": "{{baseUrl}}/m2/analytic/v2/presets/buildingload", "host": ["{{baseUrl}}"], "path": ["m2", "analytic", "v2", "presets", "buildingload"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}, {"name": "Internal server error", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"weeks\": [\n        \"<number>\",\n        \"<number>\"\n    ],\n    \"days\": [\n        \"<number>\",\n        \"<number>\"\n    ],\n    \"prevSiteId\": \"<string>\"\n}"}, "url": {"raw": "{{baseUrl}}/m2/analytic/v2/presets/buildingload", "host": ["{{baseUrl}}"], "path": ["m2", "analytic", "v2", "presets", "buildingload"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "to find a graph", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"deviceId\": \"<string>\",\n    \"startTime\": \"<string>\",\n    \"endTime\": \"<string>\",\n    \"group\": \"<string>\",\n    \"params\": [\n        \"<string>\",\n        \"<string>\"\n    ],\n    \"type\": \"<string>\"\n}"}, "url": {"raw": "{{baseUrl}}/m2/analytic/v2/plot", "host": ["{{baseUrl}}"], "path": ["m2", "analytic", "v2", "plot"]}, "description": "plotGraph"}, "response": [{"name": "Internal server error", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"deviceId\": \"<string>\",\n    \"startTime\": \"<string>\",\n    \"endTime\": \"<string>\",\n    \"group\": \"<string>\",\n    \"params\": [\n        \"<string>\",\n        \"<string>\"\n    ],\n    \"type\": \"<string>\"\n}"}, "url": {"raw": "{{baseUrl}}/m2/analytic/v2/plot", "host": ["{{baseUrl}}"], "path": ["m2", "analytic", "v2", "plot"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}, {"name": "Untitled Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"deviceId\": \"<string>\",\n    \"startTime\": \"<string>\",\n    \"endTime\": \"<string>\",\n    \"group\": \"<string>\",\n    \"params\": [\n        \"<string>\",\n        \"<string>\"\n    ],\n    \"type\": \"<string>\"\n}"}, "url": {"raw": "{{baseUrl}}/m2/analytic/v2/plot", "host": ["{{baseUrl}}"], "path": ["m2", "analytic", "v2", "plot"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "to find a data device", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/m2/analytics/v2?deviceId=<string>&startTime=<string>&endTime=<string>", "host": ["{{baseUrl}}"], "path": ["m2", "analytics", "v2"], "query": [{"key": "deviceId", "value": "<string>", "description": "(Required) "}, {"key": "startTime", "value": "<string>", "description": "(Required) "}, {"key": "endTime", "value": "<string>"}]}, "description": "findDatadevice"}, "response": [{"name": "Internal server error", "originalRequest": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/m2/analytics/v2?deviceId=<string>&startTime=<string>&endTime=<string>", "host": ["{{baseUrl}}"], "path": ["m2", "analytics", "v2"], "query": [{"key": "deviceId", "value": "<string>"}, {"key": "startTime", "value": "<string>"}, {"key": "endTime", "value": "<string>"}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}, {"name": "Untitled Response", "originalRequest": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/m2/analytics/v2?deviceId=<string>&startTime=<string>&endTime=<string>", "host": ["{{baseUrl}}"], "path": ["m2", "analytics", "v2"], "query": [{"key": "deviceId", "value": "<string>"}, {"key": "startTime", "value": "<string>"}, {"key": "endTime", "value": "<string>"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"deviceId\": \"elit nulla culpa fugiat\",\n \"startTime\": \"ut magna incididunt proident dolore\",\n \"endTime\": \"culpa exercitation\"\n}"}]}, {"name": "to get list of ahus based on performance", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"siteId\": \"<string>\",\n    \"component\": \"<string>\",\n    \"groupBy\": \"<string>\",\n    \"use\": \"<string>\",\n    \"params\": [\n        \"<string>\",\n        \"<string>\"\n    ],\n    \"type\": \"<string>\"\n}"}, "url": {"raw": "{{baseUrl}}/m2/analytics/v2/ahuPerformace", "host": ["{{baseUrl}}"], "path": ["m2", "analytics", "v2", "ahuPerformace"]}, "description": "topWorstAhus"}, "response": [{"name": "Untitled Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"siteId\": \"<string>\",\n    \"component\": \"<string>\",\n    \"groupBy\": \"<string>\",\n    \"use\": \"<string>\",\n    \"params\": [\n        \"<string>\",\n        \"<string>\"\n    ],\n    \"type\": \"<string>\"\n}"}, "url": {"raw": "{{baseUrl}}/m2/analytics/v2/ahuPerformace", "host": ["{{baseUrl}}"], "path": ["m2", "analytics", "v2", "ahuPerformace"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"best\": [\n  \"schema type not provided\",\n  \"schema type not provided\"\n ],\n \"worst\": [\n  \"schema type not provided\",\n  \"schema type not provided\"\n ]\n}"}, {"name": "Internal server error", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"siteId\": \"<string>\",\n    \"component\": \"<string>\",\n    \"groupBy\": \"<string>\",\n    \"use\": \"<string>\",\n    \"params\": [\n        \"<string>\",\n        \"<string>\"\n    ],\n    \"type\": \"<string>\"\n}"}, "url": {"raw": "{{baseUrl}}/m2/analytics/v2/ahuPerformace", "host": ["{{baseUrl}}"], "path": ["m2", "analytics", "v2", "ahuPerformace"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}], "protocolProfileBehavior": {}}, {"name": "device", "item": [{"name": "create a controller", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"deviceInfo\": {\n    \"siteId\": \"string\",\n    \"networkId\": \"string\",\n    \"regionId\": \"string\",\n    \"softwareVer\": \"string\",\n    \"hardwareVer\": \"string\",\n    \"vendorId\": \"string\",\n    \"operationMode\": \"string\",\n    \"deviceType\": \"string\",\n    \"areaId\": \"string\",\n    \"name\": \"string\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/config/v2/controllers", "host": ["{{baseUrl}}"], "path": ["config", "v2", "controllers"]}}, "response": []}, {"name": "create a device", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"deviceInfo\": {\n    \"siteId\": \"string\",\n    \"networkId\": \"string\",\n    \"regionId\": \"string\",\n    \"softwareVer\": \"string\",\n    \"hardwareVer\": \"string\",\n    \"vendorId\": \"string\",\n    \"operationMode\": \"string\",\n    \"deviceType\": \"string\",\n    \"areaId\": \"string\",\n    \"name\": \"string\",\n    \"portNumber\": \"string\",\n    \"controllerId\": \"string\",\n    \"communicationType\": \"string\",\n    \"communicationCategory\": \"string\",\n    \"driverType\": \"string\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/config/v2/devices", "host": ["{{baseUrl}}"], "path": ["config", "v2", "devices"]}}, "response": []}, {"name": "get device info", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"deviceInfo\": {\n    \"siteId\": \"string\",\n    \"networkId\": \"string\",\n    \"regionId\": \"string\",\n    \"softwareVer\": \"string\",\n    \"hardwareVer\": \"string\",\n    \"vendorId\": \"string\",\n    \"operationMode\": \"string\",\n    \"deviceType\": \"string\",\n    \"areaId\": \"string\",\n    \"name\": \"string\",\n    \"portNumber\": \"string\",\n    \"controllerId\": \"string\",\n    \"communicationType\": \"string\",\n    \"communicationCategory\": \"string\",\n    \"driverType\": \"string\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/config/v2/devices", "host": ["{{baseUrl}}"], "path": ["config", "v2", "devices"]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "site", "item": [{"name": "create a site", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"string\",\n  \"location\": \"string\"\n}"}, "url": {"raw": "{{baseUrl}}/site/v2", "host": ["{{baseUrl}}"], "path": ["site", "v2"]}}, "response": []}, {"name": "create an area", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"string\",\n  \"location\": \"string\"\n}"}, "url": {"raw": "{{baseUrl}}/site/v2/{siteId}/area", "host": ["{{baseUrl}}"], "path": ["site", "v2", "{siteId}", "area"]}}, "response": []}, {"name": "create a network", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"string\",\n  \"location\": \"string\"\n}"}, "url": {"raw": "{{baseUrl}}/site/v2/{siteId}/network", "host": ["{{baseUrl}}"], "path": ["site", "v2", "{siteId}", "network"]}}, "response": []}, {"name": "create a region", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"string\",\n  \"location\": \"string\"\n}"}, "url": {"raw": "{{baseUrl}}/site/v2/{siteId}/region", "host": ["{{baseUrl}}"], "path": ["site", "v2", "{siteId}", "region"]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "recipe", "item": [{"name": "create a recipe", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"siteId\": \"string\",\n  \"recipelabel\": [\n    \"string\"\n  ],\n  \"label\": \"string\",\n  \"maxDataNeeded\": \"string\",\n  \"actionable\": [\n    {\n      \"title\": \"string\",\n      \"description\": \"string\",\n      \"notify\": [\n        \"string\"\n      ],\n      \"accountable\": [\n        \"string\"\n      ],\n      \"type\": \"string\",\n      \"priority\": 0\n    }\n  ],\n  \"neo\": \"string\",\n  \"isShedule\": \"string\",\n  \"type\": \"string\"\n}"}, "url": {"raw": "{{baseUrl}}/site/v2", "host": ["{{baseUrl}}"], "path": ["site", "v2"]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "users", "item": [{"name": "get users of a site", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"siteId\": \"string\",\n  \"recipelabel\": [\n    \"string\"\n  ],\n  \"label\": \"string\",\n  \"maxDataNeeded\": \"string\",\n  \"actionable\": [\n    {\n      \"title\": \"string\",\n      \"description\": \"string\",\n      \"notify\": [\n        \"string\"\n      ],\n      \"accountable\": [\n        \"string\"\n      ],\n      \"type\": \"string\",\n      \"priority\": 0\n    }\n  ],\n  \"neo\": \"string\",\n  \"isShedule\": \"string\",\n  \"type\": \"string\"\n}"}, "url": {"raw": "{{baseUrl}}/{siteId}/users", "host": ["{{baseUrl}}"], "path": ["{siteId}", "users"]}}, "response": []}], "protocolProfileBehavior": {}}], "variable": [{"id": "baseUrl", "key": "baseUrl", "value": "https://www.smartjoules.net/v2", "type": "string"}], "protocolProfileBehavior": {}}