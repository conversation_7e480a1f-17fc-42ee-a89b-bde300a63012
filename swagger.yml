swagger: '2.0'
info:
  version: '1.0'
  title: SmartJoules JouleTrack Web apis
  description: 'Joule track web clients will be the major consumers of these apis'
  contact: {}
host: 'api.smartjoules.org'
basePath: /v2
schemes:
  - https
consumes:
  - application/json
produces:
  - application/json
paths:
  /auth/v2/login:
    post:
      tags:
      - "Authentication"
      description: 'Login'
      summary: 'To get token'
      operationId: 'login'
      deprecated: false
      security: []
      produces:
        - application/json
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: "#/definitions/login"
      responses:
        '200':
          description: ''
          schema:
            $ref: "#/definitions/loginResponse"
        '500':
          description: 'Internal server error'
  /auth/v2/issueToken:
    post:
      tags:
      - "Authentication"
      description: 'issueToken'
      summary: 'To get token'
      operationId: 'issueToken'
      deprecated: false
      security: []
      produces:
        - application/json
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: "#/definitions/issueToken"
      responses:
        '200':
          description: ''
          schema:
            $ref: "#/definitions/loginResponse"
        '500':
          description: 'Internal server error'
  /m2/analytic/v2/presets/buildingload:
    post:
      tags:
      - "Analytics"
      description: 'buildingLoad'
      summary: 'To get building load'
      operationId: 'buildingload'
      deprecated: false
      security: []
      produces:
        - application/json
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: "#/definitions/buildingload"
      responses:
        '200':
          description: ''
        '500':
          description: 'Internal server error'
  /m2/analytics/v2:
    post:
      tags:
      - "Analytics"
      description: 'findDatadevice'
      summary: 'to find a data device'
      operationId: 'findDatadevice'
      deprecated: false
      security: []
      produces:
        - application/json
      parameters:
        - name: deviceId
          type: string
          in: query
          required: true
        - name: startTime
          type: string
          in: query
          required: true
        - name: endTime
          type: string
          in: query
      responses:
        '200':
          description: ''
          schema:
            $ref: "#/definitions/findDataDeviceResponse"
        '500':
          description: 'Internal server error'
  /m2/analytic/v2/plot:
    post:
      tags:
      - "Analytics"
      description: 'plotGraph'
      summary: 'to find a graph'
      operationId: 'plotGraph'
      deprecated: false
      security: []
      produces:
        - application/json
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: "#/definitions/plotGraph"
      responses:
        '200':
          description: ''
        '500':
          description: 'Internal server error'
  /m2/analytics/v2/ahuPerformace:
    post:
      tags:
      - "Analytics"
      description: 'topWorstAhus'
      summary: 'to get list of ahus based on performance'
      operationId: 'topWorstAhus'
      deprecated: false
      security: []
      produces:
        - application/json
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: "#/definitions/ahuPerformance"
      responses:
        '200':
          description: ''
          schema:
            $ref: "#/definitions/ahuPerformanceResponse"
        '500':
          description: 'Internal server error'
  /config/v2/controllers:
    get:
      tags:
      - "Device"
      description: 'getController'
      summary: 'Get a Controller'
      operationId: 'getController'
      deprecated: false
      security: []
      produces:
        - application/json
      parameters:
        - name: cntrlId
          in: query
          required: true
          type: string
      responses:
        '200':
          description: ''
        '500':
          description: 'Internal server error'
    post:
      tags:
      - "Device"
      description: 'AddNewController'
      summary: 'Add New Controller'
      operationId: 'addNewController'
      deprecated: false
      security: []
      produces:
        - application/json
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: "#/definitions/newController"
      responses:
        '200':
          description: ''
        '500':
          description: 'Internal server error'
    put:
      tags:
      - "Device"
      description: 'updateController'
      summary: 'update a Controller'
      operationId: 'updateController'
      deprecated: false
      security: []
      produces:
        - application/json
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: "#/definitions/newController"
      responses:
        '200':
          description: ''
        '500':
          description: 'Internal server error'
  /config/v2/device:
    post:
      tags:
      - "Device"
      description: 'AddNewDevice'
      summary: 'Add New Device'
      operationId: 'AddNewDevice'
      deprecated: false
      security: []
      produces:
        - application/json
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: "#/definitions/newDevice"
      responses:
        '200':
          description: ''
        '500':
          description: 'Internal server error'
    put:
      tags:
      - "Device"
      description: 'UpdateNewDevice'
      summary: 'Update Device'
      operationId: 'updateDevice'
      deprecated: false
      security: []
      produces:
        - application/json
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: "#/definitions/newDevice"
      responses:
        '200':
          description: ''
        '500':
          description: 'Internal server error'
  /config/v2/devices:
    get:
      tags:
      - "Device"
      description: 'getDevice'
      summary: 'get Device'
      operationId: 'getDevice'
      deprecated: false
      security: []
      produces:
        - application/json
      parameters:
        - name: deviceId
          type: string
          in: query
          required: true
        - name: siteId
          type: string
          in: query
          required: true
      responses:
        '200':
          description: ''
        '500':
          description: 'Internal server error'
  /recipe:
    post:
      tags:
      - "Recipe"
      description: 'createRecipe'
      summary: 'Creates a Joule Recipe'
      operationId: 'createRecipe'
      deprecated: false
      security: []
      produces:
        - application/json
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: "#/definitions/createRecipe"
      responses:
        '200':
          description: ''
        '500':
          description: 'Internal server error'
  /m2/recipe/v2/schedule:
    post:
      tags:
      - "Recipe"
      description: 'createSchedule'
      summary: 'Creates a Joule Recipe Schedule'
      operationId: 'createSchedule'
      deprecated: false
      security: []
      produces:
        - application/json
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: "#/definitions/createSchedule"
      responses:
        '200':
          description: ''
        '500':
          description: 'Internal server error'
  /m2/baseline/v2/adjustment:
    get:
      tags:
      - "Baseline"
      description: 'findAdjustment'
      summary: 'Get the last done adjustment in baseline'
      operationId: 'findAdjustment'
      deprecated: false
      security: []
      produces:
        - application/json
      responses:
        '200':
          description: ''
        '500':
          description: 'Internal server error'
    post:
      tags:
      - "Baseline"
      description: 'createAdjustment'
      summary: 'Add new adjustment in site consumption unit'
      operationId: 'createAdjustment'
      deprecated: false
      security: []
      produces:
        - application/json
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: "#/definitions/createAdjustment"
      responses:
        '200':
          description: ''
        '500':
          description: 'Internal server error'
  /m2/baseline/v2/baseline:
    post:
      tags:
      - "Baseline"
      description: 'createBaseline'
      summary: 'Create Basline for 0th year of project.Users Manually enter baselines for 0th year and dejoule uses this to show baseline for current year'
      operationId: 'createBaseline'
      deprecated: false
      security: []
      produces:
        - application/json
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: "#/definitions/createBaseline"
      responses:
        '200':
          description: ''
        '500':
          description: 'Internal server error'
  /m2/baseline/v2/dynamicTarget:
    post:
      tags:
      - "Baseline"
      description: 'createDynamictarget'
      summary: 'This function is to create dynamic target in a baseline. Targets are entered in site consumption unit'
      operationId: 'createDynamictarget'
      deprecated: false
      security: []
      produces:
        - application/json
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: "#/definitions/createDynamictarget"
      responses:
        '200':
          description: ''
        '500':
          description: 'Internal server error'
  /m2/baseline/v2/currentPrevNext:
    get:
      tags:
      - "Baseline"
      description: 'currentPreviousTomoBaselne'
      summary: 'Get current baseline, next and previous baseline for user input timestamp'
      operationId: 'currentPreviousTomoBaselne'
      deprecated: false
      security: []
      produces:
        - application/json
      parameters:
        - name: timestamp
          in: query
          required: true
          type: string
      responses:
        '200':
          description: ''
        '500':
          description: 'Internal server error'
  /m2/baseline/v2/consumptionAndBaseline:
    get:
      tags:
      - "Baseline"
      description: 'findbaseline'
      summary: 'This API is used to show baseline, target and actual cons. on dashboard graph'
      operationId: 'findbaseline'
      deprecated: false
      security: []
      produces:
        - application/json
      parameters:
        - name: currentDate
          in: query
          required: true
          type: string
      responses:
        '200':
          description: ''
        '500':
          description: 'Internal server error'
  /m2/baseline/v2/baseline':
    get:
      tags:
      - "Baseline"
      description: 'findSitesBaselnie'
      summary: 'Find API to query baseline table'
      operationId: 'findSitesBaselnie'
      deprecated: false
      security: []
      produces:
        - application/json
      responses:
        '200':
          description: ''
        '500':
          description: 'Internal server error'
  /m2/baseline/v2/dynamicTargets:
    get:
      tags:
      - "Baseline"
      description: 'findTargets'
      summary: 'This API is to populate dynamic targets in calendar when user visit dynamic target page'
      operationId: 'findTargets'
      deprecated: false
      security: []
      produces:
        - application/json
      parameters:
        - name: date
          in: query
          required: true
          type: string
      responses:
        '200':
          description: ''
        '500':
          description: 'Internal server error'
  /m2/baseline/v2/prevYearConsumtion:
    get:
      tags:
      - "Baseline"
      description: 'mapPrevyearConsumptionToThisYear'
      summary: 'Return consumption info mapped to prev year in site-consumption unit. Used when FE queries last year consumption'
      operationId: 'mapPrevyearConsumptionToThisYear'
      deprecated: false
      security: []
      produces:
        - application/json
      parameters:
        - name: startDate
          in: query
          required: true
          type: string
        - name: endDate
          in: query
          required: true
          type: string
      responses:
        '200':
          description: ''
        '500':
          description: 'Internal server error'
  /site/v2:
    post:
      tags:
      - "Site"
      description: 'createSite'
      summary: 'Creates a Site'
      operationId: 'createSite'
      deprecated: false
      security: []
      produces:
        - application/json
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: "#/definitions/createSite"
      responses:
        '200':
          description: ''
        '500':
          description: 'Internal server error'
  /site/v2/{siteId}/area:
    post:
      tags:
      - "Site"
      description: 'createArea'
      summary: 'Creates a Area'
      operationId: 'createArea'
      deprecated: false
      security: []
      produces:
        - application/json
      parameters:
        - name: siteId
          type: string
          in: path
          required: true
          
        - name: body
          in: body
          required: true
          schema:
            $ref: "#/definitions/createArea"
      responses:
        '200':
          description: ''
        '500':
          description: 'Internal server error'
  /site/v2/{siteId}/network:
    post:
      tags:
      - "Site"
      description: 'addNetwork'
      summary: 'add a Network'
      operationId: 'addNetwork'
      deprecated: false
      security: []
      produces:
        - application/json
      parameters:
        - name: siteId
          type: string
          in: path
          required: true
          
        - name: body
          in: body
          required: true
          schema:
            $ref: "#/definitions/createNetwork"
      responses:
        '200':
          description: ''
        '500':
          description: 'Internal server error'
  /site/v2/{siteId}/region:
    post:
      tags:
      - "Site"
      description: 'addRegion'
      summary: 'add a Region'
      operationId: 'addRegion'
      deprecated: false
      security: []
      produces:
        - application/json
      parameters:
        - name: siteId
          type: string
          in: path
          required: true
          
        - name: body
          in: body
          required: true
          schema:
            $ref: "#/definitions/createRegion"
      responses:
        '200':
          description: ''
        '500':
          description: 'Internal server error'
  /{siteId}/users:
    post:
      tags:
      - "Users"
      description: 'getSiteUsers'
      summary: 'get Site Users'
      operationId: 'getSiteUsers'
      deprecated: false
      security: []
      produces:
        - application/json
      parameters:
        - name: siteId
          type: string
          in: path
          required: true
      responses:
        '200':
          description: ''
          schema:
            $ref: "#/definitions/siteUsers"
        '500':
          description: 'Internal server error'
definitions:
  user:
    type: "object"
    properties:
      userId:
        type: "string"
      role:
        type: "string"
      phone:
        type: "string"
      designation:
        type: "string"
      name:
        type: "string"
      email:
        type: "string"
      userOrganization:
        type: "string"
      defaultSite:
        type: "string"
      policiesGroup:
        type: object
      notify:
        type: "string"
      accountable:
        type: "string"
  siteUsers:
    type: "array"
    items:
      $ref: '#/definitions/user'
  createRegion:
    type: "object"
    properties:
      name:
        type: "string"
      location:
        type: "string"
  createNetwork:
    type: "object"
    properties:
      name:
        type: "string"
      location:
        type: "string"
  createArea:
    type: "object"
    properties:
      name:
        type: "string"
      location:
        type: "string"
  createSite:
    type: "object"
    properties:
      name:
        type: "string"
      location:
        type: "string"
  createSchedule:
    type: "object"
    properties:
      rid:
        type: "string"
      startDate:
        type: "string"
      endDate:
        type: "string"
      startTime:
        type: "string"
      endTime:
        type: "string"
      allDay:
        type: "string"
      repeatInterval:
        type: "string"
      siteId:
        type: "string"
  createRecipe:
    type: "object"
    properties:
      siteId:
        type: "string"
      recipelabel:
        type: "array"
        items: 
          type: "string"
      label:
        type: "string"
      maxDataNeeded:
        type: "string"
      actionable:
        type: "array"
        items:
          type: object
          properties:
            title:
              type: "string"
            description:
              type: "string"
            notify:
              type: "array"
              items:
                type: "string"
            accountable:
              type: "array"
              items:
                type: "string"
            type:
              type: "string"
            priority:
              type: "number"
      neo:
        type: "string"
      isShedule:
        type: "string"
      type:
        type: "string"
  newDevice:
    type: "object"
    properties:
      deviceInfo:
        $ref: '#/definitions/deviceInfo'
  newController:
    type: "object"
    properties:
      deviceInfo:
        $ref: '#/definitions/deviceInfo'
  deviceInfo:
    type: "object"
    properties:
      siteId:
        type: "string"
      networkId:
        type: "string"
      regionId:
        type: "string"
      softwareVer:
        type: "string"
      hardwareVer:
        type: "string"
      vendorId:
        type: "string"
      operationMode:
        type: "string"
      deviceType:
        type: "string"
      areaId:
        type: "string"
      name:
        type: "string"
      portNumber:
        type: "string"
      controllerId:
        type: "string"
      communicationType:
        type: "string"
      communicationCategory:
        type: "string"
      driverType:
        type: "string"
  ahuPerformanceResponse:
    type: "object"
    properties:
      best:
        type: "array"
        items: {}
      worst:
        type: "array"
        items: {}
  ahuPerformance:
    type: "object"
    properties:
      siteId:
        type: "string"
      component:
        type: "string"
      groupBy:
        type: "string"
        enum: ['day', 'week', 'month']
      use:
        type: "string"
        enum: ['OutputFrequency', 'Actuator']
      params:
        type: "array"
        items:
          type: string
      type:
        type: "string"
        enum: ['line', 'heatmap', 'buildingLoad']
  plotGraph:
    type: "object"
    properties:
      deviceId:
        type: "string"
      startTime:
        type: "string"
      endTime:
        type: "string"
      group:
        type: "string"
        enum: ['hour', 'day', 'minute', 'minutes', 'week', 'month']
      params:
        type: "array"
        items:
          type: string
      type:
        type: "string"
        enum: ['line', 'heatmap', 'buildingLoad']
  findDataDeviceResponse:
    type: "object"
    properties:
      deviceId:
        type: "string"
      startTime:
        type: "string"
      endTime:
        type: "string"
  buildingload:
    type: "object"
    properties:
      weeks:
        type: "array"
        items:
          type: number
          enum: [0,1,2,3,4]
      days:
        type: "array"
        items:
          type: number
          enum: [0,1,2,3,4,5,6]
      prevSiteId:
        type: "string"
  issueToken:
    type: "object"
    properties:
      siteId:
        type: "string"
      secret:
        type: "string"
      prevSiteId:
        type: "string"
  login:
    type: "object"
    properties:
      userId:
        type: "string"
      password:
        type: "string"
  loginResponse:
    type: "object"
    properties:
      token:
        type: "string"
  createAdjustment:
    type: "object"
    properties:
      adjustmentvalue:
        type: "number"
      description:
        type: "string"
  createBaseline:
    type: "object"
    properties:
      baselines:
        type: "array"
        items:
          type: "object"
          properties:
            startDate:
              type: "string"
            endDate:
              type: "string"
            consumptionValue:
              type: "number"
  createDynamictarget:
    type: "object"
    properties:
      targets:
        type: "array"
        items:
          type: "object"
          properties:
            date:
              type: "string"
            actualTarget:
              type: "number"
            target:
              type: "number"

        