{
  "env": {
    //    "commonjs": true,
    "es2021": true,
    "mocha": true,
    "es6": true,
    "node": true
  },
  "globals": {
    "sails": true,
    "_": true,
    "ConfiguratorTaggedDevices": "readonly",
    "ConfiguratorTaggedDevicesParam": "readonly",
    "ConfiguratorSystem": "readonly",
    "Components": "readonly",
    "DeviceControlRelationshipConfig": "readonly",
    "AlertTemplate": "readonly",
    "AlertInventory": "readonly",
    "Recipe": "readonly",
    "NotificationMessageTemplate": "readonly",
    "RecipeInfo": "readonly"

  },
  "extends": [
    "airbnb-base"
  ],
  "parserOptions": {
    "ecmaVersion": 2020,
    "sourceType": "module"
  },
  "rules": {
    "quotes": "off",
    "prefer-const": "off",
    "object-shorthand": "off",
    "no-underscore-dangle": "off",
    "no-restricted-syntax": "off",
    "class-methods-use-this": "off",
    "no-plusplus": "off",
    "no-empty-function": "off",
    "object-curly-newline": "off",
    "comma-dangle": "off",
    "quote-props": "off",
    "semi": "off",
    "new-cap":"off",
    "no-prototype-builtins":"off",
    "spaced-comment": "off",
    "max-len": "off",
    "eqeqeq": "off",
    "func-names": "off",
    "no-restricted-globals": "off",
    "no-continue": "off",
    "camelcase": [
      "error",
      {
        "properties": "never",
        // <-- allows snake_case in object properties
        "ignoreDestructuring": true
      }
    ]
  }
}
