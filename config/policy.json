{"AC Plant": {"displayName": "AC Plant", "pageView": false, "subHeadings": {"command": {"displayName": "Command", "policies": {"write": {"displayName": "Write", "routePolicyMap": {"actions": ["controls/send-command-to-control-asset"]}, "hasAccess": false}}}, "SW Thermostat": {"displayName": "SW Thermostat", "policies": {"update": {"displayName": "Create/Update/Delete", "routePolicyMap": {"actions": ["recipe/createrecipe", "recipe/updaterecipe", "recipe/delete-recipe", "recipe/createschedule", "recipe/delete-schedule", "recipe/deployrecipe", "recipe/deployschedule", "recipe/startstop-recipe", "recipe/pause-recipe"]}, "hasAccess": false}}}, "Command History": {"displayName": "Command History", "policies": {"read": {"displayName": "View", "routePolicyMap": {"actions": ["controls/fetch-last-command", "controls/get-command-history", "controls/get-command-history-meta-info"]}, "hasAccess": false}}}, "Maintenance Mode": {"displayName": "Maintenance Mode", "policies": {"set": {"displayName": "Set", "routePolicyMap": {"actions": ["maintenanceMode/set-maintenance-mode"]}, "hasAccess": false}}}}}, "CONFIGURATOR_SYSTEMS": {"displayName": "Configurator System", "pageView": false, "subHeadings": {"systems_page": {"displayName": "systems_page", "policies": {"edit": {"displayName": "Edit", "routePolicyMap": {"actions": ["configurator/save-system-order", "configuratorTable/create-table", "systems/create-system", "configuratorTable/update-table", "configuratorTable/create-table-group", "configuratorTable/update-table-group", "configurator/create-system-category", "configurator/create-configurator-system", "configurator/save-system-svg", "configurator/update-system", "configuratorPage/save-svg-page-data-tagging", "configuratorPage/upload-svg-for-graphic-page", "configurator/save-system-data-tagging", "configuratorPage/create-configurator-page", "configuratorPage/update-configurator-page", "configuratorGraph/create-graph-type-page", "configuratorGraph/update-graph-property", "configuratorGraph/save-graph-config"]}, "hasAccess": false}, "delete": {"displayName": "Delete", "routePolicyMap": {"actions": ["configurator/delete-configurator-system", "configuratorPage/delete-configurator-page", "configuratorTable/delete-table", "configuratorTable/delete-table-group", "configurator/delete-system-category"]}, "hasAccess": false}, "publish": {"displayName": "Publish/Unpublish", "routePolicyMap": {"actions": ["configuratorPage/publish-configurator-page", "configuratorPage/unpublish-configurator-page", "configurator/unpublish", "configurator/publish"]}, "hasAccess": false}}}}}, "MAINTENANCE_CARD": {"displayName": "Maintenance Card", "pageView": false, "subHeadings": {"maintenance_overview": {"displayName": "Maintenance Overview", "policies": {"view": {"displayName": "View", "routePolicyMap": {}, "hasAccess": false}, "create": {"displayName": "Create", "routePolicyMap": {"actions": ["maintenanceCard/create-maintenance-ticket"]}, "hasAccess": false}, "edit": {"displayName": "Edit", "routePolicyMap": {"actions": ["maintenanceCard/update-maintenance-ticket"]}, "hasAccess": false}, "delete": {"displayName": "Delete", "routePolicyMap": {"actions": ["maintenanceCard/delete-maintenance-ticket"]}, "hasAccess": false}}}}}, "SMART_ALERT": {"displayName": "<PERSON> Alert", "pageView": false, "subHeadings": {"smart_alert_overview": {"displayName": "Smart Alert Overview", "policies": {"view": {"displayName": "View", "routePolicyMap": {}, "hasAccess": false}, "create": {"displayName": "Create", "routePolicyMap": {}, "hasAccess": false}, "edit": {"displayName": "Edit", "routePolicyMap": {}, "hasAccess": false}, "delete": {"displayName": "Delete", "routePolicyMap": {}, "hasAccess": false}}}}}, "Joule Recipes": {"displayName": "Joule Recipes", "pageView": false, "subHeadings": {"recipe": {"displayName": "Recipe", "policies": {"create": {"displayName": "Create", "routePolicyMap": {"actions": ["superRecipe/create-recipe", "superRecipe/create-recipe-template"]}, "hasAccess": false}, "read": {"displayName": "Read", "routePolicyMap": {"actions": ["superRecipe/fetch-recipe-by-id", "superRecipe/fetch-recipe-template-by-id", "superRecipe/fetch-super-recipe-templates", "superRecipe/fetch-super-recipes"]}, "hasAccess": false}, "update": {"displayName": "Update", "routePolicyMap": {"actions": ["superRecipe/edit-recipe-by-id"]}, "hasAccess": false}, "delete": {"displayName": "Delete", "routePolicyMap": {"actions": ["superRecipe/delete-recipe-by-id", "superRecipe/delete-recipe-template-by-id"]}, "hasAccess": false}, "state_change": {"displayName": "Deploy/Pause/Play", "routePolicyMap": {"actions": ["superRecipe/manage-running-state", "superRecipe/deploy-super-recipe"]}, "hasAccess": false}}}, "schedule": {"displayName": "Schedule", "policies": {"create": {"displayName": "Create", "routePolicyMap": {"actions": ["superRecipe/create-schedules"]}, "hasAccess": false}, "edit": {"displayName": "Edit", "routePolicyMap": {"actions": ["superRecipe/edit-schedule-by-id"]}, "hasAccess": false}, "delete": {"displayName": "Delete", "routePolicyMap": {"actions": ["superRecipe/delete-schedule-by-id"]}, "hasAccess": false}, "find": {"displayName": "Find", "routePolicyMap": {"actions": []}, "hasAccess": false}}}}}, "Configuration": {"displayName": "Configuration", "pageView": false, "subHeadings": {"config": {"displayName": "Config", "policies": {"read": {"displayName": "Read", "routePolicyMap": {"actions": []}, "hasAccess": false}, "create": {"displayName": "Create", "routePolicyMap": {"actions": ["site/edit-site-info", "site/add-area", "site/add-region", "site/add-network", "nodes/create-node", "device/add-device", "component/bulk-component-generation", "device/generate-parameter-sheet", "device/save-parameter-sheet"]}, "hasAccess": false}, "edit": {"displayName": "Edit", "routePolicyMap": {"actions": ["site/edit-area", "site/edit-region", "component/edit-component"]}, "hasAccess": false}, "delete": {"displayName": "Delete", "routePolicyMap": {"actions": ["site/delete-region", "site/delete-area"]}, "hasAccess": false}}}, "MODE": {"displayName": "Mode", "policies": {"Set": {"displayName": "Set", "routePolicyMap": {"actions": ["component/change-mode-of-asset-control"]}, "hasAccess": false}}}}}, "Consumption": {"displayName": "Consumption", "pageView": false, "subHeadings": {}}, "Detail Analytics": {"displayName": "Detail Analytics", "pageView": false, "subHeadings": {}}, "Device": {"displayName": "<PERSON><PERSON>", "pageView": false, "subHeadings": {"Recalab device": {"displayName": "Recalab Device", "policies": {"write": {"displayName": "Write", "routePolicyMap": {"actions": []}, "hasAccess": false}}}}}, "Diagnostic": {"displayName": "Diagnostic", "pageView": false, "subHeadings": {"Request(IPs NS)": {"displayName": "Request(IPs Network Strength)", "policies": {"write": {"displayName": "Write", "routePolicyMap": {"actions": []}, "hasAccess": false}}}, "Calculate DQI": {"displayName": "Calculate DQI", "policies": {"read": {"displayName": "Read", "routePolicyMap": {"actions": []}, "hasAccess": false}}}, "CICD Update": {"displayName": "CICD Update", "policies": {"write": {"displayName": "Write", "routePolicyMap": {"actions": []}, "hasAccess": false}}}}}, "Energy Meter": {"displayName": "Energy Meter", "pageView": false, "subHeadings": {}}, "User": {"displayName": "User", "pageView": false, "subHeadings": {"user": {"displayName": "User", "policies": {"update": {"displayName": "Update", "routePolicyMap": {"actions": ["user/update-user"]}, "hasAccess": false}, "create": {"displayName": "Create", "routePolicyMap": {"actions": ["user/create-user"]}, "hasAccess": false}, "read": {"displayName": "Get All Users", "routePolicyMap": {"actions": ["user/get-all-users"]}, "hasAccess": false}, "delete": {"displayName": "Delete", "routePolicyMap": {"actions": ["user/delete-user"]}, "hasAccess": false}}}, "UserRole": {"displayName": "User Role", "policies": {"update": {"displayName": "Update", "routePolicyMap": {"actions": ["user/update-role"]}, "hasAccess": false}, "create": {"displayName": "Create", "routePolicyMap": {"actions": ["user/create-role"]}, "hasAccess": false}, "delete": {"displayName": "Delete", "routePolicyMap": {"actions": ["user/delete-role"]}, "hasAccess": false}}}}}, "Site": {"displayName": "Site", "pageView": false, "subHeadings": {"Site Info": {"displayName": "Site Info", "policies": {"edit": {"displayName": "Edit", "routePolicyMap": {"actions": ["component/upload-component-files"]}, "hasAccess": false}}}, "component": {"displayName": "Component", "policies": {"create": {"displayName": "Create", "routePolicyMap": {"actions": ["component/add-component"]}, "hasAccess": false}, "delete": {"displayName": "Delete", "routePolicyMap": {"actions": []}, "hasAccess": false}}}}}}