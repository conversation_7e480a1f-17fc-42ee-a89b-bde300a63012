const fs = require("fs");
const path = require("path");

module.exports = {
  monolithURL: "http://prod-jouletrack-api:1337",
  cacheHost: process.env.REDIS_HOST,
  cachePort: 6379,
  dynamoDBCache: process.env.DYNAMODB_DAX_CLUSTER_ENDPOINT,
  IOT_CONFIG: {
    host: "a1hz6xr4hc68uh.iot.us-west-2.amazonaws.com",
    key: fs.readFileSync(path.join(__dirname, "/../../api/services/event/certs-prod/private.key")),
    certPath: path.join(__dirname, "../", "iotCoreCert/rootCA-prod.pem"),
    cert: fs.readFileSync(path.join(__dirname, "/../../api/services/event/certs-prod/cert.crt")),
    ca: fs.readFileSync(path.join(__dirname, "/../../api/services/event/certs-prod/rootCA.pem")),
    port: 8883,
    region: process.env?.IOT_CORE_REGION || "us-west-2",
  },
  MQTT_URL: "http://mqtt:5555",
  allowedHost: [],
  models: {
    migrate: "safe",
    datastore: "prodDb",
  },
  sockets: {},
  log: {
    level: process.env.LOG_LEVEL || "info",
  },
  blueprints: {
    shortcuts: false,
  },
  security: {
    cors: {
      allRoutes: true,
      allowOrigins: "*",
      allowCredentials: false,
      allowRequestHeaders:
        "Authorization,Content-Type,X-Transaction-Id,X-Current-Time,X-Amzn-Request-Id,X-Forwarded-Host,X-Forwarded-For,X-Amzn-Trace-Id", // this is "required" for pre-flight
    },
  },
  session: {
    // useless, we anywy dont use it
    cookie: {
      // secure: true, we dont use cookie anyways and this is HTTP secure tag in cookie
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
    },
  },
  http: {
    cache: 365.25 * 24 * 60 * 60 * 1000, // One year
  },
  PROCESS_SITES: ["rpml-bij"],
  RABBITMQ_URL: `amqps://${process.env.RABBITMQ_USER}:${process.env.RABBITMQ_PASS}@${process.env.RABBITMQ_SERVER_ADDRESS}`,
  REGION: process.env.REGION,
  SMS_KALEYRA_URL: process.env.SMS_KALEYRA_URL,
  SMS_KALEYRA_KEY: process.env.SMS_KALEYRA_KEY,
  SMS_KALEYRA_SID: process.env.SMS_KALEYRA_SID,
  SMS_KALEYRA_TYPE: process.env.SMS_KALEYRA_TYPE,
  KALEYRA_SMS_SENDER: process.env.KALEYRA_SMS_SENDER,
  NOVU_CONFIG: {
    SECRET_KEY: process.env.NOVU_SECRET_KEY,
  },
  DATA_EXCHANGE_AUTH_CONFIG: {
    tokenSecret:
      "VersionOneSecretKeyForSecureAuthenticationOfDataExchangeServicesAndCommunicationsBetweenSystems",
    validSources: ["externalDataExchangeService"],
    validAppIds: ["dejoule"],
  },
  IOT_AUTH_CONFIG: {
    tokenSecret:
      "InitialVersionSecretEncryptionKeyForSecureAuthenticationOfInternetOfThingsDevicesAndCommunications",
    validSources: ["iot"],
    validAppIds: ["dejoule"],
  },
  kafka: {
    kafkaEnabled: process.env.KAFKA_ENABLED ?? true,
    clientId: process.env.KAFKA_CLIENT_ID || "default-client-id",
    brokers: (process.env.KAFKA_BROKERS || "localhost:9092").split(","),
    securityProtocol: process.env.KAFKA_SECURITY_PROTOCOL || "SASL_SSL",
    username: process.env.KAFKA_USERNAME || null,
    password: process.env.KAFKA_PASSWORD || null,
    connectionTimeout: parseInt(process.env.KAFKA_CONNECTION_TIMEOUT, 10) || 30000,
    groupId: process.env.KAFKA_GROUP_ID || "default-group-id",
    topics: {
      auditLogTopic: process.env.KAFKA_AUDIT_LOG_TOPIC,
    },
  },
};
