const fs = require("fs");
const path = require("path");

module.exports = {
  cacheHost: "localhost",
  cachePort: 6379,
  IOT_CONFIG: {
    host: "a1hz6xr4hc68uh-ats.iot.ap-south-1.amazonaws.com",
    key: fs.readFileSync(path.join(__dirname, "/../../api/services/event/certs-dev/private.key")),
    cert: fs.readFileSync(path.join(__dirname, "/../../api/services/event/certs-dev/cert.crt")),
    ca: fs.readFileSync(path.join(__dirname, "/../../api/services/event/certs-dev/rootCA.pem")),
    port: 8883,
  },
  allowedHost: ["::ffff:127.0.0.1", "127.0.0.1", "::1"],
  models: {
    migrate: "safe",
    datastore: "testingDb",
  },
  sockets: {
    adapter: "memory",
  },
  log: {
    level: "warn",
  },
  PROCESS_SITES: ["rpml-bij"],

  IOT_AUTH_CONFIG: {
    tokenSecret:
      "InitialVersionSecretEncryptionKeyForSecureAuthenticationOfInternetOfThingsDevicesAndCommunications",
    validSources: ["iot"],
    validAppIds: ["dejoule"],
  },
  DATA_EXCHANGE_AUTH_CONFIG: {
    tokenSecret: "VersionOneSecretKeyForSecureAuthenticationOfDataExchangeServicesAndCommunicationsBetweenSystems",
    validSources: ["externalDataExchangeService"],
    validAppIds: ["dejoule"],
  }
};
