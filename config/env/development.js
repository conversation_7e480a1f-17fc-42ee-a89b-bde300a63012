const fs = require('fs');
const path = require('path');

module.exports = {
  monolithURL: 'http://localhost:1336',
  cacheHost: process.env.CACHE_HOST,
  cachePort: process.env.CACHE_PORT,
  dynamoDBCache: process.env.DYNAMODB_DAX_CLUSTER_ENDPOINT,
  IOT_CONFIG: {
    host: 'a1hz6xr4hc68uh.iot.us-west-2.amazonaws.com',
    key: fs.readFileSync(path.join(__dirname, '/../../api/services/event/certs-prod/private.key')),
    certPath: path.join(__dirname, '../', 'iotCoreCert/rootCA-prod.pem'),
    cert: fs.readFileSync(path.join(__dirname, '/../../api/services/event/certs-prod/cert.crt')),
    ca: fs.readFileSync(path.join(__dirname, '/../../api/services/event/certs-prod/rootCA.pem')),
    port: 8883,
    region: process.env?.IOT_CORE_REGION || 'us-west-2',
  },
  MQTT_URL: 'http://localhost:5555',
  allowedHost: ['::ffff:127.0.0.1', '127.0.0.1', '::1'],
  models: {
    migrate: 'safe',
    datastore: 'devDb',
  },
  sockets: {
    adapter: 'memory',
  },
  log: {
    level: process.env.LOG_LEVEL || 'info',
  },
  PROCESS_SITES: ['rpml-bij'],

  REGION: process.env.REGION,
  SMS_KALEYRA_URL: 'https://zeta3.free.beeceptor.com', // this website always 200. template to avoid local problem
  SMS_KALEYRA_KEY: 'TEST',
  SMS_KALEYRA_SID: 'TEST',
  SMS_KALEYRA_TYPE: 'TEST',
  KALEYRA_SMS_SENDER: 'TEST',
  RABBITMQ_URL: process.env.RABBITMQ_URL,
  NOVU_CONFIG: {
    SECRET_KEY: process.env.NOVU_SECRET_KEY,
  },
  DATA_EXCHANGE_AUTH_CONFIG: {
    tokenSecret:
      'VersionOneSecretKeyForSecureAuthenticationOfDataExchangeServicesAndCommunicationsBetweenSystems',
    validSources: ['externalDataExchangeService'],
    validAppIds: ['dejoule'],
  },
  IOT_AUTH_CONFIG: {
    tokenSecret:
      'InitialVersionSecretEncryptionKeyForSecureAuthenticationOfInternetOfThingsDevicesAndCommunications',
    validSources: ['iot'],
    validAppIds: ['dejoule'],
  },
  kafka: {
    kafkaEnabled: process.env.KAFKA_ENABLED ?? false,
    clientId: process.env.KAFKA_CLIENT_ID || 'default-client-id',
    brokers: (process.env.KAFKA_BROKERS || 'localhost:9092').split(','),
    securityProtocol: process.env.KAFKA_SECURITY_PROTOCOL || 'SASL_SSL',
    username: process.env.KAFKA_USERNAME || null,
    password: process.env.KAFKA_PASSWORD || null,
    connectionTimeout: parseInt(process.env.KAFKA_CONNECTION_TIMEOUT, 10) || 30000,
    groupId: process.env.KAFKA_GROUP_ID || 'default-group-id',
    topics: {
      auditLogTopic: process.env.KAFKA_AUDIT_LOG_TOPIC,
    },
  },
};
