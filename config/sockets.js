let socketConfig = {};

if (process.env.NODE_ENV === 'production') {
  socketConfig = {
    pingTimeout: 60000,
    transports: ['websocket'], // not using polling, chi
    adapter: '@sailshq/socket.io-redis',
    url: `redis://${process.env.REDIS_HOST}:6379`,
    beforeConnect: function (handshake, cb) {
      // here need to add authentication or allow cors
      // better is just use cors and COWSH will be gone ftw
      // or io.sails.useCORSRouteToGetCookie = false in front end
      cb(undefined, true);
    },
  };
} else {
  socketConfig = {
    pingTimeout: 60000,
    transports: ['websocket'], // not using polling, chi
    url: 'redis://localhost:6379', // use redis to store socket and not in-memory
    // adapter: 'memory', we dont use in memory socket, if we want to uncomment this
  };
}

/**
 * Cant access sails.config in this file else this configration should have been
 * in /env/*.js file
 */

console.log('[-]Running server in ' + process.env.NODE_ENV + ' mode');
console.log(`[-]Sockets are connected at ${socketConfig.url}`);
module.exports.sockets = socketConfig;
