const flaverr = require("flaverr");
const Joi = require("joi");
const moment = require("moment");
module.exports = {
  validateDeviceParamLineGraph: function(inputs) {

    const analyticsValidationSchema = Joi.object({
      siteId: Joi.string().required(),
    
      startTime: Joi.string()
        .isoDate()
        .required()
        .description("Start time in date format (ISO 8601)"),
    
      endTime: Joi.string()
        .isoDate()
        .required()
        .description("End time in date format (ISO 8601)"),
    
      deviceId: Joi.string()
        .required()
        .description("A unique device ID"),
    
      abbr: Joi.string()
        .required()
        .description("A unique parameter name, e.g., kw, pf, vpp, a_amp, kva, kvar"),
    
    });
  
    const { error } = analyticsValidationSchema.validate(inputs);
    if (error) {
      throw flaverr({
            code: 'E_INPUT_PARAMS',
            msg: error.message,
            statusCode: 400
      })
    }
    const {startTime, endTime} = inputs;
    if (!moment(startTime).isValid()) {
      throw flaverr({
        code: 'E_INVALID_DATE_TIME',
        msg: 'Please enter valid startTime',
        statusCode: 400
      })
    }
    if (!moment(endTime).isValid()) {
      throw flaverr({
        code: 'E_INVALID_DATE_TIME',
        msg: 'Please enter valid endTime',
        statusCode: 400
      })
    }
  },
  validateTimeWindow: function(startTime, endTime, utcOffset) {
    const startTimeWithMoment = moment(startTime).utcOffset(utcOffset);
    const endTimeWithMoment = moment(endTime).utcOffset(utcOffset);
    if (endTimeWithMoment.diff(startTimeWithMoment, 'days') > 30) {
      throw flaverr({
        code: 'E_INVALID_TIME_WINDOW',
        msg: 'Time window limit exceeded 30 days.',
        statusCode: 400
      })
    }
  }
}