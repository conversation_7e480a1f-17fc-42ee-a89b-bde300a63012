const uuid = require('uuid');

module.exports = {
    /**
     * Creates object for template query
     * @param {object} inputs: input values to create req obj.
     * @returns request objecte to query DB.
     */
    buildInitialPacket(inputs) {
        const graphId = uuid();
        let key;
        if (inputs.global) {
            key = `global_${inputs._userMeta._site}`;
        } else {
            key = `${inputs._userMeta.id}_${inputs._userMeta._site}`;
        }
        const userId_siteId = key;
        return { userId_siteId, graphId, requestPayload: inputs.requestPayload, graphType: inputs.graphType, name: inputs.name, folderName: inputs.folderName };
    },

    createGraphObject(graphs) {
        const graphList = [];
        let graphData = {};
        for (let graph of graphs) {
            const payload = {
                devices: graph.requestPayload.devices,
                group: graph.requestPayload.group,
                deviceType: graph.requestPayload.deviceType,
                graphType: graph.graphType,
                name: graph.name,
                graphId: graph.graphId,
            }
            graphData[graph.folderName] = graphData[graph.folderName] ? [...graphData[graph.folderName], payload] : [payload];
        }
        for (let graph in graphData) {
            graphList.push({ folderName: graph, graphs: graphData[graph] })
        }
        return graphList;
    }
};