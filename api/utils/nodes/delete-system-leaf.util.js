
const globalHelper = require('../globalhelper'); 

module.exports = {
  checkInput: function(body, nodeInfo){
    let errors = globalHelper.checkObjectKeys(body, ["nodeId"], '');

    if(!nodeInfo) 
      errors.push("Leaf node attempted to be deleted does not exist in DB.");
    if(nodeInfo.is_deleted == true)
      errors.push("Leaf Node already deleted");
    if (nodeInfo.level_type != "controller" && nodeInfo.level_type != "component")
      errors.push("Leaf node attemped to be deleted is neither a controller or a component");

    if (errors.length === 0) return { "status": true }
    else return {
      "status": false,
      errors
    }
  },
};