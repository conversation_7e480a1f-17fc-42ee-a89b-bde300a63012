
const globalHelper = require('../globalhelper'); 

module.exports = {
  checkInput: function(body, doesSiteExist, systemInfo, parentNodeInfo, sameEntryCheck, systemDefinition, parentHierarchyInfo, isLayer){
    let { levelType, systemId, parentId } = body;
    let errors;
    if(isLayer){
      errors = globalHelper.checkObjectKeys(body, ["systemId", "parentId", "name", "siteId", "layerType"]);
    } else {
      errors = globalHelper.checkObjectKeys(body, ["levelType", "systemId", "parentId", "name", "deviceId"]);
    }
    
    if (!doesSiteExist) 
      errors.push("Site does not exist!");
    const possibleLevelTypes = ["component", "controller", "layer"];
    if (!possibleLevelTypes.includes(levelType))
      errors.push("levelType not in: 'component/controller'");
    if (!systemInfo) 
      errors.push(`System with ${body.systemId} not found in DB.`);
    if (!parentNodeInfo){
      if(isLayer){
        if ( parentId != 0 ) 
          errors.push("Parent node not found in system");
      } else {
        // Checking in case no layers have been defined on the system.
        if ( parentId !=0 || (parentId == 0 && systemDefinition.length != 0) )
          errors.push("Parent node not found in system or in site.");
      }
    } else {
      if(parentNodeInfo.system_id != systemId) 
          errors.push("SystemId does not match that of the parent");
      if(isLayer){
        if (parentHierarchyInfo == null){ // This value is null, if no entry of layerType and systemId was found.
          errors.push("Layer type not found in system!")
        } else if ( parentHierarchyInfo==true && parentId != 0 ){
          // If parentHierarchyInfo == true, it implies that the parentId of the childLayerType was 0 and there is no parent.
            errors.push("parentId should be 0 as the layerType has no parent in the system definition OR if parentId is correct, layerType cannot be added to this parentNode. Please check system definition.")
        }
        else if ( parentNodeInfo.layer_type != parentHierarchyInfo.layer_type )
          errors.push("layerType of parentId and layerType in hierarchy do not match. Combination of layerType and parentId is incorrect.");
      } else {
        if(parentNodeInfo.level_type != "layer")
          errors.push("Controller/ component can only be added under layers. Not under other controllers/ components");
      }
    }
    if (!isLayer){
      let duplicateEntry = false;
      duplicateEntry = sameEntryCheck.some(possibleDuplicateEntry => possibleDuplicateEntry.is_deleted == false);
      if(duplicateEntry)
        errors.push("Same deviceId exists in the same system and parent node.");
    } else {
      if (sameEntryCheck)
        errors.push("Layer with the same name found under the same parent.");
    }
    
    if (errors.length === 0) return { "status": true }
    else return {
      "status": false,
      errors
    }
  },
};