const flaverr = require('flaverr');
module.exports = {
  throwsExceptionCommandParameterIsNotReady: ({
    displayName,
    expression,
    device_abbr,
    key,
    deviceId
  }) => {
    if (_.isEmpty(expression)) {
      throw flaverr({
        code: 'E_COMMAND_PARAM_NOT_CONFIGURED',
        message: `Command Parameter ${displayName}'s expression is not configured property`,
        HTTP_STATUS_CODE: 400
      });
    }
    if (_.isEmpty(deviceId)) {
      throw flaverr({
        code: 'E_COMMAND_PARAM_NOT_CONFIGURED',
        message: `Command Parameter ${displayName}'s deviceId is not configured`,
        HTTP_STATUS_CODE: 400
      });
    }
    if (_.isEmpty(key) && _.isEmpty(device_abbr)) {
      throw flaverr({
        code: 'E_COMMAND_PARAM_NOT_CONFIGURED',
        message: `Command Parameter ${displayName}'s abbr is not configured`,
        HTTP_STATUS_CODE: 400
      });
    }
  },
  throwsExceptionControlNotFound: (control) => {
    throw flaverr({
      code: 'E_CONTROL_NOT_FOUND',
      message: `"${control}" does not exist. Please check the component configuration page's command section.`,
      HTTP_STATUS_CODE: 400
    });
  },
  throwsExceptionControlNotConfigured: (controlName) => {
    throw flaverr({
      code: 'E_CONTROL_CONFIG_ERROR',
      message: `this control "${controlName}" is not configured properly. please check Feedback Expression and Command Device is filled properly`,
      HTTP_STATUS_CODE: 400
    });
  },
  throwsExceptionCommandNotFoundInControl: (commandAbbr, controlConfig) => {
    throw flaverr({
      code: 'E_COMMAND_REQUEST_INVALID',
      message: `command.${commandAbbr} not exist in control configuration`,
      HTTP_STATUS_CODE: 400
    });
  },
  throwsExceptionIncorrectControlMode: (controlName) => {
    throw flaverr({
      code: 'E_INVALID_CONTROL_MODE',
      message: `This control (${controlName}) is not in Jouletrack mode. To send a command, asset's control should be in Jouletrack mode.`,
      HTTP_STATUS_CODE: 400
    });
  },
  throwsExceptionSiteIdRequired: () => {
    throw flaverr({
      code: 'E_SITE_ID_MISSING',
      message: 'site id is require',
      HTTP_STATUS_CODE: 400
    });
  },
  throwsExceptionComponentIdRequired: () => {
    throw flaverr({
      code: 'E_COMPONENT_ID_REQUIRED',
      message: 'component id is required',
      HTTP_STATUS_CODE: 400
    });
  },
  throwsExceptionControlAbbrRequired: () => {
    throw flaverr({
      code: 'E_CONTROl_ABBR_IS_REQUIRED',
      message: 'control Abbr  is required',
      HTTP_STATUS_CODE: 400
    });
  },
  throwExceptionComponentIdNotFound: (componentId) => {
    throw flaverr({
      code: 'E_COMPONENT_ID_NOT_FOUND',
      message: `Component id ${componentId} does not exist`,
      HTTP_STATUS_CODE: 400
    });
  },
  throwExceptionDeviceNotExist: (errObj) => {
    const {code,  message} = errObj;
    throw flaverr({
      code,
      message,
      HTTP_STATUS_CODE: 400
    });
  },
  throwExceptionControllerNotAttached:(errObj) => {
    const {code,  message} = errObj;
    throw flaverr({
      code,
      message,
      HTTP_STATUS_CODE: 400
    });
  },
  throwExceptionControllerNotExist: (errObj) => {
    const {code,  message} = errObj;
    throw flaverr({
      code,
      message,
      HTTP_STATUS_CODE: 400
    });
  },
  throwExceptionInvalidComponent: (componentId) => {
    throw flaverr({
      code: 'E_INVALID_COMPONENT_ID',
      message: `Component ${componentId} does not exist.`,
      HTTP_STATUS_CODE: 400
    });
  },
  throwExceptionInvalidSite: (siteId) => {
    throw flaverr({
      code: 'E_INVALID_SITE_ID',
      message: `Site ${siteId} does not exist.`,
      HTTP_STATUS_CODE: 400
    });
  }
};
