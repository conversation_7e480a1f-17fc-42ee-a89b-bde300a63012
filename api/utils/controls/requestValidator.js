const Joi = require("joi");
const flaverr = require("flaverr");

const validateCommandHistoryList = function (params) {
  const schema = Joi.object({
    page: Joi.number().min(1).required(),
    rows: Joi.number().min(1).max(100).required(),
    commandSource: Joi.string().valid('jouletrack', 'recipe', 'cpa').required(),
  });

  const { error } = schema.validate(params);
  if (error) {
    throw flaverr("E_INPUT_VALIDATION", new Error(error.message));
  }
};

const validateCommandHistoryQueryParam = function (params) {
  const schema = Joi.object({
    offset: Joi.number().min(0).required(),
    rows: Joi.number().min(1).max(100).required(),
    commandSource: Joi.string().valid('jouletrack', 'recipe', 'cpa'),
    siteId: Joi.string().required(),
    componentId: Joi.string().required(),
    controlAbbr: Joi.string().required(),
  });

  const { error } = schema.validate(params);
  if (error) {
    throw flaverr("E_INPUT_VALIDATION", new Error(error.message));
  }
}


const validateFetchLastCommand = function(params) {
  const schema = Joi.object({
    siteId: Joi.string().required(),
    componentId: Joi.string().required(),
    controlAbbr: Joi.string().required(),
  });

  const { error } = schema.validate(params);
  if (error) {
    throw flaverr("E_INPUT_VALIDATION", new Error(error.message));
  }
}


module.exports = {
    validateCommandHistoryList,
    validateCommandHistoryQueryParam,
    validateFetchLastCommand
};
