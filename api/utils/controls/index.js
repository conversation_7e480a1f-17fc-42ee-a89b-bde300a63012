const moment = require("moment");
const iotFeedbackResponse = require("../../services/controls/lib/IoTFeedbackControlStateDict.json");
const flaverr = require("flaverr");

module.exports = {
  isControlConfiguredToAcceptCommand: function (controlConfig) {
    const {
      controlProperty,
      controlType,
    } = controlConfig;
    if (controlType == 'BIT') {
      return isCommandConfigured(controlProperty.left) && isCommandConfigured(controlProperty.right);
    }
    if (controlType == 'VIT') {
      return isCommandConfigured(controlProperty);
    }
    return false;

    function isCommandConfigured(commandParameterRow) {
      const {
        expression,
        device_abbr,
        key,
        deviceId,
      } = commandParameterRow;
      if (_.isEmpty(expression) || _.isEmpty(deviceId) || (_.isEmpty(key) && _.isEmpty(device_abbr))) return false;
      return true;
    }
  },
  isCommandRequestValid: function (commandRequest, controlConfig) {
    const {
      controlProperty,
      controlType,
    } = controlConfig;
    if (controlType == 'BIT') {
      return (controlProperty.left.commandAbbr == commandRequest.abbr || controlProperty.right.commandAbbr == commandRequest.abbr);
    }
    if (controlType == 'VIT') {
      return controlProperty.commandAbbr == commandRequest.abbr;
    }
    return false;

  },
  getCommandParameterDetail: function (commandRequest, controlConfig) {
    if (!this.isCommandRequestValid(commandRequest, controlConfig)) return;
    const {
      controlProperty,
      controlType,
    } = controlConfig;
    if (controlType == 'BIT' && controlProperty.left.commandAbbr == commandRequest.abbr) {
      return controlProperty.left;
    }
    if (controlType == 'BIT' && controlProperty.right.commandAbbr == commandRequest.abbr) {
      return controlProperty.right;
    }
    if (controlType == 'VIT' && controlProperty.commandAbbr == commandRequest.abbr) {
      return controlProperty;
    }
    return;
  },
  ControlStateDict: {
    '1': 'LISTENING_MODE',
    '2': 'COMMAND_EXECUTION_IN_PROGRESS',
    '3': 'DISABLED_MODE'
  },
  buildCommandHistoryMetaInfoObj: function (componentInfo, componentControlMetaInfo) {
    const metaInfoObj = {};
    metaInfoObj.componentTitle = componentInfo.name;
    metaInfoObj.sourceCommand = getComponentSourceCommand();
    metaInfoObj.controlAbbr = getComponentControlsAbbr(componentControlMetaInfo);
    return metaInfoObj;
    function getComponentControlsAbbr(componentControlMetaInfo) {
      return  componentControlMetaInfo.map((curr) => {
        const { controlAbbr, controlName } = curr;
        return { displayName: controlName, value: controlAbbr };
      });
    }
  },
  buildCommandHistoryObj: function (commandHistoryRawData, controlAbbrRelationship) {
    const commandHistoryObj = new Map();
    for (const row of commandHistoryRawData) {
      const {
        timestamp,
        dataParamAbbr,
        commandSource,
        status,
        recipeId,
        recipeName,
        userId,
        commandType,
        commandValue,
        deviceId,
        timezoneOffset,
        commandUid,
        commandAbbr,
        count
      } = row;
     
      const variablesToCheck = [
        timestamp,
        dataParamAbbr,
        commandSource,
        status,
        userId,
        commandType,
        commandValue,
      ];

      if ( _.some(variablesToCheck, variable =>  variable === null || 
            variable === undefined || 
            (typeof variable === 'string' && variable.trim().length === 0)
          )
      ) {
        continue;
      };

      if (!iotFeedbackResponse[status]?.stateCode) {
        sails.log(`[Error > Command history List] Unable to find command history mapping for status ${status}`)
        continue;
      }
      const day = moment(timestamp).utcOffset(timezoneOffset).format("MMMM Do YYYY");

      const item = {
        time: moment(timestamp).utcOffset(timezoneOffset).format("HH:mm:ss"),
        commandSourceInfo: {
          userId,
          ...getRecipeInfoByCommandSource(recipeId, recipeName, commandSource)
        },
        commandState: {
          ...getCommandState(status),
          ...getCommandTypeInfo({ commandType, commandValue, deviceId, commandAbbr }, controlAbbrRelationship),
        },
        dataParamAbbr,
        source: commandSource,
        commandUid
      };

      if (['recipe','cpa'].includes(commandSource)) item.commandSourceInfo.count = count;

      if (commandHistoryObj.has(day)) {
        commandHistoryObj.get(day).commandDetails.push(item);
      } else {
        commandHistoryObj.set(day, {
          date: day,
          commandDetails: [
            item
          ],
        });
      }
    }

    const structuredHistory = Array.from(commandHistoryObj.values());
    return structuredHistory;

    function getRecipeInfoByCommandSource(recipeId, recipeName, commandSource) {
      let commandSourceInfo = {}
      if (commandSource.toLowerCase() === 'recipe' ) {
        commandSourceInfo = getRecipeInfo(recipeId, recipeName)
      }
      return commandSourceInfo;

      function getRecipeInfo(recipeId, recipeName) {
        return {
          recipeId,
          recipeName
        }
      }
    }


  },
  filterCommandHistorySchema: function(commandHistoryRawData, commandSource) {
    const commandHistoryData = getDistinctCommandWithCount(commandHistoryRawData, commandSource)
    const finalSchema = [];
    for(const commandRecord of commandHistoryData) {
      const { 
        commandsource: commandSource,
        _time: timestamp,
        commandvalue: commandValue,
        controlabbr: controlAbbr,
        commandabbr: commandAbbr,
        commandtype: commandType,
        dataparamabbr: dataParamAbbr,
        deviceid: deviceId,
        recipeid: recipeId,
        recipetitle: recipeName,
        userid: userId,
        status,
        timezoneFormat: timezoneOffset,
        commandkey: commandUid,
        count
      } = commandRecord;
      const userIdByCommandSource = commandSource === 'jouletrack' ? userId : commandSource; 
      const historyFilterObj = { 
          commandSource,
          timestamp,
          commandValue,
          controlAbbr,
          commandAbbr,
          commandType,
          dataParamAbbr,
          deviceId,
          recipeId,
          recipeName,
          userId: userIdByCommandSource,
          status,
          timezoneOffset,
          commandUid,
          count
      }
      finalSchema.push(historyFilterObj)
    }
    return finalSchema;
  },
  filterCommandRetentionSchema: function(commandRetentionRawData) {
    const finalSchema = [];
    for(const commandRecord of commandRetentionRawData) {
      const { 
        commandsource: commandSource,
        _time: timestamp,
        commandvalue: commandValue,
        controlabbr: controlAbbr,
        commandabbr: commandAbbr,
        commanddeviceid: deviceId,
        commandrecipeid: recipeId,
        commandfeedbackstatuscode: status,
        timezoneFormat: timezoneOffset,
        retentionrecipeid: commandUid
      } = commandRecord;
      const historyFilterObj = { 
          commandSource,
          timestamp,
          commandValue,
          controlAbbr,
          commandAbbr,
          deviceId,
          recipeId,
          userId: 'commandretention',
          status,
          timezoneOffset,
          commandUid
      }
      finalSchema.push(historyFilterObj)
    }
    return finalSchema;
  },
  buildCommandRetentionObj: function (commandRetentionObj, controlAbbrRelationship) {
    const {
      timezoneOffset,
      status,
      timestamp,
      commandSource,
      commandUid,
      recipeId,
      deviceId,
      commandValue,
      userId,
      commandAbbr
    } = commandRetentionObj[0];
    const commandType = controlAbbrRelationship.controlType;
    const dataParamAbbr = controlAbbrRelationship.dataParamAbbr
    const day = moment(timestamp).utcOffset(timezoneOffset).format("MMMM Do YYYY");
    const item = {
      time: moment(timestamp).utcOffset(timezoneOffset).format("HH:mm:ss"),
      commandSourceInfo: {
        userId
      },
      commandState: {
        ...getCommandTypeInfo({ commandType, commandValue, deviceId, commandAbbr }, controlAbbrRelationship),
      },
      dataParamAbbr,
      source: commandSource,
      commandUid
    };

    const response = {
      date: day,
      commandDetails: [
        item
      ]
    }

    if (recipeId) {
      item.commandSourceInfo.recipeId = recipeId;
      item.commandSourceInfo.recipeName = `Command retention:- ${controlAbbrRelationship.description}`
    }
    if (status) item.commandState = {
      ...item.commandState,
      ...getCommandState(status)
    }

    return response;
  },
  getCommandHistoryQueryBasedOnFilter: function (commandSource, controlType) {

    // const allowedStatus = [0,1,3,4,6,8,9,13,-1,14,18,19,11,15,20,16].map(status => `"status" = ${status}`).join(' OR ');

    switch (commandSource) {
      case 'jouletrack': {
        return getJouletrackFluxQuery(controlType)
      }
      case 'cpa': {
        return getCPAFluxQuery(controlType)
      }

      case 'recipe': {
          return getRecipeFluxQuery(controlType)
      }

      default: {
        throw flaverr({
          code:'E_INVALID_COMMAND_SOURCE', 
          message: `Invalid command source. Please ensure the command source is either 'recipe', 'CPA', or 'JouleTrack'.`,
          statusCode: 400
        })
      }
    }


    }
};

function getComponentSourceCommand() {
  return [
    {
      displayName: "JouleTrack",
      value: "jouletrack",
    },
    {
      displayName: "Recipe",
      value: "recipe",
    },
    {
      displayName: "CPA",
      value: "cpa",
    },
  ];
}

function getCommandState(status) {
  const commandState = {
    "description" :  'Feedback: ' + iotFeedbackResponse[status].message,
    "status" : Number(iotFeedbackResponse[status].stateCode),
  };
  return commandState;
}

function getCommandTypeInfo(commandTypeInfo, controlAbbrRelationship) {
  const { commandType, commandValue, deviceId, commandAbbr } = commandTypeInfo;
  const commandTypeObj = {
    "label":"",
    "value": commandValue,
    "type": commandType
  }
  if (commandType.toUpperCase() === 'BIT') {
    const {left, right} = controlAbbrRelationship.controlProperty
    if (left.deviceId == deviceId && commandAbbr == left.commandAbbr) {
      commandTypeObj.value = left.value
      commandTypeObj.label = left.label
    } else if (right.deviceId == deviceId && commandAbbr == right.commandAbbr) {
      commandTypeObj.value = right.value
      commandTypeObj.label = right.label
    }
  } 
  if (commandType.toUpperCase() === 'VIT') {
    commandTypeObj.label = controlAbbrRelationship.controlProperty.label;
  }

  return commandTypeObj
}

function getCommandState(status) {
  const commandState = {
    "description" :  'Feedback: ' + iotFeedbackResponse[status].message,
    "status" : Number(iotFeedbackResponse[status].stateCode),
  };
  return commandState;
}

function getCommandTypeInfo(commandTypeInfo, controlAbbrRelationship) {
  const { commandType, commandValue, deviceId, commandAbbr } = commandTypeInfo;
  const commandTypeObj = {
    "label":"",
    "value": commandValue,
    "type": commandType
  }
  if (commandType.toUpperCase() === 'BIT') {
    const {left, right} = controlAbbrRelationship.controlProperty
    if (left.deviceId == deviceId && commandAbbr == left.commandAbbr) {
      commandTypeObj.value = left.value
      commandTypeObj.label = left.label
    } else if (right.deviceId == deviceId && commandAbbr == right.commandAbbr) {
      commandTypeObj.value = right.value
      commandTypeObj.label = right.label
    }
  } 
  if (commandType.toUpperCase() === 'VIT') {
    commandTypeObj.label = controlAbbrRelationship.controlProperty.label;
  }

  return commandTypeObj
}

function getCPAFluxQuery(controlType) {
  if (controlType != 'VIT') {
    return `
    from(bucket: "{{bucket}}")
    |> range(start: -90d)
    |> filter(fn: (r) => r["_measurement"] == "{{measurement}}")
    |> filter(fn: (r) => r["siteid"] == "{{siteId}}")
    |> filter(fn: (r) => r["componentid"] == "{{componentId}}")
    |> filter(fn: (r) => r["commandsource"] == "joulerecipe")
    |> filter(fn: (r) => r["controlabbr"] == "{{controlAbbr}}")
    |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
    |> filter(fn: (r) => r["journeystep"] == "{{journeyStep}}" and r["status"] != 4)
    |> group(columns: ["controlabbr"], mode:"by")
    |> filter(fn: (r) => r["commandcategory"] == "cpa")
    |> sort(columns: ["_time"], desc: true)
    |> limit(n: {{rows}}, offset: {{offset}})
  `
  }

  return `
  data = from(bucket: "{{bucket}}")
  |> range(start: -90d)
  |> filter(fn: (r) => r["_measurement"] == "{{measurement}}")
  |> filter(fn: (r) => r["siteid"] == "{{siteId}}")
  |> filter(fn: (r) => r["componentid"] == "{{componentId}}")
  |> filter(fn: (r) => r["commandsource"] == "joulerecipe")
  |> filter(fn: (r) => r["controlabbr"] == "{{controlAbbr}}")
  |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
  |> filter(fn: (r) => r["journeystep"] == "{{journeyStep}}" and r["status"] != 4)
  |> group(columns: ["controlabbr"], mode:"by")
  |> filter(fn: (r) => r["commandcategory"] == "cpa")
  |> sort(columns: ["_time"], desc: true)
  |> map(fn: (r) => ({ r with count: 1.0, previousStatus: r.status, previousVal: r.commandvalue}))  
  |> cumulativeSum(columns: ["count"])

  first = data
      |> first(column: "commandvalue") 

  last = data
      |> last(column: "commandvalue")
      |> map(fn: (r) => ({r with commandvalue: r.commandvalue+1.0})) 
      // Drop the last record used for taking the count only

  handleDuplicate = data
    |> difference(nonNegative: false, columns: ["previousVal", "previousStatus"])
    |> filter(fn: (r) => r.previousVal != 0 or r.previousStatus != 0)


  mergedData = union(tables: [first, handleDuplicate, last])
    |> sort(columns: ["_time"], desc: true)  // Ensure proper sorting after the merge
    |> limit(n: {{rows}}, offset: {{offset}})
    |> yield(name: "command-history-recipe-delta")

`
}

function getRecipeFluxQuery(controlType) {
  if (controlType != 'VIT') {
    return `
          from(bucket: "{{bucket}}")
          |> range(start: -90d)
          |> filter(fn: (r) => r["_measurement"] == "{{measurement}}")
          |> filter(fn: (r) => r["siteid"] == "{{siteId}}")
          |> filter(fn: (r) => r["componentid"] == "{{componentId}}")
          |> filter(fn: (r) => r["commandsource"] == "joulerecipe")
          |> filter(fn: (r) => r["controlabbr"] == "{{controlAbbr}}")
          |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
          |> filter(fn: (r) => r["journeystep"] == "{{journeyStep}}" and r["status"] != 4)
          |> group(columns: ["controlabbr"], mode:"by")  
          |> filter(fn: (r) => r["commandcategory"] != "cpa")
          |> sort(columns: ["_time"], desc: true)
          |> limit(n: {{rows}}, offset: {{offset}})
        `
  }




  return `          
                data = from(bucket: "{{bucket}}")
                  |> range(start: -90d)
                  |> filter(fn: (r) => r["_measurement"] == "{{measurement}}")
                  |> filter(fn: (r) => r["siteid"] == "{{siteId}}")
                  |> filter(fn: (r) => r["componentid"] == "{{componentId}}")
                  |> filter(fn: (r) => r["commandsource"] == "joulerecipe")
                  |> filter(fn: (r) => r["controlabbr"] == "{{controlAbbr}}")
                  |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
                  |> filter(fn: (r) => r["journeystep"] == "{{journeyStep}}" and r["status"] != 4)
                  |> group(columns: ["controlabbr"], mode:"by")  
                  |> filter(fn: (r) => r["commandcategory"] != "cpa")
                  |> sort(columns: ["_time"], desc: true)
                  |> map(fn: (r) => ({ r with count: 1.0, previousStatus: r.status, previousVal: r.commandvalue}))  
                  |> cumulativeSum(columns: ["count"])


                  

                first = data
                  |> first(column: "commandvalue") 

                last = data
                  |> last(column: "commandvalue")
                  |> map(fn: (r) => ({r with commandvalue: r.commandvalue+1.0})) 
                  // Drop the last record used for taking the count only

                handleDuplicate = data
                  |> difference(nonNegative: false, columns: ["previousVal", "previousStatus"])
                  |> filter(fn: (r) => r.previousVal != 0 or r.previousStatus != 0)


                mergedData = union(tables: [first, handleDuplicate, last])
                  |> sort(columns: ["_time"], desc: true)  // Ensure proper sorting after the merge
                  |> limit(n: {{rows}}, offset: {{offset}})
                  |> yield(name: "command-history-recipe-delta")
                `
}

function getJouletrackFluxQuery() {
  return `
       from(bucket: "{{bucket}}")
      |> range(start: -90d)
      |> filter(fn: (r) => r["_measurement"] == "{{measurement}}")
      |> filter(fn: (r) => r["siteid"] == "{{siteId}}")
      |> filter(fn: (r) => r["componentid"] == "{{componentId}}")
      |> filter(fn: (r) => r["commandsource"] == "jouletrack")
      |> filter(fn: (r) => r["controlabbr"] == "{{controlAbbr}}")
      |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
      |> filter(fn: (r) => r["journeystep"] == "{{journeyStep}}")
      |> group(columns: ["controlabbr"], mode:"by")
      |> sort(columns: ["_time"], desc: true)
      |> limit(n: {{rows}}, offset: {{offset}})
      `
}



function getDistinctCommandWithCount(commandHistory, commandSource) {
  if (!['recipe', 'cpa'].includes(commandSource)) return commandHistory;
  if (commandHistory.length <= 1) return []
  const distinctCommandWithCount = commandHistory.slice(0, -1);
  for (let i = 0; i < commandHistory.length-1; i++) {
    distinctCommandWithCount[i].count = commandHistory[i+1].count - commandHistory[i].count;
  }
  return distinctCommandWithCount;
}