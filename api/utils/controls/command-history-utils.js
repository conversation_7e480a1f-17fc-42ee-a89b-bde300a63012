const flaverr = require('flaverr');
const Joi = require('joi')

const validateCommandHistoryBuilderObj = (commandObj) => {

    const schema = Joi.object({
        bucket: Joi.string().required(),
        measurement: Joi.string().required()
    }).unknown(true);


    const { error } = schema.validate(commandObj);
    if (error) {
        throw flaverr({
            HTTP_STATUS_CODE: 400,
            message: error.message,
        })
    }
}

const validateCommandHistoryDirector = (commandObj) => {
    const schema = Joi.object({
    siteId: Joi.string().required(),
    source: Joi.string().required(),
    uniqId: Joi.string().required(),
    device_abbr: Joi.string().required(),
    controllerId: Joi.string().required(),
    componentId: Joi.string().required(),
    controlAbbr: Joi.string().required(),
    commandParam: Joi.string().required(),
    deviceId: Joi.string().required(),
    userId: Joi.string().required(),
    dataParamAbbr: Joi.string().required(),
    commandValue: Joi.any().required(), 
    controlType: Joi.string().required(),
    controllerDeviceType:Joi.string().required(),
    controllerHardwareVer:Joi.string().required()
    }).unknown(true);
    const { error } = schema.validate(commandObj);
    if (error) {
        throw flaverr({
            HTTP_STATUS_CODE: 400,
            message: error.message,
        })
    }

}
const throwExceptionInvalidInitialization = () => {
    throw flaverr({
        HTTP_STATUS_CODE: 400,
        message: 'Command history can not be initialized',
    })
}
module.exports = {
    validateCommandHistoryBuilderObj,
    validateCommandHistoryDirector,
    throwExceptionInvalidInitialization
}