module.exports = {
    getConsumptionUnit : function(consumptionParameter){
        let unit;
        let splitConsumptionParameter = consumptionParameter.split("_");
        if(splitConsumptionParameter.length == 2){
          unit = splitConsumptionParameter[1];
          if(unit == "ebkwh") unit = "kwh";
          return unit;
        } else if(splitConsumptionParameter.length == 1){
          unit = splitConsumptionParameter[0];
          if(unit == "ebkwh") unit = "kwh";
          return unit;
        } else return null;
      }
}