
const eneryConsumptionUtils = require("./utils");
const globalHelper = require('../globalhelper');
const moment = require("moment-timezone");
const MOMENT_FORMAT = 'YYYY-MM-DDTHH:mm:00Z';
moment.tz.setDefault("Asia/Kolkata");

module.exports = {
  checkInput: function(body){
    let errors = globalHelper.checkObjectKeys(body, [], '');
    if (errors.length === 0) return { "status": true }
    else return {
      "status": false,
      errors
    }
  },
  generateStartAndEndTime(groupby, start, end){
    // isIn: ['hour','days', 'weeks', 'months', 'custom'],
    let status = false, problems = [];
    // let endTime = moment().format(MOMENT_FORMAT);
    let endTime = moment().subtract(1,"min").format(MOMENT_FORMAT); // Subtracting 1 minute to maintain accuracy as data takes time to populate.
    switch(groupby){
      case "hour":
        startTime = moment().subtract(1, "hour").format(MOMENT_FORMAT);
        break;
      case "days":
        startTime = moment().subtract(1, "days").format(MOMENT_FORMAT);
        break;
      case "weeks":
        startTime = moment().subtract(1, "weeks").format(MOMENT_FORMAT);
        break;
      case "months":
        startTime = moment().subtract(1, "months").format(MOMENT_FORMAT);
        break;
      case "custom":
        if(moment(start).isValid) {
          startTime = moment(start).format(MOMENT_FORMAT);
        } else problems.push("Invalid StartTime sent for groupby custom");
        if(moment(end).isValid) {
          endTime = moment(end).format(MOMENT_FORMAT);
        } else problems.push("Invalid StartTime sent for groupby custom");
        break;
      default:
        problems.push("Invalid groupBy sent. Unable to generate start Time and endTime");
    }
    if(problems.length == 0) status = true;
    return {
      status,
      problems,
      startTime,
      endTime
    }
  },
  formatResponseObject(consumptionPromises, unitPreferences){ 
    let err = [], data = [], preferredUnitForConsumption = unitPreferences.cons, debug = {};
    consumptionPromises.forEach(promiseResult => {
      try {
        debug[promiseResult.value.energyMeterId] = promiseResult.value;
        if (promiseResult.status === 'rejected'){
          const { energyMeterId, message } = promiseResult.value;
          err.push({
            deviceId: energyMeterId,
            errorMessage: message
          });
        }
        if ( promiseResult.status === 'fulfilled' ){
          const { consumption, energyMeterId, consumptionParameter } = promiseResult.value;
          const parameterUnit = eneryConsumptionUtils.getConsumptionUnit(consumptionParameter);
          const formattedConsumption = globalHelper.convertConsumptionToPreferredUnit(consumption, parameterUnit, preferredUnitForConsumption);
          data.push({
            deviceId: energyMeterId,
            consumption: formattedConsumption,
            kw: 0
          })
        }
      } catch (error) {
        sails.log.error("[consumption-page-utils] >> Error formatting response object!");
        sails.log.error(error);
      }
    })
    return{
      err,
      data,
      debug
    };
  },
};