const flaverr = require("flaverr");
const Joi = require("joi");
const moment = require("moment");


const validateTimestamp = (startTime, endTime) => {
   // Ensure start time is before end time and the range does not exceed 90 days
   const startDate = moment(startTime, "YYYY-MM-DD").utcOffset(330);
   const endDate = moment(endTime, "YYYY-MM-DD").utcOffset(330);

   if (!startDate.isValid() || !endDate.isValid()) {
    throw new flaverr({
      errorCode: 'E_ELECTRICAL_POWER_INVALID_DATE',
      statusCode: 400,
      msg: 'Invalid date format. Dates must be in "YYYY-MM-DD" format'
    });
   }

   if (startDate.isAfter(endDate)) {
    throw new flaverr({
      errorCode: 'E_ELECTRICAL_POWER_INVALID_DATE',
      statusCode: 400,
      msg: 'Start date must be before end date.'
    });
   }

   const duration = moment.duration(endDate.diff(startDate)).asDays();
   if (duration > 90) {
    throw new flaverr({
      errorCode: 'E_ELECTRICAL_POWER_INVALID_TIME_RANGE',
      statusCode: 400,
      msg: 'The time range must not exceed 90 days.'
    });
   }
}

const validateConsumptionPayload = ({
  deviceId,
  deviceClass,
  abbr,
  startTime,
  endTime,
  siteId
 }) => {

  const schema = Joi.object({
    deviceId: Joi.alternatives().try(Joi.number(), Joi.string()).required(),
    deviceClass: Joi.string().valid('device', 'component').required(),
    abbr: Joi.array().items(Joi.string()).min(1).required(),
    startTime: Joi.date().required(),
    endTime: Joi.date().required(),
    siteId: Joi.string().required()
  });
  const { error } = schema.validate({
    deviceId,
    deviceClass,
    abbr,
    startTime,
    endTime,
    siteId
   });

  if (error) {
    throw new flaverr({
      errorCode: 'E_ELECTRICAL_POWER_INVALID_REQUEST',
      statusCode: 400,
      msg: error.message
    });
  }

  validateTimestamp(startTime, endTime);

}

const validateConsumptionByDeviceIds = ({
  deviceIds,
  deviceClass,
  abbr,
  startTime,
  endTime,
  siteId
 }) => {
  const schema = Joi.object({
    deviceIds: Joi.array().min(1).required(),
    deviceClass: Joi.string().valid('device', 'component').required(),
    abbr: Joi.array().items(Joi.string()).min(1).required(),
    startTime: Joi.date().required(),
    endTime: Joi.date().required(),
    siteId: Joi.string().required(),
  });
  const { error } = schema.validate({
    deviceIds,
    deviceClass,
    abbr,
    startTime,
    endTime,
    siteId
   });

  if (error) {
    throw new flaverr({
      errorCode: 'E_ELECTRICAL_POWER_INVALID_REQUEST',
      statusCode: 400,
      msg: error.message
    });
  }

  validateTimestamp(startTime, endTime);
}

const validateConsumptionBatch = (nodes) => {
  const schema = Joi.array().items(Joi.object({
    deviceId: Joi.alternatives().try(Joi.number(), Joi.string()).required(),
    deviceClass: Joi.string().valid('device', 'component').required(),
    abbr: Joi.array().items(Joi.string()).min(1).required(),
    startTime: Joi.date().required(),
    endTime: Joi.date().required(),
    siteId: Joi.string().required(),
  }));
  const { error } = schema.validate(nodes);

  if (error) {
    throw new flaverr({
      errorCode: 'E_ELECTRICAL_POWER_INVALID_REQUEST',
      statusCode: 400,
      msg: error.message
    });
  }
  for (const node of nodes) {
    const {startTime, endTime} = node;
    validateTimestamp(startTime, endTime);
  }
}

module.exports = {
validateConsumptionPayload: validateConsumptionPayload,
validateConsumptionByDeviceIds,
validateConsumptionBatch
}
