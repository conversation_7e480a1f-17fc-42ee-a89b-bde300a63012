
const eneryConsumptionUtils = require("./utils");
const globalHelper = require('../globalhelper'); 
const moment = require("moment-timezone");
const MOMENT_FORMAT = 'YYYY-MM-DDTHH:mm:00Z';
moment.tz.setDefault("Asia/Kolkata");

module.exports = {
  checkInput: function(body){
    let errors = globalHelper.checkObjectKeys(body, [], '');
    if (errors.length === 0) return { "status": true }
    else return {
      "status": false,
      errors
    }
  },
  generateStartAndEndTime(){
    const startTime = moment().startOf("month").format(MOMENT_FORMAT);
    const endTime = moment().subtract(1,"minute").format(MOMENT_FORMAT);
    return { startTime, endTime };
  },
  formatResponseObject(consumptionPromises, unitPreferences){
    let monthlyConsumption = 0, preferredUnitForConsumption = unitPreferences.cons, errors = [], debug = {};
    consumptionPromises.forEach(promiseResult => {
      try {
        debug[promiseResult.value.energyMeterId] = promiseResult.value;
        if (promiseResult.status === 'rejected'){
          const { energyMeterId, message } = promiseResult.value;
          errors.push({
            deviceId: energyMeterId,
            errorMessage: message
          });
        }
        if ( promiseResult.status === 'fulfilled' ){
          const { consumption, consumptionParameter } = promiseResult.value;
          const parameterUnit = eneryConsumptionUtils.getConsumptionUnit(consumptionParameter);
          const formattedConsumption = globalHelper.convertConsumptionToPreferredUnit(consumption, parameterUnit, preferredUnitForConsumption);
          if(formattedConsumption) monthlyConsumption += formattedConsumption;
        }
      } catch (error) {
        sails.log.error("[consumption-page-utils] >> Error formatting response object!");
        sails.log.error(error);
      }
    })
    return {
      consumption: monthlyConsumption, debug
    }
  },
};