const globalHelpers = require("../globalhelper");

const KNOWN_MODES = ["jouletrack", "joulerecipe"];
const moment = require("moment-timezone");
moment.tz.add("Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6");
moment.tz.setDefault("Asia/Kolkata");

const modeWithLabelMap = new Map([
  ["jouletrack", { key: "jouletrack", label: "Track Mode" }],
  ["joulerecipe", { key: "joulerecipe", label: "Recipe Mode" }],
  ["joulethermostat", { key: "joulethermostat", label: "Thermostat Mode" }],
]);

module.exports = {
  /**
   * Find the mode of particular parameter in mode packet from database(if not found return jouletrack)
   * @param {object} modePacket database packet of mode table.
   * @param {object} modePacket.allModes Object with all the known modes of particular device
   * @param {*} parameter
   */
  getDeviceParameterModeFromModePacket: function (modePacket, parameter) {
    let currentMode, allDeviceModes;

    if (modePacket === undefined) {
      currentMode = "jouletrack";
    } else {
      allDeviceModes = globalHelpers.toJson(modePacket.allModes);
      if (allDeviceModes === undefined) {
        currentMode = "jouletrack";
      } else {
        let parameterMode = allDeviceModes[parameter];
        if (parameterMode === undefined || KNOWN_MODES.indexOf(parameterMode) === -1) {
          currentMode = "jouletrack";
        } else {
          currentMode = allDeviceModes[parameter];
        }
      }
    }

    return currentMode;
  },

  /**
   * @description Retrieves the mode label based on the provided mode.
   * @param {string} mode - The mode value.
   * @returns {Object} The mode object with key and label.
   */
  getModeWithLabel: function (mode) {
    if (!mode || _.isEmpty(mode)) {
      return { key: "jouletrack", label: "Track Mode" };
    }
    return modeWithLabelMap.get(mode);
  },
};
