const Joi = require("joi");
const flaverr = require("flaverr");

const AuditPayloadSchema = Joi.object({
  site_id: Joi.string().required(),
  asset_id: Joi.string().optional(),
  user_id: Joi.string().required(),
  event_name: Joi.string().required(),
  prev_state: Joi.object().pattern(Joi.string(), Joi.any()).allow(null),
  curr_state: Joi.object().pattern(Joi.string(), Joi.any()).allow(null),
  request_id: Joi.string().required(),
  api_end_point: Joi.string().required(),
  timestamp: Joi.string().required(),
});

const validateAuditPayload = (inputs) => {
  const { error } = AuditPayloadSchema.validate(inputs);
  if (error) {
    throw flaverr("E_INPUT_VALIDATION", new Error(error.message));
  }
};
module.exports = {
  validateAuditPayload,
};
