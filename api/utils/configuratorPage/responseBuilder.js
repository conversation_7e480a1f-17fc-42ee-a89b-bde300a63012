const s3Service = require('../../services/s3/s3.public');

module.exports = {
  async dashboardResponseGenerator(rawDashboardData) {
    const DashboardResponseMap = {};
    for (const row of rawDashboardData) {
      if (!DashboardResponseMap.hasOwnProperty(row.systemId)) {
        DashboardResponseMap[row.systemId] = {
          name: row.systemName,
          id: row.systemId,
          _subSystems: {}
        };
      }
      if (row.subsystemId && !DashboardResponseMap[row.systemId]._subSystems.hasOwnProperty(row.subsystemId) ) {
        DashboardResponseMap[row.systemId]._subSystems[row.subsystemId] = {
          id:row.subsystemId,
          icon: row.subsystemIcon && (await s3Service.getSignedUrl(process.env.CONFIGURATOR_BUCKET, row.subsystemIcon)),
          name: row.subsystemName,
          description: row.subsystemDescription,
          _pages: {},
          subsystemOrder: row.subsystemOrder
        };
      }
      if (row.pageId && !DashboardResponseMap[row.systemId]._subSystems[row.subsystemId]._pages.hasOwnProperty(row.pageId)) {
        DashboardResponseMap[row.systemId]._subSystems[row.subsystemId]._pages[row.pageId] = {
          id: row.pageId,
          type: row.pageType,
          name: row.pageName,
          isPublished: row.isPagePublished,
          order: row.pageOrder
        };
      }
    }

    const finalObject = [];
    for (const value of Object.values(DashboardResponseMap)) {

      const _obj = Object.assign({}, value);
      const { _subSystems } = _obj;
      _obj.subSystems = Object.values(_subSystems)
        .sort((a, b) => a.subsystemOrder - b.subsystemOrder)
        .map(subSystem => {
          const { subsystemOrder, ...rest } = subSystem;
          return rest;
        });

      for(const subSystem of _obj.subSystems){
        const {_pages} = subSystem;
        subSystem.pages = Object.values(_pages).sort((a, b) => {
          if ((a.order == 0 && b.order == 0)) {
            return a.id - b.id;
          }
          if (a.order == 0) return 1;
          if (b.order == 0) return -1;
          if (a.order == b.order) {
            return a.id - b.id;
          }
          return a.order - b.order;
        }) || []
        delete subSystem._pages;
      }
      delete _obj._subSystems;
      finalObject.push(_obj);
    }
    return finalObject;
  },
  async buildGraphDetail(rows) {
    const nodes = new Map();
    const links = new Map();
    let graphProperty = {};
    let type = '';
    let name = '';
    let status = 0;
    let id = 0;
    if (rows.length === 0) {
      return {
        nodes: [],
        data: [],
        graphProperty,
        type,
        name,
        status,
        id
      };
    }
    graphProperty = JSON.parse(rows[0].graphProperty || "{}");
    type = rows[0].type;
    name = rows[0].name;
    status = rows[0].status;
    id = rows[0].id;
    for (const row of rows) {
      if (row.deviceId && !nodes.has(row.deviceId)) {
        nodes.set(row.deviceId, {
          deviceId: row.deviceId,
          deviceClass: row.deviceClass,
          deviceType: row.deviceType,
          driverType: row.driverType,
          column: row.column,
          color: row.color,
          offset: row.offset
        });
      }
      if (row.sourceId && row.targetId) {
        const src = await SankeyGraphNodes.findOne({ id: row.sourceId });
        const tar = await SankeyGraphNodes.findOne({ id: row.targetId });

        if (src && tar) {
          const linkKey = `${src.deviceId}-${tar.deviceId}`;
          if (!links.has(linkKey)) {
            links.set(linkKey, { sourceId: src.deviceId, targetId: tar.deviceId, order: row.order });
          }
        }
      }
    }
    return {
      nodes: Array.from(nodes.values()).sort((a, b) => a.column - b.column),
      data: Array.from(links.values()).sort((a, b) => a.order - b.order).map(link => [
        link.sourceId,
        link.targetId,
        Math.floor(Math.random() * 90) + 10 // Placeholder weight
      ]),
      graphProperty,
      type,
      name,
      status,
      id
    };
  }
};
