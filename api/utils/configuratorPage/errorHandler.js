const flaverr = require("flaverr")

const throwExceptionInvalidPageType = (pageId) => {
  throw flaverr('E_INVALID_PAGE_TYPE', new Error(`Invalid page type. Page ID: ${pageId}`))
}

const throwExceptionInvalidPage = (pageId) => {
  throw flaverr('E_INVALID_PAGE', new Error(`Invalid page with id ${pageId}`))
}

const throwExceptionInvalidSubSystemId = (subSystemId) => {
  throw flaverr('E_INVALID_SUBSYSTEM_ID', new Error(`Invalid configurator subsystem ${subSystemId}`))
}

const throwExceptionInvalidSite = (siteId) => {
  throw flaverr('E_INVALID_SITE', new Error(`Site id ${siteId} does not exist`))
}

const throwExceptionInvalidSiteMapping = (siteId) => {
  throw flaverr('E_INVALID_SITE', new Error(`Site id ${siteId} does not mapped to the page`))
}


const throwExceptionInvalidPageId = (pageId) => {
  throw flaverr("E_INVALID_PAGE", new Error('Page does not exist of pageId: ' + pageId));
}

const throwExceptionUniqueElementIdViolates = (deviceId, uiElementType) => {
  const message = `Conflict! An element with the same ElementId for "${uiElementType}" with deviceId "${deviceId}" already exists. Please delete it and create a new one with a unique ID.`
  throw flaverr("E_UNIQUE", new Error(message));
}
module.exports = {
  throwExceptionInvalidPageType,
  throwExceptionInvalidSubSystemId,
  throwExceptionInvalidPage,
  throwExceptionInvalidSite,
  throwExceptionInvalidSiteMapping,
  throwExceptionInvalidPageId,
  throwExceptionUniqueElementIdViolates
}
