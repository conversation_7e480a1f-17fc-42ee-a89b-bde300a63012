const Joi = require('joi');
const flaverr = require('flaverr');
module.exports = {
  validatePageCreateRequest(params) {
    const schema = Joi.object()
      .keys({
        siteId: Joi.string().required(),
        subsystemId: Joi.string().regex(/^[1-9]\d*$/).required(),
        type: Joi.number().valid(1, 2, 3).required(),
        title: Joi.string().trim().regex(/^(?![0-9])[0-9A-Za-z]*$/).max(50).required()
      }).unknown(true);
    const { error } = schema.validate(params);
    if (error) {
      throw flaverr('E_INPUT_VALIDATION', new Error(error.message));
    }
  },
  validateFetchSystem: function (params) {
    const schema = Joi.object()
      .keys({
        siteId: Joi.string()
          .required(),
        pageId: Joi.number()
          .required()
      })
      .unknown(true);
    const {
      value,
      error
    } = schema.validate(params);
    if (error) {
      throw flaverr('INPUT_VALIDATION_ERROR', new Error(error.message));
    } else {
      return value;
    }
  },
  validatePageUpdateRequest(params) {
    const schema = Joi.object()
      .keys({
        siteId: Joi.string()
          .required(),
        subsystemId: Joi.string()
          .regex(/^[1-9]\d*$/)
          .required(),
        pageId: Joi.string()
          .regex(/^[1-9]\d*$/)
          .required(),
        title: Joi.string()
          .trim()
          .max(50)
          .required()
      }).unknown(true);
    const { error } = schema.validate(params);
    if (error) {
      throw flaverr('E_INPUT_VALIDATION', new Error(error.message));
    }
  },

  validateSaveSVGRequest(params) {
    const schema = Joi.object()
      .keys({
        siteId: Joi.string()
          .required(),
        pageId: Joi.number()
          .required(),
      }).unknown(true);
    const { error } = schema.validate(params);
    if (error) {
      throw flaverr('E_INPUT_VALIDATION', new Error(error.message));
    }
  },

  validatePageDeleteRequest(params) {
    const schema = Joi.object()
      .keys({
        siteId: Joi.string()
          .required(),
        subsystemId: Joi.string()
          .regex(/^[1-9]\d*$/)
          .required(),
        pageId: Joi.string()
          .regex(/^[1-9]\d*$/)
          .required()
      }).unknown(true);
    const { error } = schema.validate(params);
    if (error) {
      throw flaverr('E_INPUT_VALIDATION', new Error(error.message));
    }
  },

  validateFetchAllPageRequest(params) {
    const schema = Joi.object()
      .keys({
        siteId: Joi.string()
          .required(),
        subsystemId: Joi.string()
          .regex(/^[1-9]\d*$/)
          .required()
      });
    const { error } = schema.validate(params);
    if (error) {
      throw flaverr('E_INPUT_VALIDATION', new Error(error.message));
    }
  },

  validateCustomConfigRequest(request) {
    const schema = Joi.object({
      siteId: Joi.string()
        .required(),
      pageId: Joi.string()
        .regex(/^[1-9]\d*$/)
        .required(),
      customConfig: Joi.object({
        css: Joi.array()
          .items(
            Joi.object({
              selector: Joi.string()
                .required(),
              styles: Joi.object()
                .pattern(Joi.string(), Joi.alternatives()
                  .try(Joi.string(), Joi.number()))
                .required(),
            }),
          )
          .required(),
      })
        .required(),
    })
      .unknown(true);

    const { error } = schema.validate(request);
    if (error) {
      throw flaverr('E_INPUT_VALIDATION', new Error(error.message));
    }
  },

  validatePublishConfiguratorPageRequest(params) {
    const schema = Joi.object()
      .keys({
        siteId: Joi.string()
          .required(),
        pageId: Joi.string()
          .regex(/^[1-9]\d*$/)
          .required()
      }).unknown(true);
    const { error } = schema.validate(params);
    if (error) {
      throw flaverr('E_INPUT_VALIDATION', new Error(error.message));
    }
  },

  validateUnpublishConfiguratorPageRequest(params) {
    const schema = Joi.object()
      .keys({
        siteId: Joi.string()
          .required(),
        pageId: Joi.string()
          .regex(/^[1-9]\d*$/)
          .required()
      }).unknown(true);
    const { error } = schema.validate(params);
    if (error) {
      throw flaverr('E_INPUT_VALIDATION', new Error(error.message));
    }
  },
  validateSavePageOrderRequest(params) {
    const schema = Joi.object()
      .keys({
        siteId: Joi.string().required(),
        subsystemId: Joi.string().regex(/^[1-9]\d*$/).required(),
        pageIds: Joi.array().items(Joi.number().integer().min(1)).required()
      })
    const { error } = schema.validate(params);
    if (error) {
      throw flaverr('E_INPUT_VALIDATION', new Error(error.message));
    }
  },
};
