module.exports = {
  /**
   * Add some required fields to parameter object of parameter table. For data type
   * parameter add variance, offset etc. In case of control parameter add information
   * about its feedback port.
   * @param {object} param Parameter object of a parameter table
   * @param {object} devFeedbackMap contains 'deviceId_abbr' to 'port details' mapping
   * Basically containing information about feedback ports in a controller
   */
  filterParams: function (param, devFeedbackMap) {
    const {
      filterOldVal,
      filterVariance,
      filterExistence,
      deviceId,
      abbr,
      utilityType,
      driver,
      max,
      min,
      index,
      mulFactor,
      regType,
      offset,
    } = param;
    const properties = {
      driver,
      max,
      min,
      index,
      regType,
      mulFactor,
      offset,
    };
    const filter = {
      'oldVal': filterOldVal,
      'variance': filterVariance,
      'existence': filterExistence,
    };
    param['filter'] = filter;
    param['properties'] = properties;
    if (utilityType === 'command') {
      const key = `${deviceId}_${abbr}`;
      if (devFeedbackMap[key]) {
        param['feedbackPort'] = devFeedbackMap[key];
      }
    }
    delete param.driver;
    delete param.max;
    delete param.min;
    delete param.index;
    delete param.mulFactor;
    delete param.offset;
    delete param.deviceId_abbr;
    delete param.filter_existence;
    delete param.filter_oldVal;
    delete param.filter_variance;
    delete param.siteId;
    delete param.abbr;
    return  param;
  },

  flattenDeviceParameterIterator: function(deviceType, driverType, deviceId, siteId){
    return function(parameter){
      let paramObj = JSON.parse(JSON.stringify(parameter));
      let { filter, abbr, type, properties } = paramObj;
      paramObj["deviceId"] = deviceId;
      if (typeof filter != "undefined") {
        Object.keys(filter).forEach((key) => {
          let val = filter[key];
          paramObj[`filter_${key}`] = val;
        });
      }
      if ( properties ){
        Object.keys(properties).forEach((key)=>{
          paramObj[key]=properties[key];
        });
      }
      paramObj.siteId=siteId;
      paramObj.mode = "jt";
      paramObj.utilityType = type;
      paramObj.deviceId_abbr=`${deviceId}_${abbr}`;
      paramObj.inheritedFrom = `${deviceType}@${driverType}`;
      delete paramObj.filter;
      delete paramObj.properties;
      return paramObj;
    }
  }

};
