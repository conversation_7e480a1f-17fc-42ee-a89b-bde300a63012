
const globalHelper = require('../globalhelper'); 

module.exports = {
  checkInput(body, systemInfo, parentIdExists, layerTypeExists, parentDefinition) {
    let errors = globalHelper.checkObjectKeys(body, ["systemId", "parentId", "layerName", "layerType"], '');

    let { parentId } = body;

    if (!systemInfo) errors.push("System not found. Please enter valid system Id");
    if (parentIdExists) errors.push("Parent Id already exists in system. UI does not allow multiple children types in the hierarchy currently.");
    if (layerTypeExists) errors.push("Layer type already exists in system.");
    if (!parentDefinition && parentId!=0) {
      // Checking if parentId != 0  as root nodes will not have a parent hierarchy in the system.
      errors.push("Parent system definition not found.");
    }

    if (errors.length === 0) return { "status": true }
    else return {
      "status": false,
      errors
    }
  },
};