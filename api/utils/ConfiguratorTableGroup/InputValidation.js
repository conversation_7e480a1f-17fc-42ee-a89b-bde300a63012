const flaverr = require("flaverr");
const Joi = require("joi");

const validateRequestParamsTableGroup = (params) => {
  const schema = Joi.object()
    .keys({
      name: Joi.string().required(),
      pageId: Joi.number().required(),
    })
    .unknown();
  const { error } = schema.validate(params);
  if (error) {
    throw flaverr("E_INPUT_VALIDATION", new Error(error.message));
  }
};

const validateRequestParamsDeleteTableGroup = (params) => {
  const schema = Joi.object().keys({
    tableGroupId: Joi.number().required(),
    pageId: Joi.number().required(),
  });
  const { error } = schema.validate(params);
  if (error) {
    throw flaverr("E_INPUT_VALIDATION", new Error(error.message));
  }
};

const validateRequestParamsUpdateTableGroup = (params) => {
  const schema = Joi.object().keys({
    pageId: Joi.string()
      .regex(/^[1-9]\d*$/)
      .required(),
    tableGroupId: Joi.string()
      .regex(/^[1-9]\d*$/)
      .required(),
    name: Joi.string().trim().max(50).required(),
  });
  const { error } = schema.validate(params);
  if (error) {
    throw flaverr("E_INPUT_VALIDATION", new Error(error.message));
  }
};

const throwExceptionInvalidPageId = (pageId) => {
  throw flaverr("E_INVALID_PAGE", new Error("Page does not exist for pageId: " + pageId));
};

const throwExceptionInvalidPageType = (pageId) => {
  throw flaverr("E_INVALID_PAGE_TYPE", new Error("Invalid Page type for pageId: " + pageId));
};

const throwExceptionInvalidTableGroup = (pageRefId, tableGroupId) => {
  throw flaverr(
    "E_INVALID_TABLE_GROUP",
    new Error(`Invalid table group for pageId:${pageRefId} and tableGroupId: ${tableGroupId}`),
  );
};

const throwExceptionOnlyTableGroupExist = (pageRefId, tableGroupId) => {
  throw flaverr(
    "E_INVALID_TABLE_GROUP",
    new Error(
      `There is only one table group available for the specified page ID:${pageRefId} and tableGroup ID: ${tableGroupId} exist and tt cannot be deleted.`,
    ),
  );
};

const throwExceptionNoTableGroupExist = (pageRefId, tableGroupId) => {
  throw flaverr(
    "E_INVALID_TABLE_GROUP",
    new Error(
      `Table groups does not exist for pageId:${pageRefId} and tableGroupId: ${tableGroupId}.`,
    ),
  );
};

module.exports = {
  validateRequestParamsTableGroup,
  throwExceptionInvalidPageType,
  throwExceptionInvalidPageId,
  throwExceptionInvalidTableGroup,
  validateRequestParamsDeleteTableGroup,
  throwExceptionInvalidTableGroup,
  validateRequestParamsUpdateTableGroup,
  throwExceptionOnlyTableGroupExist,
  throwExceptionNoTableGroupExist,
};
