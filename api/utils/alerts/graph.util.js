const moment = require('moment');
const flaverr = require('flaverr');
const Joi = require('joi');
module.exports = {
  validateAlertSquareWaveGraph: function(inputs) {
    const analyticsValidationSchema = Joi.object({
      siteId: Joi.string().required(),
      startTime: Joi.string()
        .isoDate()
        .required()
        .description("Start time in date format (ISO 8601)"),
      endTime: Joi.string()
        .isoDate()
        .required()
        .description("End time in date format (ISO 8601)"),
      alertId: Joi.number()
        .required()
        .description("AlertId"),
    });

    const { error } = analyticsValidationSchema.validate(inputs);
    if (error) {
      throw flaverr({
        code: 'E_INPUT_PARAMS',
        msg: error.message,
        statusCode: 400
      })
    }
    const {startTime, endTime} = inputs;
    if (!moment(startTime).isValid()) {
      throw flaverr({
        code: 'E_INVALID_DATE_TIME',
        msg: 'Please enter valid startTime',
        statusCode: 400
      })
    }
    if (!moment(endTime).isValid()) {
      throw flaverr({
        code: 'E_INVALID_DATE_TIME',
        msg: 'Please enter valid endTime',
        statusCode: 400
      })
    }

    if (moment(endTime).isBefore(startTime)) {
      throw flaverr({
        code: 'E_INVALID_DATE_TIME',
        msg: 'Start time must be greater then end time.',
        statusCode: 400
      })
    }
  },
  validateTimeWindow: function(startTime, endTime, utcOffset) {
    const startTimeWithMoment = moment(startTime).utcOffset(utcOffset);
    const endTimeWithMoment = moment(endTime).utcOffset(utcOffset);
    if (endTimeWithMoment.diff(startTimeWithMoment, 'days') > 90) {
      throw flaverr({
        code: 'E_INVALID_TIME_WINDOW',
        msg: 'Time window limit exceeded 90 days.',
        statusCode: 400
      })
    }
  },
fillTimeFrameWindow: function({startTime, endTime, record, value}) {
  const timeFrameResult = [];
  let currentTime = moment(startTime);
  let end = moment(endTime);

  if (record.length) end = moment(record[0][0]);

  while (currentTime.isBefore(end)) {
    timeFrameResult.push([currentTime.unix()*1000, value]);
    currentTime.add(1, 'minute');
  }
  let i =0;
  while(record.length && record[i][1] == null) {
    record[i]._value = value;
    ++i;
  }

  const result = timeFrameResult.concat(record);
  return  result;
},
}