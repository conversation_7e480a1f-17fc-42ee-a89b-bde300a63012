/**
 * Utility functions for alert subscription validation and processing
 */

const Joi = require('joi');
const flaverr = require('flaverr');

const schemas = {
  channels: Joi.array()
    .items(Joi.string().valid('email', 'sms', 'whatsapp'))
    .min(1)
    .default(['email']),

  subscriptionInputs: Joi.object({
    siteId: Joi.string().trim().required(),
    alertId: Joi.number().required(),
    subscriberId: Joi.string().trim().required()
  }),

  pauseInputs: Joi.object({
    siteId: Joi.string().trim().required(),
    alertId: Joi.number().required(),
    subscriberId: Joi.string().trim().required(),
    pausedTill: Joi.date().iso().greater('now').allow(null)
  })
};

module.exports = {
  /**
   * Validates channels input for alert subscription
   * @param {Array} channels - Array of notification channels
   * @returns {Array} - Returns validated channels array
   */
  validateChannels(channels) {
    const { error, value } = schemas.channels.validate(channels);
    if (error) {
      throw flaverr('E_BAD_REQUEST', new Error(error.details[0].message));
    }
    return value;
  },

  /**
   * Validates subscription inputs
   * @param {Object} inputs - Subscription inputs
   * @returns {Object} - Returns validated inputs object
   */
  validateSubscriptionInputs(inputs) {
    const { error, value } = schemas.subscriptionInputs.validate(inputs, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      throw flaverr('E_BAD_REQUEST', new Error(error.details.map(d => d.message).join(', ')));
    }
    return value;
  },

  /**
   * Validates pause inputs
   * @param {Object} inputs - Pause inputs
   * @returns {Object} - Returns validated inputs object
   */
  validatePauseInputs(inputs) {
    const { error, value } = schemas.pauseInputs.validate(inputs, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      throw flaverr('E_BAD_REQUEST', new Error(error.details.map(d => d.message).join(', ')));
    }
    return value;
  }
};
