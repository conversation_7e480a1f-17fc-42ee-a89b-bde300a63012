const Joi = require("joi");

module.exports = {
  requestValidator: function (input) {
    const schema = Joi.object({
      deviceId: Joi.string().required(),
      siteId: Joi.string().required(),
      from: Joi.date().optional(),
      to: Joi.date().optional().greater(Joi.ref('from'))
    }).unknown(true);

    const { error } = schema.validate(input);
    if (error) {
      sails.log.error(error);
      return false;
    }
    return true;
  },
};
