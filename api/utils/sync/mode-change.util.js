const globalHelper = require('../globalhelper');

module.exports = {
  /**
   * It removes all the paused recipes from the list of recipes passed
   * @param {Array} recipes An array of recipe info objects 
   */
  removePausedRecipes: (recipes) => {
    return recipes.filter((recipe) => {
      if(recipe.switchOff === '1' || recipe.switchOff === undefined) return false;
      return true;
    });
  },

  /**
   * Given the list of all recipes and mapping of new modes with did.param it forms a packet of all afected recipes
   * @param {Array} recipes List of all recipes fetched from DB
   * @param {Object} modes {'123.start': 'jouletrack'}
   */
  getAffectedRecipesMap: (recipes, modes) => {
    let controllerRecipeMap = {};
    for(let recipe of recipes) {
      let actionable = globalHelper.toArray(recipe.actionable) || [];
      for(let action of actionable) {
        action = globalHelper.toJson(action) || {};
        if(action.type === 'action') {
          let didDotParam = `${action.did}.${action.command}`;
          if(didDotParam in modes) {
            recipe.didDotParam = didDotParam;
            if(recipe.runOn in controllerRecipeMap) controllerRecipeMap[recipe.runOn].push(recipe);
            else controllerRecipeMap[recipe.runOn] = [recipe];
          }
        }
      }
    }
    return controllerRecipeMap;
  },

  /**
   * Given the mapping of all controllers with affected recipes and the new modes mapping, it forms the final recipe packet for controller
   * @param {Object} controllerRecipeMap Mapping of each controller to an array containing all affected recipes running on it.
   * @param {Object} modes {'123.start': 'jouletrack'}
   */
  getRecipePacketForNewModes: (controllerRecipeMap, modes) => {
    let newControllerRecipeMap = {};
    for(let ctrlId in controllerRecipeMap) {
      let newRecipeArray = [];
      for (let eachRecipe of controllerRecipeMap[ctrlId]) {
        let newMode = modes[eachRecipe.didDotParam];
        let newRecipePacket = {
          rid: eachRecipe.rid
        };
        switch(newMode) {
          case 'jouletrack':
            newRecipePacket.switchOff = '2';
            break;

          case 'joulerecipe':
            if(eachRecipe.appType === 'thermal') newRecipePacket.switchOff = '2';
            else newRecipePacket.switchOff = '0';
            break;

          case 'thermostat':
            if(eachRecipe.appType === 'thermal') newRecipePacket.switchOff = '0';
            else newRecipePacket.switchOff = '2';
            break;

          default:
            // TODO anything
            break;
        }
        newRecipeArray.push(newRecipePacket);
      }
      newControllerRecipeMap[ctrlId] = newRecipeArray;
    }
    return newControllerRecipeMap;
  }
};
