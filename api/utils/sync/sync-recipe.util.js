module.exports = {

  /**
   * Given the fetched data of all recipes from sync table and list of recipe ID from controller, it filters and forms the packet for controller
   * @param {Array} syncData An array containing the sync table data for all recipes
   * @param {Array} ridList An aray of all the recipe IDs running on a particular controller.
   */
  filterAndFormSyncPacket: (syncData, ridList) => {
    let finalSyncPacket = {};
    for (let eachPacket of syncData) {
      let rid = eachPacket['type'].split('_')[1];
      if (rid === undefined) continue;
      if (ridList.includes(rid)) {
        finalSyncPacket[rid] = {
          switchOff: eachPacket.switchOff
        };
      }
    }
    return finalSyncPacket;
  }
};
