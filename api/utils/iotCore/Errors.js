const flaverr = require('flaverr');
module.exports = {
  throwsExceptionTopicIsRequired: function () {
    throw flaverr({
      code: 'E_IOT_TOPIC_IS_EMPTY',
      message: `IoT Topic can not be empty`,
      HTTP_STATUS_CODE: 400
    });

  },
  throwsExceptionPacketIsRequired: function () {
    throw flaverr({
      code: 'E_PACKET_IS_EMPTY',
      message: `packet can not be empty`,
      HTTP_STATUS_CODE: 400
    });

  },
  throwsExceptionUnableToConnectToService: function (message) {
    throw flaverr({
      code: 'E_JOULETRACK_MQTT_DOWN',
      message: `Unable to communicate to IoT Core Layer - ${message}`,
      HTTP_STATUS_CODE: 502
    });
  },
  throwsExceptionUnhandledJouletrackMqttCall: function () {
    throw flaverr({
      code: 'E_UNABLE_TO_CONNECT',
      message: `Unable to connect to JouleTrack MQTT Microservice. Please communicate with PD Team`,
      HTTP_STATUS_CODE: 500
    });
  },
  throwsExceptionErrorCodeReceivedFromJouleTrackMqttService: function (code, message) {
    throw flaverr({
      code: 'E_REJECT_ERROR_RECEIVED',
      message: `Error received from jouletrack mqtt microservice. Please contact to PD team.-${message}. Code:${code}`,
      HTTP_STATUS_CODE: 503
    });
  }
};
