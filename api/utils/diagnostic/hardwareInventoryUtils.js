const globalHelpers = require("../globalhelper");

module.exports = {
  /**
   * @description Input check for MQTT payload.
   * @param {string} method 
   * @param {string} controllerId 
   * @param {string} hardwareId 
   * @returns {object} status is true if everything is fine. "problems" is an array containing all the problems found.
   */
  checkInput(method, controllerId, hardwareId ){
    let problems = [];
    let status = true;
    if (method === undefined || (method !== "create" && method !== "update")){
      problems.push( "Illegal 'method' value: ", method );
      status = false;
    } else if (controllerId === undefined || typeof(controllerId) != "string"){
      problems.push("controllerId is not defined or is not a string.")
      status = false;
    } else if (hardwareId === undefined || typeof(hardwareId) != "string"){
      problems.push("hardwareId is not defined or is not a string.")
      status = false;
    }
    return { problems, status };
  }
};
