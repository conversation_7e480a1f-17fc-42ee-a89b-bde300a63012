const globalHelpers = require("../globalhelper");

module.exports = {
  /**
   *
   * @param {Object} versionDetails version details from DB
   * @returns {Object} formattedVersion
   */
  formatVersions(versionDetails, operation) {
    let formattedVersion = {
      version: versionDetails.version,
      git: {},
      docker: {},
      operation,
    };

    for (let [key, value] of Object.entries(versionDetails)) {
      value = globalHelpers.toJson(value);

      if (value && value.type == "docker") {
        formattedVersion[value.type][value.name] = value.version;
      }

      if (value && value.type == "git") {
        formattedVersion[value.type][value.name] = value.version;
      }
    }
    return formattedVersion;
  },
  /**
   *
   * @param {String} deviceId
   * @returns String
   * @todo prepare publish topic for operation: versioncheck
   */
  preparePublishTopic(deviceId) {
    return `m2/response/${deviceId}/diagnostics`;
  },
};
