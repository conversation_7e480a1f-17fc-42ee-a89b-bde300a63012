module.exports = {
  /**
   * Create a map of siteId and its user role on that site.
   * @param {array} userPreferences Array of Userprefence object from usersitemap table
   */
  getSiteIdRoleObjectFromUserPreferences: (userPrefernces) => {
    let siteIdRoleObj = {};

    userPrefernces.forEach((userPrefernce) => {
      let role = userPrefernce['role'];
      let siteId = userPrefernce['siteId'];
      siteIdRoleObj[siteId] = role;
    });

    return siteIdRoleObj;
  },
};
