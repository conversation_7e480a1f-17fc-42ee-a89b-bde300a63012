const userUtils = require('./utils');
const globalhelper = require('../globalhelper');

module.exports = {
  createInitialUserInfoObject: (inputs) => {
    let { email, name, designation, phone, userOrganization, policiesGroup, userId = false, defaultSite = false , password} = inputs;
    const validNameRegex = /^(?!\s*$)[a-zA-Z0-9\s-]+$/;
    if(!validNameRegex.test(name)) return { problems: ['Invalid name, no special characters allowed'] };
    if (!globalhelper.isEmail(email)) return { problems: ['Invalid Email'] };
    if (globalhelper.isNullish(policiesGroup)) return { problems: ['User doesn\'t have a policy.'] };
    if (!userId) userId = email;
    else if (userId !== email) return { problems: ['User ID and Email did not match'] };
    /**get the first site from the available sites*/
    if (!defaultSite) defaultSite = Object.keys(policiesGroup)[0];
    name = name.trim();
    password = password || userUtils.getDefaultPassword();
    let notify = [], accountable = [];
    let userInfoObject = {
      email,
      name,
      designation,
      phone,
      userId,
      userOrganization,
      policiesGroup,
      password,
      defaultSite,
      notify,
      accountable
    };
    return userInfoObject;
  },
};
