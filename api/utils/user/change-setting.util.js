const flaverr = require("flaverr");

module.exports = {
  getPersonSiteRoleOrgMapping: function(newRoles) {
    let personSiteRoleMapping = {};
    let personOrganizationMapping = {};

    if (newRoles.constructor.name !== 'Array') return { problems: ['Data packet should be of type array'] };

    for (let eachObject of newRoles) {
      let { userId, siteId, role, userOrganization } = eachObject;
      if (!userId || !siteId || !role || !userOrganization) return { problems: ['Required keys are missing from the objects'] };
      if (!(userId in personSiteRoleMapping)) {
        personSiteRoleMapping[userId] = {};
        personOrganizationMapping[userId] = userOrganization;
      }
      personSiteRoleMapping[userId][siteId] = role;
    }

    return { personSiteRoleMapping, personOrganizationMapping };
  },
  /**
   * @description validate inputs
   * @param {*} inputs 
   * @returns {Array}
   */
  validateInputs: function(inputs) {
    const err = []
    for (const key of Object.keys(inputs)) {
      if (!inputs[key]) {
        err.push(`${key}`)
      }
    }
    return err;
  }
};
