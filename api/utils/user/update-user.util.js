const Joi = require("joi");
const flaverr = require("flaverr");

module.exports = {
  validateUpdateUserInput(inputs) {
    const schema = Joi.object({
      email: Joi.string().email(),
      name: Joi.string().pattern(/^(?!\s*$)[a-zA-Z0-9\s-]+$/),
      designation: Joi.string(),
      phone: Joi.string().pattern(/^[0-9]+$/),
      userId: Joi.string(),
      userOrganization: Joi.string().required(),
      defaultSite: Joi.string(),
    }).unknown(true);

    const { error } = schema.validate(inputs);
    if (error) {
      throw flaverr("E_INPUT_VALIDATION", new Error(error.message));
    }
  },
};
