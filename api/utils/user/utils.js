const moment = require('moment-timezone');
moment.tz.add(
	'Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6'
);
moment.tz.setDefault('Asia/Kolkata');
const globalHelper = require('../globalhelper');

const DEFAULT_DJ_NOTIF = {
  'JouleRecipe': '0',
  'nudge': '0'
};
const DEFAULT_MAIL_CONFIG = {
  'JouleRecipe': '0',
  'nudge': '0'
};
const DEFAULT_MSG_CONFIG = {
  'JouleRecipe': '0',
  'nudge': '0'
};
const DEFAULT_DJ = {

  'JouleRecipe': '0',
  'nudge': '0'
};
const DEFAULT_UNIT = {
  'temperature': 'degC',
  'delTemperature': 'delC',
  'pressure': 'kPa',
  'length': 'm',
  'cons': 'kvah'
};
const DEFAULT_PASSWORD = 'smartjoules123';
const VALID_KEYS_IN_POLICY = {
  policyPage: ['displayName', 'pageView', 'subHeadings'],
  subHeadings: ['displayName', 'policies'],
  policies: ['displayName', 'routePolicyMap', 'hasAccess']
};
const DEFAULT_ROLE = 'Joule Fellows';

module.exports = {
  /**
   * Returns the default password
   * @returns {string} Default password
   */
  getDefaultPassword: () => {
    return DEFAULT_PASSWORD;
  },

  /**
   * Returns the default role
   * @returns {string} Default role
   */
  getDefaultRole: () => {
    return DEFAULT_ROLE;
  },

  /**
   * Adds the default preferences to the passed object without overriding the existing values
   * @param {object} allPreferences All preferences inside object
   * @returns {object} object with added default preferences
   */
  addDefaultPreferences: (allPreferences) => {
    allPreferences.dj = {...DEFAULT_DJ, ...allPreferences.dj};
    allPreferences.djNotif = {...DEFAULT_DJ_NOTIF, ...allPreferences.djNotif};
    allPreferences.mailConfig = {...DEFAULT_MAIL_CONFIG, ...allPreferences.mailConfig};
    allPreferences.msgConfig = {...DEFAULT_MSG_CONFIG, ...allPreferences.msgConfig};
    allPreferences.unitPreference = {...DEFAULT_UNIT, ...allPreferences.unitPreference};
  },

  /**
   * Makes the value to false of each key of passed object
   * @param {object} obj Object whose all values are to be changed to false
   * @returns {object} object with all false values
   */
  turnEachKeyToFalse: function(obj) {
    for (let key in obj) {
      obj[key] = false;
    }
    return globalHelper.toJson(obj);
  },

  /**
   * It parses the policy.json file format to generate the flat object of route and policy name
   * @param {object} policies Object containing the nested policies and its info
   * @returns {object} flattened object of route and its policy name if it has access.
   */
  flattenPolicies: function(policies) {
    try {
      let flattenedPolicies = {};
      for (let eachPolicy in policies) {
        let subHeadings = policies[eachPolicy].subHeadings;
        for (let eachSubHeading in subHeadings) {
          let policies = subHeadings[eachSubHeading].policies;
          for (let eachSubheadingPolicy in policies) {
            let routePolicyMap = policies[eachSubheadingPolicy].routePolicyMap;
            if (!policies[eachSubheadingPolicy].hasAccess) routePolicyMap = this.turnEachKeyToFalse(routePolicyMap);
            flattenedPolicies = {...flattenedPolicies, ...routePolicyMap};
          }
        }
      }
      return flattenedPolicies;
    } catch (e) {
      throw 'Invalid Policy Object';
    }
  },

  /**
   * It checks for the existence of each key in the passed object
   * @param {object} obj Object in which keys are to be checked.
   * @param {array} keysArray An array of keys which are to be checked. 
   * @returns {bool} true if all keys exists else false.
   */
  checkForKeysInObject: function(obj, keysArray) {
    for (let eachKey of keysArray) {
      if (eachKey in obj) continue;
      else return false;
    }
    return true;
  },

  /**
   * It parses the policy.json file format and validates whether required keys are present or not.
   * @param {object} policies Object containing the nested policies and its info.
   * @returns {bool} true if its successfully validated else false.
   */
  validatePolicies: function(policies) {
    for (let eachPolicy in policies) {
      let policyData = policies[eachPolicy];
      if (!this.checkForKeysInObject(policyData, VALID_KEYS_IN_POLICY.policyPage)) return false;
      let subHeadings = policyData.subHeadings;
      for (let eachSubHead in subHeadings) {
        let subHeadingData = subHeadings[eachSubHead];
        if (!this.checkForKeysInObject(subHeadingData, VALID_KEYS_IN_POLICY.subHeadings)) return false;
        let innerPolicies = subHeadingData.policies;
        for (let eachInnerPolicy in innerPolicies) {
          let innerPolicyData = innerPolicies[eachInnerPolicy];
          if (!this.checkForKeysInObject(innerPolicyData, VALID_KEYS_IN_POLICY.policies)) return false;
        }
      }
    }
    return true;
  }
};
