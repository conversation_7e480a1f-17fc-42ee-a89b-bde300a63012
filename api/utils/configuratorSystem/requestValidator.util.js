const Joi = require("joi");
const flaverr = require("flaverr");
module.exports = {
  validateCreateNewSystemObject: function (params) {
    const schema = Joi.object()
      .keys({
        systemName: Joi.string()
          .max(100)
          .pattern(/^[a-zA-Z0-9_ -]+$/)
          .required(),
        systemCategoryId: Joi.number().required(),
        description: Joi.string().max(60).allow(""),
        siteId: Joi.string().required(),
      })
      .unknown(true);
    const { value, error } = schema.validate(params);
    if (error) {
      throw flaverr("INPUT_VALIDATION_ERROR", new Error(error.message));
    } else {
      return value;
    }
  },
  validateSaveSvgRequest: function (params) {
    const schema = Joi.object()
      .keys({
        systemId: Joi.number().required(),
        siteId: Joi.string().required(),
      })
      .unknown(true);
    const { value, error } = schema.validate(params);
    if (error) {
      throw flaverr("INPUT_VALIDATION_ERROR", new Error(error.message));
    } else {
      return value;
    }
  },
  validateFetchSystem: function (params) {
    const schema = Joi.object()
      .keys({
        systemId: Joi.number().required(),
        siteId: Joi.string().required(),
        pageId: Joi.number().required()
      })
      .unknown(true);
    const { value, error } = schema.validate(params);
    if (error) {
      throw flaverr("INPUT_VALIDATION_ERROR", new Error(error.message));
    } else {
      return value;
    }
  },
  validateUpdateSystem: function (params) {
    const schema = Joi.object()
      .keys({
        systemName: Joi.string()
          .max(100)
          .pattern(/^[a-zA-Z0-9_ -]+$/),
        systemCategoryId: Joi.number(),
        description: Joi.string().max(500).allow(""),
        systemId: Joi.number().required(),
        siteId: Joi.string().required(),
        icon: Joi.string(),
      })
      .unknown(true);
    const { value, error } = schema.validate(params);
    if (error) {
      throw flaverr("INPUT_VALIDATION_ERROR", new Error(error.message));
    } else {
      return value;
    }
  },
  validateDeleteSystemConfigurator: function (systemId) {
    const schema = Joi.object()
      .keys({
        systemId: Joi.number().required(),
      })
      .unknown(true);
    const { value, error } = schema.validate({ systemId });
    if (error) {
      throw flaverr("INPUT_VALIDATION_ERROR", new Error(error.message));
    } else {
      return value;
    }
  },
  validateDeleteSystemRequest: function (params) {
    const schema = Joi.object()
      .keys({
        systemId: Joi.number().required(),
        siteId: Joi.string().required(),
      })
      .unknown(true);
    const { value, error } = schema.validate(params);
    if (error) {
      throw flaverr("INPUT_VALIDATION_ERROR", new Error(error.message));
    } else {
      return value;
    }
  },
  _validateSystemSVGTagging: function (params) {
    const ALLOWED_OPERATION = ["InsertNewNode", "UpdateNode", "DeleteNode"];
    const ALLOWED_UI_ELEMENT_TYPE = [
      "floatingParameter",
      "floatingControl",
      "floatingLabel",
      "svgAttachedCard",
      "floatingCard",
    ];
    const CommonInsertNodeValidationObj = {
      deviceId: Joi.string().required(), // Unique id generated by FE at their side
      elementId: Joi.string().required(),
      uiElementType: Joi.string().required(),
      offset: Joi.object({
        x: Joi.number().required(),
        y: Joi.number().required(),
        width: Joi.number().required(),
        height: Joi.number().required(),
        top: Joi.number().required(),
        right: Joi.number().required(),
        bottom: Joi.number().required(),
        left: Joi.number().required(),
      }).required(),
    };
    const CommonDeleteNodeValidation = Joi.object({
      assetData: Joi.object({
        id: Joi.number().required(),
      }).unknown(true),
      operation: Joi.string().valid("DeleteNode").required(),
    });
    const ValidationCollection = {
      svgAttachedCard: {
        InsertNewNode: Joi.object({
          assetData: Joi.object({
            ...CommonInsertNodeValidationObj,
            deviceClass: Joi.string().valid("component").required(),
            svgLocationId: Joi.string().required(),
            data: Joi.object({
              parameters: Joi.array().min(1).items(Joi.string()).required(), // Style exist in the component
              style: Joi.object().required().unknown(true),
              position: Joi.object({
                x: Joi.number(),
                y: Joi.number(),
                width: Joi.number(),
                height: Joi.number(),
                top: Joi.number(),
                right: Joi.number(),
                bottom: Joi.number(),
                left: Joi.number(),
              }).required(),
            }).required(),
            controls: Joi.object({
              position: Joi.object({
                x: Joi.number(),
                y: Joi.number(),
                width: Joi.number(),
                height: Joi.number(),
                top: Joi.number(),
                right: Joi.number(),
                bottom: Joi.number(),
                left: Joi.number(),
              }).required(),
              controlAbbr: Joi.array().min(1).items(Joi.string()).required(),
              style: Joi.object().required().unknown(true),
            }).optional(),
          }).required(),
          operation: Joi.string().valid("InsertNewNode").required(),
        }),
        UpdateNode: Joi.object({
          assetData: Joi.object({
            id: Joi.number().required(),
            deviceId: Joi.string().required(),
            deviceClass: Joi.string().valid("component").required(),
            offset: Joi.object({
              x: Joi.number().required(),
              y: Joi.number().required(),
              width: Joi.number().required(),
              height: Joi.number().required(),
              top: Joi.number().required(),
              right: Joi.number().required(),
              bottom: Joi.number().required(),
              left: Joi.number().required(),
            }).optional(),
            data: Joi.object({
              parameters: Joi.array().min(1).items(Joi.string()).required(),
              style: Joi.object().required().unknown(true),
              position: Joi.object({
                x: Joi.number(),
                y: Joi.number(),
                width: Joi.number(),
                height: Joi.number(),
                top: Joi.number(),
                right: Joi.number(),
                bottom: Joi.number(),
                left: Joi.number(),
              }).required(),
            }).optional(),
            controls: Joi.object({
              style: Joi.object().required().unknown(true),
              position: Joi.object({
                x: Joi.number(),
                y: Joi.number(),
                width: Joi.number(),
                height: Joi.number(),
                top: Joi.number(),
                right: Joi.number(),
                bottom: Joi.number(),
                left: Joi.number(),
              }).required(),
              controlAbbr: Joi.array().items(Joi.string()).required(),
            }).optional(),
            uiElementType: Joi.string().valid("svgAttachedCard").required(),
          }).required(),
          operation: Joi.string().valid("UpdateNode").required(),
        }),
        DeleteNode: CommonDeleteNodeValidation,
      },
      floatingCard: {
        InsertNewNode: Joi.object({
          assetData: Joi.object({
            ...CommonInsertNodeValidationObj,
            deviceClass: Joi.string().valid("component").required(),
            data: Joi.object({
              parameters: Joi.array().items(Joi.string()).required(),
              position: Joi.object({
                x: Joi.number(),
                y: Joi.number(),
                width: Joi.number(),
                height: Joi.number(),
                top: Joi.number(),
                right: Joi.number(),
                bottom: Joi.number(),
                left: Joi.number(),
              }).optional(),
              style: Joi.object().optional(),
            }).required(),
            controls: Joi.object({
              position: Joi.object({
                x: Joi.number(),
                y: Joi.number(),
                width: Joi.number(),
                height: Joi.number(),
                top: Joi.number(),
                right: Joi.number(),
                bottom: Joi.number(),
                left: Joi.number(),
              })
                .optional()
                .unknown(false),
              controlAbbr: Joi.array().items(Joi.string()).required(),
              style: Joi.object().optional(),
            }),
            position: Joi.object({
              x: Joi.number().required(),
              y: Joi.number().required(),
              width: Joi.number().required(),
              height: Joi.number().required(),
              top: Joi.number().required(),
              right: Joi.number().required(),
              bottom: Joi.number().required(),
              left: Joi.number().required(),
            })
              .required()
              .unknown(false),
            style: Joi.object().required(),
          }).required(),
          operation: Joi.string().valid("InsertNewNode").required(),
        }),
        UpdateNode: Joi.object({
          assetData: Joi.object({
            id: Joi.number().required(),
            offset: Joi.object({
              x: Joi.number().required(),
              y: Joi.number().required(),
              width: Joi.number().required(),
              height: Joi.number().required(),
              top: Joi.number().required(),
              right: Joi.number().required(),
              bottom: Joi.number().required(),
              left: Joi.number().required(),
            }).optional(),
            deviceId: Joi.alternatives(Joi.number(), Joi.string()).required(),
            deviceClass: Joi.string().valid("component").required(),
            data: Joi.object({
              parameters: Joi.array().items(Joi.string()).required(),
              position: Joi.object({
                x: Joi.number(),
                y: Joi.number(),
                width: Joi.number(),
                height: Joi.number(),
                top: Joi.number(),
                right: Joi.number(),
                bottom: Joi.number(),
                left: Joi.number(),
              }).optional(),
              style: Joi.object().optional(),
            }).optional(),
            controls: Joi.object({
              position: Joi.object({
                x: Joi.number(),
                y: Joi.number(),
                width: Joi.number(),
                height: Joi.number(),
                top: Joi.number(),
                right: Joi.number(),
                bottom: Joi.number(),
                left: Joi.number(),
              })
                .optional()
                .unknown(false),
              style: Joi.object().optional(),
              controlAbbr: Joi.array().items(Joi.string()).required(),
            }).optional(),
            position: Joi.object({
              x: Joi.number().required(),
              y: Joi.number().required(),
              width: Joi.number().required(),
              height: Joi.number().required(),
              top: Joi.number().required(),
              right: Joi.number().required(),
              bottom: Joi.number().required(),
              left: Joi.number().required(),
            })
              .optional()
              .unknown(false),
            style: Joi.object().optional().unknown(true),
            uiElementType: Joi.string().valid("floatingCard").required(),
          }).required(),
          operation: Joi.string().valid("UpdateNode").required(),
        }),
        DeleteNode: CommonDeleteNodeValidation,
      },
      floatingParameter: {
        InsertNewNode: Joi.object({
          assetData: Joi.object({
            ...CommonInsertNodeValidationObj,
            deviceClass: Joi.string().valid("component", "device").required(),
            displayFormat: Joi.object().required(),
            data: Joi.object({
              parameters: Joi.array().items(Joi.string()).min(1).max(2).required(),
              position: Joi.object({
                x: Joi.number(),
                y: Joi.number(),
                width: Joi.number(),
                height: Joi.number(),
                top: Joi.number(),
                right: Joi.number(),
                bottom: Joi.number(),
                left: Joi.number(),
              }).required(),
              style: Joi.object().required(),
            }).required(),
            position: Joi.object({
              x: Joi.number(),
              y: Joi.number(),
              width: Joi.number(),
              height: Joi.number(),
              top: Joi.number(),
              right: Joi.number(),
              bottom: Joi.number(),
              left: Joi.number(),
            }).required(),
            style: Joi.object().required(),
          }).required(),
          operation: Joi.string().valid("InsertNewNode").required(),
        }),
        UpdateNode: Joi.object({
          assetData: Joi.object({
            id: Joi.number().required(),
            deviceId: Joi.string().required(),
            deviceClass: Joi.string().valid("component", "device").required(),
            displayFormat: Joi.object().optional(),
            data: Joi.object({
              parameters: Joi.array().items(Joi.string()).min(1).max(1).required(),
              style: Joi.object().required(),
              position: Joi.object({
                x: Joi.number(),
                y: Joi.number(),
                width: Joi.number(),
                height: Joi.number(),
                top: Joi.number(),
                right: Joi.number(),
                bottom: Joi.number(),
                left: Joi.number(),
              }).required(),
            }).optional(),
            offset: Joi.object({
              x: Joi.number().required(),
              y: Joi.number().required(),
              width: Joi.number().required(),
              height: Joi.number().required(),
              top: Joi.number().required(),
              right: Joi.number().required(),
              bottom: Joi.number().required(),
              left: Joi.number().required(),
            }).optional(),
            uiElementType: Joi.string().valid("floatingParameter").required(),
            position: Joi.object({
              x: Joi.number(),
              y: Joi.number(),
              width: Joi.number(),
              height: Joi.number(),
              top: Joi.number(),
              right: Joi.number(),
              bottom: Joi.number(),
              left: Joi.number(),
            }).optional(),
            style: Joi.object().optional(),
          }).required(),
          operation: Joi.string().valid("UpdateNode").required(),
        }),
        DeleteNode: CommonDeleteNodeValidation,
      },
      floatingControl: {
        InsertNewNode: Joi.object({
          assetData: Joi.object({
            ...CommonInsertNodeValidationObj,
            deviceClass: Joi.string().valid("component").required(),
            displayFormat: Joi.object({
              showName: Joi.boolean(),
              showValue: Joi.boolean(),
            })
              .required()
              .unknown(true),
            controls: Joi.object({
              position: Joi.object({
                x: Joi.number(),
                y: Joi.number(),
                width: Joi.number(),
                height: Joi.number(),
                top: Joi.number(),
                right: Joi.number(),
                bottom: Joi.number(),
                left: Joi.number(),
              }).required(),
              controlAbbr: Joi.array().items(Joi.string()).min(1).max(1).required(),
              style: Joi.object().required(),
            }).required(),
            position: Joi.object({
              x: Joi.number(),
              y: Joi.number(),
              width: Joi.number(),
              height: Joi.number(),
              top: Joi.number(),
              right: Joi.number(),
              bottom: Joi.number(),
              left: Joi.number(),
            }).required(),
            style: Joi.object().required(),
          }).required(),
          operation: Joi.string().valid("InsertNewNode").required(),
        }),
        UpdateNode: Joi.object({
          assetData: Joi.object({
            id: Joi.number().required(),
            deviceId: Joi.string().required(),
            displayFormat: Joi.object().optional(),
            deviceClass: Joi.string().valid("component").required(),
            controls: Joi.object({
              position: Joi.object({
                x: Joi.number(),
                y: Joi.number(),
                width: Joi.number(),
                height: Joi.number(),
                top: Joi.number(),
                right: Joi.number(),
                bottom: Joi.number(),
                left: Joi.number(),
              }).required(),
              controlAbbr: Joi.array().items(Joi.string()).min(1).max(1).required(),
              style: Joi.object().required(),
            }).optional(),
            offset: Joi.object({
              x: Joi.number().required(),
              y: Joi.number().required(),
              width: Joi.number().required(),
              height: Joi.number().required(),
              top: Joi.number().required(),
              right: Joi.number().required(),
              bottom: Joi.number().required(),
              left: Joi.number().required(),
            }).optional(),
            uiElementType: Joi.string().valid("floatingControl").required(),
            position: Joi.object({
              x: Joi.number(),
              y: Joi.number(),
              width: Joi.number(),
              height: Joi.number(),
              top: Joi.number(),
              right: Joi.number(),
              bottom: Joi.number(),
              left: Joi.number(),
            }).optional(),
            style: Joi.object().optional(),
          }).required(),
          operation: Joi.string().valid("UpdateNode").required(),
        }),
        DeleteNode: CommonDeleteNodeValidation,
      },
      floatingLabel: {
        InsertNewNode: Joi.object({
          assetData: Joi.object({
            elementId: Joi.string().required(),
            style: Joi.object().optional(),
            uiElementType: Joi.string().required(),
            label: Joi.string().required(),
            offset: Joi.object({
              x: Joi.number().required(),
              y: Joi.number().required(),
              width: Joi.number().required(),
              height: Joi.number().required(),
              top: Joi.number().required(),
              right: Joi.number().required(),
              bottom: Joi.number().required(),
              left: Joi.number().required(),
            }).required(),
            position: Joi.object({
              x: Joi.number(),
              y: Joi.number(),
              width: Joi.number(),
              height: Joi.number(),
              top: Joi.number(),
              right: Joi.number(),
              bottom: Joi.number(),
              left: Joi.number(),
            }).required(),
          }).required(),
          operation: Joi.string().valid("InsertNewNode").required(),
        }),
        UpdateNode: Joi.object({
          assetData: Joi.object({
            id: Joi.number().required(),
            style: Joi.object().optional(),
            label: Joi.string().optional(),
            offset: Joi.object({
              x: Joi.number().required(),
              y: Joi.number().required(),
              width: Joi.number().required(),
              height: Joi.number().required(),
              top: Joi.number().required(),
              right: Joi.number().required(),
              bottom: Joi.number().required(),
              left: Joi.number().required(),
            }).optional(),
            position: Joi.object({
              x: Joi.number(),
              y: Joi.number(),
              width: Joi.number(),
              height: Joi.number(),
              top: Joi.number(),
              right: Joi.number(),
              bottom: Joi.number(),
              left: Joi.number(),
            }).optional(),
            uiElementType: Joi.string().valid("floatingLabel").required(),
          }).required(),
          operation: Joi.string().valid("UpdateNode").required(),
        }),
        DeleteNode: CommonDeleteNodeValidation,
      },
    };
    const { operation, assetData } = params;

    // Determine which schema to use based on the operation and uiElementType
    if (!ALLOWED_OPERATION.includes(operation)) {
      throw flaverr({
        code: "E_INPUT_VALIDATION",
        msg: `Operation ${operation} not valid`,
        data: params,
      });
    }
    if (!ALLOWED_UI_ELEMENT_TYPE.includes(assetData.uiElementType)) {
      throw flaverr({
        code: "E_INPUT_VALIDATION",
        msg: `asset uiElementType name '${assetData.uiElementType}' not allowed`,
        data: params,
      });
    }
    let validationSchema;
    if (operation === "InsertNewNode") {
      validationSchema = ValidationCollection[assetData.uiElementType].InsertNewNode;
    } else if (operation === "UpdateNode") {
      validationSchema = ValidationCollection[assetData.uiElementType].UpdateNode;
    } else if (operation === "DeleteNode") {
      validationSchema = ValidationCollection[assetData.uiElementType].DeleteNode;
    } else {
      throw flaverr({
        code: "E_INPUT_VALIDATION",
        msg: `Invalid Operation ${operation} Please provide allowed operations ${ALLOWED_OPERATION.join(
          ","
        )}`,
        data: params,
      });
    }

    // Perform the validation
    const { value: validationResult, error } = validationSchema.validate({
      assetData,
      operation,
    });
    if (error) {
      throw flaverr({
        code: "E_INPUT_VALIDATION",
        msg: error.message,
        data: params,
      });
    }
    return validationResult;
  },
  validateInputRequest: function (payload) {
    
    const { 
      subSystemId,
      siteId,
      pageId,
      dataTaggerDetail 
    } = payload

    const schema = Joi.object()
    .keys({
      subSystemId: Joi.number().required(),
      siteId: Joi.string().required(),
      pageId: Joi.number().required(),
      dataTaggerDetail : Joi.array().required().min(1)
    })
    .unknown(true);
    const { error } = schema.validate({ 
      subSystemId,
      siteId,
      pageId,
      dataTaggerDetail 
    });
    if (error) {
      throw flaverr("E_INPUT_VALIDATION", new Error(error.message));
    }
    const allErrorHandler = [];
    dataTaggerDetail.forEach((node, index) => {
      try {
        this._validateSystemSVGTagging(node);
      } catch (e) {
        allErrorHandler.push(`[${index}].${e.msg}`);
      }
    });
    if (allErrorHandler.length) {
      throw flaverr({
        code: "E_INPUT_VALIDATION",
        message: allErrorHandler,
      });
    }
  },
};
