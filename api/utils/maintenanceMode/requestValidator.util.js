const Joi = require('joi');
const flaverr = require('flaverr');

module.exports = {
  validateFetchMaintenanceModeDetailsByAssets(payload) {
    const schema = Joi.object()
      .keys({
        assetList: Joi.array()
          .items(
            Joi.object({
              deviceId: Joi.alternatives()
                .try(Joi.string()
                  .required(), Joi.number()
                  .required()),
              deviceClass: Joi.string()
                .valid('component', 'device')
                .required(),
            })
          )
          .required(),
          siteId: Joi.string().required(),
      });

    const { error } = schema.validate(payload);
    if (error) {
      throw flaverr('E_INPUT_VALIDATION', new Error(error.message));
    }
  },
  validateMaintenanceModeHistoryInputsByAsset(payload) {
    const schema = Joi.object({
      assetId: Joi.alternatives().try(Joi.string().required(), Joi.number().required()),
      siteId: Joi.string().required(),
      startTime: Joi.string().required(),
      endTime: Joi.string().required(),
    });

    const { error } = schema.validate(payload);
    if (error) {
      throw flaverr("E_INPUT_VALIDATION", new Error(error.message));
    }
  },
};
