
const flaverr = require('flaverr');
const moment = require('moment')
const globalHelper = require('../globalhelper');
const utils = require('./utils');
const _ = require('lodash');

module.exports = {
  /**
   * Check if all values in input array integers or not
   * @param {array} inputDatas Input Array
   */
  isInputIntergerArray: function(inputDatas) {

    if ( globalHelper.isNumberArray(inputDatas) ) {
      return true;
    } else {
      throw new Error('Only integer values are allowed as value of weeks/days');
    }
  },

  /**
   * Get exact dateTime from previous month based on weeks and day passed.
   * @param {array} weeks Inetger array of weeks. [0,1,2] means week 1,2 and 3 of a month
   * @param {array} days Inetger array of days. [0,1,2,..6] means day sunday, monday, tuesday and sunday
   * @return array of dates(yyyy-mm-dd hh:mm)
   */
  getDatesFromWeekAndDays: function(weeks, days) {

    let startOfPrevMonth = globalHelper
      .toMoment()
      .subtract(1, 'month')
      .startOf('month');
    let dates = [];

    for (let week of weeks){
      for (let day of days) {
        let dayOfPrevMonth = startOfPrevMonth.day(); // 0 means subday, 1means monday

        if (((week) * 7 + day) - dayOfPrevMonth < 0) {
          // if the day user requested doesnt exist in that week
          // usually say week 0(i.e week 1) starts from tuesday so it will
          // not have monday in it. so this if block will skip adding it to
          // query list
          continue;
        }
        day = day - dayOfPrevMonth; // bcz if user send 0 he meant sunday, but if this month started from tuesday we need to adjust it
        let startTime = startOfPrevMonth.clone().add(week, 'week').add(day, 'day').format();
        dates.push(startTime);
      }
    }
    return dates;
  },

  /**
   * Find the unit preference of user from his unitPreference object on that site. If not exist give sites default unit preference
   * @param {string} siteId Site id. In case user consumption unit is not set, choose sites default unit
   * @param {object} unitPreference _userMeta.unitPreference. Object of all the unit preference of a user on that site.
   */
  getConsumptionUnitFromUnitPreference: function(siteId, unitPreference) {
    let consumptionUnit;
    if (globalHelper.isNullish(unitPreference) ||
      unitPreference.constructor.name !== 'Object') {
      unitPreference = {};
    }
    consumptionUnit = unitPreference['cons'];

    if(utils.isValidConsumptionUnit(consumptionUnit)) {
      return consumptionUnit;
    } else {
      consumptionUnit = utils.getDefaultConsumptionUnit(siteId);
      return consumptionUnit;
    }
  },

  /**
   * Given kw data of main meters on multiple dates, find the total consumption of kw on those dates group by hour
   * @param {array} differentDaysConsumptionData 2D array of kw values from different dates [[{ts, data},{}...],[]..[]]
   * @param {integer} EMCount Count of total number of main meters in whos data is there in differentDaysConsumptionData
   */
  calculateBuildingConsumption: function(
    differentDaysConsumptionData,
    EMCount
  ) {
    let buildingConsumption = {}, minuteWiseSegregatedData = {},
      hourWiseSegregatedData = {};

    for(let consumptionDatas of differentDaysConsumptionData) {
      for(let consumptionData of consumptionDatas) {
        let {timestamp, data: { kw }} = consumptionData;
        if (kw === null) continue;

        if (minuteWiseSegregatedData[timestamp] === undefined) {
          minuteWiseSegregatedData[timestamp] = [kw]
        } else {
          minuteWiseSegregatedData[timestamp].push(kw);
        }
      }
    }
    for (let timestamp in minuteWiseSegregatedData) {
      let EMDataAtTimeStamp = minuteWiseSegregatedData[timestamp];

      if (EMDataAtTimeStamp.length === EMCount) {
        // ALL EM data is present
        let startHourUnix = globalHelper.toMoment(timestamp).startOf('hour').unix() * 1000;
        let EMSum = EMDataAtTimeStamp.reduce((a, b) => a + b, 0)
        if (hourWiseSegregatedData[startHourUnix] === undefined) {
          hourWiseSegregatedData[startHourUnix] = [EMSum]
        } else {
          hourWiseSegregatedData[startHourUnix].push(EMSum);
        }
      }

    }

    for(let unixTSHour in hourWiseSegregatedData){
      let unixTSHourInt = parseInt(unixTSHour);
      let startDay = globalHelper.toMoment(unixTSHourInt)
        .add(1,'day')
        .startOf('day')
        .format();
      let averageHourData = globalHelper.roundNumber(_.meanBy(hourWiseSegregatedData[unixTSHour]), 2);

      if (buildingConsumption[startDay] === undefined){
        buildingConsumption[startDay] = [];
      }

      buildingConsumption[startDay].push([unixTSHour, averageHourData]);
    }

    return buildingConsumption;

  },

  getTimeRangesForBuildingLoad(days, week) {
    if (!week && !days) {
      return [
        { startTime: moment().subtract(1, "day").startOf("day"), endTime: moment().startOf("day") },
        { startTime: moment().subtract(2, "days").startOf("day"), endTime: moment().subtract(1, "day").startOf("day") },
        { startTime: moment().subtract(1, "week").subtract(1, "day").startOf("day"), endTime: moment().subtract(1, "week").startOf("day") }
      ].map(range => ({
        startTime: range.startTime.format("YYYY-MM-DDTHH:mm:ssZ"),
        endTime: range.endTime.format("YYYY-MM-DDTHH:mm:ssZ")
      }));
    }
    if (days && week) {
      return this._generateCustomTimeRanges(days.split(','), week.split(','));
    }
    throw flaverr({
      code: 'E_INVALID_PARAMS',
      message: 'Invalid parameters provided. Either days and week or both should be provided.'
    });
  },
  _generateCustomTimeRanges(days, weeks) {
    const baseDate = moment().subtract(1, "month").startOf("month");
    const firstDayOfWeek = baseDate.day();
    return weeks.flatMap(week =>
      days.map(day => {
        const adjustedDay = day - firstDayOfWeek;
        const startTime = baseDate.clone().add(week, "weeks").add(adjustedDay, "days");
        const endTime = startTime.clone().add(1, "day");
        return {
          startTime: startTime.format("YYYY-MM-DDTHH:mm:ssZ"),
          endTime: endTime.format("YYYY-MM-DDTHH:mm:ssZ")
        };
      })
    ).filter(range => moment(range.startTime).isAfter(baseDate));
  },
};
