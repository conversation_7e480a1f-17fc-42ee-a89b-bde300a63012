
const globalHelpers = require('../globalhelper');

module.exports = {

  /**
   * Given device data array and a param, Find param count where
   * param value is greater than 0.1
   * Currently 0.1 is hardcoded. We basically check if data.kwh > 0.1
   * then increment run mintes 
   * @param {array} deviceDatas Array of objects of deviceData
   * @param {string} param Parameter in deviceData to Average
   */
  countDeviceIsRunning: function (deviceDatas, param) {
    let count = 0;

    for (let data of deviceDatas) {
      let paramValue = data[param];
      if (!globalHelpers.isNullish(paramValue)) {
        if (paramValue > 0.1) {
          count++;
        }
      }
    }
    return globalHelpers.roundNumber(count / 60);
  },
};