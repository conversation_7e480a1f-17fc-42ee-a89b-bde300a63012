const moment = require("moment-timezone");
moment.tz.add(
  "Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6"
);
moment.tz.setDefault("Asia/Kolkata");
const globalHelper = require("../globalhelper");
const Joi = require("joi");

module.exports = {
  checkInput: function (body) {
    let errors = globalHelper.checkObjectKeys(body, [], "");
    if (errors.length === 0) return true;
    else throw new Error(errors);
  },
  requestValidator: function (input) {
    const schema = Joi.object()
      .keys({
        startTime: Joi.date().required(),
        endTime: Joi.date().required(),
        groupBy: Joi.string()
          .required()
          .valid(...["d", "h"]),
        deviceIds: Joi.string().required(),
        params: Joi.string().required(),
      })
      .unknown(true);
    const { error } = schema.validate(input);
    if (error) {
      sails.log.error(error);
      return false;
    } else {
      return true;
    }
  },
  formatRunminuteData: function (data) {
    return data.reduce((acm, curr) => {
      acm[curr.component] = {
        runminutes: [],
      };
      curr.data.forEach((it) => {
        acm[curr.component].runminutes.push([
          moment(it.timestamp).unix() * 1000,
          it._value,
        ]);
      });
      return acm;
    }, {});
  },
  responseObjectFactory: function (params, componentList) {
    const paramMap = params.reduce((acm, curr) => {
      acm[curr] = [];
      return acm;
    }, {});
    const responseObject = componentList.reduce((acm, curr) => {
      acm[curr] = paramMap;
      return acm;
    }, {});
    return responseObject;
  },
};
