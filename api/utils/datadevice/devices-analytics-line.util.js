const moment = require("moment-timezone");
const globalHelper = require("../globalhelper");
const Joi = require("joi");

module.exports = {
  requestValidator: function (input) {
    const schema = Joi.object()
      .keys({
        startTime: Joi.date().required(),
        endTime: Joi.date().required(),
        groupBy: Joi.string()
          .required()
          .valid(...["d", "h", "m"]),
        deviceIds: Joi.array().items(Joi.string()).required(),
        params: Joi.string().required(),
      })
      .unknown(true);
    const { error } = schema.validate(input);
    if (error) {
      sails.log.error(error);
      return false;
    } else {
      return true;
    }
  },
};
