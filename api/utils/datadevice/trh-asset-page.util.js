
const globalHelper = require('../globalhelper'); 
const moment = require('moment');

module.exports = {

  
  /**
   * @description this function generate timestamp for trh in duration of today,lastday and week.
   * all the timestamps are in RFC339 format and in GMT timezone.
   * 
   * @definition  today - today's midnight to now e.g 2021-12-24T00:00:00+05:30 to 2021-12-24T09:37:00+05:30
   *              lastDay - 2021-12-23T00:00:00+05:30 to 2021-12-24T00:00:00+05:30
   *               week - 2021-12-16T00:00:00+05:30 to 2021-12-23T00:00:00+05:30
   * Please note that In influx db start range is inclusive and end time range is exclusive.Thats why instead of 23:59, we'have used
   * 00:00 of next day ensure 23:59 data should be include in our query
   * @returns { object }
   */
  getTimestamp:function(){
    const _periods = ['today','lastDay','week'];
    const timeFormat = "YYYY-MM-DDTHH:mm:00Z";
    let currentTimestamp = moment();

    
    // for today
    const _todayStart = moment(currentTimestamp).startOf('day').format(timeFormat);
    const _todayEnd = moment(currentTimestamp).add(1,'millisecond').format(timeFormat);

    // for yesterday
    const _yesterdayStart = moment(currentTimestamp).subtract(1, 'day').startOf('day').format(timeFormat);
    const _yesterdayEnd = moment(_yesterdayStart).endOf('day').add(1,'millisecond').format(timeFormat);

    // for week
    //NOTE : we are taking last seven days from yesterday, because we already computing the data for yesterday in lastDay period.
    const _weekStart = moment(currentTimestamp).subtract(8,'day').startOf('day').format(timeFormat)
    const _weekEnd = moment(currentTimestamp).subtract(2,'day').endOf('day').add(1,'millisecond').format(timeFormat)

    return {
      today: {start:_todayStart, end:_todayEnd},
      lastDay:{ start:_yesterdayStart, end:_yesterdayEnd },
      week : { start: _weekStart, end: _weekEnd }
    }


  },

  /**
   * @description Sums up all the values in the timeseries data to find total TRh of that time range.
   * @param {array} timeseriesTrhData 2D array of [timestamp, data value]
   * @returns {number} Single reduced value
   */
  reduceTrhValues: function(timeseriesTrhData){
    let reducedValue = timeseriesTrhData.reduce((sum, dataPoint) => {
      let dataValue = dataPoint[1];
      return sum + dataValue;
    }, 0);
    let formattedData = globalHelper.roundNumber(reducedValue);
    return formattedData;
  },

  /**
   * @description Calculates total plant room trh values for different intervals
   * By adding trh values of different components of the same interval.
   * @param {object} trhData {
   *  componentId: {
   *    interval: trhData (number)  
   *  }
   * }
   * @param {array} timeIntervals Array of timeinterval keys to help look up in the dictionary.
   * @returns {object} TotalPlantRoom Data
   */
  calculateTotalPlantRoomTrh: function(trhData, timeIntervals){
    let totalPlantRoomTrhData = {};
    for(let component in trhData){
      let componentTrhData = trhData[component];
      timeIntervals.forEach(interval => {
        if (totalPlantRoomTrhData[interval] == undefined){
          totalPlantRoomTrhData[interval]= componentTrhData[interval];
        } else {
          totalPlantRoomTrhData[interval] += componentTrhData[interval];
        }        
      });
    }
    return totalPlantRoomTrhData;
  },

  /**
   * @description Generates return object as per front end requirement.
   * @param {object} trhData 
   * @param {object} totalPlantRoomTrhData 
   * @param {string} componentId ComponentId of the asset page
   * @returns 
   */
  generateReturnObject: function(trhData, totalPlantRoomTrhData, componentId){
    let returnObject = {
      thisComponent: {
        'lastDay': globalHelper.roundNumber(trhData[componentId]['yesterday'], 0),
        'today': globalHelper.roundNumber(trhData[componentId]['today'], 0),
        'week': globalHelper.roundNumber(trhData[componentId]['lastweek'], 0)
      },
      totalPlantRoom: {
        'lastDay': globalHelper.roundNumber(totalPlantRoomTrhData['yesterday'], 0),
        'today': globalHelper.roundNumber(totalPlantRoomTrhData['today'], 0),
        'week': globalHelper.roundNumber(totalPlantRoomTrhData['lastweek'], 0)
      }
    };
    return returnObject
  },
  /**
   * @description this function is creating a map of component and duration period with trh value
   * @param {*} trhData | raw trh data
   * @param {*} durationMap | duration map
   * @returns | object array 
   */

  trhSiteWiseDataMap:function(trhData,durationMap){
    const data = trhData.reduce((acm,curr,index)=> {     
      const { componentId,trh } = curr
      let _map = Object.assign( {}, durationMap);
      trh.forEach(_trh=> {
        const { duration, data } = _trh
        if(Array.isArray(data) && data.length){
          _map[duration] = data[0]._value;
        }
      })
      acm.push(_map);
      return acm;
    },[])
    
    return data;
  },

  /**
   * @description generating and formatting response for api
   * @param {boolean} isAssetConfiguredAtTr | 
   * @param {object} durationDefaultMap 
   * @param {array} trhData 
   * @param {string} mainComponentId 
   * @returns {object} | {
      thisComponent<optional>:{
        today:0,
        lastDay:0,
        week:0
      },
      totalPlantRoom:{
        today:0,
        lastDay:0,
        week:0
      }
    }
   */
  generateResponseObject:function(isAssetConfiguredAtTr, durationDefaultMap,trhData,mainComponentId){
    const responseObject={
      thisComponent:{
        today:0,
        lastDay:0,
        week:0
      },
      totalPlantRoom:{
        today:0,
        lastDay:0,
        week:0
      }
    };
    Object.assign(responseObject.totalPlantRoom,trhData[0])
    if(isAssetConfiguredAtTr){
      Object.assign(responseObject.thisComponent,trhData[1])
    } else {
      delete responseObject.thisComponent;
    }
    return responseObject;
  }


};