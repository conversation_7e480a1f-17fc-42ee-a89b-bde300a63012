const globalHelpers = require('../globalhelper');
const utils = require('./utils');
const globalhelper = require('../globalhelper');

module.exports = {

  /**
   * Given device data and param to group on, this function will group data using average function
   * @param {array} deviceDatas Array of device data object from deviceData table
   *  between start and end time
   * @param {string} group Group by value.
   *  Enum('hour', 'day', 'minute', 'minutes', 'week', 'month' )
   * @param {array} params List of device params to group data on
   * @param {string} startTime Start time of data
   * @param {string} endTime End time of data
   */
  filterDataForLineGraph(
    deviceDatas, group, params, startTime, endTime,
  ) {
    const plotGraph = utils.groupDevicedataOnParamBetweenStartAndEndTime(
      deviceDatas, group, params, startTime, endTime, this.averageDeviceParamFnc,
    );
    return plotGraph;
  },

  /**
   * Given device data array and a param, perform average of that 'param'
   * @param {array} deviceDatas Array of objects of deviceData
   * @param {string} param Parameter in deviceData to Average
   */
  averageDeviceParamFnc(deviceDatas, param) {
    let value = 0;
    let count = 0;

    for (const data of deviceDatas) {
      const paramValue = data[param];
      if (!globalHelpers.isNullish(paramValue)) {
        count++;
        value += paramValue;
      }
    }
    const average = globalHelpers.roundNumber(value / count, 2);
    return average;
  },

  /**
   * Filter data for heatmap graphs. The output looks like
   * param1 = [
   *    [startDayNumer=0, hourOfDay=0,  value1],
   *    [startDayNumer=0, hourOfDay=1,  value2],
   *    ...
   *    [startDayNumer=0, hourOfDay=23, value3],   <- change day + 1 after 23 hours
   *    [startDayNumer=1, hourOfDay=0,  value4],
   * ]
   *  and value is just average of value in that hour of that param
   * @param {array} deviceDatas Array of device data object from deviceData table
   *  between start and end time
   * @param {string} group Group by value.
   *  Enum('hour', 'day', 'minute', 'minutes', 'week', 'month' )
   * @param {array} params List of device params to group data on
   * @param {string} startTime Start time of data
   * @param {string} endTime End time of data
   */
  filterDataForHeatMap(
    deviceDatas, group, params, startTime, endTime,
  ) {
    const linePlotGraph = utils.groupDevicedataOnParamBetweenStartAndEndTime(
      deviceDatas, group, params, startTime, endTime, this.averageDeviceParamFnc,
    ); // linePlotGraph has line data of params sorted on timestamp

    const heatMapPlotGraph = params.reduce((acc, param) => {
      acc[param] = {
        plot: [], maximum: 0, minimum: 0,
      };
      return acc;
    }, {});
    const startTimeMoment = globalHelpers.toMoment(startTime);

    for (const param in linePlotGraph) {
      const plotData = [];
      const paramDatas = linePlotGraph[param];
      let mininumValue = Infinity; let
        maximumValue = -Infinity;
      for (const timestampDataList of paramDatas) {
        const [timestamp, paramData] = timestampDataList;
        const dayDiffBetweenCurrDataAndStartTime = globalHelpers.toMoment(timestamp).diff(
          startTimeMoment,
          'days',
        );
        const hourOfDay = globalHelpers.toMoment(timestamp).hour();
        plotData.push([dayDiffBetweenCurrDataAndStartTime, hourOfDay, paramData]);
        if (isNaN(paramData) !== true) {
          mininumValue = Math.min(mininumValue, paramData);
          maximumValue = Math.max(maximumValue, paramData);
        }
      }
      if (mininumValue === Infinity) mininumValue = 0;
      if (maximumValue === -Infinity) maximumValue = 0;

      heatMapPlotGraph[param].plot = plotData;
      heatMapPlotGraph[param].minimum = mininumValue;
      heatMapPlotGraph[param].maximum = maximumValue;
    }

    return heatMapPlotGraph;
  },

  /**
   * Filter data for spectral graphs. Spectral of frequency chart is
   * basically just the parameter and its occurances
   * @param {array} deviceDatas Array of device data object from deviceData table between start and end time
   * @param {array} params List of device params to group data on
   * @param {string} startTime Start time of data
   * @param {string} endTime End time of data
   */
  filterDataForSpectralGraph(deviceDatas, params) {
    const frequencyTable = {}; // holds {param: {value, count}... ,  param2}
    params.forEach((param) => frequencyTable[param] = {});

    deviceDatas.forEach((deviceDataPkt) => {
      const { data } = deviceDataPkt;
      for (const param of params) {
        if (globalhelper.isNullish(data[param]) === true) {
          // param doesnt exist in data packet
          continue;
        }
        const paramValue = globalHelpers.roundNumber(data[param], 2);
        if (frequencyTable[param][paramValue] === undefined) {
          frequencyTable[param][paramValue] = 0;
        }
        frequencyTable[param][paramValue] += 1;
      }
    });

    return frequencyTable;
  },

};
