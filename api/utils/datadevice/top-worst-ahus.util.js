
const globalHelper = require('../globalhelper');

module.exports = {

  /**
   * Given device data array and a param, perform average of that 'param'
   * @param {array} deviceDatas Array of objects of deviceData
   * @param {string} param Parameter in deviceData to Average
   * @returns null for no data available at all,
   */
  calculateStandardDeviation: function(deviceDatas, param) {
    let SD = null;

    if (deviceDatas.length === 0) {
      return SD; // no data is coming
    }
    let dataPoints = deviceDatas
      .map(deviceData => deviceData.data[param])
      .filter(dataPoint => {
        if (dataPoint === null) return false;
        else return true;
      });

    if (dataPoints.length === 0) {
      return null; // has ahu attached still no data but other data is coming
    }
    SD = globalHelper.calculateStandardDeviation(dataPoints);
    return SD;
  },

  /**
   * Given a list of ahu ids with there standard deviation find top N ahus which
   * have highest SD and N worst ahus. If a AHU has 0 SD its considered worst, else
   * we sort ahu's on SD and choose top N.
   * @param {array} ahuIdStandardDeviationObjList Array of object with AhuId and its Standard deviation value [{Id, SD}, ...]
   * @param {Integer} N Find N top ahu's and N worst Ahu's from list passed on bases of standard deviation
   */
  getNBestAndWorstAhuIdsOnBasesOfSD: function(ahuIdStandardDeviationObjList, N) {
    let bestAhuIds = [], sortedAhuIdSDObjList = [], worstAhuIds = [];

    sortedAhuIdSDObjList = ahuIdStandardDeviationObjList.sort((a, b) => {
      return b.SD - a.SD;
    });

    for(let index = 0; index < sortedAhuIdSDObjList.length; index++) {
      let ahuIdSDObj = sortedAhuIdSDObjList[index];
      let {SD, deviceId} = ahuIdSDObj;

      if (SD === 0) {
        let worstAhuIdsSDObjList = sortedAhuIdSDObjList.slice(index, index + N);
        worstAhuIdsSDObjList.forEach(ahuIdsSDObj => {
          worstAhuIds.unshift(ahuIdsSDObj.deviceId);
        });

        break;
      } else {
        if (index <= N) {
          bestAhuIds.push(deviceId)
        } else {
          worstAhuIds.push(deviceId);
        }
      }
    }
    bestAhuIds = bestAhuIds.slice(0,N);
    worstAhuIds = worstAhuIds.slice(0, N);
    return {bestAhuIds, worstAhuIds};
  }
};
