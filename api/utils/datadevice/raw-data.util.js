
const globalHelper = require('../globalhelper'); 
const moment = require("moment");
const MOMENT_FORMAT = 'YYYY-MM-DD HH:mm';


module.exports = {
  checkInput: function(body){
    let errors = globalHelper.checkObjectKeys(body, [], '');
    if (errors.length === 0) return { "status": true }
    else return {
      "status": false,
      errors
    }
  },
  formatTimestamps: function(start, end){
    let problems = [], status = false, startTime, endTime;
    if(moment(start).isValid){
      startTime = moment(start).format(MOMENT_FORMAT);
    } else problems.push("Invalid start time stamp");
    if(moment(end).isValid){
      endTime = moment(end).format(MOMENT_FORMAT);
    } else problems.push("Invalid end time stamp");
    if(problems.length == 0) status = true;
    return{
      startTime, endTime, problems, status
    }
  }
};