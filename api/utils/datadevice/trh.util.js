
const globalHelper = require('../globalhelper'); 
const moment = require("moment");

module.exports = {
  /**
   * @description Checks for valid groupBy values.
   * @param {string} groupBy Should be either "hour" or "day"
   * @returns true if no errors found.
   */
  checkInput: function(groupBy){
    let errors = [];
    const possibleGroupBy = ["hour", "day"];
    if (!possibleGroupBy.includes(groupBy))
      errors.push("'groupBy' field should be either 'hour' or 'day'");
    if (errors.length === 0) return true;
    else throw new Error(errors);
  },

  /**
   * @description Converts timestamp from datapoints to Unix timestamp. Incase group by is day, sums up all the
   * averaged TRh values while being grouped by day.
   * @param {array} trhData 2D array returned by "calculateTRH" function in util file.
   * @param {string} groupBy can contain: "hour/ day"
   * @returns {array} 2D array: [[timestamp, TRH value]]. Timestamp will be grouped by "groupBy" passed. Timestamp will be in unix format.
   */
  groupyByCalculations: function(trhData, groupBy){
    let formattedData = [];
    if (groupBy === 'hour'){
      formattedData = trhData.map(dataPoint => {
        const timestamp = dataPoint[0];
        const value = dataPoint[1];
        const unixTimestamp = moment(timestamp).valueOf();
        return [unixTimestamp, value];
      })
    } else if (groupBy === 'day') {
      const momentDayFormat = 'YYYY-MM-DD';
      let groupedByDateData = {};
      trhData.forEach(dataPoint => {
        const timestamp = moment(dataPoint[0]).format(momentDayFormat);
        const value = dataPoint[1];
        if(!groupedByDateData[timestamp]){
          groupedByDateData[timestamp] = [value];
        } else {
          groupedByDateData[timestamp].push(value);
        }
      });
      for (let timestamp in groupedByDateData){
        const dataValues = groupedByDateData[timestamp];
        const sum = dataValues.reduce((previousValue, currentValue) => previousValue + currentValue, 0);
        const unixTimestamp = moment(timestamp).valueOf();
        const roundedSum = globalHelper.roundNumber(sum);
        formattedData.push([unixTimestamp, roundedSum]);
      }
    } else {
      throw new Error('Incorrent "groupBy value"');
    }
    return formattedData;
  },
  /**
   * @description Calculates the total plant room TRh values by adding all TRh values for the same hour from different chillers.
   * @param {object} allComponentData Map of componentId: 2D-TRh-Data. 
   * @returns {array} 2D array: [[timestamp, TRH value]].
   */
  calculateTotalPlantRoomTRH: function(allComponentData){
    let timestampDataMap = {}, totalPlantRoomData = [];
    Object.keys(allComponentData).forEach(componentId =>{
      const componentTRHData = allComponentData[componentId];
      componentTRHData.forEach(dataPoint =>{
        const timestamp = dataPoint[0];
        const value = dataPoint[1];
        if (!timestampDataMap[timestamp]){
          timestampDataMap[timestamp] = [value];
        } else {
          timestampDataMap[timestamp].push(value);
        }
      });
    });
    Object.keys(timestampDataMap).forEach(timestamp =>{
      const dataValues = timestampDataMap[timestamp];
      const numericalTimestamp = Number(timestamp);
      const sum = dataValues.reduce((previousValue, currentValue) => previousValue + currentValue, 0);
      const roundedSum = globalHelper.roundNumber(sum);
      totalPlantRoomData.push([numericalTimestamp, roundedSum]);
    });
    return totalPlantRoomData;
  },

  /**
   * @description Rounds of all values in the 2D arrays to a whole number.
   * @param {object} returnObject Return Object
   * @returns {object} formatted return object
   */
  formatReturnObject: function(returnObject){
    Object.keys(returnObject).forEach(componentId => {
      let dataArray = returnObject[componentId];
      dataArray.forEach(dataPoint => {
        dataPoint[1] = globalHelper.roundNumber(dataPoint[1], 0);
      });
    });
    return returnObject;
  },
  
  totalPlantRoomTrhCalculator:function(queryData){
    const totalPlantRoom={}
    const componentWiseTrhResult = queryData.reduce((acm,curr)=>{
      const {componentId, trh} = curr
      const _graphData = trh.reduce((acm,curr)=>{
        const {_time,_value} = curr;
        let _timestamp = moment(_time).unix()*1000
        acm.push([_timestamp, Number.parseInt(_value)])
        if(totalPlantRoom[_timestamp]){
          totalPlantRoom[_timestamp]+=Number.parseInt(_value);
        } else {
          totalPlantRoom[_timestamp]=Number.parseInt(_value)
        }
        return acm;
      },[])
      acm[componentId]=_graphData;
      return acm;
    },{})
    return { totalPlantRoom }
  },
  formatResponseObject:function( chillerWiseTonnageDelivered, totalPlantRoomTonnageDelivered) {
    const _obj = {
      totalPlantRoom:[]
    }
    const formattedChillerWiseTrh = chillerWiseTonnageDelivered.reduce((acm,curr)=>{
      const {componentId, trh} = curr
      const _graphData = trh.reduce((acm,curr)=>{
        const {_time,_value} = curr;
        let _timestamp = moment(_time).unix()*1000
        acm.push([_timestamp, Number.parseInt(_value)])
        return acm;
      },[])
      acm[componentId]=_graphData;
      return acm;
    },{})
    const formattedTotalPlantRoomTrh = totalPlantRoomTonnageDelivered.reduce((acm,curr)=>{
      const {_time,_value} = curr;
      let _timestamp = moment(_time).unix()*1000
      acm.push([_timestamp, Number.parseInt(_value)])
      return acm;
    },[])


    Object.assign( _obj, formattedChillerWiseTrh );
  _obj.totalPlantRoom = formattedTotalPlantRoomTrh
    return _obj;
  }
};