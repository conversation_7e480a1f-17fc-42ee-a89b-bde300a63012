const globalHelpers = require('../globalhelper');
const moment = require('moment');
module.exports = {
    /**
     * @description Merge and formate the object for response purpose into the dashboard.
     * @param {Object} siteConfig
     * @returns {Object}
     */
    dashboardConsumptionData: function(siteConfig) {
        const {
            lastTimestamp,
            weeklyConsumptionData, 
            hourlyWiseConsumptionData, 
            lastDayConsumption,
        } = siteConfig;
        const {finalLoad:hourlyLoadPattern, currentLoad, hrCons: hourlyCons} = hourlyWiseConsumptionData;
        const {weekCons, weekConsPattern: weekConsDaily} = weeklyConsumptionData;

        return {
            hCons: Number(hourlyCons.toFixed(3)) || null,
            dCons: Number(lastDayConsumption.toFixed(3)) || null,
            load: currentLoad ||  0,
            loadPattern: hourlyLoadPattern,
            lastTimestamp,
            weekConsPattern: weekConsDaily,
            weekCons: weekCons || null,
            lastTimestamp
        }
    },
    /**
     * @description Build the format of the data taken from influxDB response 
     * with required no of consumption added to the timestamp and further process
     * for hourlyWiseConsumption
     * @param {Array} dashboardRawData
     * @param {String} userPreferredUnit
     * @return {Object}
     */
     filterConsumptionData: function(dashboardRawData, userPreferredUnit) {
        const timestampDataMap = {};
        for (let row of dashboardRawData) {
            if (row._time === '_time') {
                continue;
            }
            const consData = globalHelpers.getConsumptionFromDeviceData(row, userPreferredUnit);
            timestampDataMap[row._time] = {
                load: [parseInt(row.kw), 1],
                consData: [consData, 1]
            }
        }
        return timestampDataMap;
    },
    /**
     * @description To calculate the total load and consumption in an overall hour
     * @param {Object} timestampMappedData 
     * @returns 
     */
    hourlyWiseConsumption: function(hourlyLoadPattern, hourlyConsumption, currentLoad) {
        const finalLoad = [];
        for (let load of hourlyLoadPattern) {
            const startHourTimestamp = moment(load._time).startOf('h').unix()*1000;
            finalLoad.push([
                startHourTimestamp,
                Math.round(Number(load.kw))
            ])
        }
        return {
            finalLoad,
            currentLoad,
            hrCons: hourlyConsumption
        } 
    },
    /**
     * @description Filter the raw data for weekly consumption
     * @param {Object} rawData influxDB formatted raw data
     * @param {String} userPreferredUnit
     * @return {Object} filteredData
     */
    weeklyConsumptionData: function (rawData, userPreferredUnit) {
        if (_.isEmpty(rawData)) {
            sails.log.info('Site data not present');
            return {
                weekCons: 0,
                weekConsPattern: []
            }
        }

        let weekConsPattern = [];
        let totalWeeklyConsumption = 0;

        for (let i = 0; i < rawData.length; i++) {
            if (rawData[i]._time === '_time') {
                continue;
            }
            const consumption = this.cleanedActualCons(rawData[i], userPreferredUnit)
            totalWeeklyConsumption += typeof consumption === 'number' && consumption;
            const day = moment(rawData[i]._time).unix()*1000;
            const dayConsumption = [day, consumption];
            weekConsPattern.push(dayConsumption)
        }
        return {
            weekCons: Number(totalWeeklyConsumption.toFixed(3)),
            weekConsPattern: weekConsPattern
        }
    },
    /**
     * @description To filter the data of influxDB by mapping _field and _value in a key and pair
     * @param {Array} fluxQueryRawData 
     * @returns {Array} finalData
     */
    filterInfluxDbRawData: function (fluxQueryRawData) {
        const finalData = [];
        for (const tsRow of fluxQueryRawData) {
            finalData.push({
                _time: tsRow._time,
                 kw: tsRow._value
            })
        }
        return finalData;
    },
     /**
   * @description Get the cleaned actual power for daily consumption.
   * @param {Object} consRowData
   * @param {Object} userPreferredUnit
   * @return {String} actualPower
   */
    cleanedActualCons: function(consRowData, userPreferredUnit) {
        if (userPreferredUnit === 'kwh') {
            if (consRowData['corrected_kwh']) {
                return Number(consRowData['corrected_kwh']);
            }
            if (consRowData['corrected_ebkwh']) {
                return Number(consRowData['corrected_ebkwh']);
            } 
            return 0;
        }
        if (userPreferredUnit === 'kvah') {
            if (consRowData['corrected_kvah']) {
                return Number(consRowData['corrected_kvah']);
            }
            if (consRowData['corrected_ebkvah']) {
                return Number(consRowData['corrected_ebkvah']);
            } 
            return 0;
        }
        return null;
  },
  lastDayConsumption: function(consumptionData, userPrefConsUnit) {
    const consObj = {};
    for (const cons of consumptionData) {
        consObj[cons._field] = cons._value;
    }
    let lastDayConsumption;
    if (userPrefConsUnit === 'kvah') {
        if (consObj['corrected_kvah']) {
            lastDayConsumption = consObj['corrected_kvah']
        }

        if (consObj['corrected_ebkvah']) {
            lastDayConsumption = consObj['corrected_kvah']
        }
    }
    if (userPrefConsUnit === 'kwh') {
        if (consObj['corrected_kwh']) {
            lastDayConsumption = consObj['corrected_kwh']
        }

        if (consObj['corrected_ebkwh']) {
            lastDayConsumption = consObj['corrected_ebkwh']
        }
    }
    return Number(lastDayConsumption);
},

mergePatternBasedCons: function(sourcePatternCons, targetPatternCons) {
    if (_.isEmpty(sourcePatternCons)) return targetPatternCons;
    sourcePatternCons = sourcePatternCons.reduce((acc, sr) => {
        acc[sr[0]] = sr[1]
        return acc;
    }, {})
    targetPatternCons = targetPatternCons.reduce((acc, tr) => {
        acc[tr[0]] = tr[1];
        return acc;
    }, {})
    for (const key of Object.keys(sourcePatternCons)) {
        if (targetPatternCons[key]) {
            sourcePatternCons[key] += targetPatternCons[key]
        }
    }
    const consPattern = []
    for (const [key, value] of Object.entries(sourcePatternCons)) {
        consPattern.push([Number(key), Number(value)])
    }
    return consPattern;
},
/**
 * 
 * @param {Array} mergeDiffDeviceTypeCons 
 * @return {Object} responseObject
 */
mergeDataDeviceType: function(mergeDiffDeviceTypeCons) {
    const responseObject = mergeDiffDeviceTypeCons.reduce((acc, device) => {
        if (!acc.lastTimestamp) {
            acc.lastTimestamp = device.hasOwnProperty('lastTimestamp') && device.lastTimestamp;
        }
        acc.hCons += device.hCons && device.hCons;
        acc.dCons += device.dCons && device.dCons;
        acc.load += device.load && device.load;
        acc.weekCons += device.weekCons && device.weekCons;
        acc.loadPattern = this.mergePatternBasedCons(acc.loadPattern, device.loadPattern);
        acc.weekConsPattern = this.mergePatternBasedCons(acc.weekConsPattern, device.weekConsPattern);
        return acc;
    },{
        hCons:0,
        dCons: 0,
        load: 0,
        loadPattern: [],
        weekConsPattern: [],
        weekCons: 0,
        lastTimestamp: null,
    })

    for (const consParameter of Object.keys(responseObject)) {
        if (responseObject[consParameter] == 0) {
            responseObject[consParameter] = 'NA'
        }
    }
    
    return responseObject;
},
getDeviceTypeAbbr: function(type) {
    const deviceTypeField = {
        'nf29': ['corrected_kwh', 'corrected_kvah'],
        'expertPro':['corrected_ebkwh', 'corrected_ebkvah']
    }
    return deviceTypeField[type];
}
  
}