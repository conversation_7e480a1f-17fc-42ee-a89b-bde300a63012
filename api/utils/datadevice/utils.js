const globalHelpers = require('../globalhelper');

const moment = require("moment");

const MAX_ALLOWED_DAYS = 180;
const VALID_CONSUMPTION_UNITS = ['kvah', 'kwh'];
const DEFAULT_CONSUMPTION_UNIT = 'kvah';
const GROUP_BY = ['hour', 'day', 'minute', 'minutes', 'week', 'month'];

module.exports = {
  MAX_ALLOWED_DAYS,
  GROUP_BY,


  /**
   * @description Takes timeseries device data of a chiller containing the parameter "tr" in it's "data" packet. Calculates TRh, ie,
   * averages the data for the hour and returns it grouped by the hour.
   * @param {array} deviceData 
   * @returns {array} 2D array [[timestamp, TRh], [timestamp2, TRh]]. Timestamp formatted in string format: YYYY-MM-DD HH
   */
  calculateTRH: function(deviceData){
    let groupedTRData = {};
    const momentFormat = "YYYY-MM-DD HH";
    deviceData.forEach(dataPoint => {
      const timestamp = moment(dataPoint["timestamp"]).format(momentFormat);
      const trValue = dataPoint["data"]["tr"];
      if (globalHelpers.isNullish(trValue)) return; // Skip adding to group if data not present.
      if (!groupedTRData[timestamp]){
        groupedTRData[timestamp] = [trValue];
      } else {
        groupedTRData[timestamp].push(trValue);
      }
    });
    let aggregatedData = [];
    for (let timestamp in groupedTRData){
      const dataValues = groupedTRData[timestamp];
      const totalPoints = dataValues.length;
      const sum = dataValues.reduce((previousValue, currentValue) => previousValue + currentValue, 0);
      const average = sum / totalPoints;
      const roundedAverage = globalHelpers.roundNumber(average);
      aggregatedData.push([timestamp, roundedAverage]);
    }
    return aggregatedData;
  },

  /**
   * Get the default unit for consumption on this site (kvah or kwh)
   * @param {string} siteId Site id
   */
  getDefaultConsumptionUnit: function (siteId) {
    return DEFAULT_CONSUMPTION_UNIT;
  },
  /**
   * Sort the dataPoints according to timestamp and return the nearest datapoint to provided timestamp
   * @param {string} timestamp TimeStamp in YYYY-mm-dd/unix format to sort datapoints on
   * @param {array} dataPoints Array of data points from deviceData table
   */
  getNearestDataToTimestamp: function (timestamp, dataPoints) {
    let tsMoment = globalHelpers.toMoment(timestamp);

    dataPoints
      .map((dataPoint) => {
        dataPoint['sortKey'] = Math.abs(
          globalHelpers.toMoment(dataPoint['timestamp']).diff(tsMoment, 'min')
        );
        return dataPoint;
      })
      .sort((dataPoint1, dataPoint2) => {
        return dataPoint1['sortKey'] - dataPoint2['sortKey'];
      });

    return dataPoints[0];
  },
  /**
   * Out of all the parameters the dataDevice packet have, Fetch the required params or null if not available
   * @returns deviceData with deviceData['data'] = {param1: null/value, param2: null/value ...}
   * @param {object} deviceData Single deviceData packet from deviceData table
   * @param {array} params Array of parameters to look for in deviceData object
   */
  getParamDataFromDeviceDataPacket: function (deviceData, params) {
    if (params.constructor.name !== 'Array') {
      sails.log.warn(
        'getParamDataFromDeviceDataPacket: Params should be of type Array'
      );
      params = [];
    }
    if (globalHelpers.isNullish(deviceData)) {
      return deviceData;
    }

    let filteredDeviceData = {},
      data = globalHelpers.toJson(deviceData['data']);

    for (let param of params) {
      filteredDeviceData[param] =
        !globalHelpers.isNullish(data[param]) && data[param] !== 'null'
          ? Number(data[param])
          : null;
    }
    deviceData['data'] = filteredDeviceData;
    return deviceData;
  },

  /**
   * Club device data groupBy timestamp. So it returns
   * {  timestampGroup1: [ deviceData.data1, deviceData.data2... ]
   *    timestampGroup2 ...
   * }
   * @param {object} deviceData Array of deviceData objects from deviceData table
   * @param {array} params Array of parameters to groupBy on
   * @param {string} startTime The start time from which data was queried
   * @param {string} groupBy group by value (minutes/hour/day/week)
   */
  groupDataOnTimestamp: function (deviceData, startTime, groupBy) {
    let groupedParams = {};
    let unixSecondsInGroup =
      (globalHelpers.toMoment().unix() -
        globalHelpers.toMoment().subtract(1, groupBy).unix()) *
      1000;
    let startTimeUnix = globalHelpers.toMoment(startTime).unix() * 1000;

    deviceData.forEach((deviceData) => {
      let { timestamp, data } = deviceData;
      let currTimeUnix = globalHelpers.toMoment(timestamp).unix() * 1000;
      let diffFromStartTime = currTimeUnix - startTimeUnix;

      let groupCount = Math.floor(diffFromStartTime / unixSecondsInGroup);
      let timestampGroup = startTimeUnix + groupCount * unixSecondsInGroup; // same as moment(startTime).add(groupCount, groupBy).unix() but efficient

      if (groupedParams[timestampGroup] === undefined) {
        groupedParams[timestampGroup] = [];
      }
      groupedParams[timestampGroup].push(data);
    });
    return groupedParams;
  },

  /**
   * Group device data on ('day'/'month'/'week'/'min').
   * Apply the function 'callback' on each group for parameter param.
   * @param {array} deviceDatas Array of device data objects
   * @param {string} group Groupby value to group data on ('day'/'month'/'week'/'min')
   * @param {array} params Array of params to get from each data packet
   * @param {strinf} startTime Start time of data
   * @param {string} endTime End
   * @param {Function} ApplyFnc Callback function to apply in grouped device data.
   *
   * ApplyFnc Inputs: a Array of devicedata -> [{param1,param2},...] and a paramName -> Param1
   * It should return a numerical value applying whatever algo required on list of data on parameter param
   *
   * @returns Returns {param: [startOfGroupts, CalculatedValue]}
   */
  groupDevicedataOnParamBetweenStartAndEndTime: function (
    deviceDatas,
    group,
    params,
    startTime,
    endTime,
    ApplyFnc
  ) {
    let plotGraph = params.reduce((acc, param) => {
      acc[param] = [];
      return acc;
    }, {});
    let groupedByData = this.groupDataOnTimestamp(
      deviceDatas,
      startTime,
      group
    );
    let currentGroupMoment = globalHelpers
      .toMoment(startTime)
      .startOf('minutes');
    let endGroupMoment = globalHelpers.toMoment(endTime).startOf('minutes');
    let count = 100000000; // count to avoid infinite loop

    while (currentGroupMoment < endGroupMoment && count-- !== 0) {
      let currentGroupTSUnix = currentGroupMoment.unix() * 1000;
      let deviceDatasInGroup = groupedByData[currentGroupTSUnix] || [];

      for (let param of params) {
        let calculatedValue = ApplyFnc(deviceDatasInGroup, param);

        plotGraph[param].push([currentGroupTSUnix, calculatedValue]);
      }

      currentGroupMoment.add(1, group);
    }
    if (count === 0) {
      sails.log.error(
        `Count value reached 0 for group ${group} ${startTime} ${endTime}`
      );
    }
    return plotGraph;
  },

  isValidConsumptionUnit: function (unit) {
    if (typeof unit !== 'string') {
      return false;
    }
    if (VALID_CONSUMPTION_UNITS.includes(unit)) {
      return true;
    } else {
      return false;
    }
  },
  /**
   *
   * @param {object} dataPacket Single Data packet from device data table
   * @param {strign} consumptionUnit Unit of consumption. enum(kvah, kwh)
   *
   * @return float consumption_value in preferred unit OR null if data not found
   */
  getConsumptionFromDataPacketInUnitPreference: function (
    dataPacket,
    consumptionUnit
  ) {
    if (dataPacket.constructor.name !== 'Object') {
      return null;
    }
    let kvah = Number(dataPacket['kvah']);
    let kwh = Number(dataPacket['kwh']);

    if (isNaN(kvah) && isNaN(kwh)) {
      // no way to find consumption
      return null;
    }
    if (consumptionUnit === 'kvah') {
      if (isNaN(kvah)) {
        // means kwh exist so need kvah
        kvah = globalHelpers.convertKWHToKVAH(kwh);
      }
      return kvah
    } else if (consumptionUnit === 'kwh') {
      if (isNaN(kwh)) {
        // means kvah exist so need kwh
        kwh = globalHelpers.convertKvahToKwh(kvah);
      }
      return kwh
    } else {
      // some unkown consumption unit came
      throw new Error('Invalid consumption unit in datadevice.utils.js: getConsumptionFromDataPacketInUnitPreference');
    }

  },
};
