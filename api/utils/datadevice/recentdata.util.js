
const moment = require('moment');
module.exports = {
  /**
   * @function formatDataForFrontend
   * @description Formats data as per frontend requirement. Values in the data packet to be extracted to the main object. 
   * If no data present, send object with only deviceId.
   * @param {array} deviceData Array of objects containing last data packet received from a device in the last 5 minutes.
   */
  formatDataForFrontend: function (deviceDataMap){
    let returnObject = [];
    Object.keys(deviceDataMap).forEach(deviceId => {
      let dataPacket = deviceDataMap[deviceId];
      if (dataPacket === null) {
        returnObject.push({
          deviceId
        });
        return;
      }
      for(const key in dataPacket.data) {
        if (dataPacket.data[key] === "null") {
          dataPacket.data[key] = null
        }
      }
      let tempObject = {
        deviceId: dataPacket.deviceId,
        ...dataPacket.data,
      };
      returnObject.push(tempObject);
    });
    return returnObject;
  },
  formatRunMinutesData: function (rawData) {
    delete rawData.siteId
    delete rawData.componentId
    delete rawData.name
    const timezoneOffset = rawData.timezoneOffset
    delete rawData.timezoneOffset
    const time = Number(rawData.time)/1000000
    delete rawData.time
    rawData.timestamp = moment(time).utcOffset(timezoneOffset).format("YYYY-MM-DDTHH:mm:00:00Z")
    const filterData =  {};
    for(let key in rawData) {
      if (rawData[key] != '' && rawData[key] != 'null') {
        filterData[key] = !isNaN(Number(rawData[key])) ? Number(rawData[key]) : rawData[key];
      }
    }
    return filterData
  }
};
