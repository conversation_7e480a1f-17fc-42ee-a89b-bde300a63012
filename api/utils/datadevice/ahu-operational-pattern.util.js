const Joi = require("joi");

module.exports = {
  requestValidator: function (input) {
    const schema = Joi.object()
      .keys({
        deviceType: Joi.string().required(),
        latestBy: Joi.string()
          .required()
          .valid(...["day", "week", "month"]),
        use: Joi.string().required(),
      })
      .unknown(true);
    const { error } = schema.validate(input);
    if (error) {
      sails.log.error(error);
      return false;
    } else {
      return true;
    }
  },
};
