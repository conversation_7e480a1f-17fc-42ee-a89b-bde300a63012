const Joi = require("joi");

module.exports = {
  requestValidator: function (input) {
    const schema = Joi.object()
      .keys({
        startTime: Joi.date().required(),
        endTime: Joi.date().required(),
        groupBy: Joi.string()
          .required()
          .valid(...["hour", "day", "month"]),
        emList: Joi.array().items(Joi.string()).required(),
      })
      .unknown(true);
    const { error } = schema.validate(input);
    if (error) {
      sails.log.error(error);
      return false;
    } else {
      return true;
    }
  },
};
