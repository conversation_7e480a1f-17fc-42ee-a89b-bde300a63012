const globalHelper = require('../globalhelper');
const utils = require('./utils');

module.exports = {
  /**
   * Check if User input is correct
   * @param {array} baselines Array of baseline to insert(user input)
   * @returns Boolean True/False if user input is correct
   */
  checkInput(baselines) {
    baselines.forEach((baseline) => {
      const {
        startDate, endDate, consumptionValue, target,
      } = baseline;

      if (globalHelper.isValidDateTime(startDate) === false) {
        throw new Error('Not A valid startDate', startDate);
      }
      if (globalHelper.isValidDateTime(endDate) === false) {
        throw new Error('Not A valid endDate', endDate);
      }
      if (Number.isNaN(Number(consumptionValue)) === true) {
        throw new Error('Not valid actual baseline', consumptionValue);
      }
      if (Number.isNaN(Number(target)) === true) {
        throw new Error('Not valid target', target);
      }
    });
    return true;
  },
  /**
   * Check a single User Baseline from user input is correct.
   * If the baseline is correct we format it to input to baseline table
   * @param {Object} baseline Single Baseline From user input
   * @returns Object Baseline Object if all user input is correct else Problems
   */
  getValidBaselineInDBSchemaFormat(baseline) {
    const { consumptionValue, target, siteId } = baseline;
    let { startDate, endDate } = baseline;

    startDate = globalHelper.toMoment(startDate).startOf('day');
    endDate = globalHelper.toMoment(endDate).startOf('day');

    if (target > 100 || target < 0) {
      return { problems: 'Percent change should be between 0-100' };
    }
    if (endDate.diff(startDate, 'days') > utils.MAX_DAYS_IN_BASELINE) {
      return { problems: `Cannot have startdate enddate difference more than ${utils.MAX_DAYS_IN_BASELINE} days` };
    }
    if (startDate > endDate) {
      return { problems: 'Start date cannot be greater then end date' };
    }

    return {
      startDate: startDate.format('MM-DD-YYYY'),
      endDate: endDate.format('YYYY-MM-DD'),
      siteId,
      consumptionValue,
      target,
    };
  }
};
