const flaverr = require('flaverr');
const Joi = require('joi');
const moment = require('moment')
const globalHelper = require('../globalhelper');

const VALID_CONSUMPTION_UNITS = ['kvah', 'kwh'];
const MAX_DAYS_IN_BASELINE = 40;
const CONSUMPTION_DAU_UNIT = 'kvah';

const BASELINE_INPUT_DATE_FORMAT = 'YYYY-MM-DD';
const DYNAMODB_START_DATE_BASELINE_FORMAT = "MM-DD-YYYY";
const DYNAMODB_END_DATE_BASELINE_FORMAT = "YYYY-MM-DD";


module.exports = {
  MAX_DAYS_IN_BASELINE,
  CONSUMPTION_DAU_UNIT,
  BASELINE_INPUT_DATE_FORMAT,
  DYNAMODB_START_DATE_BASELINE_FORMAT,
  DYNAMODB_END_DATE_BASELINE_FORMAT,

  isValidConsumptionUnit(unit) {
    if (typeof unit !== 'string') {
      return false;
    }
    if (VALID_CONSUMPTION_UNITS.includes(unit)) {
      return true;
    }
    return false;
  },
  /**
   * Translate a date to previous year date based on
   * exact week of day.
   * @param {string} date date timestring
   * @returns Moment object
   */
  translateToPreviousYearDate(date, startDate, endDate) {
    const dateMoment = globalHelper.toMoment(date);
    const startDateMoment = globalHelper.toMoment(startDate);
    const endDateMoment = globalHelper.toMoment(endDate);
    const currWeek = dateMoment.week() - dateMoment.clone().startOf('month').week();
    const currMonth = dateMoment.month(); // 3 means april (0 means jan)
    const currDay = dateMoment.day(); // 0 means sunday, 6 means saturday, 1 means monday
    const prevYearDate = dateMoment
      .clone()
      .subtract(1, 'year')
      .startOf('year')
      .add(currMonth, 'month')
      .add(currWeek, 'week')
      .startOf('week')
      .add(currDay, 'day');

    if (startDate === undefined || endDateMoment === undefined) {
      return prevYearDate;
    }

    const diffPrevYearDateAndStartdate = startDateMoment.diff(prevYearDate, 'days');
    const diffPrevDateFromBaselineEndDate = endDateMoment.diff(prevYearDate, 'days');

    if (diffPrevYearDateAndStartdate > 0) {
      const weeksToAdd = Math.ceil(Math.abs(diffPrevYearDateAndStartdate) /7);
      prevYearDate.add(weeksToAdd, 'week');
    }
    if (diffPrevDateFromBaselineEndDate < 0) {
      const weeksToSubtract = Math.ceil(Math.abs(diffPrevDateFromBaselineEndDate) /7);
      prevYearDate.subtract(weeksToSubtract, 'week');
    }

    return prevYearDate;
  },
  /**
   * Given Baseline object with start and enddate of the baseline
   * Calculate the correct baselines after adding/subtracting the adjustments
   * @param {Object} baseline Baseline Object from baseline table
   * @param {Object} previousAdjustment Adjustment obejct from adjust table.
   * @param {Array} thisMonthsAdjustments All the other adjustments this month.
   * Array of Adjustments object
   */
  getMonthsBaselineAfterAdjustmentsInPrefferedUit(
    baseline, previousAdjustment, thisMonthsAdjustments, preferredUnit, currUnit,
  ) {
    let { consumptionValue } = baseline;
    const { startDate, endDate } = baseline;
    // .. this startDate and endDate coming should be actual this year start and endate
    // .. we also need a field "is1styear" in baseline
    const monthsBaseline = {};
    const timeStampAdjustMentMap = thisMonthsAdjustments.reduce((acc, adjustment) => {
      let { timestamp } = adjustment;
      timestamp = timestamp.slice(0, 10);
      if (acc[timestamp] === undefined) {
        acc[timestamp] = 0;
      }
      acc[timestamp] += adjustment.relative;
      return acc;
    }, {});

    if (previousAdjustment !== undefined) {
      consumptionValue += previousAdjustment.absolute;
    }

    const date = globalHelper.toMoment(startDate);
    const endDateMoment = globalHelper.toMoment(endDate);
    const daysInBaseline = endDateMoment.diff(date, 'days') + 1; // +1 to include last date
    consumptionValue /= daysInBaseline;

    for (; endDateMoment.diff(date, 'days') >= 0; date.add(1, 'day')) {
      const tempFormattedDate = date.format('YYYY-MM-DD');
      if (timeStampAdjustMentMap[tempFormattedDate] !== undefined) {
        consumptionValue += timeStampAdjustMentMap[tempFormattedDate];
      }
      monthsBaseline[tempFormattedDate] = globalHelper.convertConsumptionToPreferredUnit(
        consumptionValue,
        currUnit,
        preferredUnit,
      );
    }

    return monthsBaseline;
  },
  baselineInputValidation: (params) => {
    const schema = Joi.object({
      prevBaseline: Joi.object().allow(null).when(Joi.ref('$strict'), {
        is: true,
        then: Joi.object({
          siteId: Joi.string().required(),
          startDate: Joi.date().iso().required(),
          endDate: Joi.date().iso().greater(Joi.ref('startDate')).required(),
        }).unknown(true),
      }),
      newBaseline: Joi.object({
          siteId: Joi.string().required(),
          startDate: Joi.date().iso().required(),
          endDate: Joi.date().iso().greater(Joi.ref('startDate')).required(),
          consumptionValue: Joi.number().integer().positive().required(),
          target: Joi.number().min(0).max(100).required()
        }).unknown(true),
    });
    const options = {
      context: {
        strict: true
      }
    };


    const { error, value } = schema.validate(params,options);
    if (error) {
      throw new flaverr('E_INPUT_VALIDATION', error)
    }
    return value;
  },
  deleteBaselineInputValidation: (params) => {
    const schema =Joi.object({
      startDate: Joi.date().iso().required(),
      siteId: Joi.string().required(),
    });

    const { error, value } = schema.validate(params);
    if (error) {
      throw new flaverr('E_INPUT_VALIDATION', error)
    }
    return value;
  },
  isAnyCycleExceedTheDayLimit: (baselines, maxDayLimit) => {
    return  baselines.some((baseline) => {
      const startDate = baseline.startDate;
      const endDate = baseline.endDate;
      const diffInDays = endDate.diff(startDate, 'days');
      return diffInDays > maxDayLimit;
    });
  },
  baselineSortedByStartDate:(baselines)=> {
    return baselines.sort((a, b) => {
      const startDateA = a.startDate;
      const startDateB = b.startDate;
      return startDateA.diff(startDateB); // in milliseconds
    });
  },
  getOverlappedDaysIfAnyCycleOverlapping: (sortedBaseLine)=> {
    const startDateOccur = new Map();
    const endDateOccur = new Map();
    const dayOverLapping = []
    for (const baseline of sortedBaseLine) {
      let {startDate, endDate} = baseline
      startDate = startDate.format();
      endDate = endDate.format();
      if (!startDateOccur.has(startDate)) {
        startDateOccur.set(startDate, 1)
      } else {
        startDateOccur.set(startDate, startDateOccur.get(startDate)+1)
      }


      if (!endDateOccur.has(endDate)) {
        endDateOccur.set(endDate, 1)
      } else {
        endDateOccur.set(endDate, endDateOccur.get(endDate)+1)
      }

    }
    for (const [day, counter] of startDateOccur.entries()) {
      if (counter >= 2) {
        dayOverLapping.push(day)
      }
    }
    for (const [day, counter] of endDateOccur.entries()) {
      if (counter >= 2) {
        dayOverLapping.push(day)
      }
    }
    return dayOverLapping
  },
  splitTheSameDayIfAnySameDayOverlapping: (sortedBaseLine)=>{
      const baselineWithSplitEndDate = []
      let prev = {
        startDate: null,
        endDate: null
      }
      for (const baseline of sortedBaseLine) {
        const { startDate: momentStartDate, endDate: momentEndDate } = baseline
        if (!prev.startDate && !prev.endDate) {
          prev.startDate = momentStartDate;
          prev.endDate = momentEndDate;

          baseline.startDate = momentStartDate;
          baseline.endDate = momentEndDate
          baselineWithSplitEndDate.push(baseline)
          continue;
        }

        if (prev.endDate && prev.endDate.isSame(momentStartDate, 'day')) {
          baseline.startDate = momentStartDate.set({ hour: 12, minute: 0, second: 0, millisecond: 0 })
          baseline.endDate = momentEndDate;
          baselineWithSplitEndDate.push(baseline)
          prev.startDate = baseline.startDate
          prev.endDate = momentEndDate
        } else {
          baseline.startDate = momentStartDate;
          baseline.endDate = momentEndDate
          baselineWithSplitEndDate.push(baseline)
        }
    }
    return baselineWithSplitEndDate;
  },
  checkIfAnyCycleExistInAnyOtherInterval: (baselineSortedDayWithMoments) => {
    return baselineSortedDayWithMoments.some((baseline, index) => {
      const {endDate: currBaselineEndDate} = baseline;
      if (!baselineSortedDayWithMoments[index +1]) {
        return false;
      }
      const {startDate:nextBaselineStartDate, endDate: nextBaselineEndDate} = baselineSortedDayWithMoments[index + 1];
      if (nextBaselineStartDate && nextBaselineEndDate) {
        return currBaselineEndDate.isBetween(nextBaselineStartDate, nextBaselineEndDate);
      }
      return false;
    });
  },
  mergeNewBaselineWithOld: (newBaseline, oldBaseline) => {
    const mergedBaseline = [];
    mergedBaseline.push(...newBaseline,...oldBaseline);
    return mergedBaseline.sort((a, b) => {
      const startDateA = a.startDate;
      const startDateB = b.startDate;
      return startDateA.diff(startDateB); // in milliseconds
    });
  },
  getOverlappedBaselineCycle:  (baselines) => {
      for (let i=1;i<baselines.length;i++){
        const {startDate:nextStartDate,endDate:nextEndDate} = baselines[i];
        const { startDate:prevStartDate, endDate:prevEndDate } = baselines[i-1];
        if(nextStartDate.isSameOrAfter(prevEndDate)) {
          continue;
        }
        return [baselines[i-1],baselines[i]];
      }
  },
  sortBaseline(baseline) {
    return baseline.sort((curr, next) => {
      const currStart = curr.startDate;
      const nextStart = next.startDate;
      const currEnd = curr.endDate;
      const nextEnd = next.endDate;
      if (moment(currStart).isAfter(nextStart,'days')) return 1;
      if (moment(currStart).isBefore(nextStart,'days')) return -1;
      if (moment(currEnd).isAfter(nextEnd,'days')) return 1;
      return -1;
    });
  },
  _validateNewBaseline(siteId, newBaseline) {
    const hasInconsistentSiteId = newBaseline.filter(it=>it.siteId != siteId);
    if (hasInconsistentSiteId.length) {
      throw flaverr(
        {
          code: "INCONSISTENT_SITE_ID_NOT_ALLOWED",
          raw: hasInconsistentSiteId,
        },
        new Error(
          "site id is not correct for baseline cycles",
        )
      );
    }

    // if any baseline row's startDate < endDate, throw exception
    const hasInvalidBaselineRows =  newBaseline.filter(
      this._baselineRangeComparator
    );
    if (hasInvalidBaselineRows.length) {
      throw flaverr(
        {
          code: "INVALID_BASELINE_ROW_EXIST",
          raw: hasInvalidBaselineRows,
        },
        new Error(
          "end date is smaller than start date or baseline cycle days is more 35 days(maximum allowes baseline cycle period)",
        )
      );
    }

    const hasInvalidTarget = newBaseline.filter(
      this._baselineTargetValidator
    )
    if (hasInvalidTarget.length) {
      throw flaverr(
        {
          code: "INVALID_BASELINE_ROW_EXIST",
          raw: hasInvalidTarget,
        },
        new Error(
          `Baseline of cycle ${hasInvalidTarget[0].startDate}_${hasInvalidTarget[0].endDate} is having invalid target.`,
        )
      );
    }
    return
  },

   /**
   * this function is taking a baseline'w row
   * @param {*} baseline
   * @returns
   */
   _baselineRangeComparator ({startDate,endDate}) {
    startDate = moment(startDate, BASELINE_INPUT_DATE_FORMAT).utcOffset(330);
    endDate = moment(endDate, BASELINE_INPUT_DATE_FORMAT).utcOffset(330);
    if(!endDate.isAfter(startDate)) return true

    const intervalInDays = moment(endDate).diff(startDate,'days');
    if(intervalInDays > 35) return true

    return false
  },
  _baselineTargetValidator(baseline){
    const {target} = baseline
    if (target < 0 || target > 100) {
      return baseline
    }
  },
  validateMaxDaysExceedInCycle(baseline){
    let {startDate, endDate} = baseline;
    startDate = moment(baseline.startDate, BASELINE_INPUT_DATE_FORMAT);
    endDate = moment(baseline.endDate, BASELINE_INPUT_DATE_FORMAT);
    if (endDate.diff(startDate, 'days') > 35) {
      return true
    }
  },
  validateInconsistentSiteId(oldBaseline, newBaseline, siteId) {
    if (
      oldBaseline && oldBaseline.siteId !== siteId ||
      newBaseline && newBaseline.siteId !== siteId
      ) {
        return true;
    }
    return false
  },
  getCurrentDateBaseline: function(baseline, {date, month}) {
    return baseline.filter((cycle) => {
      const {startDate, endDate} = cycle;
      const currentDate = moment().startOf('day');
      currentDate.date(date);
      currentDate.month(month);
      if (
        currentDate.year(startDate.year()).isSame(startDate) ||
        currentDate.year(endDate.year()).isSame(endDate) ||
        currentDate.year(startDate.year()).isBetween(startDate, endDate) ||
        currentDate.year(endDate.year()).isBetween(startDate, endDate)
      ) {
        return cycle;
      }
    })
  },
  //BASELINE related time format should be existed only in baseline utils file. use everywhere
  formattedBaselineVO:function(baselineObj, currentYear){
    const {startDate, endDate, consumptionValue, target, createdAt, updatedAt} = baselineObj
    let startDateWithCurrYear, endDateWithCurrYear;
    if (startDate.year() === endDate.year()) {
      startDateWithCurrYear = startDate.year(currentYear);
      endDateWithCurrYear = endDate.year(currentYear);
    } else if ( endDate.year() > startDate.year()) {
      startDateWithCurrYear = startDate.year(currentYear);
      endDateWithCurrYear =  endDate.year(currentYear + 1);
    }
    const baselineFinalObj = {
      startDate: startDateWithCurrYear.format(BASELINE_INPUT_DATE_FORMAT),
      endDate: endDateWithCurrYear.format(BASELINE_INPUT_DATE_FORMAT),
      consumptionValue,
      target,
    }
    if (baselineObj.hasOwnProperty('createdAt')) {
      baselineFinalObj.createdAt = createdAt
    }
    if (baselineObj.hasOwnProperty('updatedAt')) {
      baselineFinalObj.updatedAt = updatedAt
    }
    return baselineFinalObj
  },
};
