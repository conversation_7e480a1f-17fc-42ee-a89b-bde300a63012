const Joi = require('joi');
const moment = require('moment');
module.exports = {
  validateInputJson:function(payload){
    const errorBlock = [];
    if(!Array.isArray(payload)){
      errorBlock.push(`INPUT_SHOULD_ARRAY`);
      return errorBlock;
    }
    const schema = Joi.object().keys({
      product_id:Joi.string().required(),
      shift_id: Joi.string().required(),
      production_value:Joi.number().required(),
      production_date: Joi.date().required(),
      unit: Joi.string().required()
    })

    payload.forEach(data=>{
      const { error } = schema.validate(data);
      if( error) {
        errorBlock.push(error.message)
      }
      let { production_date } = data;
    })

    if(errorBlock.length){
      return errorBlock;
    }
  },
}
