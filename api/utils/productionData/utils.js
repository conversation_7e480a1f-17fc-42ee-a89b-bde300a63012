const moment = require('moment');
module.exports = {
  responseObject:function(shifts, products, productionData, startDate, endDate){
    const object = {
      shifts:[],
      products:[],
      data:[],
    }

    const productMap = products.reduce((acm,curr)=>{
      acm.set(curr.sk,curr)
      return acm;
    }, new Map())
    const productionDataCellFactory =(product)=>{
      return {
        product_id:product.sk,
        value:null,
        unit: product.unit,
        name: product.product_name,
        createdBy:null,
        createdAt:null,
        production_id: null,
      }
    }
    const shiftMap = shifts.reduce((acm,curr)=>{
      acm.set(curr.sk,curr)
      return acm;
    }, new Map())
    const shiftWiseProductRows = function(){
      function getCell(){
        let _cell = new Map();
        for(let [key, value] of productMap){
          _cell.set(key,productionDataCellFactory(value));
        }
        return _cell;
      }
      let _groupedRows = new Map();
      for(let [key, value] of shiftMap){
        _groupedRows.set(key,getCell())
      }
      return _groupedRows;
    }
    const responseMap = this.dateListBetweenTwoDates(startDate,endDate).reduce((acm, curr) => {
      acm.set(curr, shiftWiseProductRows())
      return acm;
    }, new Map());
    productionData.forEach((data)=>{
      let { sk,product_id,shift_id,production_value,production_unit,createdBy, createdAt } = data;
      let productionDate = sk.split('#')[0];
      if(responseMap.has(productionDate)){
        let _productionRecord = responseMap.get(productionDate).get(shift_id).get(product_id);
        _productionRecord.product_id = product_id;
        _productionRecord.value = production_value;
        // _productionRecord.unit = production_unit;
        _productionRecord.name = productMap.get(product_id).product_name;
        _productionRecord.createdAt = createdAt
        _productionRecord.createdBy = createdBy
        _productionRecord.production_id = sk;
      } else {
        throw new Error("INVALID_CASE");
      }
    })

    // noinspection JSMismatchedCollectionQueryUpdate
    let  finalData = []
    for(let [date, shifts] of responseMap){
      let obj = { date:date,shifts:[]}
      for(let [shiftId, productionRecords] of shifts ){
        obj.shifts.push({
          shiftId:shiftId,
          productionData:[]
        })
        for(let [productId, record] of productionRecords){
          obj.shifts[obj.shifts.length-1].productionData.push(record)
        }
      }
      finalData.push(obj);
    }
    return {
      shifts: [...shiftMap.values()],
      products: [...productMap.values()],
      data:finalData
    };
  },
  dateListBetweenTwoDates:function(startDate, endDate){
    let date = []
    while(moment(startDate) <= moment(endDate)){
      date.push(startDate);
      startDate = moment(startDate).add(1, 'days').format("YYYYMMDD");
    }
    return date;
  },
  validateRecords:function(records, shift, product){
    const errorBlock = [];
    records.forEach(record=>{
      let _errorBlock = [];
      const { product_id, shift_id, production_value, production_date } = record;
      if(!shift.has(record.shift_id)){
        _errorBlock.push(`INVALID_SHIFT_ID-${record.shift_id}`);
      }
      if(!product.has(record.product_id)){
        _errorBlock.push(`INVALID_PRODUCT_ID-${record.product_id}`);
      }
      let _isFutureDate = moment(production_date).isAfter(moment(),'days');
      if(_isFutureDate){
        _errorBlock.push(`INVALID_PRODUCTION_DATE - ${production_date}`);
      }
      if(_errorBlock.length)
        errorBlock.push(_errorBlock);
    })
    return errorBlock.length ? { error: errorBlock } : { error: null };
  }


}
