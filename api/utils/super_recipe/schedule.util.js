const Joi = require('joi');
const flaverr = require('flaverr');

const validateScheduleData = (dataArray) => {
  const timeRangeSchema = Joi.array()
    .items(
      Joi.object({
        start_time: Joi.string()
          .isoDate()
          .required()
          .messages({
            'string.base': 'Start time must be a string.',
            'string.isoDate': 'Start time must be in ISO 8601 format.',
            'any.required': 'Start time is required.',
          }),
        end_time: Joi.string()
          .isoDate()
          .required()
          .messages({
            'string.base': 'End time must be a string.',
            'string.isoDate': 'End time must be in ISO 8601 format.',
            'any.required': 'End time is required.',
          })
      })
    )
    .required()
    .messages({
      'array.base': 'Time range must be an array of objects.',
      'any.required': 'Time range is required.',
    });

  const schema = Joi.array()
    .items(
      Joi.object({
        id: Joi.number()
          .optional(),
        recipe_id: Joi.number()
          .integer()
          .required()
          .messages({
            'number.base': 'Recipe ID must be a number.',
            'number.empty': 'Recipe ID cannot be empty.',
            'any.required': 'Recipe ID is required.',
          }),
        customDays: Joi.array()
          .optional(),
        repeat_type: Joi.string()
          .valid('daily', 'custom', 'dailySlot')
          .required()
          .messages({
            'string.base': 'Repeat type must be a string.',
            'any.only': 'Repeat type must be either "daily" or "custom".',
            'any.required': 'Repeat type is required.',
          }),
        status: Joi.number()
          .integer()
          .valid(0, 1)
          .required()
          .messages({
            'number.base': 'Status must be a number.',
            'any.only': 'Status must be either 0 or 1.',
            'any.required': 'Status is required.',
          }),
        start_date: Joi.string()
          .pattern(/^\d{4}-\d{2}-\d{2}$/)
          .required()
          .messages({
            'string.base': 'Start date must be a string.',
            'string.pattern.base': 'Start date must be in YYYY-MM-DD format.',
            'any.required': 'Start date is required.',
          }),
        end_date: Joi.string()
          .pattern(/^\d{4}-\d{2}-\d{2}$/)
          .optional()
          .messages({
            'string.base': 'End date must be a string.',
            'string.pattern.base': 'End date must be in YYYY-MM-DD format.',
          }),
        time_range: timeRangeSchema,
      })
    )
    .required()
    .messages({
      'array.base': 'Input must be an array of objects.',
      'any.required': 'Input array is required.',
    });

  const { error } = schema.validate(dataArray, { abortEarly: false });

  if (error) {
    const errorMessages = error.details.map((detail) => detail.message)
      .join(', ');
    throw flaverr('E_INPUT_VALIDATION', new Error(`Validation failed: ${errorMessages}`));
  }
};

module.exports = {
  validateScheduleData,
};
