
const globalHelper = require('../globalhelper');


module.exports = {
  parseScheduleObjectForFrontEnd: function (schedule) {
    let parsedObject = {};

    let ts = globalHelper.toArray(schedule['ts']);
    let startDateTime = globalHelper.formatDateTime(ts[0][0]);
    let endDateTime = globalHelper.formatDateTime(ts[0][1]);
    if(schedule?.['repeat_type'] === 'custom' && ts?.[0]?.[2]) {
      const daysOfWeek = ["sun", "mon", "tue", "wed", "thu", "fri", "sat"];
      const matchedDays = ts[0][2].match(/\b(?:sun|mon|tue|wed|thu|fri|sat)\b/g) || [];
      parsedObject['repeatOn']=daysOfWeek.map(day => matchedDays.includes(day));
    }
    if (startDateTime === endDateTime) {
      endDateTime = globalHelper.toMoment(endDateTime)
        .add(30, 'minutes')
        .format('YYYY-MM-DD HH:mm');
    }

    parsedObject['schedule'] = [[startDateTime, endDateTime]];

    let startTime = startDateTime.split(' ');
    let endTime = endDateTime.split(' ');

    parsedObject['startTime'] = startTime;
    parsedObject['endTime'] = endTime;

    return parsedObject;
  }
};
