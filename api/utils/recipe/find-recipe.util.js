const utils = require('./utils');
const globalHelper = require('../globalhelper');

module.exports = {
  JSONifyRecipeObjectForFrontEnd: function(recipe) {

    let currenTime = globalHelper.getCurrentUnixTs();
    let { isSchedule } = recipe;

    if (isSchedule === 'true') {
      recipe['lastRunTime'] = recipe['isActive'] ? globalHelper.formatDateTime(recipe['isActive']) : 'NA';
      recipe['isActive']    = utils.isRecipeActive(currenTime, recipe['isActive']);
      recipe['observable']  = '';
      recipe['obs_params']  = {};
      recipe['isSchedule']  = true;
      recipe['recipelabel'] = globalHelper.toArray(recipe['recipelabel']);
      recipe['actionable']  = globalHelper.toArray(recipe['actionable']);
      recipe['componentsType'] = globalHelper.toArray(recipe['componentsType']);

    } else if (isSchedule === 'false') {
      recipe['lastRunTime'] = recipe['isActive'] ? globalHelper.formatDateTime(recipe['isActive']) : `${utils.LAST_IS_ACTIVE_DAYS} days ago`
      recipe['isActive']    = utils.isRecipeActive(currenTime, recipe['isActive']);
      recipe['isSchedule']  = false;
      recipe['operators']   = globalHelper.toJson(recipe['operator']);
      recipe['actionable']  = globalHelper.toArray(recipe['actionable']);
      recipe['params']      = globalHelper.toJson(recipe['params']);
      recipe['recipelabel'] = globalHelper.toArray(recipe['recipelabel']);
      recipe['componentsType'] = globalHelper.toArray(recipe['componentsType']);
      delete (recipe['operator']);
    }
    return recipe;
  }
};
