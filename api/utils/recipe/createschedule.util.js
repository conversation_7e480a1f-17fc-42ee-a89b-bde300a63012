const recipeutil = require('./utils');
const moment = require('moment');
const globalHelper = require('../globalhelper');
const utils = require('./utils');

module.exports = {

  createDailyCron: function (startDate, endDate, startTime, endTime) {
    let saveCron = false;
    saveCron = recipeutil.parseTime(moment(startDate), moment(endDate), startTime, endTime);
    return saveCron.map(cron => {
      return [
        startDate + ' ' + startTime,
        endDate + ' ' + endTime,
        cron,
      ];
    });
  },

  validTime: function (time) {
    if (/^\d{2}:\d{2}$/.test(time)) {
      return true;
    } else {
      return false;
    }
  },
  /**
   * checks if given input is in valid 'date' format or not
   * @param {string} date Date in string
   * @returns {boolean} false is startDate is not valid else date true
   */
  isValidDate: function (date) {

    if (!date) return false;

    try {
      date = moment(date).format('YYYY-MM-DD');
    } catch (e) {
      sails.log.error('Error in globaleHelper.isValidDate: ' + e);
      return false;
    }
    if (date === 'Invalid date') {
      return false;
    } else {
      return date;
    }

  },

  /**
   * Given an array of days, check if array has all valid days or not
   * valid days = 'mon', 'tue', 'wed', 'thu','fri','sat' and 'sun'
   * @param {array} daysArray Array of days 
   * @returns {bool} True/False if array is valid.
   */
  isValidDays: function (daysArray) {
    let isValidDay = true;

    if (daysArray === undefined || daysArray.constructor.name !== 'Array') {
      throw new Error('Invalid custom days');
    }

    for (let day of daysArray) {
      if (utils.VALID_DAYS.indexOf(day) === -1) {
        isValidDay = false;
        break;
      }
    }

    if (isValidDay === false) {
      throw new Error('Invalid custom days');
    }
    return true;
  },

  createNoIntervalCron: function (startDate, endDate, startTime, endTime) {
    let crons = recipeutil.parseTime(moment(startDate), moment(endDate), startTime, endTime);
    return crons.map(cron => {
      return [
        startDate + ' ' + startTime,
        endDate + ' ' + endTime,
        cron,
      ];
    });
  },
  /**
   * Create crontab's of given time. The cron is within specific start and end time.
   * The cron formed also have ability to run on custom days and not monday-sunday.
   * @param {string} startDate YYYY-MM-DD formatted start date
   * @param {string} endDate YYYY-MM-DD formatted end date
   * @param {string} startTime HH:mm formatted start time 
   * @param {string} endTime HH:mm formatted end time
   * @param {Array} customDays Array of days ['mon','fri','sat','sun']
   */
  createCustomCron: function (startDate, endDate, startTime, endTime, customDays) {
    let crons = recipeutil.parseTime(moment(startDate), moment(endDate), startTime, endTime);
    let daysStr = customDays.join();

    return crons.map(cron => {
      let tempCron = cron.split(' ');
      tempCron[4] = daysStr;

      return [
        startDate + ' ' + startTime,
        endDate + ' ' + endTime,
        tempCron.join(' '),
      ];
    });
  },

  addSampleTimeToCron: function (sampleTime, schedule) {
    let ts = globalHelper.toArray(schedule.ts);

    if (sampleTime <= 1 || isNaN(sampleTime)) {
      return true;
    }
    if (sampleTime && sampleTime < 60) {
      ts = ts.map((eachSchedule) => {
        let cron = eachSchedule[2];
        let cronArray = cron.split(' ');
        let min = cronArray[0];
        min = `${min}/${sampleTime}`;
        cronArray[0] = min;
        cron = cronArray.join(' ');
        eachSchedule[2] = cron;
        return eachSchedule;
      });
      schedule.ts = JSON.stringify(ts);  // Modifying the schedule object itself not returning anything
    }
  },
};
