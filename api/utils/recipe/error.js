const flaverr = require("flaverr")

module.exports = {
  throwActuatorThermostatConfigExists: function () {
    throw flaverr({
      code: 'NO_ACTIVE_RECIPE_EXIST',
      message: `There is no active thermostat recipe is running right now. Please check the thermostat configuration`,
      HTTP_STATUS_CODE: 400
    });
  },
  throwInvalidRequiredParameterForOTPCMode:function(param){
    throw flaverr({
      code: 'REQUIRED_PARAMETER_NOT_SET',
      message: `${param} is required data parameter that is missing/not configured at component`,
      HTTP_STATUS_CODE: 400
    });
  },
  throwRecipeNotExists: function (rid) {
    throw flaverr({
      code: 'RECIPE_NOT_EXIST',
      message: `${rid} does not exists`,
      HTTP_STATUS_CODE: 400
    });
  },
  throwErrorComponentIsNotValid: function (component) {
    const { deviceType, data: dataParams } = component
    const ALLOWED_ASSET_TYPE = ["ahu", "criticalAhu"]
    const MANDATORY_DATA_PARAMETERS = ["otthmswstatus", "otsettemp"]
    if (!ALLOWED_ASSET_TYPE.includes(deviceType)) {
      throw flaverr({
        code: 'INVALID_ASSET_TYPE',
        message: `only ${ALLOWED_ASSET_TYPE.join(", ")} type assets are allowed`,
        HTTP_STATUS_CODE: 400
      });
    }

    const dataParamExpressionMap = dataParams.reduce((acm, curr) => {
      const { key, expression } = curr
      acm[key] = expression
      return acm;
    }, {})

    MANDATORY_DATA_PARAMETERS.forEach(it => {
      if (!dataParamExpressionMap.hasOwnProperty(it) || _.isEmpty(dataParamExpressionMap[it])) {
        throw flaverr({
          code: 'REQUIRED_PARAMETER_NOT_SET',
          message: `${it} is required data parameter that is missing/not configured at component`,
          HTTP_STATUS_CODE: 400
        });
      }
    })

  }
}
