const globalHelper = require('../../utils/globalhelper');

module.exports = {
  getSendObj: function(recipe, formattedSchdeuleInfo, formattedToSendAction) {
    let { topic, maxDataNeeded, maxLogsNeeded, appType, misc } = recipe;
    topic = topic ? globalHelper.toArray(topic) : [];
    maxDataNeeded = maxDataNeeded ? maxDataNeeded : 0;
    maxLogsNeeded = maxLogsNeeded ? maxLogsNeeded : 0;
    misc = misc ? globalHelper.toJson(misc) : {};

    return {
      'recipeInfo': {
        'maxLogsNeeded': maxLogsNeeded,
        'failsafe': JSON.stringify({}),
        'everyMinuteTopics': globalHelper.toArray(recipe.everyMinuteTopics),
        'runOn': recipe.runOn,
        'topics': topic,
        'recipe': recipe.formula,
        'syncAsyncAction': 'True',
        'dependentOnOthers': globalHelper.toArray(recipe.dependentOnOthers),
        'controllers': globalHelper.toArray(recipe.dependentOnOthers),
        'switchOff': '0',
        'actionAlert': globalHelper.toArray(formattedToSendAction),
        'startNow': 'True',
        'maxDataNeeded': maxDataNeeded,
        'rid': recipe.rid,
        'notRun': (recipe.notRun),  // start and end time
        'alwaysRun': recipe.alwaysRun,
        'appType': appType ? appType : 'recipe',
        'misc': misc
      },
      'scheduleInfo': formattedSchdeuleInfo,
      'operation': 'recipeInit'
    };
  }
};
