const uuid = require('uuid');
const globalHelper = require('../globalhelper');
const utils = require('./utils');

const CALCULATEDPARAMREGEX = /(valueAt|isNan)/; // Strings which if exists in recipe formula should make recipe Run on server

module.exports = {

  /**
   * User input configration object for recipe
   * @param {object} inputs
   */
  buildInitialPacket(inputs) {
    const {
      siteId, type, neo, isSchedule, label, componentsType,
      formula, params, operators, maxDataNeeded, runInterval,
      appType = 'recipe', misc = {}, componentId, attachments,
    } = inputs;
    const user = inputs._userMeta.id;
    const rid = uuid();
    let recipelabel; let actionable; let
      mainObj;

    recipelabel = inputs.recipelabel.map(utils.getRecipeCategory); // Convert recipe Category from FrontEnd to Backend understandable
    if (recipelabel.indexOf(undefined) !== -1) {
      return { problems: ['Invalid Recipe Label'] };
    }

    actionable = inputs.actionable.map((action) => // adding a categories & uniqueId to each actionable
      ({ ...action, uniqId: utils.createUniqueIdForActionable(action, rid), category: recipelabel }));

    /**Checking for valid priority*/
    const validPriorities = [0,1,2,3,4];
    let isHypercritical = false;
    for( let i in actionable){
      let actionableObject = actionable[i];
      let { priority } = actionableObject;
      if (!validPriorities.includes(priority))
        return { problems: ['Invalid "priority" value'] };
      if ( priority == 4) {
        actionableObject["isHypercritical"] = true;
        isHypercritical = true;
      }
    }

    mainObj = {
      ...utils.recipeDefaults,
      ...{
        rid,
        siteId,
        type,
        label,
        isSchedule,
        user,
        neo,
        maxDataNeeded,
        appType,
        actionable: globalHelper.toString(actionable),
        recipelabel: globalHelper.toString(recipelabel),
        componentsType: globalHelper.toString(componentsType),
        runInterval: globalHelper.toString(runInterval),
        isHypercritical
      },
    };

    if (attachments) mainObj.attachments = attachments;

    if (appType === 'recipe') {
      if (isSchedule === 'false') {
        // recipe with formula
        mainObj = {
          ...mainObj,
          formula,
          operators,
          params,
        };
      }
    } else if (appType === 'thermal') {
      mainObj = {
        ...mainObj,
        formula,
        operators,
        params,
        componentId,
        misc: globalHelper.toString(misc),
      };
    }

    return mainObj;
  },

  /**
   * Deteremine if recipe should run on server or not
   * @param {boolean} runOnPreference True/False if there is already a user preference to run this on server or not
   * @param {array} deviceIdList List of deviceIds in the recipe
   * @param {string} formula Formula od recipe
   */
  isServerRecipe(runOnPreference, deviceIdList, formula) {
    if (runOnPreference === true) { // if recipe have calculated params, we get this variable set
      return true;
    }
    if (deviceIdList.length === 0 && !formula) { // its no formula recipe with just alert in it
      return true;
    }
    if (formula && formula.match(CALCULATEDPARAMREGEX)) {
      return true;
    }
    return false;
  },
  /**
   * Given device info list, get list of controllers these devices belong to
   * @param {list} devices List of devices info
   */
  getControllerIds(devices) {
    const controllerIds = []; let
      device;
    for (let i = 0; i < devices.length; i++) {
      device = devices[i];
      if (!device || device.deviceType === 'joulesense') {
        continue;
      } else {
        device.controllerId
          ? controllerIds.push(device.controllerId) : controllerIds.push(device.deviceId);
      }
    }
    return controllerIds;
  },
  /**
   * Check if geiven string a componentId or device ID
   * @param {string} deviceId Device or componentId
   */
  getDeviceIdType(deviceId) {
    let type; let
      tempcid;
    try {
      tempcid = parseInt(deviceId);
      type = 'deviceId';
    } catch (e) {
      type = 'compId';
    }
    if (isNaN(tempcid)) {
      type = 'compId';
    }
    return type;
  },
  /**
   * Find device Id of param in data list of component
   * @param {array} dataList List of data parameters from component Table
   * @param {string} param Parameter to find in above list
   */
  getParameterFromComponentsData(dataList, param) {
    let data;
    for (let i = 0; i < dataList.length; i++) {
      data = globalHelper.toJson(dataList[i]);
      if (data.key === param) {
        return data;
      }
    }
    return undefined;
  },

  /**
   * Find device Id of param in rawParams list of process
   * @param {array} dataList List of data parameters from component Table
   * @param {string} param Parameter to find in above list
   */
  getParameterFromProcessData(dataList, param) {
    let data;
    for (let i = 0; i < dataList.length; i++) {
      data = globalHelper.toJson(dataList[i]);
      if (data.abbr === param) {
        return data;
      }
    }
    return undefined;
  },

  createFormula(deviceParamList, observableParams, formula, size, operatorsDict) {
    try {
      let topicArray = []; const
        didArray = [];
      formula += " ";
      let runOnServer = false;
      for (let i = 0; i < deviceParamList.length; i++) {
        const [did, param, willRunOnServer] = deviceParamList[i];
        const paramSymbol = Object.keys(observableParams)[i];
        runOnServer = runOnServer || willRunOnServer;

        const regexObservableParam = new RegExp(`\\${paramSymbol} `, 'gi');
        const countPatternInFormula = ((formula || '').match(regexObservableParam) || []).length; // find number of occurances of
        const topic = `db/${did}/${param}/${size}`;
        // add multiple entries of topics on bases of occurrence in formula to future delete 1
        // in case of spot query.
        topicArray = topicArray.concat(Array(countPatternInFormula).fill(topic));
        didArray.push(did);
        formula = formula.replace(regexObservableParam, `${did}.${param}.${size}`);
      }
      for (const operatorSymbol in operatorsDict) {
        formula = utils.regexReplace(operatorSymbol, operatorsDict[operatorSymbol], formula);
      }
      formula = formula.replace(/ and /gi, ' & ');
      formula = formula.replace(/ or /gi, ' | ');
      formula = this.makeSpotQueries(formula, topicArray, size);

      return {
        runOnServer,
        didArray,
        topicArray,
        formula,
      };
    } catch (error) {
      sails.log.error('recipe > create-recipe > util:createFormula');
      sails.log.error(error);
      throw (error);
    }
  },
  /**
   * @param {string} formula Parsed Formula valueAt(222.kva.23, 23) > 222.kva.10
   * @param {array} deviceToSubscribeArray Array of topics in respect to formula.
   * Formula has devices to observe and this is array of those devices.
   * example: ["db/2701/status/2", "db/2711/frequency/2",...]
   * @param {number} size Maximum data size required in this formula
   */
  makeSpotQueries(formula, deviceToSubscribeArray, size) {
    try {
      let m;
      /**Regular Expression to find Spot Query*/
      const regex = /valueAt\s*\(\s*([\d\w_-]+)\.([a-zA-Z_]+)\.(\d+)\s*,\s*(\d+)\s*\)/gm;

      /**This function will generate a topic for these valueAt queries because they are new spot queries.*/
      while ((m = regex.exec(formula)) !== null) {

        /**
         * @description Iterate over each match of the spot query.
         * @overview This is necessary to avoid infinite loops with zero-width matches.
         */
        if (m.index === regex.lastIndex) {
          regex.lastIndex += 1;
        }

        /**create Spot Query Topic*/
        const spotTopic = `spot/${m[1]}/${m[2]}/${m[4]}`;

        /**Correct the previously inserted topic as it was identified to be incorrect.*/
        const replaceWithTopic = `db/${m[1]}/${m[2]}/${size}`;

        /**The result can be accessed through the `m`-variable.*/
        const indexMatchOfreplacingValue = deviceToSubscribeArray.indexOf(
          replaceWithTopic,
        );

        if (+indexMatchOfreplacingValue !== -1) {
          deviceToSubscribeArray[
            indexMatchOfreplacingValue
          ] = spotTopic;
        }

        /**fix the formula bcz it not valueAt(2222.kva.23,23) but valueAt(2222.kva, 23)*/
        const replaceRegex = new RegExp(
          `valueAt\\s*\\(\\s*${m[1]}.${m[2]}.${m[3]}\\s*,\\s*${m[4]
          }\\s*\\)`,
        );

        /**Formula Finalization using replace*/
        formula = formula.replace(
          replaceRegex,
          `valueAt ( ${m[1]}.${m[2]} , ${m[4]}) `,
        );
      }

      return formula;
    } catch (error) {
      sails.log.error('recipe > create-recipe > util:makeSpotQueries ');
      sails.log.error(error);
      throw (error);
    }
  },
};
