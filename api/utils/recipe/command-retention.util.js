const Joi = require('joi');
const flaverr = require('flaverr');

const COMMAND_RETENTION_STATUS = {
    'Enabled': 1,
    'Pending': 2,
    'Disabled': 0
}

const  throwExceptionInvalidControlAbbr = (nonExist) => {
    throw flaverr({
        code:'E_INVALID_CONTROLS',
        message:`Following controls does not exist ${nonExist.join(', ')}`,
        HTTP_STATUS_CODE: 400
    })
}

const validateEnableSchemaVIT = (params) => {
    const schema = Joi.object({
        controlAbbr: Joi.string().required(),
        deviceId: Joi.string().required(),
        commandAbbr: Joi.string().required(),
        deviceAbbr: Joi.string().required(),
        controllerId: Joi.number().required(),
        commandType: Joi.string().required(),
        label: Joi.string().required(),
        dataParamAbbr: Joi.string().required()
    });

    const { error } = schema.validate(params);
    if (error) {
        throw flaverr({
            code: 'E_INVALID_PARAMS',
            message: error.message,
            HTTP_STATUS_CODE: 400
        })
    }
}

const validateEnableSchemaBIT= (params) => {
    const schema = Joi.object({
        controlAbbr: Joi.string().required(),
        commandType: Joi.string().required(),
        right: Joi.object({
            deviceId: Joi.string().required(),
            commandAbbr: Joi.string().required(),
            deviceAbbr: Joi.string().required(),
            controllerId: Joi.number().required(),
            label: Joi.string().required(),
            dataParamAbbr: Joi.string().required()
        }).required(),
        left: Joi.object({
            deviceId: Joi.string().required(),
            commandAbbr: Joi.string().required(),
            deviceAbbr: Joi.string().required(),
            controllerId: Joi.number().required(),
            label: Joi.string().required(),
            dataParamAbbr: Joi.string().required()
        }).required()
    })

    const { error } = schema.validate(params);
    if (error) {
        throw flaverr({
            code: 'E_INVALID_PARAMS',
            message: error.message,
            HTTP_STATUS_CODE: 400
        })
    }
}

module.exports = {
    validateInput: (params) => {
        const schema = Joi.object({
            siteId: Joi.string().required(),
            componentId: Joi.string().required(),
            controls: Joi.array().items(Joi.object({
                controlAbbr: Joi.string().required(),
                commandRetentionStatus: Joi.number().valid(0,1).required(),
            })).min(1).required()
        });

        const { error } = schema.validate(params);
        if (error) {
            throw flaverr({
                code: 'E_INVALID_PARAMS',
                message: error.message,
                HTTP_STATUS_CODE: 400
            })
        }
    },

    validateEnableSchemaVIT,
    validateEnableSchemaBIT,
    validateCommands: (commandRetention, command) => {
        const activeControlAbbr = commandRetention.reduce((acc, cur) => {
            acc.add(cur.controlAbbr)
            return acc;
        }, new Set())
        const nonExist =  _.difference(command, Array.from(activeControlAbbr));
    
        if (nonExist.length) {
            throwExceptionInvalidControlAbbr(nonExist);
        }
    
    },

    fetchCommandRetentionRecipes: (recipes, configuredParamsControlRelationship) => {
        const commandRetentionWithStatus = [];
        const activeCommandRetentionRecipes = recipes.filter((row) => row?.appType && row.appType == 'commandretention' && row.commandRetentionRecipeStatus == "1");
        const disabledCommandRetentionRecipes = recipes.filter((row) => row?.appType && row.appType == 'commandretention' && row.commandRetentionRecipeStatus == "0");
        const visitedControls = [];
        
        const controlMap = configuredParamsControlRelationship.reduce((acc, row) => {
            const controlAbbr = row.controlAbbr;
            const controlName = row.controlName;
            if(!acc.has(controlAbbr)) {
                acc.set(controlAbbr, controlName);
            }
            return acc;
        }, new Map());

        activeCommandRetentionRecipes.forEach((row) => {
            const {isStage, actionable, rid} = row;
            const commandRetentionStatus = isStage == '1' ? COMMAND_RETENTION_STATUS.Disabled : COMMAND_RETENTION_STATUS.Enabled;
            const controlAbbr = JSON.parse(actionable)[0].controlsRelationship.controlabbr
            const controlName = controlMap.get(controlAbbr);
            if (!visitedControls.includes(controlAbbr) && controlName) {
                commandRetentionWithStatus.push({
                    controlAbbr: controlAbbr,
                    controlName,
                    commandRetentionStatus,
                    rid
                })
                visitedControls.push(controlAbbr)
            }
        })

        disabledCommandRetentionRecipes.forEach((row) => {
            const { actionable, rid} = row;
            const controlAbbr = JSON.parse(actionable)[0].controlsRelationship.controlabbr
            const controlName = controlMap.get(controlAbbr);
            if (!visitedControls.includes(controlAbbr) && controlName) {
                commandRetentionWithStatus.push({
                    controlAbbr: controlAbbr,
                    controlName,
                    commandRetentionStatus: 0,
                    rid
                })
                visitedControls.push(controlAbbr)
            }
        })

        configuredParamsControlRelationship.forEach((row) => {
            const controlAbbr = row.controlAbbr;
            const controlName = controlMap.get(controlAbbr);
            if (!visitedControls.includes(controlAbbr) && controlName) {
                commandRetentionWithStatus.push({
                    controlAbbr: controlAbbr,
                    controlName,
                    commandRetentionStatus: COMMAND_RETENTION_STATUS.Disabled,
                    rid: null
                })
                visitedControls.push(controlAbbr)
            }
        })
        return commandRetentionWithStatus.sort((a, b) => {
            const nameA = a.controlName.toLowerCase();
            const nameB = b.controlName.toLowerCase();
            if (nameA < nameB) return -1;
            if (nameA > nameB) return 1;
            return 0;
        });
    },

    throwExceptionEmptyControlsList :() => {
        throw flaverr({
            code: 'E_INVALID_CONTROLS',
            message: `Controls cannot be empty`,
            HTTP_STATUS_CODE: 400
        })
    },

    throwExceptionNoActiveRecipesforControl: () => {
        throw flaverr({
            code: 'E_NO_ACTIVE_RECIPES',
            message: 'No active Recipes for Control',
            HTTP_STATUS_CODE: 400
        })
    },

    throwExceptionInvalidControlAbbr,
    throwExceptionInvalidSiteAsset: (siteId, asset) => {
        throw flaverr({
            code:'E_INVALID_SITE_ASSET',
            message:`Please provide a valid site asset. The asset '${asset}' does not exist for the site with ID '${siteId}'.`,
            HTTP_STATUS_CODE: 400
        })
    },
    validateFetchCommandRetentionInput: (params) => {
        const schema = Joi.object({
            siteId: Joi.string().required(),
            componentId: Joi.string().required(),
        });

        const { error } = schema.validate(params);
        if (error) {
            throw flaverr({
                code: 'E_INVALID_PARAMS',
                message: error.message,
                HTTP_STATUS_CODE: 400
            })
        }
    },
    validateEnableSchema: (params) => {
        const {commandType} = params;
        if (commandType == 'BIT') {
            const {
                controlAbbr,
                commandType,
                right: {
                    deviceId: rightDeviceId,
                    commandAbbr: rightCommandAbbr,
                    deviceAbbr: rightDeviceAbbr,
                    controllerId: rightControllerId,
                    label: rightLabel,
                    dataParamAbbr: rightDataParamAbbr
                },
                left: {
                    deviceId: leftDeviceId,
                    commandAbbr: leftCommandAbbr,
                    deviceAbbr: leftDeviceAbbr,
                    controllerId: leftControllerId,
                    label: leftLabel,
                    dataParamAbbr: leftDataParamAbbr
                }
            } = params;
            
             validateEnableSchemaBIT({
                controlAbbr,
                commandType,
                right: {
                deviceId: rightDeviceId,
                commandAbbr: rightCommandAbbr,
                deviceAbbr: rightDeviceAbbr ,
                controllerId: rightControllerId,
                label: rightLabel,
                dataParamAbbr: rightDataParamAbbr
                },
                left: {
                deviceId: leftDeviceId,
                commandAbbr: leftCommandAbbr,
                deviceAbbr: leftDeviceAbbr,
                controllerId: leftControllerId,
                label: leftLabel,
                dataParamAbbr: leftDataParamAbbr
                }
            })

        }  else {
            const {
                controlAbbr,
                deviceId,
                commandAbbr,
                deviceAbbr,
                controllerId,
                commandType,
                label,
                dataParamAbbr
            } = params
             validateEnableSchemaVIT({
                controlAbbr,
                deviceId,
                commandAbbr,
                deviceAbbr,
                controllerId,
                commandType,
                label,
                dataParamAbbr
            })
        }
        return

    },
    getIOTDataPacketForDisableCommand (commandRetention) {
        const {recipeId} = commandRetention;
        return {
            "data": {
              "rid": recipeId
            },
            "func": "deleteRecipe",
            "RequestId": recipeId, // Check the request id with team
            "operation": "recipeControl"
          }
    },
    throwExceptionInvalidControlState (controlAbbr) {
        throw flaverr({
            code: 'E_INVALID_CONTROL_PARAM',
            message: `Failed to set '${controlAbbr}' to 'enabled' status either not exist or in [pending, enabled] state.`,
            HTTP_STATUS_CODE: 400
        })
    },
    fetchRecipeByControlAbbr(recipes, controlAbbr) {
        let recipeId;
        recipes.forEach((recipe) => {
          const {actionable, rid} = recipe;
          if (JSON.parse(actionable)[0].controlsRelationship.controlabbr == controlAbbr) {
            recipeId = rid;
            return;
          }
        })
        return recipeId;
      },
    COMMAND_RETENTION_STATUS,
    formateResponse(sendEnableCommandToIOTe) {
        const response = {}
        if (!sendEnableCommandToIOTe) return response;

        sendEnableCommandToIOTe.forEach((enableCommandInfo)=>{
            if (!enableCommandInfo) return;
            const {recipeInfo} = enableCommandInfo;
            const recipeId = recipeInfo.rid;
            const controlAbbr =recipeInfo.actionAlert[0].controlsRelationship.controlabbr
            response[controlAbbr] = recipeId
        })
      
        return response;
    },
    componentModeResponseBuilder(enableRecipeToDisable){
        const response = {};
        enableRecipeToDisable.forEach((recipe) => {
            const {controlAbbr, rid} = recipe;
            response[controlAbbr] = rid
        })
        return response
    },
    formatSaveCommandRetention(enableCommands, disableCommands) {
        const response = [];
        if (enableCommands) {
            for(const [controlAbbr, rid] of Object.entries(enableCommands)) {
                response.push({
                    controlAbbr,
                    rid,
                    state: 2,
                    message: 'Request has been sent successfully to enable command retention.'
                })
            }
        }

        if (disableCommands) {
            for(const [controlAbbr, rid] of Object.entries(disableCommands)) {
                response.push({
                    controlAbbr,
                    rid,
                    state: 2,
                    message: 'Request has been sent successfully for disable command retention.'
                })
            }
        }
        return response;
    }
};