const moment = require('moment-timezone');
const md5 = require('md5');
const uuid = require('uuid');
const globalhelper = require('../globalhelper');
const flaverr = require('flaverr');

moment.tz.add('Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6');
moment.tz.setDefault('Asia/Kolkata');

const recipeLabelMap = {
  process: 'recipeprocess',
  comfort: 'recipecomfort',
  routine: 'reciperoutine',
  failsafe: 'recipefailsafe',
  diagnostics: 'recipediagnostics',
};
const recipeDefaults = {
  isSchedule: false,
  failsafe: JSON.stringify({}),
  isStage: 1,
  isActive: 0,
  notRun: JSON.stringify([]),
  switchOff: '0',
  alwaysRun: false,
  scheduled: JSON.stringify([]),
  maxDataNeeded: 1,
};
const priorityMap = {
  0: 'low',
  1: 'medium',
  2: 'high',
  3: 'critical',
};

const VALID_DAYS = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];

const RECIPE_IS_ACTIVE_PREFIX = 'recipe_isActive';

const LAST_IS_ACTIVE_DAYS = 10;

const ALERT_TYPES = ['energywastage', 'datanotcoming', 'comfort', 'maintenance', 'warning'];

const RECIPE_TYPES = ['stateretention', 'setpoint', 'frequencypush', 'runhour', 'actuatorvalve', 
  'tonnageinjection', 'miscellaneous'];

module.exports = {
  recipeDefaults,
  recipeLabelMap,
  uuid,
  VALID_DAYS,
  LAST_IS_ACTIVE_DAYS,
  ALERT_TYPES,
  RECIPE_TYPES,

  /**
   * Takes the rid and appends it with the recipeActive key, to make a unique string key
   * to look into redis to find the epoch time when this recipe was active.
   * @param {string} rid Recipe ID
   */
  getRecipeIsActiveKey(rid) {
    return `${RECIPE_IS_ACTIVE_PREFIX}_${rid}`;
  },
  /**
   * Takes a recipe category in any format and returns it in Backend processable format
   * @param {string} label RecipeLabel/Category in any format
   */
  getRecipeCategory(label) {
    const noSpaceLabel = label.replace(/ /g, '').toLowerCase();
    if (!noSpaceLabel || noSpaceLabel === '') {
      return undefined;
    }
    const newlabel = recipeLabelMap[noSpaceLabel] ? recipeLabelMap[noSpaceLabel] : noSpaceLabel;
    return newlabel.match(/recipe/) ? newlabel : undefined; // label should have "recipe" word in it
  },
  /**
   * Returns DevicesId list to take command on from recipe actionsable list
   * @param {Array} actionsArray Actionable array with individual action (alert/command)
   */
  getDevicesInsideActionable(actionsArray) {
    if (actionsArray.constructor.name !== 'Array') {
      actionsArray = globalhelper.toArray(actionsArray);
    }
    if (!actionsArray) {
      return [];
    }
    return actionsArray
      .map((actions) => (actions.did ? actions.did : undefined))
      .filter(Boolean);
  },
  /**
   * Determine if the formula in recipe valid or not. Is there some error in formula like
   * wring number of brackets, invalid string etc
   * @param {object} recipe recipe object thats getting saved
   */
  isFormulaCorrect(recipe, isHypercritical) {
    // Adding checks for HyperCritical Alets only.
    const { formula } = recipe;
    const formulaExpression = formula.replace(/ /gi, ""); // Removing spaces.
    let returnObject = {
      "status": true,
      "problems": []
    };
    if(isHypercritical){
      if(formulaExpression.match(/P\(/gi)){
        returnObject.problems.push("Persistence function is not allowed in a hypercritical alert configuration.");
        returnObject.status = false;
      }
      if(formulaExpression.match(/min\(/gi)){
        returnObject.problems.push("'min' function is not allowed in a hypercritical alert configuration.");
        returnObject.status = false;
      }
      if(formulaExpression.match(/max\(/gi)){
        returnObject.problems.push("'max' function is not allowed in a hypercritical alert configuration.");
        returnObject.status = false;
      }
      if(formulaExpression.match(/avg\(/gi)){
        returnObject.problems.push("'avg' function is not allowed in a hypercritical alert configuration.");
        returnObject.status = false;
      }
    }
    // NEED TO FIX THIS FUNCTION
    return returnObject;
  },
  /**
   * Strip leading and trailing || from recipe formula
   * @param {string} formula Recipe Formula from Frontend
   */
  stripFormula(formula) {
    if (formula.slice(0, 2) === '||') {
      formula = formula.slice(2, formula.length);
    }
    if (formula.slice(formula.length - 2, formula.length) === '||') {
      formula = formula.slice(0, formula.length - 2);
    }
    return formula.replace(/\|\|/gi, ' ');
  },
  /**
   *
   * @param {string} find Regex to find in string
   * @param {string} replace String to replace found regex with
   * @param {string} replaceStr Target String to look for find & replace for
   */
  regexReplace(find, replace, replaceStr) {
    const r = new RegExp(`\\${find}`, 'gi');
    return replaceStr.replace(r, replace);
  },
  /**
   * Take an expression and respond if it have calculated parameter or not
   * @param {Array} expression : An expression like  ( 69@outputfrequency + 69@inputpower ) splitted on space
   * tell if the expression provided have any arthimetic operation its considered as calculated paramter
   */
  haveCalculatedParameter(expression) {
    try {
      if (!Array.isArray(expression)) {
        return false;
      }

      if (expression.join(' ').match(/[*&|></+-]/)) {
        return true;
      }
      return false;
    } catch (e) {
      sails.log(e);
      return false;
    }
  },
  /**
   * Get the maximum occuring deviceId in the Array
   * @param {array} dids Array of device ID's
   */
  getMaxCountElement(dids) {
    if (!Array.isArray(dids)) {
      throw Error('Unable to get run on controller');
    }
    const cObj = {};
    let mC = 0; let
      mOf;
    for (const i in dids) {
      if (cObj[dids[i]]) {
        cObj[dids[i]]++;
      } else {
        cObj[dids[i]] = 1;
      }
      if (cObj[dids[i]] > mC) {
        mOf = dids[i];
        mC = cObj[dids[i]];
      }
    }
    return mOf;
  },

  parseTime(start, end, stime, etime) {
    let month = '*';
    let days = '*';
    // let year="*";
    let hour = '*';
    let min = '*';
    let minHour;
    try {
      const totalMonths = (parseInt(end.month()) + 1) - (parseInt(start.month()) + 1);
      const totalYear = parseInt(end.year()) - parseInt(start.year());

      if (start > end) {
        return null;
      }

      if (stime === etime) {
        hour = parseInt(stime.split(':')[0]);
        min = parseInt(stime.split(':')[1]);
        minHour = [[min, hour]];
        // start - stop a device
      } else {
        let [stHour, stMin] = stime.split(':');
        let [enHour, enMin] = etime.split(':');
        stMin = parseInt(stMin);
        enMin = parseInt(enMin);
        stHour = parseInt(stHour);
        enHour = parseInt(enHour);
        minHour = [];
        if (enMin === stMin && stMin === 0) {
          if ((stHour === enHour)) minHour.push([parseInt(enMin), `${stHour}`]);
          else minHour.push(['*', `${stHour}-${enHour - 1}`]);
        } else if (stMin === 0 && enMin !== 0) {
          if (stHour !== enHour) minHour.push(['*', `${stHour}-${enHour - 1}`]);
          minHour.push([`0-${enMin}`, `${enHour}`]);
        } else if (stMin !== 0 && enMin === 0) {
          minHour.push([`${stMin}-59`, `${stHour}`]);
          if (stHour !== enHour && (stHour + 1 !== enHour)) minHour.push(['*', `${stHour + 1}-${enHour - 1}`]);
        } else if (stHour === enHour) {
          minHour.push([`${stMin}-${enMin}`, `${stHour}`]);
        } else {
          minHour.push([`${stMin}-59`, `${stHour}`]);
          if (stHour + 1 !== enHour) minHour.push(['*', `${stHour + 1}-${enHour - 1}`]);
          minHour.push([`0-${enMin}`, `${enHour}`]);
        }
      }

      if (totalYear === 0) {
        if (totalMonths === 0) {
          month = parseInt(end.month()) + 1;
          const totaldates = parseInt(end.date()) - parseInt(start.date());
          if (totaldates === 0) {
            days = String(end.date());
          } else if (totaldates > 31) {
            return (null);
          } else {
            days = new Array(totaldates + 1).fill().map((v, i) => i + start.date()).join(',');
          }
        } else if (totalMonths < 12) {
          month = (new Array(totalMonths + 1).fill().map((v, i) => i + 1 + start.month())).join(',');
        } else if (totalMonths === 12) {
          month = '*';
        } else {
          return (null);
        }
      }
      const final = [];
      for (let i = 0; i < minHour.length; i++) {
        final.push([(minHour[i])[0], (minHour[i])[1], days, month, '*'].join(' '));
      }

      return (final);
    } catch (e) {
      sails.log(e);
      return null;
    }
  },
  /**
   * Check if schedule is deployed or not
   * @param {object} schedule Schedule object from schedule table
   */
  isScheduleDeployed(schedule) {
    if (schedule && schedule.isDeployed === '1') {
      return true;
    }
    return false;
  },

  /**
   * Convert the 2D ts Array into 1D Array of objects example [{rid, sid, startTime, endTime, cron1}, - - ]
   * @param {object} schedule Schedule object from schedule table
   * @param {string} schedule.rid Recipe id in schedule obj.
   * @param {string} schedule.sid Schedule id
   * @param {array} schedule.ts 2D-Array of startTime, endTime and cron. Exmaple
   * [[st1, et1, cron1], [st1, et1, cron2] ]. (YES startTime and endTime will be always same)
   */
  flattenSchedule(schedule) {
    schedule = globalhelper.toJson(schedule);

    if (!schedule) {
      throw new Error('Schedule type expected to be object: recipeutils.flattenschedule');
    }
    const {
      ts: timestamps,
      rid,
      sid: id,
    } = schedule;

    const ts = globalhelper.toArray(timestamps) || [];
    const flattenedSchdules = ts.map((ts) => ({
      rid,
      id,
      cron: ts[2],
      startdate: ts[0],
      enddate: ts[1],
    }));

    return flattenedSchdules;
  },

  absCron(c) {
    const stub = [];
    if (!c) return null;
    for (let i = 0; i < c.length; i++) {
      const temp = c[i].split(' ');
      temp[0] = temp[1] = '*';
      stub.push(temp.join(' '));
    }
    return stub;
  },

  formatScheduleInfo(scheduleInfo) {
    return scheduleInfo.map((s) => globalhelper.toArray(s.ts).map((i) => ({
      id: s.sid,
      rid: s.rid,
      cron: i[2],
      startdate: i[0],
      enddate: i[1],
    })));
  },

  getFormattedAction(actionInfo) {
    return actionInfo.map((actioni) => {
      const title = actioni.title ? actioni.title : 'No title';
      const _priority = priorityMap[actioni.priority] ? priorityMap[actioni.priority] : 'low';
      const toNotify = actioni.notify;
      const smslist = actioni.smslist ? actioni.smslist : [];

      if (actioni.type === 'action') {
        return {
          title,
          type: actioni.type,
          priority: _priority,
          uniqId: actioni.uniqId,
          did: actioni.did,
          command: actioni.command,
          value: actioni.value,
          delay: actioni.delay,
          category: actioni.category,
          notify: toNotify,
          smslist,

        };
      }
      return {
        title,
        type: actioni.type,
        priority: _priority,
        uniqId: actioni.uniqId,
        category: actioni.category,
        notify: toNotify,
        smslist,
      };
    });
  },

  removeDeployedSchedules(schedules) {
    return schedules.filter((schedule) => {
      if (schedule.isDeployed === '1') {
        return false;
      }
      return true;
    });
  },
  createUniqueIdForActionable(actionable, rid) {
    actionable = globalhelper.toString(actionable);
    if (!rid) {
      throw new Error('Required Parameter Missing recipe.utils.creteUniqueIdForActionable');
    }
    const uId = md5(actionable + rid);
    return uId;
  },
  /**
   * Tells is recipe is currently running with a buffer of 6minutes. So if the recipe
   * last ran within 6min, it will return true else false
   * @param {Integer} currentTime Current time in unix
   * @param {Integer} lastActive Last active time in unix
   * @return {boolean} Is recipe active or not
   */
  isRecipeActive(currentTime, lastActive) {
    return (currentTime - lastActive < 600000);
  },
  /**
   * Get list of deviceIds & param included in data expression of thermostat
   * @param {object} dataExpression Thermostat misc data expression
   * @returns {array} [{deviceId, param}]
   */
  getDeviceParamListFromThermostatExpression(dataExpression) {
    const deviceParamArray = [];
    for (const key in dataExpression) {
      const expression = dataExpression[key];
      if (expression === undefined) continue;
      // getDeviceParamListFromDataExpression only parses expression with @ as delimeter
      const deviceParamList = Components.getDeviceParamListFromDataExpression(expression);
      deviceParamArray.push(...deviceParamList);
    }
    return deviceParamArray;
  },
  /**
   * Given list of deviceId & param, create topic for controller to query database in format db/DEVICEID/PARAM/COUNT
   * @param {array} deviceParamArray List of objects with deviceId & Param
   */
  getTopicsArray(deviceParamArray) {
    const topics = deviceParamArray.map((deviceParam) => {
      const { deviceId, param } = deviceParam;
      return `db/${deviceId}/${param}/2`;
    });
    return topics;
  },

  parseRecipeObjectToSendToController(recipe) {
    let parsedObject = {};

    recipe = globalhelper.toJson(recipe);
    if (!recipe) {
      return false;
    }

    parsedObject = {
      failsafe: JSON.stringify({}),
      everyMinuteTopics: globalhelper.toArray(recipe.everyMinuteTopics),
      runOn: recipe.runOn,
      topics: globalhelper.toArray(recipe.everyMinuteTopics),
      recipe: recipe.formula,
      syncAsyncAction: 'True',
      dependentOnOthers: globalhelper.toArray(recipe.dependentOnOthers),
      controllers: globalhelper.toArray(recipe.dependentOnOthers),
      switchOff: '0',
      actionAlert: globalhelper.toArray(recipe.actionable),
      startNow: 'True',
      maxDataNeeded: recipe.maxDataNeeded,
      rid: recipe.rid,
      notRun: (recipe.notRun), // start and end time
      appType: recipe.appType,
      misc: globalhelper.toJson(recipe.misc),
    };

    return parsedObject;
  },

  parseThermalObjectToSendToController(recipe) {
    let parsedObject = {};
    recipe = globalhelper.toJson(recipe);
    if (!recipe) {
      return false;
    }
    const misc = globalhelper.toJson(recipe.misc);
    const { dataExpression } = misc;
    for (const key in dataExpression) {
      let formula = dataExpression[key];
      formula = formula.replace(/@/g, '.').replace(/\|\|/g, '');
      misc.dataExpression[key] = formula;
    }

    parsedObject = {
      maxLogsNeeded: recipe.maxLogNeeded,
      failsafe: JSON.stringify({}),
      everyMinuteTopics: globalhelper.toArray(recipe.everyMinuteTopics),
      runOn: recipe.runOn,
      topics: globalhelper.toArray(recipe.everyMinuteTopics),
      recipe: recipe.formula,
      syncAsyncAction: 'True',
      dependentOnOthers: globalhelper.toArray(recipe.dependentOnOthers),
      controllers: globalhelper.toArray(recipe.dependentOnOthers),
      switchOff: '0',
      actionAlert: globalhelper.toArray(recipe.actionable),
      startNow: 'True',
      maxDataNeeded: recipe.maxDataNeeded,
      rid: recipe.rid,
      notRun: (recipe.notRun), // start and end time
      alwaysRun: recipe.alwaysRun,
      appType: recipe.appType,
      misc: globalhelper.toJson(misc),
    };

    return parsedObject;
  },

  stringifyRecipeToSaveInDB(recipe) {
    if (recipe.operators) {
      recipe.operators = globalhelper.toString(recipe.operators);
    }
    if (recipe.params) {
      recipe.params = globalhelper.toString(recipe.params);
    }
    if (recipe.everyMinuteTopics) {
      recipe.everyMinuteTopics = globalhelper.toString(recipe.everyMinuteTopics);
    }
    if (recipe.dependentOnOthers) {
      recipe.dependentOnOthers = globalhelper.toString(recipe.dependentOnOthers);
    }
    if (recipe.operator) {
      recipe.operator = globalhelper.toString(recipe.operator);
    }
    if (recipe.recipelabel) {
      recipe.recipelabel = globalhelper.toString(recipe.recipelabel);
    }
    if (recipe.scheduled) {
      recipe.scheduled = globalhelper.toString(recipe.scheduled);
    }
    if (recipe.devicesIncluded) {
      recipe.devicesIncluded = globalhelper.toString(recipe.devicesIncluded);
    }
  },
  thermalRecipeToControllerObject(recipe) {
    let parsedObject = {};
    recipe = globalhelper.toJson(recipe);
    if (!recipe) {
      throw new Error(`Invalid recipe format: ${recipe}`);
    }
    const misc = globalhelper.toJson(recipe.misc);
    const { dataExpression } = misc;
    for (const key in dataExpression) {
      let formula = dataExpression[key];
      formula = formula.replace(/@/g, '.').replace(/\|\|/g, '');
      misc.dataExpression[key] = formula;
    }

    parsedObject = {
      maxLogsNeeded: recipe.maxLogNeeded,
      failsafe: JSON.stringify({}),
      everyMinuteTopics: globalhelper.toArray(recipe.everyMinuteTopics),
      runOn: recipe.runOn,
      topics: globalhelper.toArray(recipe.everyMinuteTopics),
      syncAsyncAction: 'True',
      dependentOnOthers: globalhelper.toArray(recipe.dependentOnOthers),
      controllers: globalhelper.toArray(recipe.dependentOnOthers),
      switchOff: recipe.switchOff,
      actionAlert: globalhelper.toArray(recipe.actionable),
      startNow: 'True',
      rid: recipe.rid,
      notRun: (recipe.notRun), // start and end time
      alwaysRun: recipe.alwaysRun,
      appType: recipe.appType,
      misc: globalhelper.toJson(misc),
    };
    if (recipe.hasOwnProperty('isOTPanelControllingSetpoint')){
      parsedObject.isOTPanelControllingSetpoint = recipe.isOTPanelControllingSetpoint
    }
    return parsedObject;
  },
  getComponentControlRelationshipByDeviceIdAndCommandAbbr: function(controlsRelationsHipMap, {
    did,
    command
  }) {
    try {
    if (_.isEmpty(controlsRelationsHipMap)) return null;
    for (const config of controlsRelationsHipMap) {
      const { controlType, controlProperty: property } = config;

      switch(controlType) {
        case 'BIT': {
          const left = property.left;
          const right = property.right;

          if (left && left.device_abbr === command
            && left.deviceId === did
          ) {
            return config;
          }
  
          if (right && right.device_abbr === command
             && right.deviceId === did 
          ) {
            return config;
          }
          
          break;
        }
        case 'VIT': {
          const { deviceId, device_abbr} = property
          if (
            deviceId == did && 
            device_abbr === command
          ) return config
        }
        default: {
          break;
        }
      }

    }
    return null;
  } catch(e) {
    sails.log('controlRelationShip > error', e.message);
    return null;
  }

  },
  attachControlRelationship: function(controlRelationship, {did, command}) {
    const response = {
      "controlabbr": null,
      "commandabbr": null, 
      "dataparamabbr": null,
      "commandtype" :null
    }
    if (!controlRelationship) return response;
    const { dataParamAbbr, controlType, controlAbbr, controlProperty: property } = controlRelationship;
    response.commandtype = controlType;
    response.controlabbr = controlAbbr;
    response.dataparamabbr = dataParamAbbr;

    switch(controlType) {
      case 'BIT': {
        const left = property.left;
        const right = property.right;

        if (left && left.device_abbr === command 
          && left.deviceId === did
        ) {
          response.commandabbr = left.commandAbbr;
          return response;
        }

        if (
          right && right.device_abbr === command 
          && right.deviceId === did
        ) {
          response.commandabbr = right.commandAbbr;
          return response;
        }
        
        break;
      }
      case 'VIT': {
        response.commandabbr = property.commandAbbr;
        return response;
      }
      default: {
        break;
      }
    }
    
    return response;
  },
  getIOTDataPacketToEnableCommandRetention(commandRetention) {
    if (commandRetention.commandType == 'BIT') {
        const {
            controlAbbr,
            commandType,
            right: {
                deviceId: rightDeviceId,
                commandAbbr: rightCommandAbbr,
                deviceAbbr: rightDeviceAbbr,
                controllerId: rightControllerId,
                label: rightLabel,
                dataParamAbbr: rightDataParamAbbr
            },
            left: {
                deviceId: leftDeviceId,
                commandAbbr: leftCommandAbbr,
                deviceAbbr: leftDeviceAbbr,
                controllerId: leftControllerId,
                label: leftLabel,
                dataParamAbbr: leftDataParamAbbr
            },
            siteId,
            componentId,
            recipeId,
            uniqId,
        } = commandRetention;
        return _buildCommandRetentionDataPacketBIT({
          siteId:siteId,
          componentId: componentId,
          rid: recipeId, // Check once again 
          recipeId,
          uniqId,
          commandType,
          controlAbbr,
          right: {
            deviceId: rightDeviceId,
            commandAbbr: rightCommandAbbr,
            deviceAbbr: rightDeviceAbbr ,
            controllerId: rightControllerId,
            label: rightLabel,
            dataParamAbbr: rightDataParamAbbr
          },
          left: {
            deviceId: leftDeviceId,
            commandAbbr: leftCommandAbbr,
            deviceAbbr: leftDeviceAbbr,
            controllerId: leftControllerId,
            label: leftLabel,
            dataParamAbbr: leftDataParamAbbr
           }
        }) 

      } else {
        const {
            controlAbbr,
            deviceId,
            commandAbbr,
            deviceAbbr,
            controllerId,
            commandType,
            label,
            siteId,
            componentId,
            recipeId,
            uniqId,
            dataParamAbbr
        } = commandRetention
        return _buildCommandRetentionDataPacketVIT({  
          controlAbbr,
          deviceId,
          commandAbbr,
          deviceAbbr,
          controllerId,
          siteId:siteId,
          label,
          componentId: componentId,
          recipeId,
          uniqId,
          rid: recipeId,
          commandType,
          dataParamAbbr

        })

      }

},
fetchDisableCommandInfo(recipes, controls) {
  const activeRecipes = recipes.filter((row) => 
    row?.appType && row.appType == 'commandretention' && 
    row.commandRetentionRecipeStatus == "1" && row.hasOwnProperty('actionable') && row.isStage == '0'
  );
  //Pending state
  const attachRecipeIdToControlAbbr = activeRecipes.reduce((acc, cur) => {
    const {actionable, rid} = cur;
    const controlAbbr = JSON.parse(actionable)[0].controlsRelationship.controlabbr
    acc[controlAbbr] = rid;
    return acc;
  }, {});

  const attachControllerToControlAbbr = controls.reduce((acc, cur)=> {
    const {controlAbbr, controllerId} = cur;
    acc[controlAbbr] = controllerId
    return acc;
  }, {})

  const enabledRecipesToDisable = Object.keys(attachControllerToControlAbbr).reduce((acc, controlAbbr) => {
    if (attachRecipeIdToControlAbbr.hasOwnProperty(controlAbbr) && attachRecipeIdToControlAbbr[controlAbbr]) acc.push(controlAbbr);
    return acc;
  }, []);

  const disableRecipes = enabledRecipesToDisable.reduce((acc, controlAbbr) => {
    const cur = {}
    cur.rid = attachRecipeIdToControlAbbr[controlAbbr];
    cur.controllerId = attachControllerToControlAbbr[controlAbbr];
    cur.controlAbbr = controlAbbr;
    acc.push(cur);
    return acc
  }, [])
  return disableRecipes;
},
fetchPendingStateCommandRetention(recipes, controls) {
  if (!controls.length) return;
  const pendingRecipes = recipes.filter((row) => 
    row?.appType && row.appType == 'commandretention' && row.hasOwnProperty('commandRetentionRecipeStatus') &&
    row.commandRetentionRecipeStatus == "1" && row.hasOwnProperty('actionable') && row.isStage == "1"
  );
  //Pending state
  const attachRecipeIdToControlAbbr = pendingRecipes.reduce((acc, cur) => {
    const {actionable, rid} = cur;
    const controlAbbr = JSON.parse(actionable)[0].controlsRelationship.controlabbr
    acc[controlAbbr] = rid;
    return acc;
  }, {});

  const attachControllerToControlAbbr = controls.reduce((acc, cur)=> {
    const {controlAbbr, controllerId} = cur;
    acc[controlAbbr] = controllerId
    return acc;
  }, {})

  const pendingRecipesToDisable = Object.keys(attachControllerToControlAbbr).reduce((acc, controlAbbr) => {
    if (attachRecipeIdToControlAbbr.hasOwnProperty(controlAbbr) && attachRecipeIdToControlAbbr[controlAbbr]) acc.push(controlAbbr);
    return acc;
  }, []);


  const disableRecipes = pendingRecipesToDisable.reduce((acc, controlAbbr) => {
    const cur = {}
    cur.rid = attachRecipeIdToControlAbbr[controlAbbr];
    cur.controllerId = attachControllerToControlAbbr[controlAbbr];
    cur.controlAbbr = controlAbbr;
    acc.push(cur);
    return acc
  }, [])
  return disableRecipes;
},
throwExceptionRecipeNotExistToDisable: (invalidRecipeNotExist) => {
  throw flaverr({
    code: 'E_INVALID_COMMANDS_RETENTION_TO_DISABLE',
    message: `Controls is not enabled yet to disable ${invalidRecipeNotExist.join(',')}`,
    HTTP_STATUS_CODE: 400
})
}
};


const _buildCommandRetentionDataPacketVIT = (command) => {
  return  {
     "operation": "recipeInit",
     "scheduleInfo": [],
     "recipeInfo": {
       "failsafe": "{}",
       "everyMinuteTopics": [],
       "runOn": command.controllerId.toString(),
       "topics": [], 
       "recipe": "", 
       "syncAsyncAction": "True",
       "dependentOnOthers": [],
       "controllers": [command.controllerId.toString()],
       "switchOff": "0",
       "actionAlert": [
         _buildActionDetails(command)
       ],
       "startNow": "True",
       "maxDataNeeded": "1",
       "rid": command.rid,
       "notRun": "[]",
       "appType": "commandRetentionRecipe",
       "requestId": command.rid,
     }
   }
   
 }

const  _buildActionDetails = (command) => {
   return {
     "controlsRelationship" : {
       "controlabbr": command.controlAbbr,
       "commandabbr": command.commandAbbr,
       "dataparamabbr": command.dataParamAbbr,
       "commandtype": command.commandType
       },
     "command": command.deviceAbbr,
     "did": command.deviceId,
     "parent": command.componentId,
     "value": null, 
     "title": command.label,
     "description": "",
     "type": "action",
     "notify": [],
     "smslist": [],
     "accountable": [],
     "priority": 3,
     "uniqId": command.uniqId,
     "category": ["commandretention"]
   }
 }

const  _buildCommandRetentionDataPacketBIT = (command) => {
  const {componentId, controlAbbr,commandType, uniqId} = command
   return  {
     "operation": "recipeInit",
     "scheduleInfo": [],
     "recipeInfo": {
       "failsafe": "{}",
       "everyMinuteTopics": [],
       "runOn": command.left.controllerId.toString(),
       "topics": [], 
       "recipe": "", 
       "syncAsyncAction": "True",
       "dependentOnOthers": [],
       "controllers": [command.left.controllerId.toString()],
       "switchOff": "0",
       "actionAlert": [
         _buildActionDetails({...command.left, controlAbbr, componentId, commandType, uniqId }),
         _buildActionDetails({...command.right, controlAbbr, componentId, commandType, uniqId }),
       ],
       "startNow": "True",
       "maxDataNeeded": "1",
       "rid": command.rid,
       "notRun": "[]",
       "appType": "commandRetentionRecipe",
       "requestId": command.rid,
     }
   }
 }
