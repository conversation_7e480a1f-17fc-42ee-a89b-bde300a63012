
const globalHelper = require('../../utils/globalhelper');
const utils = require('./utils');


module.exports = {

  buildInitialPacket: function (inputs, recipe) {
    let { rid, siteId, type, neo, isSchedule, label, componentsType,
      formula, params, operators, maxDataNeeded, misc = {} } = inputs; // Only take things that can change
    let { appType = 'recipe', scheduled, switchOff,
      notRun, isStage, componentId } = recipe;

    let oldMisc = globalHelper.toJson(recipe.misc);
    scheduled = globalHelper.toJson(scheduled);

    let user = inputs._userMeta.id;
    let recipelabel, actionable;

    recipelabel = inputs.recipelabel.map(utils.getRecipeCategory);  // Convert recipe Category from FrontEnd to Backend understandable
    if (recipelabel.indexOf(undefined) !== -1) {
      return { problems: ['Invalid Recipe Label'] };
    }
    notRun = globalHelper.toArray(notRun);
    actionable = inputs.actionable.map(action => { // adding a categories & uniqueId to each actionable
      return { ...action, 'uniqId': utils.createUniqueIdForActionable(action, rid), 'category': recipelabel };
    });

    if (appType === 'thermal' && !this.validateMisc(misc, oldMisc)) return { problems: ['Invalid thermostat configuration'] };

    let mainObj = {
      rid, siteId, type, label, isSchedule,
      user, neo, maxDataNeeded,
      formula, operators,
      params,
      appType,
      switchOff,
      isStage,
      misc: globalHelper.toString(misc),
      notRun,  // this is also stringified array
      scheduled, // this is also stringified array
      actionable: actionable,
      recipelabel: recipelabel,
      componentsType
    };
    if (componentId) mainObj['componentId'] = componentId;
    mainObj = globalHelper.removeNullValueFromObject(mainObj);
    return mainObj;
  },

  validateMisc: function (misc, oldMisc) {
    if (!misc.controlSettings || !misc.appSettings || !misc.dataExpression) return false;
    if (misc.controlSettings.sampleTime !== oldMisc.controlSettings.sampleTime) return false;
    return true; // write this function to validate misc keys
  },
  JSONifyRecipeObjectForController: function(recipe) {
    // not useful now
    recipe['params'] = globalHelper.toJson(recipe['params']);
    recipe['everyMinuteTopics'] = globalHelper.toArray(recipe['everyMinuteTopics']);
    recipe['dependentOnOthers'] = globalHelper.toArray(recipe['dependentOnOthers']);
    recipe['operator'] = globalHelper.toJson(recipe['operator']);
    recipe['actionable'] = globalHelper.toArray(recipe['actionable']);
    return recipe;
  }

};
