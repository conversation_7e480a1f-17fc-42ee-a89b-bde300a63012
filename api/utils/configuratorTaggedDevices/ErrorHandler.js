const flaverr = require('flaverr');

module.exports = {
  throwExceptionInvalidAbbrNotAllowed: function ({ deviceClass, invalidAbbrObj }) {
    const messageFormat = [];
    for (const [deviceId, abbrCollection] of Object.entries(invalidAbbrObj)) {
      let _dataAbbrs; let _controlAbbrs;
      if (!_.isEmpty(abbrCollection.dataParams)) {
        _dataAbbrs = abbrCollection.dataParams.join(',')
      }
      if (!_.isEmpty(abbrCollection.controlParams)) {
        _controlAbbrs = abbrCollection.controlParams.join(',')
      }
      if (_dataAbbrs) {
        messageFormat.push(`For ${deviceClass === "device" ? "deviceId" : "componentId"} ${deviceId}, these data abbrs (${_dataAbbrs}) do not exist/configured.`)
      }
      if (_controlAbbrs) {
        messageFormat.push(`For ${deviceClass === "device" ? "deviceId" : "componentId"} ${deviceId}, these control abbrs (${_controlAbbrs}) do not exist/configured.`)
      }
    }
    throw flaverr({
      code: 'E_INVALID_ABBR_NOT_ALLOWED',
      message: messageFormat,
      data: { deviceClass, invalidAbbrObj }
    });
  },
  throwExceptionInvalidComponentIdExist: function (errorDetailObj) {
    throw flaverr({
      code: 'E_COMPONENTS_NOT_FOUND',
      message: `these componentIds [${errorDetailObj.join(',')}] are not available.`,
      data: errorDetailObj
    });
  },
  throwsExceptionSvgLocationOccupied: function (errorDetailObj) {
    const {
      svgLocationId,
      newComponentId,
      existingComponentId
    } = errorDetailObj;
    throw flaverr({
      code: 'E_SVG_LOCATION_ALREADY_TAGGED',
      message: `svgLocationId "${svgLocationId}" is already tagged with Component Id "${existingComponentId}". you can not tag new component ${newComponentId} at this location`,
      data: errorDetailObj
    });
  },
  throwInvalidDataAbbrNotAllowed: function (errorDetailObj) {
    const messageFormat = `For device "${errorDetailObj.map((item) => Object.keys(item))
      .toString()}" data param/control params does not exist.
    Please check the component configuration first
    `;
    throw flaverr({
      code: 'E_INVALID_DEVICE_DATA_ABBR_NOT_ALLOWED',
      message: messageFormat,
      data: errorDetailObj
    });
  },
  // eslint-disable-next-line max-len
  throwExceptionInvalidConfiguratorTaggedDevices: function (configuratorSystemTaggedDeviceId) {
    throw flaverr('E_INVALID_TAGGING_ID', new Error(`tagging id "${configuratorSystemTaggedDeviceId}" does not exist.`));
  },
  throwExceptionInvalidDevicesExist: function (errorDetailObj) {
    throw flaverr({
      code: 'E_DEVICE_NOT_FOUND',
      message: `these deviceIds [${errorDetailObj.join(',')}] does not available.`,
      data: errorDetailObj
    });
  },
  throwExceptionInvalidConfiguratorSubsystemPage: function (pageId) {
    throw flaverr('E_INVALID_SUBSYSTEM_PAGE', new Error(`Configurator subsystem page "${pageId}" does not exist.`));

  },
  throwExceptionInvalidConfiguratorSystem: function (systemId) {
    throw flaverr('E_INVALID_CONFIGURATOR_SYSTEM', new Error(`Configurator system of "${systemId}" does not exist.`));
  },
  throwExceptionInvalidSystemMappedSite: function (siteId) {
    throw flaverr('E_INVALID_SYSTEM_SITE_MAPPED', new Error(`Configurator system does not have mapping to the site :${siteId}`));
  }
};
