const flaverr = require('flaverr');
const ErrorHandler = require('./ErrorHandler');
const Joi = require('joi');
const validateSVGTaggingControlAbbr = (deviceRelationShipData, svgTaggingData) => {
  const relationshipDataMap = buildObject(deviceRelationShipData, 'deviceRelationshipData');
  const svgTaggingDataMap = buildObject(svgTaggingData, 'svgTaggingData');

  const missingAbbr = [];

  for (let deviceId in svgTaggingDataMap) {
    const svgTaggingRow = svgTaggingDataMap[deviceId];
    const relationshipRow = relationshipDataMap[deviceId];

    const missingControlAbbr = findInvalidAbbr(
      svgTaggingRow.controls,
      relationshipRow.controls
    );

    if (missingControlAbbr.length) {
      missingAbbr.push({
        deviceId,
        missing: missingControlAbbr,
      });
    }
  }

  return missingAbbr;
};

const checkIsDuplicateComponentExist = (dataTaggingDetails) => {
  const component = new Set();
  const duplicateComponent = [];
  for (const row of dataTaggingDetails) {
    const { deviceId } = row.data;
    if (component.has(deviceId)) {
      duplicateComponent.push(deviceId);
    } else {
      component.add(deviceId);
    }
  }
  return duplicateComponent;
};

// Utility function to build the object from the given data array
const buildObject = (dataArray, type) => {
  const dataMap = {};

  if (type === 'deviceRelationshipData') {
    for (let data of dataArray) {
      const {
        deviceId,
        dataParamAbbr,
        controlAbbr
      } = data;

      if (!dataMap.hasOwnProperty(deviceId)) {
        dataMap[deviceId] = {
          parameters: [],
          controls: [],
        };
      }

      if (dataParamAbbr) {
        dataMap[deviceId].parameters.push(dataParamAbbr);
      }

      if (controlAbbr) {
        dataMap[deviceId].controls.push(controlAbbr);
      }
    }
  } else if (type === 'svgTaggingData') {
    for (let taggingData of dataArray) {
      const {
        deviceId,
        controls,
        data
      } = taggingData.data;

      dataMap[deviceId] = {
        parameters: data.parameters || [],
        controls: controls.controlAbbr || [],
      };
    }
  }

  return dataMap;
};

// Utility function to find missing abbr in the given data array
const findInvalidAbbr = (abbrArray, referenceArray) => {
  const missingAbbr = [];

  for (let abbr of abbrArray) {
    if (!referenceArray.includes(abbr)) {
      missingAbbr.push(abbr);
    }
  }

  return missingAbbr;
};

const validateSVGTaggingDataAbbr = (componentConfigDataMap, dataTaggerDetail) => {
  const dataParameterComponentMap = new Map();
  for (const taggingData of dataTaggerDetail) {
    const { data: assetData } = taggingData;
    const { deviceId } = assetData;
    dataParameterComponentMap.set(deviceId, assetData.data.parameters);
  }

  for (const [deviceId, parameters] of dataParameterComponentMap) {
    const componentConfigInfo = componentConfigDataMap.get(deviceId);
    let componentDataParameters = [];
    if (componentConfigInfo && componentConfigInfo.hasOwnProperty('data') && componentConfigInfo.data.length > 0) {
      componentDataParameters = componentConfigInfo.data.map(param => param.key);
    }
    if (componentDataParameters.length === 0 && parameters.length >= 1) {
      throw flaverr('E_COMPONENT_TAGGED_DEVICES_DATA_PARAMETER_NOT_FOUND', new Error(`No data parameter found for the ${deviceId}`));
    }

    for (const param of parameters) {
      if (!componentDataParameters.includes(param)) {
        throw flaverr('E_COMPONENT_TAGGED_DEVICES_DATA_PARAMETER_NOT_FOUND', new Error(`Data parameter ${param} not found for ${deviceId}`));
      }
    }
  }
};

const extractComponentDeviceList = (dataTaggerDetail) => {
  const componentDeviceMap = {
    devices: [],
    components: []
  };
  for (const {assetData} of dataTaggerDetail) {
    const {
      deviceClass,
      deviceId
    } = assetData;
    if (!deviceId) {
      continue;
    }
    if (deviceClass === 'component') {
      componentDeviceMap.components.push(deviceId);
    }
    if (deviceClass === 'device') {
      componentDeviceMap.devices.push(deviceId);
    }
  }
  return componentDeviceMap;
};

const extractAllTypeParamFromPayload = payload => {
  const obj = {};
  for (const {
    assetData: {
      deviceClass,
      deviceId,
      data,
      controls,
      uiElementType
    }
  } of payload) {
    if (!obj.hasOwnProperty(deviceId)) {
      obj[deviceId] = {};
      obj[deviceId].dataParams = new Set();
      obj[deviceId].controlParams = new Set();
    }
    if (data && !_.isEmpty(data.parameters)) {
      data.parameters.forEach(obj[deviceId].dataParams.add, obj[deviceId].dataParams);
    }
    if (controls && !_.isEmpty(controls.controlAbbr)) {
      controls.controlAbbr.forEach(obj[deviceId].controlParams.add, obj[deviceId].controlParams);
    }
  }
  return obj;
};

const getDuplicateTaggedComponentIfExist = (dataTaggerDetail) => {
  const uniqueCompId = new Set();
  const duplicatesComponent = [];
  dataTaggerDetail.forEach((tagging) => {
    const {
      data: {
        deviceId,
        svgLocationId
      }
    } = tagging;
    if (uniqueCompId.has(`${deviceId}_${svgLocationId}`)) {
      duplicatesComponent.push(tagging);
    } else {
      uniqueCompId.add(`${deviceId}_${svgLocationId}`);
    }
  });
  return duplicatesComponent;
};

function validateComponentIds(testComponentIds, allowedComponentIds) {
  const invalidComponentIds = testComponentIds.filter(component => !allowedComponentIds.includes(component));
  if (invalidComponentIds.length) ErrorHandler.throwExceptionInvalidComponentIdExist(invalidComponentIds)
}
function validateTaggedComponentDataControlAbbrs(incomingComponentAbbrsCollection,ComponentConfigMap){
  const invalidAbbrDevices = {};
  for (const [componentId, abbrCollection] of Object.entries(incomingComponentAbbrsCollection)) {
    const {
      dataParams,
      controlParams
    } = abbrCollection;
    const _componentConfigDetail = ComponentConfigMap.get(componentId);
    for (const abbr of dataParams.values()) {
      if (!_componentConfigDetail.isValidDataAbbr(abbr)) {
        if(!invalidAbbrDevices.hasOwnProperty(componentId)){
          invalidAbbrDevices[componentId]={
            controlParams:[],
            dataParams:[],
          }
        }
        invalidAbbrDevices[componentId].dataParams.push(abbr);
      }
    }
    for (const abbr of controlParams.values()) {
      if (!_componentConfigDetail.isValidControlAbbr(abbr)) {
        if(!invalidAbbrDevices.hasOwnProperty(componentId)){
          invalidAbbrDevices[componentId]={
            controlParams:[],
            dataParams:[],
          }
        }
        invalidAbbrDevices[componentId].controlParams.push(abbr);      }
    }
  }
  if (!_.isEmpty(invalidAbbrDevices)) {
    ErrorHandler.throwExceptionInvalidAbbrNotAllowed({ deviceClass:"component", invalidAbbrObj:invalidAbbrDevices });
  }
}
function validateDevicesExists(testDeviceId, registeredDeviceIds) {
  const invalidDeviceIds = testDeviceId.filter(deviceId => !registeredDeviceIds.includes(deviceId));
  if (invalidDeviceIds.length) ErrorHandler.throwExceptionInvalidComponentIdExist(invalidDeviceIds)
}

function validateTaggedDeviceDataAbbrs(incomingDeviceAbbrsCollection, deviceParamMap) {
  const invalidAbbrDevices = {};
  for (const [deviceId, abbrCollection] of Object.entries(incomingDeviceAbbrsCollection)) {
    const {
      dataParams,
    } = abbrCollection;
    for (const abbr of dataParams.values()) {
      if (!deviceParamMap[deviceId].map(it => it.abbr)
        .includes(abbr)) {
        if(!invalidAbbrDevices.hasOwnProperty(deviceId)){
          invalidAbbrDevices[deviceId]={
            dataParams: [],
          }
        }
        invalidAbbrDevices[deviceId].dataParams.push(abbr);
      }
    }
  }
  if (!_.isEmpty(invalidAbbrDevices)) {
    ErrorHandler.throwExceptionInvalidAbbrNotAllowed({ deviceClass:"device", invalidAbbrObj:invalidAbbrDevices });
  }
}

function getDataTaggerOfUIElementType(deviceData) {
  let {
      id,
      deviceId,
      elementId,
      svgLocationId,
      displayFormat,
      textLabelData,
      mode,
      offset,
      order,
      data: {
          parameters,
          style,
          position
      },
      controls: {
          style: controlStyle,
          controlAbbr,
          controlRelationshipData: filteredControlRelationshipData,
          position: controlPosition
      },
      uiElementType
  } = deviceData;

  let schema = {
      id,
      deviceId,
      elementId,
      svgLocationId,
      displayFormat,
      textLabelData,
      mode,
      offset,
      order,
      data: {
          parameters,
          style,
          position
      },
      controls: {
          style: controlStyle,
          controlAbbr,
          controlRelationshipData: filteredControlRelationshipData,
          position: controlPosition,
      },
      uiElementType
  };
  // ['floatingParameter', 'floatingControl', 'floatingLabel', 'svgAttachedCard', 'floatingCard'];
  switch(uiElementType) {
    case 'svgAttachedCard': {
      delete schema.textLabelData;
      delete schema.displayFormat
    }
    case 'floatingCard': {

    }
    case 'floatingLabel': {

    }
    case 'floatingControl': {

    }
    case 'floatingParameter': {

    }
    default: {
      
    }
  }

  return schema
}

module.exports = {
  validateSVGTaggingControlAbbr: validateSVGTaggingControlAbbr,
  getDuplicateTaggedComponentIfExist,
  checkIsDuplicateComponentExist,
  validateSVGTaggingDataAbbr: validateSVGTaggingDataAbbr,
  extractComponentDeviceList: extractComponentDeviceList,
  extractAllTypeParamFromPayload,
  validateComponentIds,
  validateTaggedComponentDataControlAbbrs,
  validateDevicesExists,
  validateTaggedDeviceDataAbbrs,
  getDataTaggerOfUIElementType

};
