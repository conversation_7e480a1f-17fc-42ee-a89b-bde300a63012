const moment = require('moment-timezone');
const math = require('mathjs');
const fs = require("fs");
const flaverr = require('flaverr');
const { XMLParser } = require("fast-xml-parser");

math.createUnit('delC', { definition: '1 degC' });
math.createUnit('delF', { definition: '0.5555555555555556 delC' });
moment.tz.add(
  'Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6',
);
moment.tz.setDefault("Asia/Kolkata");
moment.defaultFormat = 'YYYY-MM-DD HH:mm:00';

const DEFAULT_DECIMAL_ROUND_PLACES = 2; // round values to 2 places by default
const DEFAULT_PF = 0.98;
const ENUM_CONSUMPTION_UNITS = ['kwh', 'kvah', 'ebkwh'];
const ALLOWED_FORMATS = ["Z", "ZZ", "z", "utcOffsetInMinute", "utcOffsetInNumber", 'tz'];
/**
 * Compulsory to write name of function in below index
 * Helper functions
 * Conversion FROM X to Y
 * toJson
 * toArray
 * toString
 * toBoolean
 * toMoment
 * bufferToString
 * stringToBoolean
 * convertStringArrayToJSONArray
 * convertJSONArrayToStringArray
 * convertKWHToKVAH
 * convertKvahToKwh
 * stringifyEachKeyOfObject

 * Check true/false
 * isNullishArray
 * isNullish
 * isEmail
 * isValidDateTime
 * isComponent
 * isComponentOfTypeProcess
 * isArrayInteger

 * Higer order functions
 * unique -> higher order function
 * checkObjectKeys

 * FORMATTING INPUT
 * getCurrentUnixTs
 * getCurrentFormattedDate
 * formatDateTime
 * removeNullValueFromObject
 * generateRandomString
 * removeAllItemsFromArray
 * parseMQTTTopic
 * roundNumber
 * convertUnits
 * calculateStandardDeviation
 */

module.exports = {
  /**
   * @function saveMockData
   * @summary Generates a file "<fileName>.json" in folder, ie "./test/mockdata/*" , with the stringified version of the data passed to it. Used to help create mockData for testing purposes.
   * @param {Any Datatype} data Any data
   * <AUTHOR> Gupta
   */
  "saveMockData": function (data, fileName) {
    fs.writeFileSync(`./test/mockdata/${fileName}.json`, JSON.stringify(data));
  },
  "readMockData": function (fileName) {
    let data = fs.readFileSync(`./test/mockdata/${fileName}.json`);
    try {
      data = JSON.parse(data);
    } catch (error) {
      sails.log.error("Error parsing data read from file: ", fileName);
      throw error;
    }
    return data;
  },
  async fn(inputs) {
    // required by sails to exists
  },
  toJson(something) {
    if (!something) {
      return undefined;
    }
    try {
      const _something = something.constructor.name === 'Object'
        ? something
        : JSON.parse(something);
      if (_something.constructor.name === 'Object') {
        return _something;
      }
      return undefined;
    } catch (e) {
      return undefined;
    }
  },
  toArray(a) {
    if (!a) {
      return undefined;
    }
    try {
      a = a.constructor.name === 'Array' ? a : JSON.parse(a);
      if (a.constructor.name === 'Array') {
        return a;
      }
      return undefined;
    } catch (e) {
      return undefined;
    }
  },
  toString(object) {
    if (typeof object === 'string') {
      return object;
    }
    return JSON.stringify(object);
  },
  toBoolean(bool) {
    if (typeof bool === 'boolean') {
      return bool;
    }
    if (typeof bool === 'string') {
      if (bool === 'true') return true;
      if (bool === 'false') return false;
      if (bool === 'null') return false;
      if (bool === 'undefined') return false;
      return Boolean(bool);
    }
    return Boolean(bool);
  },
  /**
   * convert buffer to string
   * @param {array} data utf encoded array
   * @returns {string} stringified version of data
   */
  bufferToString: (data) => {
    let dataObj;
    if (Buffer.isBuffer(data)) dataObj = data.toString();
    try {
      dataObj = JSON.parse(dataObj);
    } catch (e) {
      dataObj = data.toString();
    }
    return dataObj;
  },
  /**
   * convert string to boolean
   * @param {sttring} str Input string
   */
  stringToBoolean(str) {
    if (typeof str === 'boolean') {
      return str;
    }
    if (str.toString() !== 'true' && str.toString() !== 'false') {
      return undefined;
    }
    if (str.toString() === 'true') {
      return true;
    }
    return false;
  },

  isComponentOfTypeProcess(componentId) {
    if (typeof componentId !== 'string') {
      return false;
    }
    const componentType = sails.config.PROCESS_SITES.find((siteId) => componentId.indexOf(siteId) === 0);

    if (componentType === undefined) {
      return false;
    }
    return true;
  },
  /**
   * Higher order function to use with JS array functions like .map/.filter. Removes duplicate values from array
   * @param {string} value Value to Look for in
   * @param {number} index Current Value array index
   * @param {Array} self current array iteself
   * @returns {Array} unique value
   */
  unique(value, index, self) {
    return self.indexOf(value) === index;
  },

  /**
   * @description Returns true if any of the passed values are Nullish. Read isNullish()
   * @param {array} valuesArr An array of values you want to validate.
   * @returns {boolean} true/ false
   */
  isNullishArray(valuesArr) {
    try {
      if (!Array.isArray(valuesArr)) {
        sails.log.error(
          '[-]globalHelpers.isNullish Expected values to be of type array',
        );
        valuesArr = [valuesArr];
      }
      for (let i = 0; i < valuesArr.length; i++) {
        const val = valuesArr[i];
        if (this.isNullish(val)) return true;
      }
      return false;
    } catch (e) {
      sails.log.error('globalHelpers::isNullishArray ', e);
      return true;
    }
  },

  /**
   * @description Returns true if the passed value is either
   * undefined, NAN, null, empty array, empty object or an empty string.
   * @param {any} val Value of any type.
   */
  isNullish(val) {
    if (val === '' || typeof val === 'undefined' || val === null) {
      return true;
    }
    if (val !== val) {
      // trick to check Nan
      return true;
    }
    if (Array.isArray(val) && val.length === 0) {
      return true;
    }
    if (val.constructor.name === 'Object' && Object.keys(val).length === 0) {
      return true;
    }
    return false;
  },

  /**
   * @description Checks for object keys if they are Nullish.
   * @param {object} bodyObject Object being tested
   * @param {array} valueArr Array of strings containing key names
   * @param {string} objectName Name of object being sent.
   * @returns {array} Returns array of errors if any.
   */
  checkObjectKeys(bodyObject, valueArr, objectName = 'someObject') {
    const errors = [];
    valueArr.forEach((value) => {
      if (this.isNullish(bodyObject[value])) {
        errors.push(
          `'${objectName}.${value}' was found to be Nullish or not present.`,
        );
      }
    });
    return errors;
  },

  convertStringArrayToJSONArray(arr) {
    if (!arr) {
      return;
    }
    arr = this.toArray(arr);
    if (!Array.isArray(arr)) {
      return null;
    }

    for (let i = 0; i < arr.length; i++) {
      try {
        if (typeof arr[i] === 'string') {
          arr[i] = JSON.parse(arr[i]);
        }
      } catch (e) {
        sails.log.error('globalHelper::convertStringArrayToJSONArray ', e);
      }
    }
    return arr;
  },
  safeJsonParse(str) {
    try {
      return JSON.parse(str);
    } catch (e) {
      sails.log.error('[JSON Parse Error]', e);
      return null;
    }
  },
  convertJSONArrayToStringArray(arr) {
    if (!arr) {
      return;
    }
    if (!Array.isArray(arr)) {
      return;
    }
    for (let i = 0; i < arr.length; i++) {
      try {
        if (typeof arr[i] === 'object') {
          arr[i] = JSON.stringify(arr[i]);
        }
      } catch (e) {
        sails.log.error('globalHelper::convertJSONArrayToStringArray ', e);
      }
    }
    return arr;
  },
  isEmail(email) {
    const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
  },
  /**
   * Given a string, this function checks if input string is valid date-time or not
   * @param {string} dateTime Any date-time formatted string
   */
  isValidDateTime(dateTime) {
    if (!dateTime) {
      return false;
    }
    if (typeof dateTime === 'string' && dateTime.match(/^\d+$/)) {
      dateTime = parseInt(dateTime);
    }

    const momentTs = moment(dateTime);

    if (momentTs) {
      if (momentTs.isValid()) {
        return true;
      }
      return false;
    }
    return false;
  },
  /**
   * @description Get current Date formatted in YYYY-MM-DD HH:mm
   */
  getCurrentFormattedDate() {
    return moment().format();
  },
  getCurrentUnixTs() {
    return moment().unix() * 1000;
  },
  /**
   * Given datetime/unix/any date format, parse it to Dejoule's format
   * @param {string} dateTime datetime/unix string
   */
  formatDateTime(dateTime) {
    if (typeof dateTime === 'string' && dateTime.match(/^\d+$/)) {
      dateTime = parseInt(dateTime); // if string is passed in unix format
    }
    return moment(dateTime).format();
  },
  /*
   * Remove the keys from the object which have  null/undefined as value
   */
  removeNullValueFromObject(obj) {
    const newObj = {};
    Object.keys(obj)
      .forEach((key) => {
        if (typeof obj[key] === 'undefined' || obj[key] === null) {
          // pass
        } else {
          newObj[key] = obj[key];
        }
      });
    return newObj;
  },

  /**
   * @description Generates a string of a desired length.
   * @param {integer} outputLength Length of the output string
   * @returns {string}
   */
  generateRandomString(outputLength) {
    let text = '';
    const possible = 'abcdefghijklmnopqrstuvwxyz';
    for (let i = 0; i < outputLength; i++) text += possible.charAt(Math.floor(Math.random() * possible.length));
    return text;
  },
  /**
   * Returns Moment object of input date time string.
   * NOTE: IF nothing is passed, it returns current datetime's moment object
   * @param {string} dateTime A date time string in any valid date time format
   */
  toMoment(dateTime, format) {
    return moment(dateTime, format);
  },

  /**
   * Returns Unix timestamp of input date time string.
   * NOTE: IF nothing is passed, it returns current datetime's moment object
   * @param {string} dateTime A date time string in any valid date time format
   */
  toUnixTimeStamp(dateTime) {
    if (typeof dateTime === 'string' && dateTime.match(/^\d+$/)) {
      dateTime = parseInt(dateTime); // if string is passed in unix format
    }
    return moment(dateTime).unix() * 1000;
  },

  /**
   * @description Removes all instances of a item from the Array
   * @param {Array} The original array from which items are to be removed
   * @param {any} The value to be removed from the given Array
   * @returns {Array}
   */
  removeAllItemsFromArray(array, value) {
    if (array.constructor.name !== 'Array') throw 'Passed argument is not of type Array';
    let i = 0;
    for (i = 0; i < array.length; i++) {
      if (array[i] === value) array.splice(i, 1);
    }
    return array;
  },
  parseMQTTTopic(topic) {
    if (typeof topic === 'string') {
      const [siteId, service, deviceId, event] = topic.split('/');
      return {
        siteId,
        service,
        deviceId,
        event,
      };
    }
    return {};
  },

  /**
   * Given a device/componentId, this function tells if deviceId is
   * of type component or not.
   * @param {string} deviceId unique device/componentId
   */
  isComponent(deviceId) {
    if (typeof deviceId !== 'string') {
      return false;
    }
    if (deviceId.split('_').length === 2) {
      return true;
    }
    return false;
  },

  /**
   * Round a number 'value' to decimalPlace
   * @param {number} value Value to round
   * @param {number} decimalPlace how many decimal Places to round to
   */
  roundNumber(value, decimalPlace) {
    value = Number(value);
    decimalPlace = Number(decimalPlace);

    if (this.isNullish(value)) {
      return value;
    }
    if (this.isNullish(decimalPlace)) {
      decimalPlace = DEFAULT_DECIMAL_ROUND_PLACES;
    }

    const multiplier = Math.pow(10, decimalPlace);
    return Math.round((value + Number.EPSILON) * multiplier) / multiplier;
  },

  /**
   * Convert a unit from 'currentUnit' unit to 'required' unit
   * @param {string} from unit that currently value is in
   * @param {string} to  unit that we want value to be converted to
   * @param {number} value actual value to convert
   */
  convertUnits(currentUnit, requiredUnit, value) {
    value = Number(value);

    if (this.isNullishArray([currentUnit, requiredUnit, value])) {
      return value;
    }

    try {
      const convertedValue = math.unit(value, currentUnit)
        .toNumber(requiredUnit);
      return this.roundNumber(convertedValue, 2);
    } catch (e) {
      return value;
    }
  },
  /**
   * Find the Standard deviation of passed integers data points
   * @param {array} dataPoints Array of Integer values
   */
  calculateStandardDeviation(dataPoints) {
    return math.std(dataPoints);
  },

  /**
   * Convert kwh to kvah
   * @param {float} kwh KWH value to convert to
   * @param {float} pf Power factor CONSTANT
   */
  convertKWHToKVAH(kwh, pf = DEFAULT_PF) {
    kwh = parseFloat(kwh);
    pf = parseFloat(pf);
    const kvah = kwh / pf;
    return kvah;
  },

  /**
   * Convert kvah to kwh
   * @param {float} kvah KVAHH value to convert to
   * @param {float} pf Power factor CONSTANT
   */
  convertKvahToKwh(kvah, pf = DEFAULT_PF) {
    kvah = parseFloat(kvah);
    pf = parseFloat(pf);
    const kwh = kvah * pf;
    return kwh;
  },
  /**
   * Convert the currValue from 'from' consumption unit
   * to 'preferredUnit' consumption unit
   * @param {Number} currValue current consumption value
   * @param {string} from enum(kWh/kvah) current consumption unit
   * @param {string} preferredUnit enum(kwh/kvah) required cons. unit
   */
  convertConsumptionToPreferredUnit(currValue, from, preferredUnit) {
    if (from.includes('corrected')) from = 'kwh'
    if (from === 'ebkwh') from = 'kwh';
    if (!preferredUnit) preferredUnit = 'kwh';
    if (ENUM_CONSUMPTION_UNITS.indexOf(preferredUnit) === -1) {
      throw new Error('Not a valid Consumption unit');
    }
    if (ENUM_CONSUMPTION_UNITS.indexOf(from) === -1) {
      throw new Error('Not a valid Consumption unit');
    }
    if (this.isNullish(currValue)) {
      return null;
    }
    if (from === preferredUnit) {
      return this.roundNumber(currValue);
    }

    const conversionMap = {
      kvahkwh: this.convertKvahToKwh,
      kwhkvah: this.convertKWHToKVAH,
    };

    const translateFnc = conversionMap[`${from}${preferredUnit}`];
    if (translateFnc === undefined) {
      throw new Error(`Not a valid conversion: ${from} to ${preferredUnit}`);
    }

    return this.roundNumber(
      translateFnc(currValue),
    );
  },
  /**
   * Check if input array has all integer/float values or not
   * @param {array} inputArray input array to check values of
   * @returns boolean true/false
   */
  isNumberArray(inputArray) {
    if (inputArray.constructor.name !== 'Array') {
      return false;
    }
    const nonIntInput = inputArray
      .find((data) => typeof (data) !== 'number');

    if (nonIntInput === undefined) {
      return true;
    }
    return false;
  },

  /**
   * Converts the value of each key of an object to string
   * @param {object} obj input object to be stringified
   * @returns {object} object with stringified values
   */
  stringifyEachKeyOfObject(obj) {
    for (const key in obj) {
      obj[key] = this.toString(obj[key]);
    }
    return obj;
  },

  isValidMobileNumber(number) {
    if (typeof number !== 'string') {
      return false;
    }
    if (number.match(/^(\+\d{1,3}[- ]?)?\d{10}$/)) {
      return true;
    }
    return false;
  },
  pressureUnitConversion(value, toUnit) {
    if (toUnit === "kPa") {
      return value;
    } else if (toUnit === "bar") {
      return (value / 100);
    } else if (toUnit === "psi") {
      return (value / 6.895)
    }
    throw new Error("INAVALID_CONVERSION_UNIT. ONly kPA, bar and psi from kva can be used for conversion")

  },
  removeSpacesFromStartAndEnd(string) {
    if (typeof string != "string") return string;
    return string.replace(/^\s+|\s+$/g, "");
  },

  /**
   * From a device data packet, retrieve the consumption value(kvah or kwh)
   * @param {object} data Device data packet
   * @param {string} userPreferUnit unit of consumption (kvah or kwh)
   * @returns Float(unit)
   */
  getConsumptionFromDeviceData: function (data, userPreferUnit) {
    let consumption;
    if (!data) {
      return null;
    }
    if (userPreferUnit === 'kwh') {
      consumption = parseFloat(data["corrected_kwh"]) || parseFloat(data["corrected_ebkwh"]) || 0
    }
    if (userPreferUnit === 'kvah') {
      consumption = parseFloat(data["corrected_kvah"]) || 0
    }
    return consumption;
  },
  /**
   * @function returnFilteredNumber
   * @description return the filtered number
   * @param {*} num
   * @returns {Number}
   */
  returnFilteredNumber: function (num) {
    if (num === undefined) return null;
    if (typeof num === 'string') {
      num = Number(num);
    }
    if (isNaN(num)) {
      return null;
    } else if (num > 0 && num < 10) {
      return Math.round(num * 1000) / 1000;
    } else if (num > 10 && num < 100) {
      return Math.round(num * 10) / 10;
    } else {
      return Math.round(num)
    }
  },
  isAllowedString: function (name) {
    if (!name) return false;
    const exp = '^[A-Za-z][A-Za-z0-9_ -]{0,28}[A-Za-z0-9_]$';
    const regExp = new RegExp(exp, 'g');
    if (regExp.test(name)) {
      return false;
    }
    return true
  },
  /**
   * @description parse the formula string which contains deviceIds filtering out the deviceId
   * @param {String} formulaText
   * @param {Array} deviceId
   */
  parseFormulaToIds: function (formulaText) {
    let x = formulaText.split('||').filter(Boolean);
    let devId = new Set();
    for (let i = 0; i < x.length; i++) {
      let expr = x[i];
      let devParm = expr.split('@');
      if (devParm.length == 2 && devParm[0] != 'COMMAND') {
        devId.add(devParm[0])
      }
    }
    return Array.from(devId)
  },
  convertTimezone(timezone, format) {
    if (!moment.tz.zone(timezone)) {
      throw flaverr('E_INVALID_TIMEZONE', new Error(`Invalid timezone: ${timezone}`));
    }
    if (!ALLOWED_FORMATS.includes(format)) {
      throw flaverr('E_INVALID_FORMAT', new Error(`Invalid format: ${format}`));
    }
    const momentObj = moment().tz(timezone);
    const offsetMinutes = momentObj.utcOffset(); // offset in mins
    switch (format) {
      case "utcOffsetInMinute":
        return `${offsetMinutes}m`;
      case "utcOffsetInNumber":
        return `${offsetMinutes}`;
      case 'tz':
        return timezone;
      default:
        return momentObj.format(format);
    }
  },
  standardDeviation: (values) => {
    const n = values.length;
    if (n === 0) return 0;
    const mean = values.reduce((sum, value) => sum + value, 0) / n;
    const squaredDiffs = values.map(value => Math.pow(value - mean, 2));
    const variance = squaredDiffs.reduce((sum, diff) => sum + diff, 0) / n;
    return Math.sqrt(variance);
  },
  /**
   * Parses various date formats and converts them to UTC with validation
   * @param {string} dateString - Date string to parse in various formats
   * @param {object} options - Additional options for date parsing
   * @returns {string} UTC formatted date string
   */
  parseDateToUTC: function (dateString, options = {}) {
    const {
      allowFutureDates = false,
      defaultTimezone = 'UTC'
    } = options;

    const supportedFormats = [
      'YYYY-MM-DDTHH:mm:ssZ',      // ISO with UTC offset
      'YYYY-MM-DDTHH:mm:ss.SSSZ',  // ISO with milliseconds and UTC offset
      'YYYY-MM-DDTHH:mm:ss',       // Without timezone
      'YYYY-MM-DD',                // Just date
      moment.ISO_8601              // Any valid ISO 8601 format
    ];

    let parsedDate;

    const timezonePattern = /[\+\-]\d{2}:?\d{2}$/; // for timezone offset, +05:30, -05:30, +0530, -0530
    if (timezonePattern.test(dateString)) {
      parsedDate = moment.parseZone(dateString).utc();
    } else {
      parsedDate = moment.tz(dateString, supportedFormats, true, defaultTimezone).utc();
    }

    if (!parsedDate.isValid()) {
      throw flaverr('E_BAD_REQUEST', new Error('Invalid date format'));
    }

    if (!allowFutureDates && parsedDate.isAfter(moment.utc())) {
      throw flaverr('E_BAD_REQUEST', new Error('Future dates are not allowed'));
    }

    return parsedDate.toISOString();
  },

  /**
 * Validates the content of an SVG file.
 * @param {string} filePath - The path of the SVG file to validate.
 * @returns {boolean} - Returns true if the SVG is valid, false otherwise.
 */
  isValidSvgContent(content) {
    try {
      // Check if it's a valid XML structure
      const options = {
        ignoreAttributes: false,
        attributeNamePrefix: "@_",
      };

      const parser = new XMLParser();
      let parsed = parser.parse(content, options);

      // Check if root element is SVG
      if (!parsed.svg) {
        return false;
      }

      // Additional checks for disallowed elements or attributes
      const serializedContent = JSON.stringify(parsed);

      // Disallow potentially malicious elements like <script>, <iframe>, <object>, etc.
      const disallowedElements = ["script", "iframe", "object", "embed"];
      if (disallowedElements.some((element) => serializedContent.includes(element))) {
        return false;
      }

      // Disallow inline event handlers (e.g., onload, onclick)
      const disallowedAttributes = ["@_onload", "@_onclick", "@_onmouseover"];
      if (disallowedAttributes.some((attr) => serializedContent.includes(attr))) {
        return false;
      }

      // Disallow `foreignObject` (often used for embedding arbitrary content in SVG)
      if (serializedContent.includes("foreignObject")) {
        return false;
      }

      return true;
    } catch (error) {
      sails.log.error("Error validating SVG content:", error);
      return false;
    }
  },

  isValidIcoContent(content) {
    try {
      // Checking for ICO file signature where first two bytes should be 0x00 and 0x01
      const buffer = Buffer.from(content);
      return buffer[0] === 0x00 && buffer[1] === 0x01;
    } catch (error) {
      return false;
    }
  },



  /**
   * Validates if a file is allowed based on its MIME type and content.
   * @param {Buffer} fileBuffer - The file content as a Buffer.
   * @param {string} mimeType - The MIME type of the file.
   * @returns {boolean} - True if the file is valid, false otherwise.
   */
  isValidFileContent(fileBuffer, mimeType) {

    const allowedMimeTypes = [
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/svg+xml",
      "application/pdf",
      "image/vnd.microsoft.icon",
      "application/json",
    ];
    try {
      if (!allowedMimeTypes.includes(mimeType)) {
        return false;
      }

      // Validate based on MIME type
      const methods = {
        "image/jpeg": (buffer) => module.exports.isValidImageFile(buffer),
        "image/jpg": (buffer) => module.exports.isValidImageFile(buffer),
        "image/png": (buffer) => module.exports.isValidImageFile(buffer),
        "application/pdf": (buffer) => module.exports.isValidPdfFile(buffer),
        "image/svg+xml": (buffer) => module.exports.isValidSvgContent(buffer.toString("utf8")),
        "image/vnd.microsoft.icon": (buffer) => module.exports.isValidIcoContent(buffer),
        "application/json": (buffer) => module.exports.isValidJsonFile(buffer),
      };

      if (methods[mimeType]) {
        return methods[mimeType](fileBuffer);
      }

      return false;
    } catch (error) {
      sails.log.error("Error validating file content:", error);
      return false;
    }
  },

  /**
   * Checks if a JPEG/JPG or PNG file has a valid signature.
   * @param {Buffer} fileBuffer - The file content as a Buffer.
   * @returns {boolean} - True if valid, false otherwise.
   */
  isValidImageFile(fileBuffer) {
    const jpegSignature = fileBuffer.slice(0, 2).toString("hex");
    const pngSignature = fileBuffer.slice(0, 8).toString("hex");

    return jpegSignature === "ffd8" || pngSignature === "89504e470d0a1a0a";
  },

  /**
   * Checks if a PDF file has a valid signature.
   * @param {Buffer} fileBuffer - The file content as a Buffer.
   * @returns {boolean} - True if valid, false otherwise.
   */
  isValidPdfFile(fileBuffer) {
    const pdfSignature = fileBuffer.slice(0, 5).toString("utf8");
    return pdfSignature.startsWith("%PDF-");
  },
  /**
 * Checks if a JSON file is valid.
 * @param {Buffer} fileBuffer - The file content as a Buffer.
 * @returns {boolean} - True if valid JSON, false otherwise.
 */

  isValidJsonFile(fileBuffer) {
    try {
      JSON.parse(fileBuffer.toString("utf8"));
      return true;
    } catch (error) {
      sails.log.error("Invalid JSON file content:", error);
      return false;
    }
  },
  getMeasurementByAssetIdForInflux(deviceId) {
    const isDevice = !isNaN(deviceId);
    return isDevice ? "device" : "components";
  },
  parsedToThreeDecimals(value) {
    return Number(Number(value).toFixed(3));
  }
};
