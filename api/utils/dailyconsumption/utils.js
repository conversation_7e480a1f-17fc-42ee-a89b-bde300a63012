const { words, keys } = require("lodash");


module.exports = {
  /**
   * This function received the consumption object and changes its 'actual' field with kwh value
   * and removes the actualkwh prperty from the object.
   * @param {object} consumption Consumption object as received from the database 
   * @returns {object} Modified consumed object with actual field having kwh value
   */
  getDailyConsumptionInKWH: function(consumption) {
    consumption.actual = consumption.actualkwh;
    delete consumption['actualkwh'];
    return consumption;
  },
};
