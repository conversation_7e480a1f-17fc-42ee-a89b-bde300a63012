const flaverr = require("flaverr")

module.exports = {
  throwExceptionInvalidSite: function(siteId) {
    throw flaverr({
      code:'E_INVALID_SITE_ID',
      message:`Invalid site ID ${siteId}`,
      HTTP_STATUS_CODE: 400
    })
  },
  throwExceptionCPAConfDataNotExist: function(siteId) {
    throw flaverr({
      code:'E_INVALID_CPA_CONFIG_DATA_SOURCE',
      message:`Invalid CPA configuration data source for ${siteId} does not exist.`,
      HTTP_STATUS_CODE: 400
    })
  },
  throwExceptionUnableToFetchAssetCPAConfig: function(siteId) {
    throw flaverr({
      code:'E_INVALID_CPA_CONFIG_DATA_SOURCE',
      message:`Unable to fetch CPA configuration file for ${siteId}.Please contact the administrator.`,
      HTTP_STATUS_CODE: 400
    })
  },
  CPAAssetConfigSyncList: function (cpaAssetConfigList, siteId, userId) {
    const CPAAssetConfigurationMetadata = [];
    for(const key of Object.keys(cpaAssetConfigList)) {
      const { asset_name: assetName, device_type: assetType} = cpaAssetConfigList[key];
      CPAAssetConfigurationMetadata.push({
        assetName,
        assetType,
        assetId: key,
        siteId,
        syncBy: userId
      })
    }
    sails.log('[CPAAssetConfigSyncList] > Create sync configuration asset list')
    return CPAAssetConfigurationMetadata;
  }
}