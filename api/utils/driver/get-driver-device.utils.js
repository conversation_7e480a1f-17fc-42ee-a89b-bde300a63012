const Excel = require('exceljs');
module.exports = {
  /**
   * @description To filter the incoming data by removing unwanted column,
   * adding and modify the column for response that will insert the data in Excel sheet.
   * Every item contain many parameters so that we iterate through every item and their properties
   * And put them under same key 'device_type' act as sheet name with array of object will be the data is the sheet.
   * @param {Object} drivers :-
   * It is the Object of all the driver taken from databases common {class:"devices"}
   * @returns {Object} acc :-
   * Returns Object of filtered required data that we make Excel sheet for.
   */
  filterDriverDevices: (drivers) => drivers.reduce((acc, driver) => {
    acc[driver.deviceType] = acc[driver.deviceType] || [];
    const obj = {};
    obj.device_type = driver.deviceType;
    obj.driver_name = driver.driverName;
    obj.communication = `${driver.communicationCategory}/${driver.communicationType}`;
    obj.driver_type = driver.driverType;

    /**
     * Some drivers not having the properties
     * So we keep blank them which are not present
     * And rest of the data will be shown
     */
    if (!driver.parameters) {
      acc[driver.deviceType].push({
        ...obj,
        communication: null,
        abbr: null,
        display_name: null,
        raw_unit: null,
        address: null,
        dau: null,
        mul_factor: null,
        offset: null,
      });
      return acc;
    }

    /**
     * Iterating through each item properties
     * One item can have multiple parameters
     * This will be useful to add row under one deviceType
     * So same deviceType drivers are in sequence in Excel sheet.
     */
    for (const paramItems of driver.parameters) {
      const paramObject = { ...obj };
      const paramItem = JSON.parse(paramItems);
      paramObject.abbr = paramItem.abbr;
      paramObject.display_name = paramItem.displayName;
      paramObject.raw_unit = paramItem.rawUnit;
      paramObject.address = paramItem.address;
      paramObject.dau = paramItem.dau;
      paramObject.mul_factor = paramItem.properties && paramItem.properties.mulFactor || null;
      paramObject.offset = paramItem.properties && paramItem.properties.offset || null;
      paramObject.reg_type = paramItem && paramItem.regType || null;
      paramObject.param_group = paramItem && paramItem.paramGroup || null;
      acc[driver.deviceType].push(paramObject);
    }

    /**
     * It means that we are going to switch new driver
     * For that we need a spacing line to distinguish
     * Between type of drivers in same Excel sheet.
     */
    const lineSpaceForNewDriver = {
      device_type: null,
      driver_name: null,
      communication: null,
      driver_type: null,
      abbr: null,
      display_name: null,
      raw_unit: null,
      address: null,
      dau: null,
      mul_factor: null,
      offset: null,
      regType: null,
    };
    acc[driver.deviceType].push(lineSpaceForNewDriver);
    return acc;
  }, {}),
  /**
   * @description To generate Excel file with many sheets and inserting data to each sheet.
   * @param {Object} data
   * @returns {Promise<Buffer>}
   */
  generateExcel: (data) => {
    const workBook = new Excel.Workbook();
    for (const [key, value] of Object.entries(data)) {
      const sheet = workBook.addWorksheet(key);
      // Adding colum to the sheet
      sheet.columns = [
        { header: 'device_type', key: 'device_type', width: 16 },
        { header: 'driver_name', key: 'driver_name', width: 35 },
        { header: 'driver_type', key: 'driver_type', width: 14 },
        { header: 'communication', key: 'communication', width: 15 },
        { header: 'abbr', key: 'abbr', width: 24 },
        { header: 'display_name', key: 'display_name', width: 35 },
        { header: 'raw_unit', key: 'raw_unit', width: 16 },
        { header: 'address', key: 'address', width: 16 },
        { header: 'dau', key: 'dau', width: 10 },
        { header: 'mul_factor', key: 'mul_factor', width: 10 },
        { header: 'offset', key: 'offset', width: 10 },
        { header: 'reg_type', key: 'reg_type', width: 10 },
        { header: 'param_group', key: 'param_group', width: 14 },
      ];
      /**
       * Iterate through each value and
       * Insert the row in the sheet
        */
      value.forEach((item) => sheet.addRow(item));
    }

    // await workBook.xlsx.writeFile('DriverDevices.xlsx');
    return workBook.xlsx.writeBuffer();
  },
};
