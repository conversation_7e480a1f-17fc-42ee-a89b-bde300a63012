const moment = require("moment");

module.exports = {
  async formatMaintenanceData(resMaintenanceList, siteId) {
    const timezone = await sails.helpers.getSiteTimezone.with({
      siteId,
      timezoneFormat: "utcOffsetInNumber",
    });
    const maintenanceList = [];
    for (const list of resMaintenanceList) {
      const ticket = {};
      ticket["title"] = list["subject"];
      ticket["priority"] = list["priority"];
      ticket["user"] = list["cc_emails"];
      ticket["description"] = list["description_text"];
      ticket["type"] = list["custom_fields"]["cf_maintenancetype"];
      ticket["url"] = list["custom_fields"]["cf_pageurl"];
      const devices = (list["custom_fields"] && list["custom_fields"]["cf_devices"]) || null;
      try {
        if (devices !== null && devices !== undefined && devices.split("").length > 1) {
          ticket["devices"] = JSON.parse(list["custom_fields"]["cf_devices"]);
        }
      } catch (e) {
        sails.log.error(list["custom_fields"]["cf_devices"]);
        sails.log.error(e)
      }
      ticket["attachments"] = list["attachments"];
      ticket["ticketId"] = list["id"];
      ticket["created_at"] = moment(list["created_at"])
        .utcOffset(timezone)
        .format("MMMM DD YYYY hh:mm:ss A");

      ticket["updated_at"] = moment(list["updated_at"])
        .utcOffset(timezone)
        .format("MMMM DD YYYY hh:mm:ss A");
      ticket["status"] = list["status"];
      ticket["siteId"] = list["custom_fields"]["cf_site_id"];
      const comment = list["custom_fields"]["cf_comment"];
      if (comment === undefined || comment === "undefined" || comment === "null") {
        ticket["comment"] = "";
      } else {
        ticket["comment"] = list["custom_fields"]["cf_comment"];
      }

      maintenanceList.push(ticket);
    }
    return maintenanceList;
  },

  getFormattedConversation: function (conversations) {
    let formattedconvo = [];
    for (let conversation of conversations) {
      let conversationObj = {};
      conversationObj["reply"] = conversation["body_text"].split("----")[0];
      const replyArr = conversationObj["reply"].split("#&*&#");
      if (replyArr.length > 1) {
        conversationObj["reply"] = replyArr[1];
        conversationObj["userId"] = replyArr[0];
      } else {
        conversationObj["userId"] = "<EMAIL>";
      }
      conversationObj["support_email"] = conversation["support_email"];
      conversationObj["from_email"] = conversation["from_email"];
      conversationObj["ticketId"] = conversation["ticketId"];
      const time = moment(conversation["created_at"]).fromNow(true);
      conversationObj["time"] = time;
      formattedconvo.push(conversationObj);
    }
    return formattedconvo;
  },
};
