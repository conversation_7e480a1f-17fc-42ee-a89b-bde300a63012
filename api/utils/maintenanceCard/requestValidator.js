const Joi = require("joi");
const flaverr = require("flaverr");
const moment = require("moment");

const validateCardPayload = (cardPayload) => {
  const schema = Joi.object({
    priority: Joi.number().valid(1, 2, 3, 4).required().messages({
      "any.required": "Priority is required and must be a number between 1 and 4",
      "any.only": "Priority must be one of 1, 2, 3, or 4",
    }),
    description: Joi.string().required().messages({ "any.required": "Description is required" }),
    subject: Joi.string().required().messages({ "any.required": "Subject is required" }),
    comment: Joi.string().optional(),
    email: Joi.string().email().required().messages({
      "any.required": "Email is required",
      "string.email": "Email must be a valid email address",
    }),
    ccEmails: Joi.string().optional(),
    siteId: Joi.string().required().messages({ "any.required": "Site ID is required" }),
    status: Joi.number().valid(2, 3, 4, 5, 6).required().messages({
      "any.required": "Status is required and must be a number between 2 and 6",
      "any.only": "Status must be one of 2, 3, 4, 5, or 6",
    }),
    pageUrl: Joi.string().required().messages({ "any.required": "Page URL is required" }),
    maintenanceType: Joi.string()
      .required()
      .messages({ "any.required": "Maintenance type is required" }),
    devices: Joi.string()
      .optional()
      .messages({ "any.required": "Devices information is required" }),
    attachments: Joi.any().optional(),
    id: Joi.alternatives().try(Joi.string().optional(), Joi.number().optional),
  });

  const { error } = schema.validate(cardPayload, { abortEarly: false });

  if (error) {
    const errorMessages = error.details.map((detail) => detail.message);

    throw flaverr(
      "E_INPUT_VALIDATION",
      new Error(`There were validation errors with your input: ${errorMessages}`)
    );
  }
};

const validateFetchMaintenanceTickets = (payload) => {
  const schema = Joi.object({
    siteId: Joi.string().required(),
    startDate: Joi.string()
      .custom((value, helpers) => {
        if (moment(value, "YYYY-MM-DD", true).isValid()) {
          return value;
        } else {
          return helpers.error("any.invalid", {
            message: "Start date must be a valid date in YYYY-MM-DD format.",
          });
        }
      })
      .required(),
    endDate: Joi.string()
      .custom((value, helpers) => {
        if (moment(value, "YYYY-MM-DD", true).isValid()) {
          return value;
        } else {
          return helpers.error("any.invalid", {
            message: "End date must be a valid date in YYYY-MM-DD format.",
          });
        }
      })
      .required(),
      _userMeta: Joi.object().required(),
  });

  const { error } = schema.validate(payload);
  if (error) throw flaverr("E_INPUT_VALIDATION", new Error(error.message));
};

module.exports = {
  validateCardPayload,
  validateFetchMaintenanceTickets,
};
