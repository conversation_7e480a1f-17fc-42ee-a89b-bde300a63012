const globalHelper = require('../../utils/globalhelper');
const utils = require('./utils');


module.exports = {
  /**
     * Format the alerts in specific format and keys required by Frontend
     * Club the alerts coming from same source(recipe/nudge etc) into 1 object
     * @param {array} alerts Array of alerts from notification table(raw)
     * @returns formatted array of alerts
     */
  formatNotifications: function(alerts) {

    if (alerts.constructor.name !== 'Array') {
      throw new Error('findalerts::Expected input parameter to be of type array');
    }

    let formattedAlerts = [], mapOfAlerts = {};

    for (let alert of alerts) {
      let { sourceId, ts, priority='unknown' } = alert;

      if (utils.DIGIT_PRIORITY_MAP[priority] !== undefined) {
        priority = utils.DIGIT_PRIORITY_MAP[priority];
      }

      let timestamp = parseInt(ts) || globalHelper.getCurrentUnixTs();
      
      if (mapOfAlerts[sourceId] === undefined) {
        mapOfAlerts[sourceId] = {
          title: alert['title'],
          'sub_category': globalHelper.toArray(alert['category']),
          alertType: alert['type'],
          description: alert['description'],
          eventId: alert['sourceId'],
          priority,
          time: {},
        };
      }
      let startTime = globalHelper.toMoment(timestamp).startOf('day').format('YYYY-MM-DD');
      let formattedTime = globalHelper.formatDateTime(ts);

      if (!mapOfAlerts[sourceId]['time'][startTime]) {
        mapOfAlerts[sourceId]['time'][startTime] = [];
      }
      mapOfAlerts[sourceId]['time'][startTime].push(formattedTime);
    }

    for (let sourceId in mapOfAlerts) {
      formattedAlerts.push(mapOfAlerts[sourceId]);
    }
    
    return formattedAlerts;
  }
};
