const globalHelpers = require('../globalhelper');

// List of currently supported notification. New alert type
// shows include adding values in corresponding TEMPLATE, SMS_TEMPLATES_IDS and
// ALERTTYPE_USERPREFERENCE variables.
const TYPES_OF_NOTIFICATION = ['RECIPE', 'NUDGE'];

// whenever new type of notification is added
// we have to add corresponding email-template to use.
const TEMPLATES = {
  RECIPE: 'alert-template', // views/emails/alert-template.hbs & alert-template.handlebars
  NUDGE: 'nudge-alert-template', // views/emails/nudge-alert-template.hbs and nudge-alert-template.handlebars
};
// alert type i.e RECIPE, NUDGE etc have corresponding userprefernce
// which tell if users want alerts from this type or not. For 'RECIPE' type
// alert the key 'Jo<PERSON>Recipe' in userprefernce should be set to get RECIPE alert.
// This dict is list of alert TYPE and corresponding key to search in user preference
const ALERTTYPE_USERPREFERENCE = {
  RECIPE: 'JouleRecipe',
  NUDGE: 'nudge',
};
const SMS_TEMPLATES_IDS = {
  RECIPE: '1107162453558773940',
  NUDGE: '1107162453558773940',
};
// Value in minutes about what each priority means.
// so priority 1 which corresponds to medium will go after
const PRIORITY_EXPIRY_MAP = {
  0: 720, // low -> 12 hour
  1: 360, // med -> 6 hour
  2: 60, // high -> 1 hour
  3: 15, // crit -> 15minute
  4: 15, // hypercrit -> 15minute
  low: 720,
  medium: 360,
  high: 60,
  critical: 15,
  hypercritical: 15,
};

const DIGIT_PRIORITY_MAP = {
  0: 'low',
  1: 'medium',
  2: 'high',
  3: 'critical',
  4: 'hypercritical'
};

const READ_STATUS = {
  READ: 'read',
  UNREAD: 'unread',
};

const MAX_VILPOWER_VARIABLE_LEN = 31;

module.exports = {

  TEMPLATES,
  READ_STATUS,
  DIGIT_PRIORITY_MAP,
  ALERTTYPE_USERPREFERENCE,
  TYPES_OF_NOTIFICATION,
  SMS_TEMPLATES_IDS,

  /**
   * Given an input recipe object, check if it contains all the required
   * keys that should exist in notification object
   * @param {object} payload Input object to check for validity
   * @returns {object} A well formed notification object if all keys exists else false
   */
  isValidRecipePayloadFromQueue(payload) {
    if (payload === undefined) {
      return false;
    }

    const {
      category, notify, sms, priority, title, description,
      sourceId, alertId, siteId, timestamp,
    } = payload;
    const _type = payload.type;

    if (globalHelpers.isNullishArray([sourceId, alertId,
      siteId, timestamp, title, description])) {
      return false;
    }
    if (PRIORITY_EXPIRY_MAP[priority] === undefined) {
      return false;
    }
    if (typeof _type !== 'string' || TEMPLATES[_type] === undefined) {
      return false;
    }
    if (Array.isArray(notify) === false
      || Array.isArray(sms) === false) {
      return false;
    }
    if (Array.isArray(category) === false) {
      payload.category = []; // back compatibility
    }
    if (globalHelpers.isValidDateTime(payload.timestamp) === false) {
      return false;
    }
    const notification = {
      id: payload.siteId,
      timestamp: globalHelpers.toUnixTimeStamp(payload.timestamp).toString(),
      notify: payload.notify,
      sms: payload.sms,
      priority: payload.priority.toString(),
      category: payload.category,
      title: payload.title,
      description: payload.description,
      sourceId: payload.sourceId,
      alertId: payload.alertId,
      type: payload.type,
      template: TEMPLATES[payload.type],
      siteId: payload.siteId,
    };
    if (payload.extra !== undefined && payload.extra.constructor.name === 'Object') {
      notification.extra = payload.extra;
    }
    if (payload.processes !== undefined && Array.isArray(payload.processes)) {
      notification.processes = payload.processes;
    }

    return notification;
  },
  /**
   * Given an input nudge object, check if it contains all the required 
   * keys that should exist in notification object
   * @param {object} payload Input object to check for validity
   * @returns {object} A well formed notification object if all keys exists else false
   */
  isValidNudgePayloadFromQueue(payload) {
    if (payload === undefined) {
      return false;
    }

    const {
      category, notify, sms, title, description,
      sourceId, alertId, siteId, timestamp,
    } = payload;
    const _type = payload.type;

    if (globalHelpers.isNullishArray([sourceId, alertId,
      siteId, timestamp, title, description])) {
      return false;
    }

    if (typeof _type !== 'string' || TEMPLATES[_type] === undefined) {
      return false;
    }
    if (Array.isArray(notify) === false
      || Array.isArray(sms) === false) {
      return false;
    }
    if (Array.isArray(category) === false) {
      payload.category = []; // back compatibility
    }
    if (globalHelpers.isValidDateTime(payload.timestamp) === false) {
      return false;
    }
    const notification = {
      id: payload.siteId,
      timestamp: globalHelpers.toUnixTimeStamp(payload.timestamp).toString(),
      notify: payload.notify,
      sms: payload.sms,
      priority: 'high',
      category: payload.category,
      title: payload.title,
      description: payload.description,
      sourceId: payload.sourceId,
      alertId: payload.alertId,
      type: payload.type,
      template: TEMPLATES[payload.type],
      siteId: payload.siteId,
    };
    if (payload.extra !== undefined && payload.extra.constructor.name === 'Object') {
      notification.extra = payload.extra;
    }
    if (payload.processes !== undefined && Array.isArray(payload.processes)) {
      notification.processes = payload.processes;
    }

    return notification;
  },
  /**
   * Given an input object, check if it contains all the required
   * keys that should exist in email type notification
   * @param {object} payload Input object to check for validity
   * @returns {object} A well formed email object if all keys exists else false
   */
  isValidEmailFromQueue(payload) {
    if (payload === undefined) {
      return false;
    }

    const {
      notify, title, description, siteId, timestamp, alertId, type,
    } = payload;

    if (Array.isArray(notify) === false) {
      return false;
    }
    if (TEMPLATES[type] === undefined) {
      return false;
    }
    if (globalHelpers.isNullishArray([title, description, siteId, timestamp, alertId])) {
      return false;
    }

    return true;
  },

  /**
   * Given an input object, check if it contains all the required
   * keys that should exist in SMS type notification
   * @param {object} payload Input object to check for validity
   * @returns {object} A well formed sms object if all keys exists else false
   */
  isValidSMSFromQueue(payload) {
    if (payload === undefined) {
      return false;
    }

    const {
      sms, title, description, siteId, timestamp, type,
    } = payload;

    if (Array.isArray(sms) === false) {
      return false;
    }
    if (TEMPLATES[type] === undefined) {
      return false;
    }

    if (globalHelpers.isNullishArray([title, description, siteId, timestamp])) {
      return false;
    }

    return true;
  },

  /**
   * Given a priority, return the minutes to be used as expiry time. So returning 720 means
   * expiry after 720 minutes
   * @param {string} priority enum(0,1,2,3,high,medium,low,critical) i.e priority
   * @returns {int} Expiry in minutes or Undefined.
   */
  getExpiryFromPriority(priority) {
    return PRIORITY_EXPIRY_MAP[priority] * 60; // to convert to seconds
  },

  /**
   * Make body for SMS type alert from given info.
   * @param {string} siteId site id
   * @param {string} title title of the SMS
   * @param {string} description Description of sms
   * @param {string} template Currently only 1 template of sms.
   * @param {string} timestamp Formatted timestamp when alert came
   * @param {object} extra Any extra info of alert
   * @returns {string} SMS body to send to user.
   */
  makeSMSBody(siteId, title, description, type, template, timestamp, extra) {
    // we can use hbs here

    let formattedTitle = title;
    let formattedDesc = description;
    const twoVilpowerVariableLen = MAX_VILPOWER_VARIABLE_LEN * 2;
    const threeVilpowerVariableLen = MAX_VILPOWER_VARIABLE_LEN * 3;

    if (title && title.length > twoVilpowerVariableLen) {
      formattedTitle = `${title.slice(0, twoVilpowerVariableLen - 2)}..`;
    }
    if (description && description.length > threeVilpowerVariableLen) {
      formattedDesc = `${description.slice(0, threeVilpowerVariableLen - 2)}..`;
    }
    const body = [
      'Alert from Smart Joules Pvt. Ltd.',
      '',
      `${type} Alert`,
      `Site: ${siteId} @Time=${timestamp}`,
      `Title: ${formattedTitle}`,
      `Description: ${formattedDesc}`,
      '',
      'Check details at https://smartjoules.org/. You are receiving this SMS because you were tagged in this SMS coming from Nudge/JouleRecipe. To stop receiving further SMS you can turn your SMS preference off from the user page at https://smartjoules.org/user/edit. If this SMS is not rendered correctly please contact the PD team for assistance.',
      '- Smart Joule',
    ].join('\n');

    return body;
  },
  /**
   * Filter our invalid mobile number. If mobile number is not 10 digit without code or
   * 13 digit with +91 remove the mobile number.
   * Also all +91 to all the mobile numbers where it doesnt exist.
   * @param {string} mobileNumbers Input mobile number
   * @returns {string}  filtered mobile number with added +91
   */
  filterInvalidMobileNumbers(mobileNumbers) {
    return mobileNumbers
      .filter(globalHelpers.isValidMobileNumber)
      .map((mobileNumber) => {
        if (mobileNumber.startsWith('+91') === false) {
          return `+91${mobileNumber}`;
        }
        return mobileNumber;
      });
  },

  /**
   * Joule recipe alerts coming from Server/Controller is taken as input
   * and it formats it in a format that notification service accepts for
   * joule recipe type of alert
   * @param {object} recipeAlert Joule recipe alert from server/controller
   * @param {string} recipeAlert.uniqId Unique ID for each notification (unique for every notification)
   * @param {string} recipeAlert.sourceId this is <siteId>_<alertId> . exmaple ssh_1a05a0166eff4e8118b7e59e6a7,
   * @param {string} recipeAlert.sourceInfo this is <siteId>_<recipeId>
   * @param {string} recipeAlert.triggerTime unix timestamp when alert was created
   * @param {object} recipeAlert.info Furthur info about this alert
   * @param {string} recipeAlert.info.title Title of alert
   * @param {string} recipeAlert.info.description Description of alert
   * @param {string} recipeAlert.info.priority priority of alert enum(critical, high, medium, low, 0, 1, 2, 3)
   * @param {Array} recipeAlert.info.category Categories this alert belong to
   * @param {Array} recipeAlert.info.notify Users to notify using email and Dejoule notification
   * @param {Array} recipeAlert.info.smslist Users to send SMS Too
   * @param {string} recipeAlert.info.rid Recipe Id
   * @param {string} recipeAlert.info.actionId Unique Id of alert from recipe. (Everytime a alert come from same recipe, it will have same acionId)
   *
   */
  formatJRecipeAlert(recipeAlert) {
    let { triggerTime: timestamp, info } = recipeAlert;

    info = globalHelpers.toJson(info);
    if (info.constructor.name !== 'Object') {
      return undefined;
    }

    let {
      title,
      description,
      notify = [],
      smslist: sms = [],
      priority,
      processes = [],
      rid: sourceId,
      actionId: alertId,
      category,
      siteId,
    } = info;

    if (description === undefined) {
      description = title;
    }

    const notification = {
      extra: { ...info },
      timestamp,
      title,
      description,
      notify,
      sms,
      priority,
      sourceId,
      alertId,
      category,
      siteId,
      processes,
      type: 'RECIPE',
    };

    return notification;
  },

};
