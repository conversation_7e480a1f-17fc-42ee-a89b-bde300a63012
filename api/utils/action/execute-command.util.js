const globalHelper = require("../globalhelper");

module.exports = {
  isCommandValid: function (body) {
    let errors = globalHelper.checkObjectKeys(
      body,
      [param, deviceId, command, componentId],
      ""
    );
    if (errors.length === 0) return true;
    else throw new Error(errors);
  },
  /**
   * Create a action packet for actionType=command to save it in database
   * @param {object} command Command objet from Frontend
   * @param {string} command.deviceId Unique Id  of device to which comman is to send to
   * @param {string} command.param Parameter on the device to which the command is for
   * @param {string} command.componentId The unique ID of component on which the device is installed
   * @param {integer} command.value The value of command
   * @returns Action Object that is to be saved in database
   */
  createCommanActionPacket: function ({ deviceId, param, componentId, value }) {
    let currentTime = globalHelper.getCurrentUnixTs();
    let action = {};

    return action;
  },
};
