const Joi = require('joi');
const flaverr = require('flaverr');

const deviceDriverSchema = Joi.object({
  deviceType: Joi.string().required(),
  driverType: Joi.number().required(),
  abbr: Joi.string().required(),
  class: Joi.string().valid('component', 'device').required()
});

const fetchAllFlowBasedAssetSchema = Joi.object({
  deviceDriverDetails: Joi.array().items(deviceDriverSchema).required()
});

module.exports = {
  validateFetchAllFlowBasedAsset(params) {
    const { error, value } = fetchAllFlowBasedAssetSchema.validate(params);
    if (error) {
      throw flaverr('E_INPUT_VALIDATION', new Error(error.message));
    }
    return value;
  }
};
