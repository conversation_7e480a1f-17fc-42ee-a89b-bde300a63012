const flaverr = require("flaverr");
const Joi = require("joi");

const inputValidation = (params) => {
  const schema = Joi.object()
  .keys({
    siteId: Joi.string().min(1).trim().required(),
  })
  .unknown(true);
  const { value, error } = schema.validate(params);
  if (error) {
    throw flaverr("INPUT_VALIDATION_ERROR", new Error(error.message));
  } else {
    return value;
  }
}

const throwExceptionInvalidSite = (siteId) => {
  throw flaverr('E_SITE_NOT_FOUND', new Error(`${siteId} does not exist`))
}

const throwExceptionInvalidSiteList = (siteListParam) => { 
  const siteListSchema = Joi.object()
  .keys({ 
    siteList: Joi.array().items(
        Joi.string()
            .required()
            .messages({
              'any.required': 'Please provide a siteList.',
              'string.base': 'siteList must contain strings only.',
            })
        ).min(1)
        .required()
        .messages({
          'array.min': 'siteList must have at least one item.',
        })
    })
  .unknown(true)

    const {error } = siteListSchema.validate(siteListParam);
    if (error) throw flaverr("INPUT_VALIDATION_ERROR", new Error(error.message));
    return;
}

module.exports ={
  inputValidation,
  throwExceptionInvalidSite,
  throwExceptionInvalidSiteList
}