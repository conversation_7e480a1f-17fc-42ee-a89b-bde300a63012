const flaverr = require('flaverr');
const globalHelpers = require('../globalhelper');
const BUSINESS_MODEL = ['JoulePays', 'CAAS', 'SteamOptimisation'];
const INDUSTRY_TYPE = ['hvac', 'steamPlant', 'laundry', 'chemicaldosing','ibms','jouleair','joulepay','jcool'];
const PERFORMANCE_KPI = { 'conselec': 10, 'conssteam': 12, 'trdelivred': 15 };


module.exports = {
  BUSINESS_MODEL,
  INDUSTRY_TYPE,
  PERFORMANCE_KPI,

  convertUnparsedDynamoSiteToJSON: function (site) {
    site.networks = globalHelpers.toJson(site.networks);
    site.regions = globalHelpers.toJson(site.regions);
    site.areas = globalHelpers.toJson(site.areas);
    return site;
  },

  /**
   * @description This function validate industries supported by SJPL
   * @param {string} industryType Array of industries to check against valid industry Type
   */
  isValidindustryType: function (industryType) {
    if (!INDUSTRY_TYPE.includes(industryType)) throw flaverr('E_INPUT_VALIDATION',new Error("Please Provide Valid Industry Type"));
    return true;
  },

  /**
   * @description This function validate KPI used by SJPL to measure performance
   * @param {object} performancekpi object of KPI to check against valid SITE KPI
   */

  isValidperformancekpi: function (performancekpi) {
    if (
      typeof performancekpi !== "object" &&
      Object.keys(performancekpi) &&
      performancekpi !== null
    ) {
      return false;
    }
    for (let key in performancekpi) {
      if (!PERFORMANCE_KPI.hasOwnProperty(key)) {
        throw new Error("not a valid performanceKPI");
      }
    }
    return true;
  },
  buildAreaRegionMap: function (areas, regions) {
    const map = {};
    try {
      let _areas = JSON.parse(areas);
      let _regions = JSON.parse(regions);
      for (let key in _areas) {
        map[key] = _areas[key];
        if (map[key].hasOwnProperty("regions")) {
          const _regions = map[key].regions;
          map[key].region = {};
          delete map[key].regions;
          for (let _key of _regions) {
            map[key].region[_key] = {
              name: "",
            };
          }
        }
      }

      for (let _region in _regions) {
        const { name, area } = _regions[_region];
        if (map.hasOwnProperty(area) && map[area].region.hasOwnProperty(_region)) {
          map[area].region[_region].name = name;
        }
      }
    } catch (err) {
      return {};
    }
    return map;
  },
  buildRegionAreaMap: function (areas, regions) {
    const map = {};
    try {
      let _areas = JSON.parse(areas);
      let _regions = JSON.parse(regions);

      // Build the region-area map
      for (let key in _areas) {
        if (_areas[key].hasOwnProperty("regions")) {
          const _regionIds = _areas[key].regions;
          for (let _regionId of _regionIds) {
            map[_regionId] = {
              area: {
                key,
                name: _areas[key].name,
              },
              name: "",
            };
          }
        }
      }

      for (let _regionId in _regions) {
        const { name, area } = _regions[_regionId];
        if (map.hasOwnProperty(_regionId)) {
          map[_regionId].name = name;
        }
      }
    } catch (err) {
      return {};
    }
    return map;
  },
};
