module.exports = {
  deleteEmptyStrings: function (siteInfo) {
    Object.keys(siteInfo)
      .forEach(key => {
        if (siteInfo[key] === '') {
          delete siteInfo[key];
        } // In case of empty strings
      });
  },
  generateDTO: function (input) {
    const allowedObject = new Set(['siteName', 'regionCount', 'planningDocUrl', 'circuitCount', 'unitCost','industryType','timezone']);
    let object = {};
    for (let key in input) {
      if (allowedObject.has(key)) {
        object[key] = input[key]
      }
    }
    return object;
  },
  validateCircuitCount: function (circuitCount, regions) {
    let _mappedCircuits = Object.keys(regions)
      .map(it => {
        return regions[it].hasOwnProperty('circuitId') ? parseInt(regions[it].circuitId) : null
      })
      .filter(Boolean);
      let _maxCircuitId = _.max(_mappedCircuits)
      return _maxCircuitId < circuitCount;
  }
}
