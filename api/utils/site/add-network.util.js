const globalHelper = require("../globalhelper");

module.exports = {
  /**
   * @description Network Id generation
   * @param {object} | siteNetwork
   * @param { network } | param. param will have two keys vendor and type name
   * @returns { string } | autogenerated new networkId
   */
  generateNetworkId(siteDetail, network) {
    let _networkSequenceNumber = 0;
    let maxNetworkIdList = [];
    if (siteDetail.hasOwnProperty("networks") && !_.isEmpty(siteDetail.networks)) {
        Object.keys(siteDetail.networks).forEach((key) => {
          let networkNumber = Number(key.split("-")[2]);
          maxNetworkIdList.push(networkNumber);
        });
        maxNetworkIdList.sort((a,b)=> b -a);
        _networkSequenceNumber = maxNetworkIdList[0] + 1;
    } 
    return `${network.vendor}-${network.type}-${_networkSequenceNumber}`;
  },
};
