
const globalHelper = require('../globalhelper'); 

module.exports = {
  checkInput: function(body){
    let errors = globalHelper.checkObjectKeys(body, ['areaId', 'name'], 'area');
    if (errors.length === 0) return true;
    else throw new Error(errors);
  },
  /**
   * @function checkExistingSite
   * @description Checks if siteObject existed in database. Also checks in the region being editted already existed.
   * @param {object} existingSite Existing site fetched based on siteId.
   * @param {string} areaId areaId whose name needs to be updated passed in request.
   * @returns Array of errors or null.
   */
  checkExistingSite: function(existingSite, areaId){
    let errors = [];
    if(globalHelper.isNullish(existingSite)){
      errors.push('Site not configured');
      return errors;
    }
    if(existingSite.areas === undefined || existingSite.areas[areaId] === undefined){
      errors.push('Area does not exist in site.');
      return errors;
    }
    if(errors.length === 0) return null;
    else return errors;
  },
};