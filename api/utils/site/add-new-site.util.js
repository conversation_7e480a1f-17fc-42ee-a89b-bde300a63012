const globalHelpers = require('../globalhelper');

module.exports = {

  checkInput(siteInfo) {
    const errors = globalHelpers.checkObjectKeys(siteInfo, ['siteName', 'location'], 'siteInfo');
    if (errors.length === 0) return true;
    throw new Error(errors);
  },

  /**
   * @function generatePossibleSiteIds
   * @description Generates an array of 20 possible siteIds based on the site name and its location
   * @param {string} siteName Name of the site.
   * @param {string} location Location of the site.
   */
  generatePossibleSiteIds: (siteName, location) => {
    siteName = siteName.replace(/[$-/:-?{-~!"^_`\[\]]/g, '');

    const nameArr = siteName.split(' ');
    let id1; let id2; let loc1;
    let loc2;
    switch (nameArr.length) {
      case 1:
        id1 = nameArr[0].substring(0, 2).toLowerCase();
        id2 = nameArr[0].substring(0, 3).toLowerCase();
        break;
      case 2:
        id1 = nameArr[0].substring(0, 2).toLowerCase() + nameArr[1].substring(0, 1).toLowerCase();
        id2 = nameArr[0].substring(0, 1).toLowerCase() + nameArr[1].substring(0, 2).toLowerCase();
        break;
      case 3:
        id1 = nameArr[0].substring(0, 1).toLowerCase() + nameArr[1].substring(0, 1).toLowerCase() + nameArr[2].substring(0, 1).toLowerCase();
        id2 = nameArr[0].substring(0, 1).toLowerCase() + nameArr[1].substring(0, 2).toLowerCase();
        break;
      default: {
        let shortName = '';
        nameArr.map((e) => {
          shortName += e[0];
        });
        id1 = shortName.toLowerCase();
        id2 = nameArr[0].substring(0, 2).toLowerCase() + nameArr[1].substring(0, 1).toLowerCase();
        break;
      }
    }
    loc1 = location.substring(0, 3).toLowerCase();
    loc2 = location.substring(0, 2).toLowerCase();
    const optionsArr = [`${id1}-${loc1}`, `${id1}-${loc2}`, `${id2}-${loc1}`, `${id2}-${loc2}`];
    const probableSiteIds = [...optionsArr];
    for (let i = 0; i < 20; i++) {
      probableSiteIds.push(`${id1}-${globalHelpers.generateRandomString(4)}`);
    }
    return probableSiteIds;
  },
};
