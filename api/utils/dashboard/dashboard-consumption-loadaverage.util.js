
const moment = require("moment");
const MOMENT_FORMAT = 'YYYY-MM-DDTHH:mm:00';
const TIMEZONE_MOMENT_FORMAT = 'YYYY-MM-DDTHH:mm:00Z';
const momentTimezone = require("moment-timezone");
const globalHelper = require('../globalhelper'); 

module.exports = {
  checkInput: function(body){
    let errors = globalHelper.checkObjectKeys(body, [], '');
    if (errors.length === 0) return { "status": true }
    else return {
      "status": false,
      errors
    }
  },
  generateTimeObjects: function(){
    const currentTime = moment().format(MOMENT_FORMAT);
    const timeAtStartOfDay = moment().startOf("day").format(MOMENT_FORMAT);
    return {
      lastHourTimeObject: {
        startTime: moment().startOf("hour").format(MOMENT_FORMAT),
        endTime: currentTime
      },
      todayTimeObject:{
        startTime: timeAtStartOfDay,
        endTime: currentTime
      },
      lastWeekTimeObject:{
        startTime: moment().subtract(7, "days").startOf("day").format(MOMENT_FORMAT),
        endTime: timeAtStartOfDay
      },
      loadPatternLastOneDayObject : {
        startTime: momentTimezone().subtract(1,"day").startOf("hour").format(TIMEZONE_MOMENT_FORMAT),
        endTime: momentTimezone().format(TIMEZONE_MOMENT_FORMAT)
      }
    }
  },
  formatResponseObject: function(serviceResults, timeObjects){

    const { loadPattern } = serviceResults;
    const formattedLoadPattern = loadPattern.map(influxRecord => {
      const unixTime = moment(influxRecord._time).valueOf();
      const value = globalHelper.returnFilteredNumber(influxRecord._value);
      return [unixTime, value];
  });
    const responseObject = {
      hCons: globalHelper.returnFilteredNumber(serviceResults.lastHourConsumption.totalConsumption),
      dCons: globalHelper.returnFilteredNumber(serviceResults.todayConsumption.totalConsumption),
      weekCons: globalHelper.returnFilteredNumber(serviceResults.lastWeekConsumption.totalConsumption),
      weekConsPattern: serviceResults.dailyConsumptionResult.dailyConsumption,
      load: globalHelper.returnFilteredNumber(serviceResults.currentLoad),
      loadPattern: formattedLoadPattern,
      debug: {
        lastHourConsumption:{
          debug: serviceResults.lastHourConsumption.debug,
          time: timeObjects.lastHourTimeObject
        },
        todayConsumption:{
          debug: serviceResults.todayConsumption.debug,
          time: timeObjects.todayTimeObject
        },
        lastWeekConsumption:{
          debug: serviceResults.lastWeekConsumption.debug,
          time: timeObjects.lastWeekTimeObject
        },
        dailyConsumptionForLast7Days: {
          debug: serviceResults.dailyConsumptionResult.debugArray,
          time: timeObjects.lastWeekTimeObject
        }
      }
    }
    return responseObject;
  }
};