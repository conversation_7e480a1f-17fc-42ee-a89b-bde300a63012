const Excel = require('exceljs');

module.exports = {
  generateExcel: (energyMetersData) => {
    // creates a new Excel file or workbook
    const workBook = new Excel.Workbook();
    // add a sheet to a workbook
    const sheet = workBook.addWorksheet('spc');
    // adding a column to sheet in a workbook
    sheet.columns = [
      { header: 'time', key: 'time', width: 55 },
      { header: '8475', key: '8475', width: 10 },
      { header: '8476', key: '8476', width: 10 },
      { header: '8477', key: '8477', width: 10 },
      { header: '8478', key: '8478', width: 10 },
      { header: '8479', key: '8479', width: 10 },
      { header: 'specific_power_consumption', key: 'specific_power_consumption', width: 25 },
      { header: 'air_delivery', key: 'air_delivery', width: 25 },
      // TODO: add dynamic deviceIDs
    ];
    /**
     * saving an object data to each row in an Excel sheet
     */
    energyMetersData.forEach((emItem) => sheet.addRow(emItem));
    // workBook.xlsx.writeFile('SpecificEnergyMeterInHour.xlsx');
    return workBook.xlsx.writeBuffer();
  },
  filterObjectForTimestamp: (energyMetersData, currentTimestampForExcel) => {
    /**
     * Function to put all the deviceIds under timestamp key
     * for ex: {
     *   "timestamp":[{
     *     deviceId1
     *   },{
     *     deviceId3
     *   },{
     *     etc,
     *   }]
     *   "timestamp2":[],
     * }
     */
    const energyMetersTimestamp = energyMetersData.reduce((acc, em) => {
      const time = new Date(em._time).toString();
      acc[time] = acc[time] || [];
      const obj = {};
      obj.start = new Date(em._start);
      obj.stop = new Date(em._stop);
      obj.topic = em.topic;
      obj.field = em._field;
      obj.time = time;
      obj.value = em._value;
      obj.deviceId = em.deviceId;
      acc[time].push(obj);
      return acc;
    }, {});

    /**
     * To iterate through each value pf an Object.
     * value iteration consists of deviceIds with useful data,
     * data such as: spc, air_delivery, deviceIds etc.
     * */
    for (const [key, value] of Object.entries(energyMetersTimestamp)) {
      const totalPowerByEM = value.reduce((acc, variousEnergyMeters) => {
        // eslint-disable-next-line max-len
        acc.total_power_consumed = variousEnergyMeters.field === 'kw' && variousEnergyMeters.value + acc.total_power_consumed || acc.total_power_consumed;
        acc.flow_rate = variousEnergyMeters.field === 'airflowrate' && variousEnergyMeters.value || acc.flow_rate;
        if (variousEnergyMeters.field === 'kw') acc[variousEnergyMeters.deviceId] = variousEnergyMeters.value;
        return acc;
      }, {
        total_power_consumed: 0,
        flow_rate: 0,
      });
      if (totalPowerByEM.total_power_consumed > 0 && totalPowerByEM.flow_rate > 0) {
        // eslint-disable-next-line max-len
        totalPowerByEM.specific_power_consumption = totalPowerByEM.total_power_consumed / totalPowerByEM.flow_rate;
        totalPowerByEM.air_delivery = totalPowerByEM.flow_rate;
        totalPowerByEM.time = currentTimestampForExcel;
        return totalPowerByEM;
      }
    }
    // handled case when no data present
    return {
      8475: 0,
      8476: 0,
      8477: 0,
      8478: 0,
      8479: 0,
      total_power_consumed: 0,
      time: currentTimestampForExcel,
      flow_rate: 0,
      specific_power_consumption: 0,
      air_delivery: 0,
    };
  },
};
