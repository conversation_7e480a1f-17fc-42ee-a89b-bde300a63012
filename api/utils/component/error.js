const flaverr = require("flaverr");

module.exports = {
  throwExceptionInvalidComponent: function (compId) {
    throw flaverr({
      code: 'E_INVALID_COMPONENT',
      message: `Component ${compId} does not exist`,
      HTTP_STATUS_CODE: 400
    });
  },
  throwExceptionInvalidComponentDataParam: function(compId) {
    throw flaverr({
      code: 'E_INVALID_COMPONENT',
      message: `Component ${compId} does not have a data parameter.`,
      HTTP_STATUS_CODE: 400
    });
  },
  throwExceptionInvalidDeviceId: function(e) {
    throw flaverr({
      code: e.code,
      message: e.message,
      HTTP_STATUS_CODE: 400
    });
  },
  throwExceptionInvalidAbbrToConfigure: function(compId, abbr) {
    throw flaverr({
      code: "E_INVALID_COMP_DATA_PARAMETER",
      message: `Component ${compId} does not have valid abbr ${abbr} to configure`,
      HTTP_STATUS_CODE: 400
    });
  },
  throwExceptionDeviceIdNotAttachedToComponent: function(compId) {
    throw flaverr({
      code: "E_INVALID_DEVICE_ATTACHED_TO_COMPONENT",
      message: `Component ${compId} does not have deviceId attached`,
      HTTP_STATUS_CODE: 400
    });
  },
  throwExceptionInvalidRequestId: function(requestId) {
    throw flaverr({
      code: "E_INVALID_REQUEST_ID",
      message: `Invalid requestId: ${requestId} either componentId or siteId does not matched with the requested details.`,
      HTTP_STATUS_CODE: 400
    });
  },
  throwExceptionUnableToPublishIOT: function(topic, iotPayload) {
    throw flaverr({
      code: "E_UNABLE_TO_PUBLISH_IOT",
      message: `Unable to communicate with IOT.Please try again later.`,
      HTTP_STATUS_CODE: 422,
      data: {...iotPayload, topic}
    });
  }
}