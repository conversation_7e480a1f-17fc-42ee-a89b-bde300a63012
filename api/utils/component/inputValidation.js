const flaverr = require('flaverr');
const Joi = require('joi');
module.exports = {
  /**
   * @description Validate requested input parameters
   * @param {Object} params
   * @returns {Object} input parameters
   */
  updateComponentValidateInputs: function(params) {
    const schema = Joi.object({
      siteId: Joi.string().required(),
      deviceId: Joi.string().required(),
      name: Joi.string().pattern(/^(?!\s*$)[a-zA-Z0-9\s-]+$/).required(),
      deviceType: Joi.string().required(),
      regionId: Joi.string().required(),
      driverType: Joi.string().required(),
      controllerId: Joi.string().required(),
      data: Joi.array().items(
        Joi.object({
          key: Joi.string().required(),
          expression: Joi.string().allow(null, ''),
          deviceId: Joi.string().allow(null, ''),
          displayName: Joi.string().allow(null, ''),
          dau: Joi.string().allow(null, ''),
          paramGroup: Joi.string().allow(null, ''),
          min: Joi.number().allow(null, ''),
          max: Joi.number().allow(null, ''),
        }).unknown(true)
      ),
      controls: Joi.array().items(
        Joi.object({
          key: Joi.string().required(),
          expression: Joi.alternatives().try(Joi.string().allow(null, ''), Joi.number()),
          deviceId: Joi.string().allow(null, ''),
          displayName: Joi.string().allow(null, ''),
          dau: Joi.string().allow(null, ''),
          paramGroup: Joi.string().allow(null, ''),
          min: Joi.number().allow(null, ''),
          max: Joi.number().allow(null, ''),
        }).unknown(true)
      )
    }).unknown(true);


    // Validate the data against the schema
    const { error, value } = schema.validate(params);

    // Check for errors
    if (error) {
      throw flaverr('E_INPUT_VALIDATION', new Error(error.message));
    }
    return value;
  },
  validateModeChangeRequest: function (payload) {
    const schema = Joi.object({
      modeChangeDetail: Joi.array()
        .items(
          Joi.object({
            controlAbbr: Joi.string()
              .required(),
            mode: Joi.string()
              .valid('joulerecipe', 'jouletrack')
              .required(),
          })
        ),
      assetModeLabel: Joi.string()
        .valid('hybrid', 'joulerecipe', 'jouletrack')
        .required(),
    });
    const { error } = schema.validate(payload, { abortEarly: false });
    if (error) {
      throw flaverr({
          message: error.details.map(it=>it.message),
          code: `E_INPUT_ERROR`,
          operation: 'throwExceptionControlNotConfigured',
        },
      );
    }
  },
  validateOrderControlsRequest: function (params) {
    const schema = Joi.object({
      componentId: Joi.string().trim().required(),
      controls: Joi.array().items(
        Joi.object({
          controlAbbr: Joi.string().trim().required(),
          order: Joi.number().integer().required()
        })
      ).required()
    });
    const { error, value } = schema.validate(params);
    if(error) throw flaverr('E_INPUT_VALIDATION', new Error(error.message));
    return value;
  },
  validateUserConfigParam: function(params) {
    const schema = Joi.object({
      siteId: Joi.string().trim().required(),
      abbr: Joi.string().trim().required(),
      expressionDataValue: Joi.number().required(),
      componentId: Joi.string().trim().required(),
    });
    const { error } = schema.validate(params);
    if(error) throw flaverr({
      code: 'E_INVALID_USER_CONFIG_PARAM',
      message: error.message,
      HTTP_STATUS_CODE: 400
    });
    return;
  },
  validateUpdateUserConfigDataParamFeedback: function(params) {
    const schema = Joi.object({
      siteId: Joi.string().trim().required(),
      componentId: Joi.string().trim().required(),
      requestId: Joi.string().required(),
      status: Joi.number().valid(0,1).required(),
    });
    const { error } = schema.validate(params);
    if(error) throw flaverr({
      code: 'E_INVALID_USER_CONFIG_PARAM',
      message: error.message,
      HTTP_STATUS_CODE: 400
    });
    return;
  },
  validateAddComponentInputs: function(params) {
    const schema = Joi.object({
      siteId: Joi.string().required(),
      name: Joi.string().pattern(/^(?!\s*$)[a-zA-Z0-9\s-]+$/).required(),
      deviceType: Joi.string().required(),
      controllerId: Joi.string().required(),
      regionId: Joi.string().required(),
      svgId: Joi.string().required(),
      isVirtualComponent: Joi.string(),
    })
    // .unknown(false); // Disallow unknown fields

    // Validate the input against the schema
    const { error, value } = schema.validate(params);

    // Handle validation errors
    if (error) {
      throw flaverr('E_INPUT_VALIDATION', new Error(error.message));
    }

    return value;
  },
}
