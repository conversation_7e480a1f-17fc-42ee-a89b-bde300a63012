const flaverr = require('flaverr');
const globalhelper = require("../globalhelper");

const CONTROL_TYPE = {
  variableBasedControlType: "VIT",
  binaryBasedControlType: "BIT",
};
module.exports = {
  /**
   * @description Return array of devices which being added and removed by the request
   * @param {Object} componentData Fetched record
   * @param {Object} component requested component device changes by client
   * @returns {Object} {addedDevices, removedDevices}
   */
  logChangedDevice: function (componentData, component) {
    const {
      data: compData,
      controls: compControls
    } = componentData;

    const {
      data,
      controls
    } = component;

    let oldDevices = new Set();
    let newDevices = new Set();

    // Get old devices
    const oldDeviceFromData = this.fetchDevicesFromExpression(compData);
    oldDeviceFromData.forEach((d) => oldDevices.add(d));

    const oldDeviceFromControls = this.fetchDevicesFromExpression(compControls);
    oldDeviceFromControls.forEach((d) => oldDevices.add(d));

    // New devices requested
    const newDevicesFromData = this.fetchDevicesFromExpression(data);
    newDevicesFromData.forEach((d) => newDevices.add(d));

    const newDevicesFromControls = this.fetchDevicesFromExpression(controls);
    newDevicesFromControls.forEach((d) => newDevices.add(d));

    oldDevices = Array.from(oldDevices);
    newDevices = Array.from(newDevices);
    sails.log.info("newDevices", [...newDevices]);
    sails.log.info("oldDevices", [...oldDevices]);

    let addedDevices = _.difference(newDevices, oldDevices);
    let deletedDevices = _.difference(oldDevices, newDevices);
    sails.log.info("added", [...addedDevices]);
    sails.log.info("removed", [...deletedDevices]);
    return {
      addedDevices,
      deletedDevices
    };
  },
  /**
   * @description generate componentId in a device
   * @param {String} oldComponents :- old componentId of a device
   * @param {String} componentId :- componentId to be updated
   * @param {String} operation :- [add, remove]
   * @returns {string} componentId :- comma separated componentId
   */
  generateComponentIds: function (oldComponents, componentId, operation) {
    if (operation.toLowerCase() === "remove") {
      let cIdSet = new Set(oldComponents && oldComponents.split(","));
      cIdSet.delete(componentId);
      componentId = Array.from(cIdSet)
        .filter(Boolean)
        .join(',');
      if (componentId === "") componentId = "NA";
    } else if (operation.toLowerCase() === "add") {
      if (
        typeof oldComponents === "undefined"
        || !oldComponents
        || oldComponents === "null"
        || oldComponents === "NA"
      ) {
        oldComponents = "";
      }
      if (oldComponents != "NA" && oldComponents !== "null") {
        let cIdSet = new Set(oldComponents.split(","));
        cIdSet.add(componentId);
        componentId = Array.from(cIdSet)
          .filter(Boolean)
          .join(',');
      }
    }
    return componentId;
  },
  /**
   * @description Iterate through the data parameter object for generating a response object
   * @param {String} type : [data, controls]
   * @param {Object} data : parameter data object
   * @returns {Object} return response parameter object
   */
  generateCompDeviceParameterSchema: (type, data) => {
    let result = {};
    let parameterDataKeys = {};
    let parameterControlKeys = {};
    if (data.hasOwnProperty("min")) {
      data.min = data.min;
    } else {
      data.min = "";
    }

    if (data.hasOwnProperty("max")) {
      data.max = data.max;
    } else {
      data.max = "";
    }
    if (type === "data") {
      let _parameter = {
        key: data.abbr || data.key,
        expression: data.expression || "",
        deviceId: data.deviceId || "",
        displayName: data.displayName,
        dau: data.dau,
        paramGroup: data.paramGroup,
        min: data.min,
        max: data.max,
      };
      if (
        data.hasOwnProperty("mockDataSet")
        && Array.isArray(data.mockDataSet)
        && data.mockDataSet.length
      ) {
        _parameter.mockDataSet = data.mockDataSet;
      }
      result = _parameter;
      parameterDataKeys[data.key] = data;
    } else if (type === "controls") {
      result = {
        key: data.abbr || data.key,
        expression: data.expression || "",
        deviceId: data.deviceId || "",
        displayName: data.displayName,
        dau: data.dau,
        paramGroup: data.paramGroup,
        min: data.min,
        max: data.max,
        timeout: "",
      };
      parameterControlKeys[data.key] = data;
    }
    return {
      result,
      parameterDataKeys,
      parameterControlKeys,
    };
  },
  /**
   * @description Fetch devices from expression
   * @param {Array} expressionData : expression data of component device
   * @returns {Array} devices: list of devices
   */
  fetchDevicesFromExpression: function (expressionData) {
    let devices = new Set();
    for (const d of expressionData) {
      if (d.deviceId) {
        d.deviceId
          .toString()
          .split(",")
          .forEach((e) => {
            if (e == "" || !e) return;
            devices.add(e);
          });
      }
      if (d.expression) {
        globalhelper.parseFormulaToIds(d.expression)
          .forEach((e) => {
            devices.add(e);
          });
      }
    }
    return Array.from(devices);
  },
  getComponentAllDataParameter: function (componentDevice) {
    const parameters = (componentDevice.data && JSON.parse(componentDevice.data)) || [];
    if (_.isEmpty(parameters)) return [];
    return parameters
      .map((parameter) => {
        const dataPacket = (_.isObject(parameter) && parameter) || JSON.parse(parameter);
        if (dataPacket.key && dataPacket.displayName) {
          return {
            abbr: dataPacket.key,
            name: dataPacket.displayName,
          };
        }
      })
      .filter(Boolean)
      .sort((a, b) => a.name.localeCompare(b.name));
  },
  getConfigureComponentDataParameter: function (componentDevice) {
    const parameters = (componentDevice.data && JSON.parse(componentDevice.data)) || [];
    if (_.isEmpty(parameters)) return [];
    return parameters
      .map((parameter) => {
        const dataPacket = (_.isObject(parameter) && parameter) || JSON.parse(parameter);
        if (
          (dataPacket.key
            && dataPacket.displayName
            && dataPacket.hasOwnProperty("isVirtualParameter")
            && dataPacket.isVirtualParameter)
          || (dataPacket.hasOwnProperty("expression") && !_.isEmpty(dataPacket.expression.trim()))
        ) {
          return {
            abbr: dataPacket.key,
            name: dataPacket.displayName,
          };
        }
      })
      .filter(Boolean)
      .sort((a, b) => a.name.localeCompare(b.name));
  },
  getComponentAllControlParameter: function (componentDevice) {
    const parameters = (componentDevice.controls && JSON.parse(componentDevice.controls)) || [];
    if (_.isEmpty(parameters)) return [];
    return parameters
      .map((parameter) => {
        const dataPacket = (_.isObject(parameter) && parameter) || JSON.parse(parameter);
        if (dataPacket.key && dataPacket.displayName) {
          return dataPacket;
        }
      })
      .filter(Boolean)
      .sort((a, b) => a.displayName.localeCompare(b.displayName));
  },
  getConfigureComponentControlParameter: function (componentDevice) {
    const parameters = (componentDevice.controls && JSON.parse(componentDevice.controls)) || [];
    if (_.isEmpty(parameters)) return [];
    return parameters
      .map((parameter) => {
        const dataPacket = (_.isObject(parameter) && parameter) || JSON.parse(parameter);
        if (
          dataPacket.key
          && dataPacket.displayName
          && dataPacket.hasOwnProperty("expression")
          && !_.isEmpty(dataPacket.expression.trim())
        ) {
          return dataPacket;
        }
      })
      .filter(Boolean)
      .sort((a, b) => a.displayName.localeCompare(b.displayName));
  },
  configuredControlsConfig: function (configuredControlParameters, controlRelationshipData) {
    const response = [];
    const compAbbr = configuredControlParameters.map((cp) => cp.key);
    for (const controlsRelationship of controlRelationshipData) {
      const {
        controlName,
        controlAbbr,
        controlProperty,
        controlType
      } = controlsRelationship;
      if (this.isRelationShipExistInControlConfig(controlType, controlProperty, compAbbr)) {
        response.push(controlsRelationship);
      }
    }
    return response;
  },
  isRelationShipExistInControlConfig: function (controlType, controlProperty, compAbbr) {
    if (controlType === CONTROL_TYPE.variableBasedControlType) {
      const { commandAbbr } = controlProperty;
      if (!compAbbr.includes(commandAbbr)) return false;
    } else if (controlType === CONTROL_TYPE.binaryBasedControlType) {
      const {
        left,
        right
      } = controlProperty;
      if (
        !(left && compAbbr.includes(left.commandAbbr))
        || !(right && compAbbr.includes(right.commandAbbr))
      ) {
        return false;
      }
    }
    return true;
  },
  componentModeResponseBuilder: function (deviceAbbrWiseModeList, controlWithRelationship) {
    const finalResult = {};
    const rawControlModes = deviceAbbrWiseModeList.reduce((acm, curr) => {
      const {
        commandAbbr,
        deviceId
      } = curr;
      if (!acm.hasOwnProperty(deviceId)) {
        acm[deviceId] = {};
      }
      acm[deviceId][commandAbbr] = curr;
      return acm;
    }, {});

    const controlModeCollection = [];
    for (const Control of controlWithRelationship) {
      const {
        controlType,
        controlProperty,
        controlName,
        controlAbbr
      } = Control;
      const _modeObj = {
        controlAbbr,
        controlName
      };
      let device_abbr;
      let deviceId;
      let isConfigured = false;

      if (controlType === 'BIT') {
        device_abbr = controlProperty.left.device_abbr;
        deviceId = controlProperty.left.deviceId;
        isConfigured = !!(controlProperty.left.expression && device_abbr && deviceId);
      } else if (controlType === 'VIT') {
        device_abbr = controlProperty.device_abbr;
        deviceId = controlProperty.deviceId;
        isConfigured = !!(controlProperty.expression && device_abbr && deviceId);
      }
      if (isConfigured) {
        _modeObj.mode = rawControlModes[deviceId][device_abbr].mode;
        controlModeCollection.push(_modeObj);
      }
    }
    return controlModeCollection;
  },
  /**
   * This function generate a mapping of component command abbr with device's command abbr
   * @param componentControls
   */
  buildComponentCommandDeviceCommandAbbrMap: function (componentCommands) {
    if (!Array.isArray(componentCommands)) {
      throw new Error(`componentControls should be an array`);
    }
    const map = {};
    for (const command of componentCommands) {
      const {
        key: componentCommand,
        deviceId,
        device_abbr,
        expression
      } = command;
      if (!deviceId || !expression) continue;
      if (!map.hasOwnProperty(componentCommand)) {
        map[componentCommand] = {
          [deviceId]: command,
        };
      } else {
        console.warn(`this componentCommand="${componentCommand}" can not be duplicate`);
      }
    }
    return map;
  },
  isDataParameterConfigured: function (dataParameter) {
    const { expression } = dataParameter;
    return !_.isEmpty(expression)
  },
  isControlConfigured: function (control) {
    const {
      controlType,
      controlProperty
    } = control;
    if (controlType == 'BIT') {
      const {
        left: { deviceId: leftCommandDeviceId },
        right: { deviceId: rightCommandDeviceId }
      } = controlProperty;
      if (_.isEmpty(leftCommandDeviceId) || _.isEmpty(rightCommandDeviceId)) {
        return false;
      }
      return true
    } if (controlType == 'VIT') {
      const { deviceId } = controlProperty;
      return !_.isEmpty(deviceId)
    }
    return false;
  },
  sortParameter: function (parameters) {
    const configuredParams = [];
    const unConfiguredParams = [];
    for (let param of parameters) {
      if (this.isDataParameterConfigured(param)) {
        configuredParams.push(param)
      } else {
        unConfiguredParams.push(param)
      }
    }
    configuredParams.sort((first, second) => (first.displayName.toLowerCase() > second.displayName.toLowerCase() ? 1 : -1))
    unConfiguredParams.sort((first, second) => (first.displayName.toLowerCase() > second.displayName.toLowerCase() ? 1 : -1))
    return configuredParams.concat(unConfiguredParams)
  },
  throwExceptionInvalidControl: function (controlAbbr, componentControlAbbr) {
    let invalidControl = controlAbbr.filter((it) => !componentControlAbbr.includes(it));
    if (invalidControl.length) {
      throw flaverr({
        message: `These controls (${invalidControl.join(',')}) do not exist. Please check the Control Driver or component configuration`,
        code: `E_INVALID_CONTROL`,
        operation: 'changeAssetControlMode',
        data: {
          invalidControl,
        },
      },);
    }
  },
  throwExceptionControlNotConfigured: function (controls) {
    const unConfiguredControls = controls.filter((it) => !this.isControlConfigured(it))
      .map((it) => it.controlAbbr);
    if (unConfiguredControls.length) {
      throw flaverr({
        message: `These controls (${unConfiguredControls.join(',')}) are not configured properly. Please check the deviceId,commandAbbr and expression is attached to each control`,
        code: `E_INVALID_CONTROL`,
        operation: 'throwExceptionControlNotConfigured',
        data: {
          unConfiguredControls,
        },
      },);
    }
  },
  getComponentCategoryCacheKey: (siteId) => {
    return `siteId:${siteId}:available_device_component_categories`;
  },
  assignControllerIdToRespectiveDeviceId(controls, controllerIdsMap) {
    let invalidDeviceIds = [];
    for (const control of controls) {
      if (control.controlType === "VIT") {
        if (control?.controlProperty) {
          if (!controllerIdsMap[control.controlProperty.deviceId]) {
            invalidDeviceIds.push(control.controlProperty.deviceId);
          } else {
            control.controlProperty.controllerId = controllerIdsMap[control.controlProperty.deviceId];
          }
        }
      } else if (control.controlType === "BIT") {
        if (control?.controlProperty) {
          if (!controllerIdsMap[control.controlProperty.left.deviceId])
            invalidDeviceIds.push(control.controlProperty.left.deviceId);
          else
            control.controlProperty.left.controllerId = controllerIdsMap[control.controlProperty.left.deviceId];
          if (!controllerIdsMap[control.controlProperty.right.deviceId])
            invalidDeviceIds.push(control.controlProperty.right.deviceId);
          else
            control.controlProperty.right.controllerId = controllerIdsMap[control.controlProperty.right.deviceId];
        }
      }
    }
    invalidDeviceIds = [...new Set(invalidDeviceIds)].filter(deviceId => deviceId && String(deviceId).trim() !== '');
    if (invalidDeviceIds.length > 0) {
      throw flaverr('E_INVALID_DEVICE', new Error(`Invalid device IDs found: ${invalidDeviceIds.join(', ')}`));
    }
  },
  dataParamFactoryBuilder: (packet) => {
    const obj = {
      dau: '',
      deviceId: '',
      displayName: '',
      expression: '',
      group: '',
      key: '',
      max: '',
      min: '',
      origin: '',
      paramGroup: '',
      rogueMax: '',
      rogueMin: '',
      device_abbr: '',
    };
    for (const key in packet) {
      if (obj.hasOwnProperty(key)) {
        obj[key] = packet[key];
      }
    }
    return obj;
  },
  controlParamFactoryBuilder: (paramPacket = {}) => {
    const defaultControlParams = {
      deviceId: '',
      displayName: '',
      expression: '',
      key: '',
      max: '',
      min: '',
      paramGroup: '',
      device_abbr: '',
      timeout: '',
    };
    for (const key in paramPacket) {
      if (defaultControlParams.hasOwnProperty(key)) {
        defaultControlParams[key] = paramPacket[key];
      }
    }
    return defaultControlParams;
  },
  componentModeResponseBuilderV2: function (deviceAbbrWiseModeList, controlWithRelationship) {
    const finalResult = {};
    const rawControlModes = deviceAbbrWiseModeList.reduce((acm, curr) => {
      const { commandAbbr, deviceId } = curr;
      if (!acm.hasOwnProperty(deviceId)) {
        acm[deviceId] = {};
      }
      acm[deviceId][commandAbbr] = curr;
      return acm;
    }, {});

    const controlModeCollection = {}
    for (const componentId in controlWithRelationship) {
      for (const Control of controlWithRelationship[componentId]) {
        const { controlType, controlProperty, controlName, controlAbbr } = Control;
        const _modeObj = {
          controlAbbr,
          controlName,
        };
        let device_abbr;
        let deviceId;
        let isConfigured = false;

        if (controlType === "BIT") {
          device_abbr = controlProperty.left.device_abbr;
          deviceId = controlProperty.left.deviceId;
          isConfigured = !!(controlProperty.left.expression && device_abbr && deviceId);
        } else if (controlType === "VIT") {
          device_abbr = controlProperty.device_abbr;
          deviceId = controlProperty.deviceId;
          isConfigured = !!(controlProperty.expression && device_abbr && deviceId);
        }
        if (isConfigured) {
          if(!controlModeCollection.hasOwnProperty(componentId)){
            controlModeCollection[componentId]={
              controlWiseMode:[],
              componentId,
              "assetModeLabel": {
                "key": "jouletrack",
                "label": "jouletrack"
              },
            }
          }
          _modeObj.mode = rawControlModes[deviceId][device_abbr].mode;
          controlModeCollection[componentId].controlWiseMode.push(_modeObj);
        }
      }
    }

    return controlModeCollection;
  },

}
