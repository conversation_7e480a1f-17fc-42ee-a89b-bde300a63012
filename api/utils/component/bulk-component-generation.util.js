
const globalHelper = require('../globalhelper'); 
const Joi = require("joi");

const componentParamSchema = Joi.object({
  componentName: Joi.string(),
});

module.exports = {
  /**
   * @description Input checks the following: Non conflicting component names or Ids for the same Asset Id
   * Also, creates maps/lists for the following:
   * - AssetId to component configuration
   * - List of components to be created.
   * - deviceId_paramType_assetId to componentId of components to be created.
   * @param {array} rows Array of rows read from the google sheet.
   * @returns {object} assetIdGrouping: Map of: assetId - input parameters from google sheet.
   */
  checkInput: function(rows, layerNameToIdMap){
    // let errors = globalHelper.checkObjectKeys(body, [], '');
    let errors = [];

    const assetIdGrouping = rows.reduce((acc, row) => {
      let { assetId, componentName, paramType, componentId, layerName, displayName, address } = row;
      assetId = globalHelper.removeSpacesFromStartAndEnd(assetId);
      componentName = globalHelper.removeSpacesFromStartAndEnd(componentName);
      if (assetId == "" || assetId == undefined) return acc;
      if (typeof acc[assetId] != "object") acc[assetId] = {};
      acc[assetId].assetId = assetId;
      // Collecting and adding paramTypes to array
      if (acc[assetId].paramTypes == undefined) acc[assetId].paramTypes = [];
      if (paramType) acc[assetId].paramTypes.push(paramType);
      // Assigning component name to accumulator if conflicting component names for same asset Id not found
      if (!acc[assetId].componentName)
        acc[assetId].componentName = componentName
      else if (acc[assetId].componentName != componentName && componentName!="")
        errors.push(`Conflicting component names: "${acc[assetId].componentName}" and "${componentName}" found for assetId: ${assetId}`);
      // Assigning component ID to accumulator if conflicting componentIds for same asset Id not found
      if (!acc[assetId].componentId)
        acc[assetId].componentId = componentId
      else if (acc[assetId].componentId != componentId && componentId!="")
        errors.push(`Conflicting componentId: "${acc[assetId].componentId}" and "${componentId}" found for assetId: ${assetId}`);
      // Checking for layer information
      if(!acc[assetId].layerName){
        acc[assetId].layerName = layerName;
        acc[assetId].layerId = layerNameToIdMap[layerName];
      }
      else if (acc[assetId].layerName != layerName && layerName!="")
        errors.push(`Conflicting layerName: "${acc[assetId].layerName}" and "${layerName}" found for assetId: ${assetId}`);
      // Assigning DisplayName and address to accumulator as well as a check to confirm parameter has been created before creating components.
      acc[assetId].displayName = displayName;
      acc[assetId].address = address;
      return acc;
    }, {});

    // Creating Lists for components to be created and updated
    let componentsToBeCreatedSet = new Set(), componentIdToComponentNameMapping = {};
    Object.keys(assetIdGrouping).forEach(assetId => {
      const componentParams = assetIdGrouping[assetId];
      // Deleting entries without component name being specified.
      if(componentParams.componentName == '') delete assetIdGrouping[assetId];
      // If Component name exists, but there is no componentId, it implies that component needs to be created.
      if(componentParams.componentName && !componentParams.componentId) {
        componentsToBeCreatedSet.add(assetId);
        if(!componentParams.layerName || !componentParams.layerId) 
          errors.push(`No Layer Name or corresponding Layer ID found for assetId: ${assetId} while trying to create it's component.`);
        if(!componentParams.displayName || !componentParams.address)
          errors.push(`No Display Name or Address found for parameter while trying to create component for assetId: ${assetId}. Please confirm parameter exists.`);
      }
      // If component name and componentId both exists, need to update component name after checking if it's different from already saved.
      if(componentParams.componentName && componentParams.componentId) {
        componentIdToComponentNameMapping[componentParams.componentId] = componentParams.componentName;
      }
      // const validation = componentParamSchema.validate(componentParams, {allowUnknown: true});
      // if(validation.error) errors.push(validation.error+` for assetId "${assetId}"`);
    });
    const assetIdsOfComponentsToBeCreated = Array.from(componentsToBeCreatedSet);
    if (errors.length === 0) return { "status": true, assetIdGrouping, assetIdsOfComponentsToBeCreated, componentIdToComponentNameMapping };
    else return {
      "status": false,
      errors
    }
  },
  // Generates all parameters for a component configuration apart from it data parameters.
  generateComponentCreationBodyParams: function(assetIdGrouping, siteId, generatedComponentId, controllerId, assetIdsOfComponentsToBeCreated){
    let latestComponentIdGenerated = Number(generatedComponentId.split("_")[1]);
    if(isNaN(latestComponentIdGenerated)) throw new Error("Error generating component Id!");
    const componentCreationCompleteParams = assetIdsOfComponentsToBeCreated
      .map(assetId => {
      let singleComponentParams = assetIdGrouping[assetId];
      let completeParams = {
        siteId,
        name: singleComponentParams.componentName,
        deviceId: `${siteId}_${latestComponentIdGenerated}`,
        deviceType: "fireAlarmSensor", // Hardcoded for ibms fire creation only currently.
        controllerId: controllerId,
        regiondId: "ibms",
        driverType: 0,
        isVirtualComponent: "0",
        assetId, // Used to help add data params. Not being saved in DB currently. 
        layerId: singleComponentParams.layerId, // Used to add component to hierarchy information in postgres. Not being saved in Dynamo
        regionId: "ibms",
      };
      latestComponentIdGenerated++; // Incrementing componentId for the next component to be created.
      return completeParams;
    });

    return componentCreationCompleteParams;
  },
  // Generates the data parameter configurations for each component.
  appendComponentDataParams: function( newComponentConfigurations, componentDriver, assetIdGrouping, deviceId ){
    const { parameters: componentParameters } = componentDriver;
    const inheritFromParameterMap = componentParameters.reduce((acc, parameter) => {
      const { inheritedFrom } = parameter;
      acc[inheritedFrom] = parameter;
      return acc;
    }, {});
    const componentIdToDeviceParameterAbbrMap = {}; // Used for updating the device parameters with componentIDs.
    const completeComponentConfiguration = newComponentConfigurations.map(newComponentConfig => {
      const deviceParameterList = []; // Used to track parameters being used for this component.
      const { assetId } = newComponentConfig;
      const parametersToBeAdded = assetIdGrouping[assetId].paramTypes;
      let data = parametersToBeAdded.map(paramType => {
        const parameterDriver = inheritFromParameterMap[paramType];
        const dataParameter = {
          key: parameterDriver.abbr,
          expression: `||${deviceId}@${paramType}_${assetId}||`,
          deviceId,
          displayName: parameterDriver.displayName,
          dau: parameterDriver.dau,
          paramGroup: parameterDriver.paramGroup,
          min: "",
          max: ""
        };

        // Creating map to update device parameters with componentIds later.
        const componentId = newComponentConfig.deviceId, deviceId_abbr = `${deviceId}_${paramType}_${assetId}`;
        if(!Array.isArray(componentIdToDeviceParameterAbbrMap[componentId])){
          componentIdToDeviceParameterAbbrMap[componentId] = [deviceId_abbr];
        } else {
          componentIdToDeviceParameterAbbrMap[componentId].push(deviceId_abbr);
        }
        deviceParameterList.push(deviceId_abbr);
        return dataParameter;
      });
      return {
        ...newComponentConfig,
        deviceParameterList,
        data,
        controls: []
      }
    });
    return { completeComponentConfiguration, componentIdToDeviceParameterAbbrMap};
  }
};