
const globalHelper = require('../globalhelper');

const templatetypeList = ['action', 'alert'];
module.exports = {
  /**
   * Creates object for template query
   * @param {object} inputs: input values to create req obj.
   * @returns request objecte to query DB.
   */
  buildInitialPacket(inputs) {
    const templateId = `${inputs.templateCategory}_${Date.now()}`
    return { ...inputs, templateId };
  },
};