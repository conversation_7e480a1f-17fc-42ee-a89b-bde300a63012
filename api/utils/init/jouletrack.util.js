
const globalHelper = require('../globalhelper'); 

module.exports = {
  checkInput: function(body){
    let errors = globalHelper.checkObjectKeys(body, ['_site', '_role', 'id'], '_userMeta');
    if (errors.length === 0) return false;
    else return errors;
  },
  /**
   * @function filterSiteData
   * @description Extracts a site list (containing )
   * @param {array} siteList Array of site objects fetched from sites table.
   * @param {string} siteId siteId passed in the token.
   */
  filterSiteData: function(siteList, siteId){
    let currentSite, filteredSiteList; 
    filteredSiteList = siteList.map(site => {
      if(site.siteId === siteId) currentSite = site;
      return {siteId: site.siteId, siteName: site.siteName};
    });
    return {
      currentSite,
      sitesList: filteredSiteList
    };
  },

  filterPlantList: function(plantList){
    return plantList.map(plant => ({
      name: plant.name,
      processId: plant.processId,
      plantCategories: plant.plantCategories
    }));
  },

  addParameterKeysToDevices: function(deviceList, parameterList){
    let parameterDictionary = {};
    // Populating parameter Dictionary
    parameterList.map(parameter => {
      let { deviceId, abbr, displayName, utilityType, paramGroup, dau } = parameter;
      if (!parameterDictionary[deviceId]) parameterDictionary[deviceId] = {};
      parameterDictionary[deviceId][abbr] = {
        abbr,
        displayName,
        utilityType,
        paramGroup,
        dau,
      };
    });
    return deviceList.map(device => {
      const { deviceId } = device;
      // Appending parameter object in 'param' key to device object
      if(parameterDictionary[deviceId]) device.param = parameterDictionary[deviceId];
      delete device.createdAt;
      delete device.updatedAt;
      return device;
    });
  },
};