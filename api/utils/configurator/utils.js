module.exports = {
  dynamicNavigation : function(system) {
    let responseSchema = {};
    for (let row of system) {
      const {
        system_category_id: systemCategoryId, 
        system_category_name: systemCategoryName,
        system_id: systemId, 
        system_name: systemName,
        icon
      } = row

      if (responseSchema.hasOwnProperty(systemCategoryId)) {
        responseSchema[systemCategoryId].systems.push({
          id: systemId,
          name: systemName,
          icon: icon,
        })
      } else {
        responseSchema[systemCategoryId] = {
          system_category_id: systemCategoryId,
          name: systemCategoryName,
          systems: [{
            id: systemId,
            name: systemName,
            icon: icon,
          }]
        }
      }
    }
    return Object.values(responseSchema)
  },
  fetchAllSystems: function(system) {
    let responseSchema = {};
    for (let row of system) {
      const {
        system_category_id: systemCategoryId, 
        system_category_name: systemCategoryName,
        system_id: systemId, 
        system_name: systemName,
        description,
        status,
        icon,
        svg_path:svgPath,
        updatedAt,
      } = row

      if (responseSchema.hasOwnProperty(systemCategoryId)) {
        responseSchema[systemCategoryId].systems.push({
          id: systemId,
          name: systemName,
          icon: icon,
          description,
          status,
          svg_path: svgPath,
          last_updated_at: updatedAt
        })
      } else {
        responseSchema[systemCategoryId] = {
          system_category_id: systemCategoryId,
          name: systemCategoryName,
          systems: [{
            id: systemId,
            name: systemName,
            icon: icon,
            description,
            status,
            svg_path: svgPath,
            last_updated_at: updatedAt
          }]
        }
      }
    }
    return Object.values(responseSchema)
  },
}