const Joi = require('joi');
const errorHandler = require('./graphErrorHandler')

const validateSankeyGraph = (payload) => {
    const graphSchema = Joi.object({
        nodes: Joi.array().items(
            Joi.object({
                deviceId: Joi.alternatives().conditional('deviceClass', {
                    is: 'device',
                    then: Joi.number().required(),
                    otherwise: Joi.string().required()
                }),
                deviceClass: Joi.string().valid('device', 'component').required(),
                deviceType: Joi.string().required(),
                driverType: Joi.number().required(),
                column: Joi.number().min(1),
                offset: Joi.number(),
                // color: Joi.string()
            })
        ).min(1).required(),
        data: Joi.array().items(
            Joi.array().items(
                Joi.alternatives().try(Joi.string().allow(''), Joi.number())
            ).min(1).max(2).required(),
        ).min(1).required(),
        parameterLabel: Joi.string().required(),
    });

    const { error } = graphSchema.validate(payload);
    if (error) {
        errorHandler.throwExceptionInvalidSankeyProperty(error.message);
    }
    const deviceNodeLevel = new Map();
    const invalidParentChildRelationship = new Set();
    const duplicateDeviceIds= new Set();
    const invalidDeviceIds = new Set();
    const uniqueDeviceId = new Set();
    const invalidLink = new Set();
    const invalidEmptyChildLink = new Set();
    let isEmptyParentNodesExist = false;

    payload.nodes.forEach(({deviceId, column}) => {
        const defaultColumn = column ?  column : 1;
        if (uniqueDeviceId.has(deviceId)) {
            duplicateDeviceIds.add(deviceId);
        } else {
            uniqueDeviceId.add(deviceId)
            deviceNodeLevel.set(deviceId, defaultColumn);
        }
    });
    // Check if there exist only one level
    if (!Array.from(deviceNodeLevel.values()).some((item) => item > 1)) {
        errorHandler.throwExceptionLevels()
    }
   
    payload.data.forEach(link => {
        const [
            deviceId1,
            deviceId2,
        ] = link
        // Check if is there any other level except 1 connected to "" child means no child.
        if (
            (
             typeof deviceId2 == 'string' && _.isEmpty(deviceId2.trim()) || !deviceId2
            ) && deviceNodeLevel.get(deviceId1) != 1) {
            invalidEmptyChildLink.add(deviceId1)
            return;
        }
        if ( 
            (
              typeof deviceId2 == 'string' && _.isEmpty(deviceId2.trim()) ||  !deviceId2
            ) && 
            !uniqueDeviceId.has(deviceId1)) {
            invalidDeviceIds.add(deviceId1)
            return;
        }
        if (typeof deviceId2 == 'string' && _.isEmpty(deviceId2.trim()) ||  !deviceId2) return;

        if (typeof deviceId1 == 'string' && _.isEmpty(deviceId1.trim()) ||  !deviceId1) {
            isEmptyParentNodesExist = true;
            return;
        }

        if (deviceId1 === deviceId2) { 
            invalidLink.add(deviceId1)
        }
        if (!uniqueDeviceId.has(deviceId1)) {
            invalidDeviceIds.add(deviceId1)
        }
        if (!uniqueDeviceId.has(deviceId2)) {
            invalidDeviceIds.add(deviceId2)
        }

        if (deviceNodeLevel.get(deviceId1) >= deviceNodeLevel.get(deviceId2)) {
            invalidParentChildRelationship.add(deviceId1)
        }
    })


    if (isEmptyParentNodesExist)  errorHandler.throwExceptionInvalidEmptyParentNodes()
    if (invalidEmptyChildLink.size) {
        errorHandler.throwExceptionInvalidEmptyChildLink(Array.from(invalidEmptyChildLink))
    }

    if (duplicateDeviceIds.size) {
        errorHandler.throwExceptionDuplicateDevicesFound(Array.from(duplicateDeviceIds))
    }
    if (invalidDeviceIds.size) {
        errorHandler.throwExceptionInvalidDevices(Array.from(invalidDeviceIds))
    }
    if (invalidLink.size) {
        errorHandler.throwExceptionInvalidLink(Array.from(invalidLink))
    }
    if (invalidParentChildRelationship.size) {
        errorHandler.throwExceptionInvalidParentChildRelationship(Array.from(invalidParentChildRelationship))
    }

}

module.exports = {
    validateSankeyGraph
}