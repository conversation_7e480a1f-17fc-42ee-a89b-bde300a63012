const flaverr = require('flaverr');
const validateSankeyGraphProperty  = () => {
  
}

const invalidDevices = () => {

}

const invalidGraphType = () => {
    throw flaverr({
       code: 'E_INVALID_GRAPH_TYPE', 
       msg: 'Invalid Graph Type, Please provide a valid graph type',
       statusCode: 400
    })
}

const invalidPageId = (pageId) => {
    throw flaverr({
        code: 'E_INVALID_PAGE_ID', 
        msg: `Invalid page id ${pageId}`,
        statusCode: 400
     })
}

const throwExceptionInvalidSankeyProperty = (message) => {
    throw flaverr({
        code: 'E_INVALID_PAGE_ID', 
        msg: message,
        statusCode: 400
     })
}

const throwExceptionDuplicateDevicesFound = (deviceIds) => {
    throw flaverr({
        code: 'E_INVALID_PAGE_ID', 
        msg: `Duplicate device IDs found in ${deviceIds.join(',')}`,
        statusCode: 400
     })
}

const throwExceptionInvalidDevices = (deviceIds) => {
    throw flaverr({
        code: 'E_INVALID_PAGE_ID', 
        msg: `Devices does not exist (${deviceIds.join(', ')})`,
        statusCode: 400
     })
}

const throwExceptionInvalidLink = (deviceIds) => {
    throw flaverr({
        code: 'E_INVALID_LINK', 
        msg: `Device IDs can not be attached to itself ${deviceIds.join(',')}`,
        statusCode: 400
     })
}

const throwExceptionInvalidParentChildRelationship = (deviceIds) => {
    throw flaverr({
        code: 'E_INVALID_LINK', 
        msg: `Child Device IDs can not be attached to parent levels ${deviceIds.join(',')}`,
        statusCode: 400
     })
}

const throwExceptionInvalidLabel = (label) => {
    throw flaverr({
        code: 'E_INVALID_LABEL', 
        msg: `Invalid graph label name ${label}`,
        statusCode: 400
     })
}

const throwExceptionLevels = () => {
    throw flaverr({
        code: 'E_INVALID_LEVEL', 
        msg: `Please add more than 1 level to save sankey graph.`,
        statusCode: 400
     })
}

const throwExceptionInvalidEmptyChildLink = (device) => {
    throw flaverr({
        code: 'E_INVALID_LEVEL_LINK', 
        msg: `Only level 1 can be attached to empty child link. Please check the level of these nodes (${device.join(', ')})`,
        statusCode: 400
     })
}

const throwExceptionInvalidEmptyParentNodes = () => {
    throw flaverr({
        code: 'E_INVALID_PARENT_NODES', 
        msg: `Invalid empty parent nodes exist please check the device first before save.`,
        statusCode: 400
     })
}

const throwExceptionInvalidSankeyConfig = () => {
    throw new flaverr({ 
        code: 'E_SANKEY_GRAPH_ERROR',
        statusCode: 400,
        msg: 'Sankey config does not exist'
      });
}

module.exports = {
    validateSankeyGraphProperty,
    invalidDevices,
    invalidGraphType,
    invalidPageId,
    throwExceptionInvalidSankeyProperty,
    throwExceptionDuplicateDevicesFound,
    throwExceptionInvalidDevices,
    throwExceptionInvalidLink,
    throwExceptionInvalidParentChildRelationship,
    throwExceptionInvalidLabel,
    throwExceptionLevels,
    throwExceptionInvalidEmptyChildLink,
    throwExceptionInvalidEmptyParentNodes,
    throwExceptionInvalidSankeyConfig

}