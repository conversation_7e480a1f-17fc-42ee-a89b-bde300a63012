const Joi = require('joi');
module.exports = {
  validateRegisterNewAlert: function (body) {

  },
  subscriptionCriteriaValidator: function (subscriptionCriteriaToValidate) {

    const subscriptionObjSchema = Joi.object({
      siteId: Joi.string().required(),
      deviceIds: Joi.alternatives().try(
        Joi.string().pattern(new RegExp('^\\*$')),
        Joi.array().items(Joi.string())
      ).required(),
    })

    const subscriptionCriteriaSchema = Joi.alternatives().try(
      Joi.string().pattern(new RegExp('^\\*$')),
      Joi.array().min(1).items(subscriptionObjSchema)
    )

    return subscriptionCriteriaSchema.validate(subscriptionCriteriaToValidate);
  }
};
