const Joi = require('joi');
const Flaverr = require('flaverr');
const globalHelper = require('../globalhelper')
module.exports = {
    /**
     * @description Filter all and validate the input parameters
     * @param {Object} deviceConfig
     * @returns {Object} deviceConfig
     */
    filterKeys(deviceConfig) {
      const schema = Joi.object()
        .keys({
          networkId: Joi.string().optional(),
          regionId: Joi.string().optional(),
          areaId: Joi.string().optional(),
          portNumber: Joi.number().required(),
          controllerId: Joi.string().required(),
          communicationType: Joi.string().required(),
          communicationCategory: Joi.string().required(),
          driverType: Joi.string().required(),
          deviceType: Joi.string().required(),
          name: Joi.string()
            .pattern(/^(?!\s*$)[a-zA-Z0-9\s-&]+$/)
            .required(),
          siteId: Joi.string().required(),
          slaveId: Joi.number().optional(),
          isMainMeter: Joi.boolean().optional(),
        })
        .unknown(true);
      const { error } = schema.validate(deviceConfig);
      if (error && error.message) {
        throw new Flaverr("E_BAD_REQUEST", new Error(error.message));
      }
      deviceConfig = globalHelper.removeNullValueFromObject(deviceConfig);

      if (deviceConfig.hasOwnProperty("isVirtualDevice")) {
        deviceConfig.isVirtualDevice = deviceConfig["isVirtualDevice"];
      } else {
        deviceConfig.isVirtualDevice = "0";
      }
      return deviceConfig;
    },
    /**
     * @description get FunctionType based on the driver stored in deviceTypes table.
     * @param {*} deviceDriver
     * @returns "write" || "read" || "both"
     */
    getFunctionType(deviceDriver) {
      if (globalHelper.isNullish(deviceDriver.functionType)) return "read";
      else return deviceDriver.functionType;
    },
    /**
     * @description Validate the input params for areas, region and network.
     * @param {Object} deviceConfig
     * @param {Object} _siteInfo
     * @returns Boolean
     */
    validateRegionsAndAreas(deviceConfig, siteInfo) {
        let { areaId, regionId, networkId } = deviceConfig;
        if (areaId != "ibms" || regionId != "ibms") {
          if (
              !siteInfo.areas[deviceConfig.areaId] ||
              !siteInfo.regions[deviceConfig.regionId] ||
              !siteInfo.networks[deviceConfig.networkId]
          ) {
            return false;
          }
          return true;
        } else {
          if (!siteInfo.networks[networkId]) return false;
          else return true;
        }
    },
    /**
     * @description Get all the slaveIds available for allocation to the device.
     * @param {Array} deviceList
     * @param {Object} deviceConfig
     * @returns Array<String> ['0', '1']
     */
    getAvailableSlaveIds: function(deviceList, deviceConfig) {
        const {controllerId} = deviceConfig;
        let slaveArr = new Array(255);
        slaveArr.fill(false)
        const availableIndexes = []
        for (const device of deviceList){
            if (device.controllerId == controllerId &&
                device.communicationType === "MB") {
                    slaveArr[device.slaveId] = true;
                }
        };
        for (let i = 1; i < slaveArr.length; i++) {
            if (!slaveArr[i]) {
                availableIndexes.push(i.toString())
            }
        }
        return availableIndexes;
    },
    /**
   * @description Check if a devicePort is available in an controller
   * @param {array} devices array of devices attached to controller
   * @param {string} portNumber new port number to add to an controller
   * @param {String} controllerId controllerId
   * @return {boolean} true if port number is in use
   */
  isPortNumberInUse: function (devices, portNumber, controllerId) {
    let portInUse = false;
    for (let deviceIndex in devices) {
      let device = devices[deviceIndex];
      /**
      * Port number validation in case of NMB
      *  */
      if (device.controllerId &&
        device.controllerId == controllerId &&
        device.portNumber &&
        device.portNumber == portNumber) {
        portInUse = true;
        break;
      }
    }
    return portInUse;
  },
  /**
   * @description Generate a parameter object for creating a device in a transaction method
   * @param {Objects} deviceConfig
   * @returns {Object}
   */
  generateAddDeviceParameters: function(deviceConfig) {
    const parameters = { };
    for (const param of Object.keys(deviceConfig)) {
        parameters[param] = {
                S: deviceConfig[param].toString()
            }
    }
    return parameters
  },
   /**
   * @description Increment the totalDeviceCount by 1 for allocation a new deviceId
   * @returns Number
   */
  generateDeviceId: function(totalDeviceCount) {
    if (!totalDeviceCount) {
        totalDeviceCount = { value : 0}
    }
    totalDeviceCount = Number(totalDeviceCount.value)+1;
    return totalDeviceCount;
  },
  /**
   * @description Get the total devices under _em or _mainMeter based on deviceType
   * @param {Object} _emKeystoreData
   * @param {Number} _mainMeterKeystoreData
   * @param {Object} deviceConfig
   * @returns {Object}
   */
  dynamoKeystoreKey: function (_emKeystoreData, _mainMeterKeystoreData, deviceConfig) {
    const {deviceId, isMainMeter, siteId, isEnergyMeter} = deviceConfig;
    const keystoreObject = {
      em: null,
      mainMeter: null,
    }
    if (!isEnergyMeter) {
      return keystoreObject;
    }
    if (_emKeystoreData && _emKeystoreData.list) {
        const list = typeof _emKeystoreData.list === 'Array' ? _emKeystoreData.list : _emKeystoreData.list.values;
        list.push(deviceId)
        keystoreObject.em= {
          key: _emKeystoreData.key,
          list: list
        }
    } else if (_emKeystoreData && !_emKeystoreData.list) {
      // list column not present then create a list column
        keystoreObject.em = {
          key: _emKeystoreData.key,
          list: [deviceId]
        };
    } else {
      // No key present means no record then create a new record
      keystoreObject.em = {
            key: `${siteId}_em`,
            list: [deviceId]
        }
    }
    if (isMainMeter) {
      // Value  column present
      if (_mainMeterKeystoreData && _mainMeterKeystoreData.value) {
        _mainMeterKeystoreData.value = _mainMeterKeystoreData.value.concat(','+deviceId);
      } else if (_mainMeterKeystoreData && !_mainMeterKeystoreData.value){
        // Value column not present then create a value column
        _mainMeterKeystoreData.value = deviceId;
      } else {
        // No key present then create a record
        _mainMeterKeystoreData = {
          key: `${siteId}_mainMeter`,
          value: deviceId
        }
      }
      keystoreObject.mainMeter = {
        key: _mainMeterKeystoreData.key,
        value: _mainMeterKeystoreData.value,
      }
    }
    return keystoreObject;
  }

}
