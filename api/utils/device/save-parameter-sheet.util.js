
const globalHelper = require('../globalhelper'); 
const Joi = require("joi");
const schema = Joi.object({
  parameterName: Joi.string().min(1),
  displayName: Joi.string().min(1).required(),
  address: Joi.string().min(1).required(),
  assetId: Joi.string().min(1).required(),
  paramType: Joi.string().min(1).required(),
})
module.exports = {
  checkInput: function(rows, driverConfig, existingParametersInDb, deviceConfiguration){
    const { deviceId, siteId, maxAssetId } = deviceConfiguration;
    let acceptableParamTypes = driverConfig.parameters.map(parameterInfo => parameterInfo.abbr);
    let errors = [];

    // Checking for maxAssetId in device configuration
    let maxAssetIdExists;
    if(!maxAssetId) maxAssetIdExists = false;
    else if(isNaN(Number(maxAssetId))) maxAssetIdExists = false;
    let maxAssetIdInSheet = 0;

    // Creating map of deviceId_abbr_assetId to parameter configuration
    existingParameterMap = existingParametersInDb.reduce((existingParameterMap, parameter) => {
      const { deviceId_abbr } = parameter;
      existingParameterMap[deviceId_abbr] = parameter;
      return existingParameterMap;
    }, {});

    let parametersToBeCreated = [], parametersToBeDeleted = [];

    // Checking for input checks for each row
    let checkedRows = rows.map(row => {
      const { paramType, assetId, displayName, address, componentId } = row;
      const deviceId_abbr = `${deviceId}_${paramType}_${assetId}`;
      const relevantExistingConfig = existingParameterMap[deviceId_abbr];

      if(!acceptableParamTypes.includes(paramType)){
        errors.push(`Incorrect paramType for AssetId: ${assetId}`);
        return null;
      }
      if(displayName || address || componentId ){

        if(componentId && (!displayName && !address)){
          errors.push(`Can not delete parameter with AssetId: ${assetId} as component '${componentId}' requires it. Please delete it first.`);
          return null;
        }

        const result = schema.validate(row, {allowUnknown: true});
        const { error } = result;
        if( error ){
          errors.push(error.message.concat(` for AssetId: ${assetId}`));
          return null;
        }
        
        if(!relevantExistingConfig) 
          parametersToBeCreated.push(row); // Parameter does not exist in DB but display name and address has been entered. 
        else if(relevantExistingConfig.displayName != displayName || relevantExistingConfig.address != address) 
          parametersToBeCreated.push(row); // Either the display name or the address is different. Parameter needs to be updated. 
        return row;
      } else if (relevantExistingConfig){
        // Parameter exists in DB, but display name and address have been removed from sheet. Needs to be deleted.
        parametersToBeDeleted.push({
          siteId,
          deviceId_abbr
        });
      }
    }).filter(Boolean);

    // Finding maxAssetId in Sheet
    checkedRows.forEach(row => {
      const { assetId } = row;
      if (assetId > maxAssetIdInSheet) maxAssetIdInSheet = assetId;
    });
    let newMaxAssetId;
    if (!maxAssetIdExists) newMaxAssetId = maxAssetIdInSheet;

    if (errors.length === 0) 
    return { 
      "status": true,
      checkedRows,
      newMaxAssetId,
      parametersToBeCreated,
      parametersToBeDeleted
    }
    else return {
      "status": false,
      errors,
      checkedRows,
      parametersToBeCreated,
      parametersToBeDeleted
    }
  },
};