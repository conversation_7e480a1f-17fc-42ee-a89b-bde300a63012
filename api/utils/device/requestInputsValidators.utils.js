const flaverr = require('flaverr');
const Joi = require('joi');
module.exports = {
  validateAssetInputData:function(params) {
    const schema = Joi.object().keys({
      siteId: Joi.string().required(),
      driverType: Joi.number(),
      deviceType: Joi.string(),
      assetType: Joi.string().valid('device', 'component').required(),
    }).unknown(true);
    const {
      value,
      error
    } = schema.validate(params);
    if (error) {
      throw flaverr('INPUT_VALIDATION_ERROR', new Error(error.message));
    } else {
      return value;
    }
  },
  validateAssetInputParameter: function(params) {
    const schema = Joi.object().keys({
      siteId: Joi.string().required(),
      assetId: Joi.string().required(),
      assetType: Joi.string().valid('device', 'component').required(),
      paramType: Joi.string().valid('all')
    }).unknown(true);
    const {
      value,
      error
    } = schema.validate(params);
    if (error) {
      throw flaverr('INPUT_VALIDATION_ERROR', new Error(error.message));
    } else {
      return value;
    }
  }
}
