const flaverr = require('flaverr');
const Joi = require('joi');
module.exports = {
  /**
   * @description Validate requested input parameters
   * @param {Object} params 
   * @returns {Object} input parameters
   */
  deleteController: function(params) {
    const schema = Joi.object({
      siteId: Joi.string().required(),
      controllerId: Joi.string().required(),
      industryType: Joi.string(),
    }).unknown(true)

    // Validate the data against the schema
    const { error } = schema.validate(params);

    // Check for errors
    if (error) {
      throw flaverr('E_INPUT_VALIDATION', new Error(error.message));
    }
    return;
  }
}