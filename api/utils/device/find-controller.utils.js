const globalHelpers = require('../globalhelper');
const utils = require('./utils');


module.exports = {
  /**
   * For given list of components, find information about feeback port
   * which is the port used by controller to check feedback of a
   * given command.
   * @param {object} components <PERSON><PERSON><PERSON>'s component like ahu,chiller etc
   * @return {object} {'deviceId_abbr': {config}},
   * @example {'27XX_stop': {min, max, expression, etc}},
   */
  getControllersFeedbackPortConfig: function (components) {
    return components.reduce((acc, component, index) => {
      let controls = component.controls;
      if (!controls) { return acc; }

      controls.forEach(control => {
        let { deviceId, key } = control;
        acc[`${deviceId}_${key}`] = utils.getPortConfigrationOfController(control);
      });

      return acc;
    }, {});
  },
  filterNonLinkedController: function(devices, controllerId){
    return devices.filter(device => {
      return (device.controllerId === controllerId || device.deviceId === controllerId);
    });
  }
};
