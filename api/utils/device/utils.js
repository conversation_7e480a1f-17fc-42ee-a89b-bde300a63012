
const validControllers = ['joulestat', 'jouleiocontrol', 'joulemomo', 'joulelogger', 'joulebox'];
const globalHelpers = require('../globalhelper');
const controllerNameMapping = {
  'jouleiocontrol': 'Joule<PERSON>Control-',
  'joulelogger': '<PERSON><PERSON><PERSON>ogger-',
  'joulestat': '<PERSON><PERSON><PERSON>tat-',
  'joulemomo': '<PERSON><PERSON><PERSON>omo-',
  'joulebox': '<PERSON><PERSON><PERSON>ox-',
};
// Function type for a deviceType.
// Both = A device can both read data from controller and write command to it (VFD)
// write = A device can only send commands and not read
const TYPE_TO_CATEGOTY_MAP = {
  'AVO': 'write',
  'DR': 'write',
  'VFD': 'both',
  'CHL': 'both',
  'HPC': 'both',
};

const COMMUNICATION_CATEGORIES = ['BTU', 'EM', 'RTD', 'DF', 'ACI', 'VFD',
  'CHL', 'DS', 'TDS', 'AVO', 'AVI', 'HPC', 'I2C', 'DR', 'FM', 'SN' ];

const DRIVER_CLASSES = [
  'components',
  'devices'
];

const COMMUNICATION_TYPES = ['MB', 'NMB'];

module.exports = {

  /**
   * Parse control parameter of component (component['control']) and return
   * required fields for that control parameter
   * @param {object} componentControl Control parameter of a component
   */
  getPortConfigrationOfController: function (componentControl) {
    if (globalHelpers.isNullishArray([componentControl])) { throw 'Controls paramters cant be empty utils.device.utils.js.getPortConfig'; }
    let { min, max, unit, timeout, key, expression } = componentControl;
    expression = expression.replace(/\|\|/g, ' ');
    expression = expression.substring(1, expression.length - 1);
    expression = expression.replace(/@/g, '.');

    let feedbackDevices = this.parseFormulaToIds(expression);
    let portConfig = {
      min,
      max,
      unit,
      timeout,
      key,
      deviceId: feedbackDevices,
      expression
    };
    return portConfig;
  },
  // parse Device formula to find deviceIds used in it
  parseFormulaToIds: function (formulaText) {
    let x = formulaText.split('||');
    let devId = new Set();
    for (let i = 1; i < x.length - 1; i++) {
      let expr = x[i];
      let dvParm = expr.split('@');
      if (dvParm.length === 2 && dvParm[0] !== 'COMMAND') {
        devId.add(dvParm[0]);
      }
    }
    return [...devId];
  },
  // Get Execution category(Read/write or both) according to communication category
  getDeviceExecutionType: function (communicationCategory) {

    let executionType = TYPE_TO_CATEGOTY_MAP[communicationCategory];
    if (executionType) {
      return executionType;
    } else {
      return 'read';
    }
  },
  // Is given deviceType a valid SJPL controller
  isValidControllerType: function (deviceType) {
    if (typeof (deviceType) !== 'string') {
      return false;
    }
    return (validControllers.indexOf(deviceType) !== -1 ? true : false);
  },
  // get count of SJPL controllers from a devices list
  getSJPLControllerCount: function (devicesList) {
    if (!Array.isArray(devicesList)) {
      sails.log.error('[-]device/utils/getSJPLControllerCount expected deviceList to be array');
      return;
    }
    let controllerCountMap = validControllers.reduce((acc, controller) => {
      acc[controller] = 0;
      return acc;
    }, {});

    devicesList.map(device => {
      let deviceType = device.deviceType;

      if (this.isValidControllerType(deviceType)) {
        let controllerCount = parseInt(device.name.split('-')[1]);
        controllerCountMap[deviceType] = Math.max(controllerCount, controllerCountMap[deviceType]);
      }
    });

    return controllerCountMap;

  },
  /*
  * Validate if the device have a valid regionId, areaId and networkId
  */
  isValidSiteInfo: function (site, device) {
    if (
      !site.areas[device.areaId] ||
      !site.regions[device.regionId] ||
      !site.networks[device.networkId]
    ) {
      return false;
    } else {
      return true;
    }
  },
  // get a custom name for SJPL controller
  getControllerNameFromCustomNames: function (controllerCountMap, deviceType) {
    let newName = controllerNameMapping[deviceType] + String(controllerCountMap[deviceType]);
    return newName;
  },
  getAvailableSlaveIds: function (devices) {
    let slaveArr = new Array(255).fill(false); // Modbus slaves addresses can only be from 1-255
    let availableIndexes = [];

    devices.forEach(device => {
      if (
        device.communicationType === 'MB'
      ) {
        slaveArr[device.slaveId] = true;
      }
    });
    for (let i = 1; i < 255; i++) { // limit available index to 255, cant have more than 255 modbus
      if (!slaveArr[i]) {
        availableIndexes.push(i.toString());
      }
    }
    return availableIndexes;
  },
  /*
  *  Add required field like mode, deviceId etc to all the parameters of a deviceType
  */
  addDeviceParameters: function (deviceId, driver, siteId) {

    let { parameters, deviceType, driverType } = driver;

    parameters = globalHelpers.toArray(parameters);
    if (!Array.isArray(parameters)) {
      sails.log.error('[-]createDevice.utils.js.addDeviceParameters Error, Invalid argument passed parameters, expected Array');
      return;
    }
    let paramList = parameters.map(_parameter => {
      let parameter = { ...globalHelpers.toJson(_parameter) };
      let { filter, abbr, type, properties } = parameter;
      filter = globalHelpers.toJson(filter);
      properties = globalHelpers.toJson(properties);
      parameter['siteId'] = siteId;
      parameter['mode'] = 'jt';
      parameter['utilityType'] = type;
      parameter['deviceId_abbr'] = `${deviceId}_${abbr}`;
      parameter['inheritedFrom'] = `${deviceType}@${driverType}`;
      parameter['deviceId'] = deviceId;

      if (typeof filter !== 'undefined') {
        Object.keys(filter).forEach((key) => {
          parameter[`filter_${key}`] = filter[key];
        });
      }
      if (typeof properties !== 'undefined') {
        Object.keys(properties).forEach((key) => {
          parameter[key] = properties[key];
        });
      }

      delete parameter.type;
      delete parameter.filter;
      delete parameter.properties;

      return parameter;
    });
    return paramList;
  },
  isValidDriverClass: function (driverClass) {
    if (typeof driverClass !== 'string') {
      return false;
    }
    if (DRIVER_CLASSES.indexOf(driverClass) !== -1) {
      return true;
    } else {
      return false;
    }
  },
  isValidCommunicationCategory: function(communicationCategory){
    if (typeof communicationCategory !== 'string') {
      return false;
    }
    if (COMMUNICATION_CATEGORIES.indexOf(communicationCategory) !== -1) {
      return true;
    } else {
      return false;
    }
  },
  isValidCommunicationType: function(communicationType){
    if (typeof communicationType !== 'string') {
      return false;
    }
    if (COMMUNICATION_TYPES.indexOf(communicationType) !== -1) {
      return true;
    } else {
      return false;
    }
  },
  getDeviceList: function (deviceData) {
    return deviceData.map((d) => ({
      abbr: d.key,
      name: d.name
    }));
  },
  removeControllerFromSiteRegion: function (siteObj, regionId, controllerId) {
    let { regions } = siteObj;
    regions = globalHelpers.toJson(regions);
    let controllers = new Set(regions[regionId].controller);
    controllers.delete(controllerId);
    regions[regionId].controller = [...controllers];
    return siteObj;
  },
  getDeviceCategoryCacheKey: (siteId) => {
    return `siteId:${siteId}:available_device_component_categories`;
  }
}