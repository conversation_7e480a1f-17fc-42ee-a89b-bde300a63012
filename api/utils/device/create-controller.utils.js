const globalHelpers = require('../globalhelper');
const utils = require('./utils');

module.exports = {
  validKeys: function (devicesConfig) {

    if(devicesConfig.length === 0){
      return false;
    }

    for(let i=0; i<devicesConfig.length; i++){
      let deviceConfig = devicesConfig[i];
      if (
        globalHelpers.isNullishArray([
          deviceConfig,
          deviceConfig.networkId,
          deviceConfig.regionId,
          deviceConfig.softwareVer,
          deviceConfig.hardwareVer,
          deviceConfig.vendorId,
          deviceConfig.operationMode,
          deviceConfig.deviceType,
          deviceConfig.areaId,
          deviceConfig.siteId,
          deviceConfig.name
        ])
      ) {
        sails.log.warn('[-]400, Invalid params')
        return false;
      } else {
        return true;
      }
    }

  },
  isValidVendor: function (deviceInfo) {

    let { deviceType, vendorId, name } = deviceInfo;

    if (
      deviceType !== 'joulesense' &&
      !utils.isValidControllerType(deviceType) &&
      vendorId === 'smartjoules'
    ) {
      return false;
    }

    if (
      (deviceType === 'joulesense' || vendorId !== 'smartjoules') &&
      !name
    ) {
      return false;
    }

    return true;

  },

};
