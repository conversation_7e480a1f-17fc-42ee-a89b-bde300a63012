
const globalHelper = require('../globalhelper');

module.exports = {
  checkInput: function (body) {
    let errors = globalHelper.checkObjectKeys(body, [
      'class',
      'communicationCategory',
      'communicationType',
      'deviceType',
      'driverName',
      'driverType',
      'parameters',
    ], 'driver');
    console.log(errors);
    if (errors.length === 0) return true;
    else throw new Error(errors);
  },
  isValidDeviceParameter: function (parameter) {
    parameter = globalHelper.toJson(parameter);
    if (!parameter || parameter.constructor.name !== 'Object') {
      return false;
    }
    let {
      abbr,
      displayName,
      unit,
      address,
      properties,
      filter,
      statePreference,
      errOffset,
      rawUnit,
      operation,
      dau,
      paramGroup
    } = parameter;
    let _type = parameter['type'];

    if (!abbr || !displayName) {
      return false;
    }
    if (_type !== 'data' && _type !== 'command') {
      return false;
    }

    if (address !== 'null' && isNaN(parseInt(address))) {
      return false;
    }
    if (!filter.oldVal && filter.oldVal !== 'False') {
      return false;
    }
    if (!filter.existence &&
      (filter.existence !== 'False' || filter.existence !== 'True')) {
      return false;
    }
    if(filter.variance !== 0){
      return false;
    }
    if(isNaN(parseInt(errOffset))){
      return false;
    }
    // dau -> list should be taken from within
    // rawUnit -> use pint python library for getting availble raw units
    // paramGroup
    // #TODONEXT
    return true;






  }
};
