const globalHelpers = require('../globalhelper');
const utils = require('./utils');

module.exports = {
  isValidFrontEndRequest: function (device) {
    if (
      globalHelpers.isNullishArray([
        device,
        device.deviceId
      ])
    ) {
      return false;
    } else {
      return true;
    }
  },
  // Name change of a controller is only possible if its of type joulesense
  isValidDeviceChangeNameCondition: function (deviceType, newDeviceName) {
    if (deviceType === 'joulesense' && newDeviceName) {
      return false;
    } else {
      return true;
    }
  }

};
