
const globalHelper = require('../globalhelper'); 
module.exports = {
  checkInput: function(body){
    let errors = globalHelper.checkObjectKeys(body, [], '');
    if (errors.length === 0) return { "status": true }
    else return {
      "status": false,
      errors
    }
  },
  filterSheetFields: function(deviceParameters, driverConfig, numberOfNewParams = 0, componentIdToNameMap, componentIdToLayerNameMap, maxAssetId){
    if(!maxAssetId) maxAssetId = 0;
    const paramTypeToDisplayNameMap = {};
    driverConfig.parameters.forEach(parameter => {
      paramTypeToDisplayNameMap[parameter.abbr] = parameter.displayName;
    });

    const existingFilteredFields = deviceParameters.map(parameter => {
      const { address, displayName, paramType, assetId, componentId } = parameter;
      return {
        address,
        displayName,
        parameterName: paramTypeToDisplayNameMap[paramType],
        assetId,
        paramType,
        componentId,
        componentName: componentIdToNameMap[componentId],
        layerName: componentIdToLayerNameMap[componentId],
      }
    })

    const newTemplateFields = [];
    for(let i=0; i<numberOfNewParams; i++){
      maxAssetId++;
      driverConfig.parameters.forEach(parameter => {
        newTemplateFields.push({
          address: '',
          displayName: '',
          assetId: maxAssetId,
          parameterName: parameter.displayName,
          paramType: parameter.abbr,
          componentId: '',
          componentName: '',
        })
      });
    }
    const totalFilteredFields = [...existingFilteredFields, ...newTemplateFields];
    return totalFilteredFields;
  },
  sortRowsByAssetId: function(convertedRowObjects){
    convertedRowObjects.sort((firstObject, secondObject) => {
      let firstObjectAssetId = Number(firstObject["assetId"]);
      if(isNaN(firstObjectAssetId)) firstObjectAssetId = 0;
      let secondObjectAssetId = Number(secondObject["assetId"]);
      if(isNaN(secondObjectAssetId)) secondObjectAssetId = 0;
      if(firstObjectAssetId < secondObjectAssetId) return -1;
      if(secondObjectAssetId < firstObjectAssetId) return 1;
      return 0;
    })
    return convertedRowObjects;
  }
};