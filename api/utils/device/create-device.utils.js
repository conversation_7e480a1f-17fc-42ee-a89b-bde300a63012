const globalHelpers = require('../globalhelper');

module.exports = {

  validKeys: function (deviceConfig) {
    if (
      globalHelpers.isNullishArray([
        deviceConfig,
        deviceConfig.networkId,
        deviceConfig.regionId,
        deviceConfig.portNumber,
        deviceConfig.controllerId,
        deviceConfig.communicationType,
        deviceConfig.communicationCategory,
        deviceConfig.driverType,
        deviceConfig.deviceType,
        deviceConfig.areaId,
        deviceConfig.name,
        deviceConfig.siteId
      ])
    ) {
      return false;
    } else {
      return true;
    }
  },
  /**
   * check if a devicePOrt is available in an controller
   * @param {array} devices array of devices attached to controler
   * @param {string} portNumber new port number to add to an controller
   */
  isValidPortNumber: function (devices, portNumber) {
    let portInUse = false;

    for (let deviceIndex in devices) {
      let device = devices[deviceIndex];
      // for an MB communication type device, we need to check if there are slaves available or not
      // maximum 32 slaves are possible.

      if (device.communicationType === 'MB' ) {

        if (device.slaveId && device.slaveId == portNumber){
          return true;
        }
        if ( portNumber > 32 ){
          return true;
        }
      }
      if (device.portNumber && device.portNumber == portNumber) { // we do want loose check here
        portInUse = true;
        break;
      }
    }
    return portInUse;
  },
  getDevicesAttachedToController: function (devices, controllerId) {
    return devices.filter(device => {
      // in case when device is controller, it doent have controllerId field
      if (!device || !device.controllerId) return false;

      // device.controllerId can be like 2771,2774 when 1 device is attached to multiple
      // controller that why we had to do indexOf
      return device.controllerId.indexOf(controllerId) !== -1;
    });
  }


}
