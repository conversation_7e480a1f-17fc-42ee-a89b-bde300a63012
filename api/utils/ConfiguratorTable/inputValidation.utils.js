const flaverr = require("flaverr");
const Joi = require("joi");

const validateRequestParamsComponentClassTable = (params) => {
  const schema = Joi.object({
    deviceType: Joi.string().required(),
    rows: Joi.array().items(Joi.string()).required(),
    columns: Joi.array()
      .items(
        Joi.object({
          abbr: Joi.string().required(),
          type: Joi.string().valid("data", "controls", "config").required(),
        }),
      )
      .required(),
  });

  const { error } = schema.validate(params);
  if (error) {
    throw flaverr("E_INPUT_VALIDATION", new Error(error.message));
  }
  const { rows, columns } = params;
  validateTablePropValDuplicates(columns.map((item) => item.abbr));
  validateTablePropValDuplicates(rows);
};

const validateTableInputParams = (params) => {
  function atLeastOneNonEmpty(values, helpers, message) {
    if (_.isEmpty(values)) {
      return helpers.message(message);
    }
    return values;
  }

  const schema = Joi.object({
    pageId: Joi.number().required().messages({
      "any.required": 'Please enter "pageId" before saving',
    }),
    tableGroupId: Joi.number().required().messages({
      "any.required": 'Please enter "tableGroupId" before saving',
    }),
    tableProperty: Joi.object({
      name: Joi.string().allow("").required().messages({
        "any.required": 'Please enter valid "name" before saving',
      }),
      deviceType: Joi.string().required().messages({
        "any.required": 'Please enter "deviceType" before saving',
      }),
      class: Joi.string().valid("device", "component").required().messages({
        "any.required": 'Please enter "class" before saving',
        "any.only": 'Please enter "class" before saving. Allowed values: [device, component]',
      }),
      canTranspose: Joi.number().valid(0, 1).optional().messages({
        "any.only": 'Please enter "canTranspose" as either 0 or 1',
      }),
      table: Joi.object({
        rows: Joi.array()
          .items(Joi.string())
          .custom((value, helpers) =>
            atLeastOneNonEmpty(value, helpers, "Please provide at least one rows before saving."),
          )
          .required()
          .messages({
            "any.required": "Please provide row of a table",
          }),
        columns: Joi.array()
          .items(
            Joi.object({
              abbr: Joi.string().required().messages({
                "any.required": 'Please enter "abbr" for column before saving',
              }),
              type: Joi.string().valid("data", "controls", "config").required().messages({
                "any.required": 'Please enter "type" before saving',
                "any.only": 'Please enter "type" before saving. Allowed values: data, controls',
              }),
            }),
          )
          .custom((value, helpers) =>
            atLeastOneNonEmpty(value, helpers, "Please provide at least one column before saving."),
          )
          .required()
          .messages({
            "any.required": 'Please enter "column" before saving',
          }),
      })
        .required()
        .messages({
          "any.required": 'Please enter "rows" and "column" before saving',
        }),
    })
      .required()
      .messages({
        "any.required": 'Please enter "tableProperty" before saving',
      }),
  });

  const { error } = schema.validate(params);
  if (error) {
    throw flaverr("E_INPUT_VALIDATION", new Error(error.message));
  }
};

const validateTableWithPageParams = (params) => {
  const schema = Joi.object({
    pageId: Joi.number(),
    tableGroupId: Joi.number(),
  });

  const { error } = schema.validate(params);
  if (error) {
    throw flaverr("E_INPUT_VALIDATION", new Error(error.message));
  }
};

const validateUpdateTableInputParams = (params) => {
  const schema = Joi.object({
    pageId: Joi.number(),
    tableGroupId: Joi.number(),
    tableId: Joi.number(),
    tableProperty: Joi.object({
      deviceType: Joi.string().required(),
      driverType: Joi.string().required(),
      deviceClass: Joi.string().valid("device", "component").required(),
      table: Joi.object({
        rows: Joi.array().items(Joi.string()).min(1).required(),
        columns: Joi.array()
          .items(
            Joi.object({
              abbr: Joi.string().required(),
              type: Joi.string().valid("data", "controls").required(),
            }),
          )
          .min(1)
          .required(),
      }).required(),
    }).required(),
  });
};

const validateTableDeleteRequest = (params) => {
  const schema = Joi.object().keys({
    pageId: Joi.string()
      .regex(/^[1-9]\d*$/)
      .required(),
    tgId: Joi.string()
      .regex(/^[1-9]\d*$/)
      .required(),
    tableId: Joi.string()
      .regex(/^[1-9]\d*$/)
      .required(),
  });

  const { error } = schema.validate(params);
  if (error) {
    throw flaverr("E_INPUT_VALIDATION", new Error(error.message));
  }
};

const fetchPageTableGroups = (params) => {
  const schema = Joi.object().keys({
    pageId: Joi.number().required(),
  });

  const { error } = schema.validate(params);
  if (error) {
    throw flaverr("E_INPUT_VALIDATION", new Error(error.message));
  }
};

const validateAutoCreateTableRequest = (params) => {
  const schema = Joi.object().keys({
    siteId: Joi.string().required(),
    pageId: Joi.string()
      .regex(/^[1-9]\d*$/)
      .required(),
  });
  const { error } = schema.validate(params);
  if (error) {
    throw flaverr("E_INPUT_VALIDATION", new Error(error.message));
  }
};

const validateTablePropValDuplicates = (tablePropertyValues) => {
  const uniqueValues = new Set();
  const repeatedValues = new Set();

  for (const value of tablePropertyValues) {
    if (uniqueValues.has(value)) {
      repeatedValues.add(value);
    } else {
      uniqueValues.add(value);
    }
  }
  if (repeatedValues.size) {
    throw flaverr(
      "E_INPUT_VALIDATION",
      new Error(
        `${Array.from(repeatedValues).join(
          ", ",
        )} should be unique. Please remove duplicates to proceed.`,
      ),
    );
  }
  return;
};

module.exports = {
  validateRequestParamsDeviceClassTable: function (params) {
    const schema = Joi.object({
      deviceType: Joi.string().required(),
      rows: Joi.array().items(Joi.string()).required(),
      columns: Joi.array()
        .items(
          Joi.object({
            abbr: Joi.string().required(),
            type: Joi.string().valid("data").required(),
          }),
        )
        .required(),
    });

    const { error } = schema.validate(params);
    if (error) {
      throw flaverr("E_INPUT_VALIDATION", new Error(error.message));
    }

    const { rows, columns } = params;
    validateTablePropValDuplicates(rows);
    validateTablePropValDuplicates(columns.map((item) => item.abbr));
  },
  validateRequestParamsComponentClassTable,
  validateTableInputParams,
  validateUpdateTableInputParams,
  validateTableDeleteRequest,
  validateTableWithPageParams,
  fetchPageTableGroups,
  validateAutoCreateTableRequest,
  validateTablePropValDuplicates,
};
