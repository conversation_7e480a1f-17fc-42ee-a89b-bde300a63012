const buildTableResponseSchema = (table, rows, columns) => {
  const response = {
    tableId: Number(table.id),
    name: table.name,
    deviceType: table.deviceType,
    isPublished: Number(table.isPublished),
    deviceClass: table.deviceClass,
    canTranspose: Number(table.canTranspose),
    table: {
      rows: rows.map((row) => ({ id: row.id, deviceId: row.deviceId })),
      columns: columns.map((column) => ({
        id: column.id,
        abbr: column.abbrName,
        type: column.type,
      })),
    },
  };
  return response;
};

const logChangeTableProperties = (oldTableProperties, newTableProperties) => {
  const { rows: oldRows, column: oldColumns } = oldTableProperties;
  const { rows, columns } = newTableProperties;

  // Calculate deleted and created rows
  const deletedRows = oldRows.filter(
    (oldRow) => !rows.some((newRowDeviceId) => newRowDeviceId === oldRow.deviceId)
  );
  const createdRows = rows.filter(
    (newRowDeviceId) => !oldRows.some((oldRow) => oldRow.deviceId === newRowDeviceId)
  );

  // Calculate deleted and created columns
  const deletedColumns = oldColumns.filter(
    (oldColumn) => !columns.some((newColumn) => newColumn.abbr === oldColumn.abbrName)
  );
  const createdColumns = columns.filter(
    (newColumn) => !oldColumns.some((oldColumn) => oldColumn.abbrName === newColumn.abbr)
  );

  return {
    deletedRows,
    createdRows,
    deletedColumns,
    createdColumns,
  };
};

module.exports = {
  buildTableResponseSchema,
  logChangeTableProperties,
};
