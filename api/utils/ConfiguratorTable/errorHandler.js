const flaverr = require("flaverr")

module.exports = {
  throwExceptionInvalidTableAccordianGroup: (accordianPageId) => {
    const errCode = 'E_INVALID_TABLE_ACCORDIAN_GROUP';
    const errMessage = 'Table accordian group does not exist';
    throw flaverr(errCode, new Error(errMessage))
  },
  throwExceptionInvalidDriver: (deviceType, driverType) => {
    const errCode = 'E_INVALID_DRIVER_TYPE';
    const errMessage = `Invalid driver type of deviceType:${deviceType} and driver: ${driverType} `;
    throw flaverr(errCode, new Error(errMessage))
  }, 
  /**
   * 
   * @param {Array} invalidParameter 
   */
  throwExceptionInvalidTableColParam: (invalidParameter) => {
    const errCode = 'E_INVALID_PARAMETER';
    const errMessage = `Invalid table column parameter: [${invalidParameter.join(',')}]`;
    throw flaverr(errCode, new Error(errMessage))
  },
  throwExceptionInvalidDeviceClass: () => {
    const errCode = 'E_INVALID_DEVICE_CLASS';
    const errMessage = `Invalid device class for table`;
    throw flaverr(errCode, new Error(errMessage))
  },
  throwExceptionInvalidDriverConfig: () => {
    const errCode = 'E_DRIVER_CONFIG_NOT_FOUND';
    const errMessage = `Invalid driver config for table`;
    throw flaverr(errCode, new Error(errMessage))
  },
  throwExceptionInvalidTableRows: (invalidRows)=> {
    const errCode = 'E_INVALID_TABLE_ROWS';
    const errMessage = `Invalid table rows:- ${invalidRows.join(',')}`;
    throw flaverr(errCode, new Error(errMessage))
  },
  throwExceptionInvalidTableColumns: (cols) => {
    const errCode = 'E_INVALID_TABLE_COLUMNS';
    const errMessage = `Invalid table column: ${cols.join(',')}`;
    throw flaverr(errCode, new Error(errMessage))
  },
  throwExceptionInvalidTableGroupID: (tableGroupId) => {
    const errCode = 'E_INVALID_TABLE_GROUP';
    const errMessage = `Invalid table group of id: ${tableGroupId}`;
    throw flaverr(errCode, new Error(errMessage))
  },
  throwExceptionInvalidPage: (pageId) => {
    const errCode = 'E_INVALID_PAGE';
    const errMessage = `Page does not exist for id: ${pageId}`;
    throw flaverr(errCode, new Error(errMessage))
  },
  throwExceptionInvalidPageSiteId: (pageId) => {
    const errCode = 'E_INVALID_PAGE_SITE_ID';
    const errMessage = `This page does not belong to any site id: ${pageId}.`;
    throw flaverr(errCode, new Error(errMessage))
  },
  throwExceptionInvalidTable: (table, tableGroupId) => {
    const errCode = 'E_INVALID_TABLE';
    const errMessage = `Invalid table for ${table} and tableGroupId ${tableGroupId}`;
    throw flaverr(errCode, new Error(errMessage))
  },
  throwExceptionTableNotExist: (tableId) => {
    const errCode = 'E_TABLE_NOT_EXIST';
    const errMessage = `Configurator table for ${tableId} does not exist`;
    throw flaverr(errCode, new Error(errMessage))
  },
  throwExceptionInvalidTableGroupId: (pageId) => {
    const errCode = 'E_INPUT_VALIDATION';
    const errMessage = `The requested page ID ${pageId} is not a valid page ID`;
    throw flaverr(errCode, new Error(errMessage))
  }

}