const moment = require("moment-timezone");
moment.tz.add(
  "Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6"
);
moment.tz.setDefault("Asia/Kolkata");

const globalHelper = require("../globalhelper");
module.exports = {
    /**
 * @description this function will convert timeSlot to start time and end time in RFC 339 format.
 * @param {string} timeSlot | Allowed value is 'today', 'lastday', 'lastweek', 'lastmonth', 'thisweek'
 * @returns {object} | {startTimestamp , endTimestamp}
 */
parseTimeSlot:(timeSlot)=>{
    if(!timeSlot) throw new Error('TIMESLOT_PARAMETER_REQUIRED');
    const ALLOWED_TIMESLOT = new Set(['today', 'lastday', 'lastweek', 'lastmonth', 'thisweek']);
    if(!ALLOWED_TIMESLOT.has(timeSlot.toLowerCase())) throw new Error('INVALID_TIMESLOT');

    const _timeSlot = timeSlot.toLowerCase();
    let _startTimestampUTC;
    let _endTimeStampUTC = moment().format('YYYY-MM-DDTHH:mm:ssZ');
    if (_timeSlot === 'today') {
      _startTimestampUTC = moment().startOf('day').format('YYYY-MM-DDT00:00:00Z');
    } else if (_timeSlot === 'lastday') {
      _startTimestampUTC = moment().subtract(1, 'day').startOf('day').format('YYYY-MM-DDTHH:mm:ssZ');
    } else if (_timeSlot === 'lastweek') {
      _startTimestampUTC = moment().subtract(1, 'week').startOf('day').format('YYYY-MM-DDTHH:mm:ssZ');
    } else if (_timeSlot === 'lastmonth') {
      _startTimestampUTC = moment().subtract(1, 'months').startOf('day').format('YYYY-MM-DDTHH:mm:ssZ');
    }
    return {
        startTimestamp : _startTimestampUTC,
        endTimestamp : _endTimeStampUTC
    }
},
/**
 * @description generating response object for tabular api of site leaderboard
 * @param { object } sites | key value pair fo siteId and site name
 * @param {array} rawData | tabular data array
 * @returns { array }
 */
responseObjectTabularData:(sites,rawData)=>{
  function row(){
    return {
      siteId:'',
      automation:null,
      chiller:null,
      chillerPlant:null,
      chwPump:null,
      condPump:null,
      condenserApproach:null,
      coolingTower:null,
      coolingTowerApproach:null,
      evapproach:null,
      tonnageDelivered:null,
      siteName:''
    }
  }
  const siteWiseLeaderboard = Object.keys(sites).reduce((acm,curr)=>{
    acm[curr]=row();
    acm[curr].siteId = curr;
    acm[curr].siteName =  sites[curr];
    return acm;
  },{})

  rawData.forEach(it=>{
    const { parameter, data } = it
    data.forEach(record=>{
      const { siteId, value } = record
      if(siteWiseLeaderboard.hasOwnProperty(siteId) && siteWiseLeaderboard[siteId].hasOwnProperty(parameter)){
        siteWiseLeaderboard[siteId][parameter] = value;
      }
    })
  })
  return Object.values(siteWiseLeaderboard).sort((a,b)=>{
    return a.siteName > b.siteName ? 1 : -1
  });
}
}