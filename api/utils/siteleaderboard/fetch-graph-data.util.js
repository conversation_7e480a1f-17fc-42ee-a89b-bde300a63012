const moment = require("moment-timezone");
moment.tz.add(
  "Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6"
);
moment.tz.setDefault("Asia/Kolkata");

const globalHelper = require("../globalhelper");
module.exports = {
  parseGroupByFlag: (groupBy) => {
    const allowedGroupBy = new Map();
    allowedGroupBy.set("hour", "h");
    allowedGroupBy.set("day", "d");
    return allowedGroupBy.get(groupBy);
  },
  timestampParser: (startTime, endTime, groupBy) => {
    let _startTimestampUTC = moment(parseInt(startTime)).format(
      "YYYY-MM-DDTHH:00:00Z"
    );
    let _endTimestampUTC = moment(parseInt(endTime)).format(
      "YYYY-MM-DDTHH:00:00Z"
    );
    if (groupBy == "day") {
      _startTimestampUTC = moment(parseInt(startTime)).format(
        "YYYY-MM-DDT00:00:00Z"
      );
      _endTimestampUTC = moment(parseInt(endTime)).format(
        "YYYY-MM-DDT00:00:00Z"
      );
    }
    return {
        startTime:_startTimestampUTC,
        endTime:_endTimestampUTC
    }
  },
  /**
 * @description generating response object for graphical api of site leaderboard
 * @param { object } sites | key value pair fo siteId and site name
 * @param { array } parameters | list of parameter e.g chwPump, tonnageDelivered etc.
 * @param {array} rawData | graphic data array
 * @returns { array }
 */
 responseObjectGraphicalData:(sites,parameters,rawData)=>{
    let siteParamNode = sites.reduce((acm,curr)=>{
      acm[curr]={ }
      parameters.forEach(param=>{
        acm[curr][param] = []
      })
      return acm;
    },{})
  
    rawData.forEach(record=>{
      let { parameter, data } = record
      data.forEach(it=>{
        let { _time, siteId, _value } = it;
        _value = isNaN(_value) ? _value : Number.parseFloat(_value.toFixed(2))
        siteParamNode[siteId][parameter].push([moment(_time).unix()*1000, _value])
      })
    })
    const responseObject = [];
    for(let siteId in siteParamNode){
      let dataPoint = {
        [siteId]:[]
      }
  
      for(let param in siteParamNode[siteId]){
        dataPoint[siteId].push({
          param:param,
          data:siteParamNode[siteId][param]
        })
  
      }
      responseObject.push(dataPoint);
    }
    return responseObject;
  }
};
