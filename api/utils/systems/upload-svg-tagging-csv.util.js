module.exports = {
  validateSVGTaggingWithCSV: function (xs, ys) {
    return xs.size === ys.size && [...xs].every((x) => ys.has(x));
  },
  base64ToSvgTaggingSet: function (base64String) {
    try {
      let data = Buffer.from(base64String, "base64").toString();
      let svgTaggedIds = JSON.parse(data);
      if (!Array.isArray(svgTaggedIds)) return null;
      return new Set(svgTaggedIds);
    } catch (err) {
      return null;
    }
  },
  isValidCSV: function (csvJson) {
    return csvJson.every((it) => {
      return (
        it.hasOwnProperty("device_id") &&
        it.hasOwnProperty("svg_loc_id") &&
        it.hasOwnProperty("type")
      );
    });
  },
};
