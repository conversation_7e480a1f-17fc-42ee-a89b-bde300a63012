
const globalHelper = require('../globalhelper');

module.exports = {
  /**
   * @description generates map objects to populate the hierarchy data.
   * @param {array} systemNodes Array of system_nodes of the site
   * @returns {object} { systemNodeIdMap, systemNodeChildrenMap, systemRootNode }
   * @returns {object} systemNodeIdMap: map of nodeId to its object.
   * @returns {object} systemNodeChildrenMap: map of parentIds to an array of it's children's nodeIds
   * @returns {object} systemRootNode Map of system to it's root node id.
   */
  generateSystemNodesMapObjects: function(systemNodes){
    let systemNodeIdMap = {}, systemNodeChildrenMap = {}, systemRootNode = {};
    systemNodes.forEach(systemInfo => {
      let {parent_id: parentId, id: nodeId, system_id:systemId} = systemInfo;
      systemNodeIdMap[nodeId] = systemInfo; // Map of system_node_id to it's object.

      // Mapping the root nodes of every system.
      if (parentId == 0){
        // The hierarchy can contain only one child. Schema can support but U<PERSON> is not be able to. Can uncomment the following lines to enable it.
        // if(!systemRootNode[systemId]) systemRootNode[systemId] = [nodeId];
        // else systemRootNode[systemId].push(nodeId);
        systemRootNode[systemId] = nodeId;
      }

      // Mapping parentIds to their children.
      if(!systemNodeChildrenMap[parentId]) systemNodeChildrenMap[parentId] = [nodeId];
      else systemNodeChildrenMap[parentId].push(nodeId);
    });
    return { systemNodeIdMap, systemNodeChildrenMap, systemRootNode };
  },
  /**
   * @description generates map objects to populate the layer data.
   * @param {array} systemNodes Array of system_nodes of the site
   * @returns {object} { nodeIdConfigMap, nodeConfigChildrenMap, rootNodeConfigMap }
   * @returns {object} nodeIdConfigMap: map of nodeId to its object.
   * @returns {object} nodeConfigChildrenMap: map of parentIds to an array of it's children's nodeIds
   * @returns {object} rootNodeConfigMap Map of system to an array of its root node ids.
   */
  generateNodesMapsObjects: function(configuredNodes){
    let nodeIdConfigMap = {}, nodeConfigChildrenMap = {}, rootNodeConfigMap = {};
    configuredNodes.forEach(nodeInfo => {
      let { id: nodeId, parent_id:parentId, system_id: systemId,misc } = nodeInfo;
      nodeIdConfigMap[nodeId] = nodeInfo; // Mapping nodeId to it's object.

      // Mapping array of root nodes for every system.
      if (parentId == 0){
        if (!rootNodeConfigMap[systemId]) rootNodeConfigMap[systemId] = [nodeId]
        else rootNodeConfigMap[systemId].push(nodeId);
      }

      // Mapping parentIds to their children.
      if (!nodeConfigChildrenMap[parentId]) nodeConfigChildrenMap[parentId] = [nodeId];
      else nodeConfigChildrenMap[parentId].push(nodeId);
    });
    return { nodeIdConfigMap, nodeConfigChildrenMap, rootNodeConfigMap };
  },
  /**
   * @description Recursive function that generates hierarchy and layer data.
   * @param {string} nodeId
   * @param {object} systemNodeChildrenMap Map object generated using functions above
   * @param {object} systemNodeIdMap Map object generated using functions above
   * @param {boolean} isSystemLayer true if function is running for system_nodes
   * @returns {object} Generates hierarchy and layer data required by FE.
   */
  generateHierarchyLayerData: function(nodeId, systemNodeChildrenMap, systemNodeIdMap, isSystemLayer){
    let systemNodeInfo = systemNodeIdMap[nodeId];
    let { id: layerId, layer_type: layerType, device_id: deviceId,misc } = systemNodeInfo, layerName, levelType;
    if (isSystemLayer) layerName = systemNodeInfo.layer_name;
    else {
      layerName = systemNodeInfo.name;
      levelType = systemNodeInfo.level_type;
      if (!levelType) levelType = "layer";
    }
    let child;

    if(isSystemLayer == true){
      let childNodeId = systemNodeChildrenMap[nodeId];
      if (childNodeId) child = this.generateHierarchyLayerData(childNodeId, systemNodeChildrenMap, systemNodeIdMap, isSystemLayer);
      return {
        layerId,
        layerName,
        layerType,
        child,
      };
    }
    else {
      let childNodeIds = systemNodeChildrenMap[nodeId];
      if (childNodeIds == undefined) child = [];
      else child = childNodeIds.map(nodeId => this.generateHierarchyLayerData(nodeId, systemNodeChildrenMap, systemNodeIdMap, isSystemLayer));
      if (levelType == "layer") return {
        id: layerId,
        name: layerName,
        layerType,
        child,
        levelType,
        misc
      }
      else return {
        id: layerId,
        name: layerName,
        levelType,
        deviceId,
        misc
      }
    }
  },
};
