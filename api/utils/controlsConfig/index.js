const flaverr = require('flaverr');
const Joi = require('joi')
const { ERROR_CODE } = require('../../utils/controlsConfig/constants');
const throwExceptionDuplicateCommandAbbrExist = (commandAbbr, controlAbbr) => {
  const errorMessage = `Duplicate CommandAbbr (${commandAbbr}) is not allowed. this CommandAbbr is being used at (${controlAbbr})}`
  throw flaverr('E_DUPLICATE_COMMAND_ABBR', new Error(errorMessage))
}


module.exports = {
  validateControlConfigList:function(controlConfigList,driverDataParamsList,driverCommandParamsList){
    const uniqueControlCommandAbbr = new Map();
    const uniqueControlAbbr = new Set();
    const controlTypes = ['VIT','BIT']
    const Schema = Joi.object({
      controlProperty: Joi.object().when('controlType', {
        is: 'BIT',
        then: Joi.object({
          left: Joi.object({
            commandAbbr: Joi.string().valid(...driverCommandParamsList).required(),
            value: Joi.number().required(),
            label: Joi.string().max(100).required(),
            dataParamValue: Joi.number().required()
          }).required(),
          right: Joi.object({
            commandAbbr: Joi.string().valid(...driverCommandParamsList).required(),
            value: Joi.number().required(),
            label: Joi.string().max(100).required(),
            dataParamValue: Joi.number().required()
          }).required()
        }),
        otherwise: Joi.object({
          commandAbbr: Joi.string().valid(...driverCommandParamsList).required(),
          label: Joi.string().max(100).required()
        }).required()
      }).required().custom(function(value, helpers){
        const { left, right} = value;
        // let _currentIndex = helpers.state.path[0];
        let root = helpers.state.ancestors[0]
        const { controlType, controlAbbr} = root;
        if (left && right && left.commandAbbr === right.commandAbbr) {
          const errorMessage = `left and right commandAbbr cannot be the same. You are using ${left.commandAbbr} for control left and right node in control ${root.controlName}`;
          return helpers.message(errorMessage);
        }
    
        if (controlType === 'BIT') {
          const { commandAbbr: leftCommandAbbr} = left;
          const {commandAbbr: rightCommandAbbr} = right;
          if (uniqueControlCommandAbbr.has(leftCommandAbbr)) {
            throwExceptionDuplicateCommandAbbrExist(leftCommandAbbr, uniqueControlCommandAbbr.get(leftCommandAbbr));
          } else {
            uniqueControlCommandAbbr.set(leftCommandAbbr, controlAbbr)
          }

          if (uniqueControlCommandAbbr.has(rightCommandAbbr)) {
            throwExceptionDuplicateCommandAbbrExist(rightCommandAbbr, uniqueControlCommandAbbr.get(rightCommandAbbr));
          } else {
            uniqueControlCommandAbbr.set(rightCommandAbbr, controlAbbr)
          }
        } else {
          // VIT
          const {commandAbbr} = value
          if (uniqueControlCommandAbbr.has(commandAbbr)) {
            throwExceptionDuplicateCommandAbbrExist(commandAbbr, uniqueControlCommandAbbr.get(commandAbbr))
          } else {
            uniqueControlCommandAbbr.set(commandAbbr, controlAbbr)
          }
        }

  
        return value;
      }),
      controlName: Joi.string().max(100),
      controlType: Joi.string().valid(...controlTypes).required(),
      controlAbbr: Joi.string().required().custom(function(value, helpers){

        if (uniqueControlAbbr.has(value)) {
          return helpers.message(`Duplicate controlAbbr (${value}) is not allowed.`)
        } else {
          uniqueControlAbbr.add(value)
        }
    
  
        return value;
      }),
      controlClass: Joi.string().allow(''),
      description: Joi.string().max(255).allow(''),
      dataParamAbbr: Joi.string().required().custom(function(value,helpers){
        //check dataParamAbbr should exist at driver data params
        if(!driverDataParamsList.includes(value)){
          const errorMessage = `[${helpers.state.path[0]}].dataParamAbbr '${value}' should be one of available data parameter of the driver. Available data parameters are [${driverDataParamsList.join(', ')}]'`
          return  helpers.message(errorMessage)
        }

        //Duplicate dataParamAbbr check
        const duplicateIndexes = [];
        controlConfigList.forEach((item, index) => {
          if (item.dataParamAbbr === value && index !== helpers.state.path[0]) {
            duplicateIndexes.push(index);
          }
        });
        if (duplicateIndexes.length > 0) {
          const errorMessage = `[${helpers.state.path[0]}].dataParamAbbr '${value}' is not unique. Duplicate found at indexes: ${duplicateIndexes.join(', ')}. Please ensure each dataParamAbbr should be unique`;
          return helpers.message(errorMessage);
        }

        return value;
      })
    });
    const arraySchema = Joi.array().items(Schema).unique((a, b) => a.controlAbbr === b.controlAbbr).min(1).messages({
      "array.min": "controlRelationship can not be empty."
    });
    const { error,value } = arraySchema.validate(controlConfigList,{ abortEarly: false });
    if(error) return error;
  },
  hasFullCommandParamCoverage:function(controlConfigList,allCommandParamLis){
    const _targetCommandParams = new Set();
    controlConfigList.forEach(it=>{
      const {controlType,controlProperty} = it
      if(controlType === "VIT"){
        _targetCommandParams.add(controlProperty.commandAbbr)
      } else if (controlType === "BIT"){
        _targetCommandParams.add(controlProperty.left.commandAbbr)
        _targetCommandParams.add(controlProperty.right.commandAbbr)
      }
    })
    return _.difference(allCommandParamLis, [..._targetCommandParams]).length >= 0

  },
  validateControlConfigDeleteItem: function(node) {
    const schema = Joi.object({
      deviceType: Joi.string().required(),
      driverType: Joi.string().required(),
      data: Joi.array().items(Joi.string()).required(),
    });    

    const { error } = schema.validate(node);
    if (error) {
      const errObj = {
        code: 'E_INPUT_VALIDATION', 
        data: {
            err: error.message,
       },
        statusCode: 400
      }
      throw flaverr(errObj)
    }
  },
  validateControlConfigUpsertItem: function(node) {
    const schema = Joi.object({
      deviceType: Joi.string().required(),
      driverType: Joi.string().required(),
      data: Joi.object({
        controlProperty: Joi.object().when('controlType', {
          is: 'BIT',
          then: Joi.object({
            left: Joi.object({
              commandAbbr: Joi.string().required(),
              value: Joi.number().required(),
              label: Joi.string().max(100).required(),
              dataParamValue: Joi.number().required()
            }).required(),
            right: Joi.object({
              commandAbbr: Joi.string().required(),
              value: Joi.number().required(),
              label: Joi.string().max(100).required(),
              dataParamValue: Joi.number().required()
            }).required()
          }),
          otherwise: Joi.object({
            commandAbbr: Joi.string().required(),
            label: Joi.string().max(100).required()
          })
        }).required(),
        controlName: Joi.string().max(100),
        controlType: Joi.string().valid('BIT','VIT').required(),
        controlAbbr: Joi.string().required(),
        controlClass: Joi.string().allow(''),
        description: Joi.string().max(255).allow(''),
        dataParamAbbr: Joi.string().required()
      }).required()
    });


    const { error } = schema.validate(node);
    if (error) {
      const errObj = {
        code: 'E_INPUT_VALIDATION', 
        data: {
            err: error.message,
       },
        statusCode: 400
      }
      throw flaverr(errObj)
    }
    return
  },
  throwExceptionControlsConfigNotExist:  function(controlRelationship) {
    const errObj = {
      code: 'INVALID_CONTROL_RELATIONSHIP_NOT_EXIST', 
      data: {...ERROR_CODE['INVALID_CONTROL_RELATIONSHIP_NOT_EXIST'],
      data: {
        existingControlRelationship:controlRelationship
      },
    },
      statusCode: 400
    }

    throw flaverr(errObj);
  },
  throwExceptionControlAbbrNotExist: function(controlAbbr) {
    const errObj = {
      code: 'INVALID_CONTROL_ABBR_NOT_EXIST', 
      data: {...ERROR_CODE['INVALID_CONTROL_ABBR_NOT_EXIST'],
      data: {
        controlAbbrNotExist: controlAbbr
      },
    },
      statusCode: 400
    }
    throw flaverr(errObj);
  },
  throwExceptionInvalidControlConfigList: function(error) {
    const errObj = {
      code: 'INVALID_CONTROL_CONFIG_SCHEMA', 
      data: {...ERROR_CODE['INVALID_CONTROL_CONFIG_SCHEMA'],
      data: error.details.map(it => it.message)
    },
      statusCode: 400
    }
    throw flaverr(errObj);
  }


}
