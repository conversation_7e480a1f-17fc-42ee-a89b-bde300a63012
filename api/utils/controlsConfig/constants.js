module.exports = {
  ERROR_CODE:{
    "INVALID_DRIVER_ID":{
      errorCode:"INVALID_DRIVER_ID",
      errorMessage: `The driver ID provided either does not exist in the system or does not belong to the component class. Please ensure that the driver ID is valid and corresponds to the only component class. In case of driver exist in "devicetype" table of dynamodb, check that driver class should be "components"`,
      code:10026,
    },
    "COMMAND_PARAM_COVERAGE_FAIL":{
      errorCode:"COMMAND_PARAM_COVERAGE_FAIL",
      errorMessage: "All the command params should be part of control relationship",
      code:10029
    },
    "CONTROl_PARAM_NOT_FOUND":{
      errorCode:"CONTROl_PARAM_NOT_FOUND",
      errorMessage:"The driver you are referencing does not have any control parameters associated with it. To establish the necessary relationship, control parameters must be present. Please check the 'parameter' column of the 'devicetype' table for this driver. We have attached the parameter column for your reference",
      code:10030
    },
    "INVALID_CONTROL_CONFIG_SCHEMA":{
      errorCode:"INVALID_CONTROL_CONFIG_SCHEMA",
      errorMessage:"There is one or more error in the control relationship payload. Please check 'data' key of error object in the response and resolve all the error(s) ",
      code:10031
    },
    "CONTROl_RELATIONSHIP_ALREADY_EXIST":{
      errorCode:"CONTROl_RELATIONSHIP_ALREADY_EXIST",
      errorMessage:`The controls relationship you are trying to create already exists for the provided driver ID. To update the control relationship, please ensure you clean the existing one first.`,
      code:10032
    },
    'INVALID_CONTROL_RELATIONSHIP':{
      errorCode:"INVALID_CONTROL_RELATIONSHIP",
      errorMessage:`The control relationship you are trying to update does not exist`,
      code:10033
    },
    "CONTROl_RELATIONSHIP_DOES_NOT_EXIST":{
      errorCode:"CONTROl_RELATIONSHIP_DOES_NOT_EXIST",
      errorMessage:`The control relationship you are trying to delete does not exist`,
      code:10034
    },
    "CONTROl_RELATIONSHIP_ALREADY_EXIST": {
      errorCode:"CONTROl_RELATIONSHIP_ALREADY_EXIST",
      errorMessage:`The control relationship does not exist for the requested driver`,
      code:10035
    },
    "INVALID_CONTROL_ABBR_NOT_EXIST":{
      errorCode:"INVALID_CONTROL_ABBR_NOT_EXIST",
      errorMessage:`The control abbr does not exist`,
      code:10036
    },
    "INVALID_CONTROL_RELATIONSHIP_NOT_EXIST":{
      errorCode:"INVALID_CONTROL_RELATIONSHIP_NOT_EXIST",
      errorMessage:`The control relationship does not exist for the driver`,
      code:10037
    }
  },

}
