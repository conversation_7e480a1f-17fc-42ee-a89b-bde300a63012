
const globalHelper = require('../globalhelper');

module.exports = {
  checkInput: function (body) {
    let errors = globalHelper.checkObjectKeys(body, [], '');
    if (errors.length === 0) return true;
    else throw new Error(errors);
  },
  /**
   * Get client's secret Id that identifies him in database, i.e siteId_browserHash
   * @param {string} siteId id of site we have previosuly sent token for
   * @param {string} secret client's browser hash
   */
  getClientToken: function (siteId, secret) {
    let clientId = `${siteId}_${secret}`;
    return clientId;
  }
};
