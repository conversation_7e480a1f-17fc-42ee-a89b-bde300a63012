const policies = require('./deJoulePolicy.json');

module.exports = {
  getPoliciesRoomMap: function () {
    let policyRoomMap = {};
    for (let service in policies) {
      let servicePolicy = policies[service];
      let { _description } = servicePolicy;
      let room = _description.room;

      for (let policyName in servicePolicy) {
        if (policyName !== '_description')
          policyRoomMap[policyName] = room;
      }
    }
    return policyRoomMap;
  }
};
