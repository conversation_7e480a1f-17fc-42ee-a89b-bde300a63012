const userService = require("../../services/user/user.service");

const extractSiteIdFromRedirectURL = (redirectLocationURL) => {
  if (typeof redirectLocationURL !== 'string') {
    return null;
  }
  
  redirectLocationURL = redirectLocationURL.trim() && redirectLocationURL.replace(/"|'|,|null| /gi,'')
  redirectLocationURL = redirectLocationURL.split('/').filter(Boolean);
  return  redirectLocationURL[0]
}
module.exports = {
  /**
   * if user has access from a site extract from redirectLocationURL than it will return the site
   * 
   */
  getSiteIdFromRedirectionURL: async function (userId,redirectLocationURL) {
      const sessionRestoredSiteId = extractSiteIdFromRedirectURL(redirectLocationURL)
      return sessionRestoredSiteId && await userService.getUserPreference(userId, sessionRestoredSiteId) ? sessionRestoredSiteId : null
  },
  extractSiteIdFromRedirectURL: extractSiteIdFromRedirectURL
  
}