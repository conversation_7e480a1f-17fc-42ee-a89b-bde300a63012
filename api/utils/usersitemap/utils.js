const globalHelpers = require('../globalhelper');
const defaultUnitPreference = {
  'temperature': 'degC',
  'delTemperature': 'delC',
  'pressure': 'kPa',
  'length': 'm',
  'cons': 'kwh'
};

module.exports = {
  /**
   * If there is incomplete/no user unit preference stored in userSiteMap table, this
   * will add default unit values to it.
   * @param {object} userUnitPreference Unit prefernce of user as stored in userSiteMap model/table
   */
  addDefaultsToUnitPreference: function(userUnitPreference){
    userUnitPreference = globalHelpers.toJson(userUnitPreference);
    if(!userUnitPreference) userUnitPreference = {};
    // if(!userUnitPreference.cons)
    return {...defaultUnitPreference, ...userUnitPreference};
  },
  getUserSiteMapCacheKey: function(userId) {
    return `user:${userId}:siteAccessList`;
  },

};
