/**
 * Plantvisualisations.js
 *
 * @description :: Stores visualisation configurations of Plant Page as per FE requirement.
 */
module.exports = {

  primaryKey: 'siteId',

  attributes: {
    siteId: {
      type: 'string',
      description: 'hash',
      required: true,
      example: 'ssh',
    },
    plantType: {
      type: 'string',
      description: 'range',
      // Usually industry type. Example: Currently used for Low-Side LMW requirement.
    },
    config: {
      type: 'json',
      // JSON configuration stored directly as per FE requirement.
    },
  },

};
