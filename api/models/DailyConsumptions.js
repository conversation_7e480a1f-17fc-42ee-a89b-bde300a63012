/**
 * Modes.js
 *
 * @description :: Stores the history of Mode changes of each device
 */
module.exports = {

  primaryKey: 'siteId',
  
  attributes: {
    siteId: {
      type: 'string',
      description: 'hash',
      required: true,
      example: 'ssh'
      // unique siteId
    },
    timestamp: {
      type: 'string',
      description: 'range',
      example: '2019-10-16'
      // Timestamp for which daily consumption of a site was measured
    },
    actual: {
      type: 'number',
      example: '8127'
    },

    target: {
      type: 'number',
      example: '8327'
    },

    actualkwh: {
      type: 'number',
      example: '8127'
    },

  
  }
  
  
};
  
  
