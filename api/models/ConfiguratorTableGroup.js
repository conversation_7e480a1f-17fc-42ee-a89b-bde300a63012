module.exports = {
  datastore: 'postgres',
  tableName: 'configurator_table_group',
  primaryKey: 'id',
  attributes: {
    id: {
      type: 'number',
      autoIncrement: true,
    },
    name: {
      type: 'string',
      columnType: 'varchar(255)',
      required: true,
    },
    pageRefId: {
      columnName: 'pages_ref_id',
      type: 'number',
      required: true,
    },
    status: {
      type: 'number',
      isIn: [0, 1],
      defaultsTo: 1,
      description: '0 -> inactive, 1 -> active'
    },
    created_by: {
      type: 'string',
    },
    updated_by: {
      type: 'string',
    },
    createdAt: {
      type: 'ref',
      columnType: 'timestamp',
    },
    updatedAt: {
      type: 'ref',
      columnType: 'timestamp',
    },
  },

  beforeCreate: function (values, next) {
    const timestamp =  sails.helpers.getCurrentTimestampIst.with({timestampFormat: 'YYYY-MM-DD HH:mm:ss'});
    values.createdAt = timestamp
    values.updatedAt = timestamp
    return next();
  },
  beforeUpdate: function (values, next) {
    values.updatedAt = sails.helpers.getCurrentTimestampIst.with({timestampFormat: 'YYYY-MM-DD HH:mm:ss'});
    return next();
  }
};
