/**
 * DynamicTargets.js
 *
 * @description :: Stores Date specific Dynamic target.
 */
module.exports = {

  primaryKey: 'siteId',

  attributes: {
    siteId: {
      type: 'string',
      description: 'hash',
      required: true,
      example: 'ssh',
      // unique siteId
    },
    timestamp: {
      type: 'string',
      description: 'range',
      // date of target target
      // format YYYY-MM-DD
    },
    prevYearDate: {
      type: 'string',
      // format YYYY-MM-DD
      // translated timestamp to previous year date
    },
    // percent: {
    //   type: 'number',
    //   // percentage change to be done in actual target
    // },
    // isPrevYear: {
    //   type: 'boolean',
    //   // stores if previous year data should be taken calculate
    //   // dynamic baseline or baseline cons.
    // },
    target: {
      type: 'number',
      // User input target for this day
    },
    actualTarget: {
      type: 'number',
      // actual target for this day
    },
  },

};
