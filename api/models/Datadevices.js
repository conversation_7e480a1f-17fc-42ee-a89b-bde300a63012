/**
 * DataDevice.js
 *
 * @description :: This schema is for storing all the Data of device/components coming from every controller
 */

module.exports = {

  primaryKey: 'deviceId',

  'attributes': {

    'deviceId': {
      'type': 'string', //<site_id+no.of commponents> eg ssh_2 ssh_43 OR deviceId = 2771
      'description': 'hash',
      'example': '2771'
    },
    'timestamp': {
      'type': 'string',
      'description': 'range',
    },
    'siteId': {
      'type': 'string',
    },
    'data': {
      'type': 'json'
    }
  },

};

