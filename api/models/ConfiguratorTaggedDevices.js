module.exports = {
  datastore: "postgres",
  tableName: "configurator_tagged_devices",
  primaryKey: "id",
  attributes: {
    id: {
      type: "number",
      autoIncrement: true,
    },
    configuratorSystemId: {
      type: "number",
      allowNull: true,
      columnName: "configurator_system_id",
      description: "ID found in the configurator_system table",
    },
    deviceId: {
      type: "ref",
      columnType: "character varying",
      defaultsTo: null,
      columnName: "device_id",
      description: "Device ID",
    },
    elementId: {
      type: "string",
      allowNull: false,
      columnName: "element_id",
      description: "Element Id",
    },
    deviceClass: {
      type: "string",
      columnType: "character varying",
      isIn: ["component", "device"],
      defaultsTo: "component",
      columnName: "device_Type",
      description: "Device Type",
    },
    position: {
      type: "json",
      columnType: "json",
      description: "Position of the device",
    },
    uiElementType: {
      type: "string",
      columnType: "character varying",
      allowNull: true,
      description: "UI element type",
    },
    style: {
      type: "json",
      columnType: "json",
      columnName: "elementStyle",
      description: "Element style",
    },
    offset: {
      type: "json",
      columnType: "json",
      description: "Offset of the device",
    },
    status: {
      type: "number",
      columnType: "smallint",
      defaultsTo: 1,
      description: "Status of the device",
    },
    order: {
      type: "number",
      columnName: "order",
      defaultsTo: 0,
    },
    svgLocationId: {
      type: "string",
      columnType: "character varying",
      allowNull: true,
      columnName: "svg_location_id",
    },
    displayFormat: {
      type: "ref",
      columnName: "display_format",
      columnType: "json",
      example: "{}",
      defaultsTo: "{}",
    },
    label: {
      type: "string",
      columnType: "character varying",
      allowNull: true,
      columnName: "text_label_data",
    },
    subSystemPageId: {
      type: "number",
      columnName: "sub_system_page_id",
      columnType: "Subsystem page id",
      allowNull: false,
      description: "Subsystem page id",
    },
    createdAt: {
      type: "ref",
      columnType: "timestamp",
    },
    updatedAt: {
      type: "ref",
      columnType: "timestamp",
    },
    createdBy: {
      type: "string",
      allowNull: false,
      columnName: "created_by",
    },
    updatedBy: {
      type: "string",
      allowNull: false,
      columnName: "updated_by",
    },
  },
  beforeCreate: function (values, next) {
    const timestamp = sails.helpers.getCurrentTimestampIst.with({
      timestampFormat: "YYYY-MM-DD HH:mm:ss",
    });
    values.createdAt = timestamp;
    values.updatedAt = timestamp;
    return next();
  },
  beforeUpdate: function (values, next) {
    values.updatedAt = sails.helpers.getCurrentTimestampIst.with({
      timestampFormat: "YYYY-MM-DD HH:mm:ss",
    });
    return next();
  },
};
