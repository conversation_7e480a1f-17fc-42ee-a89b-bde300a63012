/**
 * Component.js
 *
 * @description :: TODO: This functions holds configuration of each component
 */
module.exports = {

  primaryKey: 'deviceId',

  attributes: {

    deviceId: {
      type: 'string', // <site_id+_+no.of components> eg ssh_2 ssh_43
      description: 'hash',
      example: 'ssh_1',
    },
    siteId: {
      type: 'string',
      description: 'global-secondary##deviceType',
      // "index": "siteId_type_global_index-hash"
    },
    name: {
      type: 'string',
    },
    data: {
      type: 'json',
    },
    controls: {
      type: 'json',
    },
    type: {
      type: 'string',
    },
    deviceType: {
      type: 'string',
    },
    mode: {
      type: 'string',
    },
    regionId: {
      type: 'string',
    },
    ns1: {
      type: 'string',
    },
    info: {
      type: 'json',
    },
    driverType: {
      type: 'string',
    },
    controllerId: {
      type: 'string',
    },
    url: {
      type: 'string',
    },
    pidOn: {
      type: 'string',
    },
    pidRecipe: {
      type: 'string',
    },
    isVirtualComponent: {
      type: 'string',
    },
    updatedAt:{
      type: 'string',
    },
    svgId:{
      type: 'string',
    },
    modeLabel:{
      type: 'string'
    },
    deviceParameterList: { // List of parameters being used in this component. Initialised only in bulk configuration currently.
      type: 'json',
      columnType: 'stringSet'
    },
    isInMaintenanceMode:{
      type: 'string',
      description: '0 or 1',
      isIn: ["0", "1"],
      defaultsTo: '0',
    }

  },
 /**
   * Given single data expression it yiels array of its constituent {deviceId, param, type:'deviceId/componentId'  }
   * @param {string} dataExpression expression used by control/data key from component
   * @example dataExpression "2349@feedback_asdf and ssh_2@feedback_wert > 23.23"
   * @return {array} List as follows [{"deviceId":"2349","param":"feedback_asdf","type":"deviceId"},{"deviceId":"ssh_2","param":"feedback_wert","type":"componentId"}]
   */
  getDeviceParamListFromDataExpression: function (dataExpression) {
    let deviceParamList = [];

    if (typeof dataExpression !== 'string') {
      throw new Error('Required Data type string... Componets.getDeviceParamListFromDataExpression');
    }
    let finds = dataExpression.match(/([a-zA-Z_0-9.]+@[a-zA-Z_0-9]+)/gi);
    if (!finds || finds.length === 0) {
      return deviceParamList;
    }
    deviceParamList = finds.map(find => {
      let type;
      let [deviceId, param] = find.split('@');

      if (isNaN(parseInt(deviceId))) {
        type = 'componentId';
      } else {
        type = 'deviceId';
      }
      return { deviceId, param, type };
    });
    return deviceParamList;
  },
  /**
   * Checks if the component have VFD installed in it or not
   * @param {array} componetDataKey Data key from component object of component table
   */
  isComponentWithVFDInstalled: function (componetDataKey) {
    let isVFDInstalled = false;

    if (componetDataKey.constructor.name !== 'Array') {
      throw new Error('Required Data type string... Componets.isComponentWithVFDInstalled');
    }
    isVFDInstalled = componetDataKey.find(DataKey => DataKey.key === 'outputfrequency');
    if (isVFDInstalled !== undefined) {
      return true;
    } else {
      return false;
    }
  },

  /**
   * Checks if the component have Actuator installed in it or not
   * @param {array} componetDataKey Data key from component object of component table
   */
  isComponentWithActuatorInstalled: function (componetDataKey) {
    let isVFDInstalled = false;

    if (componetDataKey.constructor.name !== 'Array') {
      throw new Error('Required Data type string... Componets.isComponentWithActuatorInstalled');
    }
    isVFDInstalled = componetDataKey.find(DataKey => DataKey.key === 'actuator');
    if (isVFDInstalled !== undefined) {
      return true;
    } else {
      return false;
    }
  },


};
