/**
 * graphs.js
 *
 * @description :: Stores all the save graphs
 * @docs        :: http://sailsjs.org/documentation/concepts/models-and-orm/models
 */

module.exports = {

    primaryKey: 'userId_siteId',

    attributes: {
        'userId_siteId': {
            type: 'string',
            description: 'hash',
            required: true,
            unique: true,
            example: 'mohit@smartjoules.in_mgch'
        },
        'graphId': {
            type: 'string',
            description: 'range',
            required: true,
            unique: true,
            example: 'UUID-UUID-UUID-UUID'
        },
        'requestPayload': {
            type: 'json',
            required: true,
        },
        'folderName': {
            type: 'string',
            required: true,
        },
        'name': {
            type: 'string',
            required: true,
        },
        'graphType': {
            type: 'string',
            required: true,
        },
    }
};
