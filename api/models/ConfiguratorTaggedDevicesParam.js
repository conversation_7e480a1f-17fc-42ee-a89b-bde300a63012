/**
 * configurator_tagged_devices_param.js
 *
 * @description :: Represents the configurator_tagged_devices_param table in the database.
 * @docs        :: https://sailsjs.com/docs/concepts/models-and-orm/models
 */

module.exports = {
  datastore: "postgres",
  tableName: "configurator_tagged_devices_param",
  primaryKey: "id",
  attributes: {
    id: {
      type: "number",
      autoIncrement: true,
    },
    configuratorTaggedDevicesId: {
      type: "number",
      allowNull: true,
      columnName: "configurator_tagged_devices_id",
      description: "ID found in the configurator_tagged_devices table",
    },
    paramType: {
      type: "string",
      columnType: "character varying",
      allowNull: true,
      columnName: "param_type",
      description: "Parameter type",
    },
    paramAbbr: {
      type: "string",
      columnType: "character varying",
      allowNull: true,
      columnName: "param_abbr",
      description: "Parameter abbreviation",
    },
    position: {
      type: "ref",
      columnType: "json",
    },
    style: {
      type: "ref",
      columnType: "json",
    },
    status: {
      type: "number",
      columnType: "smallint",
      defaultsTo: 1,
      description: "Status",
    },
    order: {
      type: "number",
      columnType: "smallint",
      allowNull: true,
      description: "Order",
    },
    createdAt: {
      type: "ref",
      columnType: "timestamp",
    },
    updatedAt: {
      type: "ref",
      columnType: "timestamp",
    },
    createdBy: {
      type: "string",
      columnType: "character varying",
      allowNull: true,
      columnName: "created_by",
      description: "Created by",
    },
    updatedBy: {
      type: "string",
      columnType: "character varying",
      allowNull: true,
      columnName: "updated_by",
      description: "Updated by",
    },
  },
  beforeCreate: function (values, next) {
    const timestamp = sails.helpers.getCurrentTimestampIst.with({
      timestampFormat: "YYYY-MM-DD HH:mm:ss",
    });
    values.createdAt = timestamp;
    values.updatedAt = timestamp;
    return next();
  },
  beforeUpdate: function (values, next) {
    values.updatedAt = sails.helpers.getCurrentTimestampIst.with({
      timestampFormat: "YYYY-MM-DD HH:mm:ss",
    });
    return next();
  },
};
