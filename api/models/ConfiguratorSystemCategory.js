module.exports = {
  datastore: "postgres",
  tableName: "configurator_system_category",
  primaryKey: "id",
  attributes: {
    id: {
      type: "number",
      autoIncrement: true,
    },
    name: {
      type: "string",
      columnType: "varchar(255)",
      example: "Fire Alarm System",
      description: "System Category Name",
      required: true,
    },
    created_by: {
      type: "string",
    },
    last_updated_by: {
      type: "string",
    },
    status: {
      type: "number",
      columnType: "smallInt",
      isIn: [0, 1],
      defaultsTo: 1,
    },
    createdAt: {
      type: "ref",
      columnType: "timestamp",
    },
    updatedAt: {
      type: "ref",
      columnType: "timestamp",
    },
  },
  beforeCreate: function (values, next) {
    const timestamp = sails.helpers.getCurrentTimestampIst.with({
      timestampFormat: "YYYY-MM-DD HH:mm:ss",
    });
    values.createdAt = timestamp;
    values.updatedAt = timestamp;
    return next();
  },
  beforeUpdate: function (values, next) {
    values.updatedAt = sails.helpers.getCurrentTimestampIst.with({
      timestampFormat: "YYYY-MM-DD HH:mm:ss",
    });
    return next();
  },
};
