module.exports = {
  datastore: process.env.SMART_ALERT_DB_NAME,
  tableName: 'message',
  primaryKey: 'id',
  attributes: {
    id: {
      type: 'number',
      autoIncrement: true,
    },
    template_id: {
      type: 'number',
      required: true,
      description: 'This is the foreign key from message_template table',
    },
    vendor_template_id: {
      type: 'string',
      required: false,
      allowNull: true,
      description: 'some channel required third party message id. for example whatsapp template or message template, those kinf' +
        'of template usually registered after approval from third party vendor'
    },
    title: {
      type: 'string',
      columnType: 'TEXT',
      example: ''
    },
    body: {
      type: 'string',
      columnType: 'TEXT',
      example: ''
    },
    channel_name: {
      type: 'string',
      required: true,
      isIn: ['email', 'whatsapp', 'message', 'slack']
    },
    event_type: {
      type: 'string',
      required: true,
      //todo correct the spelling mistake of TARGET_EVENT_HAPPENED in enum
      // isIn:["TARGET_EVENT_HAPPENED","TARGET_RESOLUTION_EVENT_HAPPENED"]
    },
    createdAt: {
      type: 'ref',
      columnType: 'timestamp',
      autoCreatedAt: true,
    },
    updatedAt: {
      type: 'ref',
      columnType: 'timestamp',
      autoUpdatedAt: true,
    },
    status:{
      type:'number',
      defaultsTo:1,
      allowNull:false,
      isIn:[1,0]
    }
  },
};
