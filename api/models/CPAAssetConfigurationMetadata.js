/**
 * CPAAssetConfigurationMetadata.js
 * @description :: Stores the history in cpa_asset_configuration_metadata
 */
module.exports = {
  datastore: 'postgres',
  tableName: 'cpa_asset_configuration_metadata',
  primaryKey: 'id',
  attributes: {
    id: { type: 'number', autoIncrement: true, },
    siteId: {
      type: 'string',
      required: true,
      example: 'mgch',
      columnName:'site_id'
    },
    assetId: {
      type: 'string',
      required: true,
      example: 'mgch_1',
      columnName:'asset_id'
    },
    assetType: {
      type: 'string',
      required: true,
      example: 'chiller',
      columnName:'asset_type'
    },
    assetName: {
      type: 'string',
      required: true,
      columnName:'asset_name'
    },
    status: {
      type: 'number',
      isIn: [0,1],
      defaultsTo: 1
    },
    syncBy: {
      type: 'string',
      columnName:'sync_by',
      required: true
    },
    createdAt: { type: 'ref', columnType: 'timestamp', columnName: 'created_at' },
    updatedAt: { type: 'ref', columnType: 'timestamp', columnName: 'updated_at' },

  },
  beforeCreate: function (values, next) {
    const timestamp = sails.helpers.getCurrentTimestampIst.with({
      timestampFormat: "YYYY-MM-DD HH:mm:ss",
    });
    values.createdAt = timestamp;
    values.updatedAt = timestamp;
    return next();
  },
  beforeUpdate: function (values, next) {
    values.updatedAt = sails.helpers.getCurrentTimestampIst.with({
      timestampFormat: "YYYY-MM-DD HH:mm:ss",
    });
    return next();
  },
};
