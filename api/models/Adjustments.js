/**
 * Adjustments.js
 *
 * @description :: Stores Adjustments done in baseline
 */
module.exports = {

  primaryKey: 'siteId',

  attributes: {
    siteId: {
      type: 'string',
      description: 'hash',
      required: true,
      example: 'ssh',
      // unique siteId
    },
    timestamp: {
      type: 'string',
      description: 'range',
      // date of adjustment
    },
    absolute: {
      type: 'number',
      // SUM of all the adjustments ever done on this site.
    },
    relative: {
      type: 'number',
      // Cuurent adjustment value
    },
  },

};
