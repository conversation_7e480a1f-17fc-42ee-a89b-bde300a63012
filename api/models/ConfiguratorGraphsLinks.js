module.exports = {
  datastore: "postgres",
  tableName: "sankey_graph_links",
  primaryKey: "id",
  attributes: {
    id: {
      type: "number",
      autoIncrement: true,
    },
    sourceId: {
      type: "number",
      columnName: "source_id",
    },
    targetId: {
      type: "number",
      columnName: "target_id",
      allowNull: true,
    },
    order: {
      type: "number",
      allowNull: false,
    },
    graphRefId: {
      type: "number",
      allowNull: false,
      columnName: "graph_ref_id",
    },
    status: {
      type: "number",
      isIn: [0, 1],
      defaultsTo: 1,
      description: "0 -> inactive, 1 -> active",
    },
    createdBy: {
      type: "string",
      columnName: "created_by",
    },
    lastUpdatedBy: {
      type: "string",
      columnName: "last_updated_by",
    },
    createdAt: {
      type: "ref",
      columnType: "timestamp",
    },
    updatedAt: {
      type: "ref",
      columnType: "timestamp",
    },
  },

  beforeCreate: function (values, next) {
    const timestamp = sails.helpers.getCurrentTimestampIst.with({
      timestampFormat: "YYYY-MM-DD HH:mm:ss",
    });
    values.createdAt = timestamp;
    values.updatedAt = timestamp;
    return next();
  },
  beforeUpdate: function (values, next) {
    values.updatedAt = sails.helpers.getCurrentTimestampIst.with({
      timestampFormat: "YYYY-MM-DD HH:mm:ss",
    });
    return next();
  },
};
