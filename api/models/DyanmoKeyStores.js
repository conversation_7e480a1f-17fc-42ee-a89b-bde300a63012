/**
 * DyanmoKeyStore.js
 *
 * @description :: This is simple key:value pair that holds any key value pair on site.
 * @docs        :: http://sailsjs.org/documentation/concepts/models-and-orm/models
 */

module.exports = {

  primaryKey: 'key',

  'attributes': {

    'key': {
      'type': 'string',
      'description': 'hash'
    },

    'value': {
      'type': 'string',

    },
    'list': {
      'type': 'json',
      'columnType': 'array'
    }
  }
};
