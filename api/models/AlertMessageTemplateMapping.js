module.exports = {
  datastore: process.env.SMART_ALERT_DB_NAME,
  tableName: 'alert_message_template_mapping',
  primaryKey: 'id',
  attributes: {
    id: {
      type: 'number',
      autoIncrement: true,
    },
    alert_id: {
      type: 'number',
      required: true,
      description: 'alert id as foreign key',
    },
    message_template_id: {
      type: 'number',
      required: true,
      description: 'message template id as foreign key',
    },
    status: {
      type: 'number',
      defaultsTo: 1,
      description: '1=>active,0=>deleted'
    },
    created_by: {
      type: 'string',
      required: false,
      description: 'user id of user who has taken the action'
    },
    createdAt: {
      type: 'ref',
      columnType: 'timestamp',
      autoCreatedAt: true,
    },
    updatedAt: {
      type: 'ref',
      columnType: 'timestamp',
      autoUpdatedAt: true,
    },
  },
  beforeCreate: function (values, next) {
    values.created_by = sails.config.usersession.id;
    return next();
  }
};
