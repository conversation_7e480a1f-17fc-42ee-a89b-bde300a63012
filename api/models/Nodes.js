module.exports = {
    datastore:'postgres',
    tableName: 'nodes',
    primaryKey: 'id',
    attributes: {
      id: { type: 'number', autoIncrement: true, },
      system_id: {
        type: 'number',
        // required: true,
        description: 'ID found in system table',
      },
      parent_id: {
        type: 'number',
        // required:true,
        example: 'Id of the parent itself.'
      },
      name: {
        type: 'string',
        columnType: 'varchar(255)',
        example: 'Floor',
        description: 'Name of the node.',
      },
      layer_type: {
        type: 'string',
        columnType: 'varchar(255)',
        example: 'floor',
        description: 'Type of the layer.',
      },
      level_type: {
        type: 'string',
        columnType: 'varchar(255)',
        example: 'floor',
        description: 'Type of the level. Possible values: controller/ component/ layer.',
      },
      device_id: {
        type: 'string',
        columnType: 'varchar(255)',
        example: 'floor',
        description: 'DeviceId of controller or componentID of component.',
      },
      site_id: {
        type: 'string',
        columnType: 'varchar(255)',
        example: 'floor',
        description: 'siteId of the controller/ component/ layer.',
      },
      is_deleted: {
        type: 'ref',
        columnType: 'boolean',
        defaultsTo: false,
      },
      svg_tag_device_map: {
        type: 'ref',
        columnType: 'json',
      },
      misc:{
        type: 'ref',
        columnType: 'json',
        defaultsTo:null
      },
      createdAt: { type: 'ref', columnType: 'timestamp', autoCreatedAt: true, },
      updatedAt: { type: 'ref', columnType: 'timestamp', autoUpdatedAt: true, },
    },
  };
