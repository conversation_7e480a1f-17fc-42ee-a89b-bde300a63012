module.exports = {
  datastore: "postgres",
  tableName: "configurator_table",
  primaryKey: "id",
  attributes: {
    id: {
      type: "number",
      autoIncrement: true,
    },
    name: {
      type: "string",
      columnType: "varchar(255)",
      required: true,
    },
    deviceType: {
      type: "string",
      columnType: "varchar(255)",
      columnName: "device_type",
      required: true,
    },
    isPublished: {
      columnName: "is_published",
      type: "number",
      isIn: [0, 1],
      description: "0 -> draft, 1 -> published",
    },
    status: {
      type: "number",
      columnType: "smallInt",
      isIn: [0, 1],
      defaultsTo: 1,
    },
    deviceClass: {
      type: "string",
      columnName: "device_class",
      isIn: ["device", "component"],
      required: true,
    },
    tableGroupId: {
      type: "number",
      columnName: "table_group_id",
    },
    canTranspose: {
      type: "number",
      columnName: "can_transpose",
      columnType: "smallInt",
      isIn: [0, 1],
      defaultsTo: 0,
    },
    createdBy: {
      type: "string",
      columnName: "created_by",
    },
    createdAt: {
      type: "ref",
      columnType: "timestamp",
    },
    updatedAt: {
      type: "ref",
      columnType: "timestamp",
    },
    updatedBy: {
      type: "string",
      columnName: "updated_by",
    },
  },
  beforeCreate: function (values, next) {
    const timestamp = sails.helpers.getCurrentTimestampIst.with({
      timestampFormat: "YYYY-MM-DD HH:mm:ss",
    });
    values.createdAt = timestamp;
    values.updatedAt = timestamp;
    return next();
  },
  beforeUpdate: function (values, next) {
    values.updatedAt = sails.helpers.getCurrentTimestampIst.with({
      timestampFormat: "YYYY-MM-DD HH:mm:ss",
    });
    return next();
  },
};
