module.exports = {
    datastore: 'postgres',
    tableName: 'configurator_graphs',
    primaryKey: 'id',
    attributes: {
      id: {
        type: 'number',
        autoIncrement: true,
      },
      name: {
        type: 'string',
        columnType: 'varchar(255)'
      },
      type: {
        columnName: 'type',
        type: 'number',
        isIn: [1],
        required: true,
        description: '1 -> Sankey, 2 -> Candle Stick, 3 -> Spectral Chart, 4 -> Line Chart, 5 -> HeatMap'
      },
      status: {
        type: 'number',
        isIn: [0, 1],
        defaultsTo: 1,
        description: '0 -> inactive, 1 -> active'
      },
      subsystemPageId: {
        columnName: 'sub_system_page_id',
        type: 'number',
        required: true,
      },
      graphProperty: {
        type: 'ref',
        columnName: 'graph_property',
      },
      createdBy: {
        type: 'string',
        columnName: 'created_by',
        required:true,
      },
      lastUpdatedBy: {
        type: 'string',
        columnName: 'last_updated_by'
      },
      createdAt: {
        type: 'ref',
        columnType: 'timestamp',
      },
      updatedAt: {
        type: 'ref',
        columnType: 'timestamp',
      },
    },

    beforeCreate: function (values, next) {
      const timestamp =  sails.helpers.getCurrentTimestampIst.with({timestampFormat: 'YYYY-MM-DD HH:mm:ss'});
      values.createdAt = timestamp
      values.updatedAt = timestamp
      return next();
    },
    beforeUpdate: function (values, next) {
      values.updatedAt = sails.helpers.getCurrentTimestampIst.with({timestampFormat: 'YYYY-MM-DD HH:mm:ss'});
      return next();
    }
  };
