/**
 * @module Notifications.js
 * @description :: This will contains all the alerts/email and dejoule notification user have
 */

module.exports = {
  primaryKey: 'id',
  attributes: {
    id: {
      'type': 'string',
      'description': 'hash'
    },
    timestamp: {
      'type': 'string',
      'description': 'range',
    },
    processes: {
      type: 'json',
      columnType: 'array'
    },
    priority: {
      type: 'string'
    },
    ts: {
      type: 'string'
    },
    category: {
      type: 'json',
      columnType: 'array'
    },
    title: {
      type: 'string'
    },
    description: {
      type: 'string'
    },
    notify: {
      type: 'json',
      columnType: 'array'
    },
    sms: {
      type: 'json',
      columnType: 'array'
    },
    readstatus: {
      type: 'string'
    },
    sourceId: {
      type: 'string'
    },
    alertId: {
      type: 'string'
    },
    type: {
      type: 'string'
    },
    extra: {
      type: 'json',
      columnType: 'map'
    },
    siteId: {
      type: 'string'
    }


  },
};
  