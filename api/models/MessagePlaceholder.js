module.exports = {
  datastore: process.env.SMART_ALERT_DB_NAME,
  tableName: 'message_placeholder',
  primaryKey: 'id',
  attributes: {
    id: {
      type: 'number',
      autoIncrement: true,
    },
    placeholder_name: {
      type: 'string',
      required: true,
      description: 'placeholder name like siteId, username,currentKwh etc',
    },
    message_id: {
      type: 'number',
      required: true
    },
    createdAt: {
      type: 'ref',
      columnType: 'timestamp',
      autoCreatedAt: true,
    },
    updatedAt: {
      type: 'ref',
      columnType: 'timestamp',
      autoUpdatedAt: true,
    },
  },
};
