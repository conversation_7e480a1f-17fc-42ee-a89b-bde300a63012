module.exports = {
  datastore:'postgres',
  primaryKey: 'id',
  tableName: 'configurator_page_svg', // Match the actual table name in your database
  attributes: {
    id: { type: 'number', autoIncrement: true, },
    createdBy: {
      columnName: 'created_by',
      type: 'string',
    },
    svgPath: {
      type: 'string',
      columnName: 'svg_path',
    },
    pageRefId: {
      type: 'string',
      columnName: 'pages_ref_id',
    },
    updatedBy: {
      columnName: 'updated_by',
      type: 'string',
    },
    status: {
      type: 'number',
      isIn: [0, 1],
      defaultsTo: 1,
      description: '0 -> inactive, 1 -> active'
    },
    createdAt: { type: 'ref', columnType: 'timestamp'},
    updatedAt: { type: 'ref', columnType: 'timestamp'},
  },
  beforeCreate: function (values, next) {
    const timestamp =  sails.helpers.getCurrentTimestampIst.with({timestampFormat: 'YYYY-MM-DD HH:mm:ss'});
    values.createdAt = timestamp
    values.updatedAt = timestamp
    return next();
  },
  beforeUpdate: function (values, next) {
    values.updatedAt =  sails.helpers.getCurrentTimestampIst.with({timestampFormat: 'YYYY-MM-DD HH:mm:ss'});
    return next();
  }
};
