module.exports = {
  datastore: "postgres",
  tableName: "configurator_table_column",
  primaryKey: "id",
  attributes: {
    id: {
      type: "number",
      autoIncrement: true,
    },
    abbrName: {
      type: "string",
      columnType: "varchar(255)",
      columnName: "abbr_name",
      required: true,
    },
    type: {
      type: "string",
      columnType: "varchar(255)",
      columnName: "type",
      required: true,
    },
    status: {
      type: "number",
      columnType: "smallInt",
      isIn: [0, 1],
      defaultsTo: 1,
    },
    tableId: {
      type: "string",
      columnName: "table_id",
    },
    createdBy: {
      type: "string",
      columnName: "created_by",
    },
    createdAt: {
      type: "ref",
      columnType: "timestamp",
    },
    updatedAt: {
      type: "ref",
      columnType: "timestamp",
    },
    updatedBy: {
      type: "string",
      columnName: "updated_by",
    },
    order: {
      type: "number",
    },
  },
  beforeCreate: function (values, next) {
    const timestamp = sails.helpers.getCurrentTimestampIst.with({
      timestampFormat: "YYYY-MM-DD HH:mm:ss",
    });
    values.createdAt = timestamp;
    values.updatedAt = timestamp;
    return next();
  },
  beforeUpdate: function (values, next) {
    values.updatedAt = sails.helpers.getCurrentTimestampIst.with({
      timestampFormat: "YYYY-MM-DD HH:mm:ss",
    });
    return next();
  },
};
