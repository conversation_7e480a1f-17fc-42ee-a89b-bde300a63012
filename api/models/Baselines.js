/**
 * Modes.js
 *
 * @description :: Stores the history of Mode changes of each device
 */
module.exports = {

  primaryKey: 'siteId',

  attributes: {
    siteId: {
      type: 'string',
      description: 'hash',
      required: true,
      example: 'ssh',
      // unique siteId
    },
    startDate: {
      type: 'string',
      description: 'range',
      // Start date of baseline, in the format MM-DD-YYYY 
      // to query baseline using the query : baseline.startsWith(month)
      // to get baseline of a month
    },
    endDate: {
      type: 'string',
      // End date of baseline
    },
    consumptionValue: {
      type: 'number',
      // User input actual consumption recorded for 0th year
    },
    target: {
      type: 'number',
      // In percentage the target %
    },
  },
};
