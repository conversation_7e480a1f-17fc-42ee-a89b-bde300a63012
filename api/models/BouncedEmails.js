/**
 * Plantvisualisations.js
 *
 * @description :: Stores contextual information of emails that bounced after the notification service sent via SES.
 */
module.exports = {

  primaryKey: 'emailId',
  tableName: 'bouncedemails',
  attributes: {
    emailId: {
      type: 'string',
      description: 'hash',
      required: true,
      example: '<EMAIL>',
    },
    timestamp: {
      type: 'string',
      description: 'range',
      // Timestamp when SES attempted to send the email.
    },
    to: {
      type: 'json',
      columnType: 'array'
      // JSON configuration stored directly as per FE requirement.
    },
    subject: {
      type: 'string'
      // Subject of the email set.
    }
  },

};
