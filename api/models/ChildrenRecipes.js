module.exports = {
  datastore: 'postgres',
  tableName: 'children_recipes',
  primaryKey: 'id',

  attributes: {
    id: {
      type: 'number',
      autoIncrement: true,
    },
    parent_recipe_id: {
      model: 'recipeinfo',
    },
    execution_order: {
      type: 'number',
    },
    uniqid: {
      type: 'string',
      unique: true,
    },
    status: {
      type: 'number',
      defaultsTo: 1
    },
    formula: {
      type: 'string',
    },
    block_type: {
      type: 'string',
    },
    observation_time: {
      type: 'string',
    },
    expression_template: {
      type: 'string',
    },
    params: {
      type: 'json',
    },
    operators: {
      type: 'json',
    },
    everyMinuteTopics: {
      type: 'json',
    },
    description: {
      type: 'string',
    },
    createdAt: {
      type: 'ref',
      columnType: 'timestamp',
      autoCreatedAt: true,
    },
    updatedAt: {
      type: 'ref',
      columnType: 'timestamp',
      autoUpdatedAt: true,
    },
    actions: {
      collection: 'recipeactions',
      via: 'recipe_id',
    },
  },
};
