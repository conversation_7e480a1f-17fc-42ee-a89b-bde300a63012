module.exports = {
  datastore: 'postgres',
  tableName: 'sub_system_pages',
  primaryKey: 'id',
  attributes: {
    id: {
      type: 'number',
      autoIncrement: true,
    },
    title: {
      type: 'string',
      columnType: 'varchar(255)',
      required: true,
      maxLength: 50
    },
    pageType: {
      columnName: 'page_type',
      type: 'number',
      isIn: [1, 2, 3],
      required: true,
      description: '1 -> svg, 2 -> table, 3 -> graph'
    },
    status: {
      type: 'number',
      isIn: [0, 1],
      defaultsTo: 1,
      description: '0 -> inactive, 1 -> active'
    },
    isPublished: {
      columnName: 'is_published',
      type: 'number',
      isIn: [0, 1],
      defaultsTo: 0,
      description: '0 -> draft, 1 -> published'
    },
    subsystemId: {
      columnName: 'sub_system_id',
      type: 'number',
      required: true,
    },
    order: {
      type: 'number',
      defaultsTo: 0
    },
    created_by: {
      type: 'string',
      required: true,
    },
    last_updated_by: {
      type: 'string',
    },
    createdAt: {
      type: 'ref',
      columnType: 'timestamp',
    },
    updatedAt: {
      type: 'ref',
      columnType: 'timestamp',
    },
    customConfig: {
      columnName: 'misc',
      type: 'ref',
      columnType: 'json',
      required: false
    }
  },

  beforeCreate: function (values, next) {
    const timestamp =  sails.helpers.getCurrentTimestampIst.with({timestampFormat: 'YYYY-MM-DD HH:mm:ss'});
    values.createdAt = timestamp
    values.updatedAt = timestamp
    return next();
  },
  beforeUpdate: function (values, next) {
    values.updatedAt = sails.helpers.getCurrentTimestampIst.with({timestampFormat: 'YYYY-MM-DD HH:mm:ss'});
    return next();
  }
};
