/**
 * Device.js
 *
 * @description :: TODO: You might write a short summary of how this model works and what it represents here.
 * @docs        :: http://sailsjs.org/documentation/concepts/models-and-orm/models
 */
module.exports = {

  primaryKey: 'deviceId',

  attributes: {
    deviceId: {
      type: 'string',
      description: 'hash',
      required: true,
      unique: true,
      maxLength: 120,
      example: '2771',
    },
    siteId: {
      type: 'string',
      description: 'global-secondary##deviceId',
      required: true,
      maxLength: 120,
    },
    networkId: {
      type: 'string',
    },
    currentVersion: {
      type: 'string',
    },
    expectedVersion: {
      type: 'string',
    },
    regionId: {
      type: 'string',
    },
    name: {
      type: 'string',
    },
    deviceType: {
      type: 'string',
    },
    areaId: {
      type: 'string',
    },
    componentId: {
      type: 'string',
    },
    // add a param whether device is controller or slave
    // attributes used for controllers

    hardwareVer: {
      type: 'string',
    },
    softwareVer: {
      type: 'string',
    },
    operationMode: {
      type: 'string',
      extendedDescription: 'network or standalone',
    },
    baudRate: {
      type: 'string',
    },
    parity: {
      type: 'string',
    },
    stopbit: {
      type: 'string',
    },
    vendorId: {
      type: 'string',
    },
    hasLCD: {
      type: 'string',
    },
    lcdDisplayList: { // for joulestat only to display which port's param should be displayed
      type: 'json',
    },
    pidOn: {
      type: 'string',
    },
    pid: {
      type: 'json',
    },

    // attributes used specifically in devices

    communicationType: {
      type: 'string',
      isIn: ['MB', 'NMB'],
    },
    communicationCategory: {
      type: 'string',
    },
    driverType: {
      type: 'string',
    },
    slaveId: { // updateable
      type: 'string',
    },
    controllerId: {
      type: 'string',
    },
    portNumber: {
      type: 'string',
    },
    functionType: {
      type: 'string',
    },
    mbBatchReadEnable: {
      type: 'string'
    },
    // Energy Meter specific keys
    isMainMeter: { // editable
      type: 'string',
    },
    parentEM: { // editable
      type: 'string',
    },

    // Diagnostics Page related keys
    maintenanceMode: {
      type: 'boolean',
    },
    maintenanceStart: {
      type: 'string',
    },
    maintenanceEnd: {
      type: 'string',
    },
    remoteAccess: {
      type: 'number',
    },
    remoteAccessPort: {
      type: 'string',
    },
    parentComponent: {
      type: 'string', // waise its array
    },

    mode: {
      type: 'string',
    },
    isConfigured: {
      type: 'string',
    },
    // params related to config of controller
    configTS: {
      type: 'string',
    },
    IPAddress: {
      type: 'string',
    },
    remoteBridge: {
      type: 'string',
    },
    localBridge: {
      type: 'string',
    },
    localBroker: {
      type: 'string',
    },
    deviceMeta: {
      type: 'json',
    },
    internetStatus: {
      type: 'string',
    },
    expectedInternetStatus: {
      type: 'string',
    },
    isSlaveController: {
      type: 'boolean',
    },
    isVirtualDevice:{
			"type":"string"
		},
    sheetId: { // Used to save Google Sheet ID for MobusIP Device Parameter Configuration
			"type":"string"
		},
    maxAssetId: {
      "type": "string" // Used to save max assetId generated for a parameter on a device. Used only in Bulk Configuration for ModbusIP currently.
    },
    modbusFromUSB: { // Used for controller configuration.
			"type": "boolean"
		},
    isInMaintenanceMode:{
      type: 'string',
      description: '0 or 1',
      isIn: ["0", "1"],
      defaultsTo: '0',
    }
  },

};
