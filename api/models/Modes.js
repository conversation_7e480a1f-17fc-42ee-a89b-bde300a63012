/**
 * Modes.js
 *
 * @description :: Stores the history of Mode changes of each device
 */
module.exports = {

  primaryKey: 'did',

  attributes: {
    did: {
      type: 'string',
      description: 'hash',
      required: true,
      example: 'deviceId.commandName'
      // 2218.setPoint, 2218.maintainance etc
    },
    timestamp: {
      type: 'string',
      description: 'range',
      required: true,
      example: '1586953917000' // time stamp in unix
    },
    changedMode: {
      type: 'string',
    },
    siteId: {
      type: 'string',
    },
    allModes: {
      type: 'string',
    },
    currMode: {
      type: 'string',
    },
    createdBy: {
      type: 'string',
    }


  }


};

