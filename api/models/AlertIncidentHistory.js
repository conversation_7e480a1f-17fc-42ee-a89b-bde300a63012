module.exports = {
  datastore: process.env.SMART_ALERT_DB_NAME,
  tableName: 'alert_incident_history',
  primaryKey: 'id',

  attributes: {
    id: {
      type: 'number',
      autoIncrement: true,
    },
    uuid: {
      type: 'string',
      unique: true,
      required: true
    },
    alert_inventory_id: {
      type: 'number',
      required: true
    },
    issue_occurred_at: {
      type: 'ref',
      columnType: 'timestamp',
      required: true
    },
    issue_resolved_at: {
      type: 'ref',
      columnType: 'timestamp'
    },
    acknowledge_by: {
      type: 'string',
      description: 'Email address of the person who acknowledged the incident',
    },
    acknowledge_ts: {
      type: 'ref',
      columnType: 'timestamp'
    },
    escalated_at: {
      type: 'ref',
      columnType: 'timestamp'
    },
    assign_to: {
      type: 'string'
    },
    resolution_reason: {
      type: 'string',
      columnType: 'text'
    },
    occurred_event_count: {
      type: 'number',
      defaultsTo: 1,
      description: 'Number of times this alert occurred during its active period'
    },
    recent_occurred_event_ts: {
      type: 'ref',
      columnType: 'timestamp',
      description: 'Timestamp of the most recent occurrence of this alert'
    },
    createdAt: {
      columnName: 'created_at',
      type: 'ref',
      columnType: 'timestamp',
      autoCreatedAt: true
    },
    updatedAt: {
      columnName: 'updated_at',
      type: 'ref',
      columnType: 'timestamp',
      autoUpdatedAt: true
    }
  }
};
