module.exports = {
  datastore: process.env.SMART_ALERT_DB_NAME,
  tableName: 'alert_subscribers',
  primaryKey: 'id',
  attributes: {
    id: {
      type: 'number',
      autoIncrement: true,
    },
    alert_id: {
      type: 'ref',
      description: 'FK -> references the id column in the alert_template table'
    },
    alert_template_ref_id: {
      type: 'number',
      description: 'FK -> references the id column in the alert_template table'
    },
    site_id: {
      type: 'string',
      description: 'The site where the alert is generated'
    },
    device_id: {
      type: 'string',
      description: 'The device where the alert is generated'
    },
    subscriber_id: {
      type: 'string',
    },
    status: {
      type: 'number',
      defaultsTo: 1,
      description: '1=>subscribed,0=>unsubscribed'
    },
    priority: {
      type: 'number',
      defaultsTo: 1,
      description: '0=>critical,1=>normal'
    },
    subscribed_at: {
      type: 'ref',
      columnType: 'timestamp',
    },
    unsubscribed_at: {
      type: 'ref',
      columnType: 'timestamp',
    },
    subscribe_at: {
      type: 'ref',
      columnType: 'timestamp',
    },
    unsubscribe_at: {
      type: 'ref',
      columnType: 'timestamp'
    },
    notify_on_email: {
      type: 'boolean',
      defaultsTo: true,
    },
    notify_on_whatsapp: {
      type: 'boolean',
      defaultsTo: false,
    },
    notify_on_sms: {
      type: 'boolean',
      defaultsTo: false,
    },
    paused_till: {
      type: 'ref',
      columnType: 'timestamp',
    },
    createdAt: {
      type: 'ref',
      columnType: 'timestamp',
      autoCreatedAt: true,
    },
    updatedAt: {
      type: 'ref',
      columnType: 'timestamp',
      autoUpdatedAt: true,
    },
    user_id: {
      type: 'string',
      description: 'The user who wants to subscribe/unsubscribe'
    }
  },
  beforeUpdate: function (values, next) {
    if (values.status == 0) {
      values.unsubscribe_at = new Date();
      values.subscribe_at = null;
    } else if (values.status == 1) {
      values.subscribe_at = new Date();
      values.unsubscribe_at = null;
    }
    return next();
  },
  beforeCreate: function (values, next) {
    values.subscribe_at = new Date();
    values.subscribed_at = values.subscribed_at || new Date();
    return next();
  }
};
