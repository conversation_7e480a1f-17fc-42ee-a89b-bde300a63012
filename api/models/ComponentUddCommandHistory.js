/**
 * ComponentUddCommandHistory.js
 * @description :: Stores the history of Mode changes of each device
 */
module.exports = {
  datastore: "postgres",
  tableName: "component_udd_command_history",
  primaryKey: "id",
  attributes: {
    id: {
      type: "number",
      autoIncrement: true,
    },
    created_by: {
      type: "string",
      required: true,
    },
    createdAt: {
      type: "ref",
      columnType: "timestamp",
      columnName: 'created_at',
    },
    updatedAt: {
      type: "ref",
      columnType: "timestamp",
      columnName: 'updated_at',
    },
    siteId: {
      type: "string",
      columnName: 'site_id',
      required: true,
    },
    componentId: {
      type: "string",
      columnName: 'comp_id',
      required: true,
    },
    requestId: {
      type: "string",
      columnName: 'request_id',
      unique: true,
      required: true,
    },
    abbr: {
      type: "string",
      required: true,
    },
    value: {
      type: "number",
      required: true,
    },
    status: {
      type: "number",
      isIn: [0, 1, 2], // 0 = Failure, 1 = Success, 2 = Pending
      defaultsTo: 2,
    },
   
  },
  beforeCreate: (values, next) => {
    const timestamp = sails.helpers.getCurrentTimestampIst.with({
      timestampFormat: "YYYY-MM-DD HH:mm:ss",
    });
    values.createdAt = timestamp;
    return next();
  },
  beforeUpdate: function (values, next) {
    values.updatedAt = sails.helpers.getCurrentTimestampIst.with({
      timestampFormat: "YYYY-MM-DD HH:mm:ss",
    });
    return next();
  },
};
