/**
 * AlertInventory.js
 *
 * @description :: Model representing the alert inventory.
 */

module.exports = {
  datastore: process.env.SMART_ALERT_DB_NAME,
  tableName: 'alert_inventory',
  primaryKey: 'id',
  attributes: {
    id: {
      type: 'number',
      autoIncrement: true,
    },
    alert_template_ref_id: {
      type: 'number',
      required: true,
    },
    name: {
      type: 'string',
      required: true,
    },
    description: {
      type: 'string',
    },
    siteId: {
      columnName: 'siteid',
      type: 'string',
      required: true,
    },
    severity: {
      type: 'string',
      isIn: ['low', 'medium', 'high', 'critical'],
      required: true,
    },
    asset_id: {
      type: 'string',
    },
    asset_type: {
      type: 'string',
    },
    escalation_time_in_min: {
      type: 'number',
    },
    escalated_to: {
      type: 'json',
      defaultsTo: [],
    },
    created_by: {
      type: 'string',
    },
    createdAt: {
      type: 'ref',
      columnType: 'timestamp',
      autoCreatedAt: true,
    },
    updatedAt: {
      type: 'ref',
      columnType: 'timestamp',
      autoUpdatedAt: true,
    },
    status: {
      type: 'number',
      defaultsTo: 1,
    },
    observer_execution_ref_id: {
      type: 'string',
    }
  },
};
