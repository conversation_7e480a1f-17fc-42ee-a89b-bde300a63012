/**
 * DeviceType.js
 *
 * @description :: TODO: You might write a short summary of how this model works and what it represents here.
 * @docs        :: http://sailsjs.org/documentation/concepts/models-and-orm/models
 */

module.exports = {

  primaryKey: 'deviceType',

  'attributes': {
    'deviceType': {
      type: 'string',
      description: 'hash',
    },
    'driverType': {
      type: 'string',
      description: 'range',
    },
    'driverName': {
      type: 'string'
    },
    'params': {
      type: 'json'
    },
    'parameters': {
      type: 'json',
      columnType: 'stringSet'
    },
    'communicationCategory': {
      type: 'string'
    },
    'communicationType': {
      type: 'string'
    },
    'class': {
      type: 'string'
    },
    'functionType': {
      type: 'string'
    },
    'svgIds': {
      type: 'json',
      columnType: 'stringSet'
    },
    'mbBatchReadEnable': {
      type: 'string'
    }
  }
};
