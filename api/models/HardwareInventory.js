/**
 * Hardwareinventory.js
 *
 * @description :: Stores Adjustments done in baseline
 */
module.exports = {

  primaryKey: 'hardwareId',

  attributes: {
    hardwareId: {
      type: 'string',
      description: 'hash',
      required: true,
      example: 'ssh',
      // unique hardwareId of a controller
    },
    controllerId: {
      type: 'string',
      description: 'range',
      // deviceId of the controller
    },
    timestamp: {
      type: 'number',
      // UNIX timestamp of when the entry was made. Latest one for any hardwareId confirms it's the most recent and current controllerId.
    },
    source: {
      type: 'string',
      // Source of the Packet. Currently can be 'jouleone' or 'dejoule'
    },
  },

};
