module.exports = {
  datastore: process.env.SMART_ALERT_DB_NAME,
  tableName: 'message_template',
  primaryKey: 'id',
  attributes: {
    id: {
      type: 'number',
      autoIncrement: true,
    },
    name: {
      type: 'string',
      required: true,
      description: 'message template name',
    },
    created_by: {
      type: 'string',
      example: 'prave<PERSON><PERSON>@smartjoules.in'
    },
    status: {
      type: 'number',
      defaultsTo: 1,
      description: '1=>active,0=>deleted'
    },
    last_updated_by: {
      type: 'string',
      example: '<EMAIL>'
    },
    createdAt: {
      type: 'ref',
      columnType: 'timestamp',
      autoCreatedAt: true,
    },
    updatedAt: {
      type: 'ref',
      columnType: 'timestamp',
      autoUpdatedAt: true,
    },
  },
  beforeCreate: function (values, next) {
    values.created_by = sails.config.usersession.id;
    return next();
  },
  beforeUpdate: function (values, next) {
    values.last_updated_by = sails.config.usersession.id;
    return next();
  },
};
