/**
 * Processes.js
 *
 * @description :: Contains schema for different processes.
 * @docs        :: http://sailsjs.org/documentation/concepts/models-and-orm/models
 */

module.exports = {
  'primaryKey': 'processId',
  // 'tableName': 'processes',
  'attributes': {
    'processId': {
      'type': 'string',
      'description': 'hash',
      'required': true
    },
    'siteId': {
      'type': 'string',
      // 'description': 'range',
      'description': 'global-secondary##type',
      // 'index': 'siteId_type_global_index-hash',
    },
    'type': {
      'type': 'string',
      // 'index': 'siteId_type_global_index-range',
    },
    'name': {
      'type': 'string',
    },
    'componentType': {
      'type': 'string',
    },
    'rawParams': {
      'type': 'json',
      'columnType': 'array',
    },
    'calcParams': {
      'type': 'json',
      'columnType': 'array',
    },
    'plantParams': {
      'type': 'json',
      'columnType': 'array',
    },
    'plantDevices': {
      'type': 'json',
      'columnType': 'array',
    },
    'components': {
      'type': 'json',
      'columnType': 'array',
    },
    'controls': {
      'type': 'json',
      'columnType': 'array',
    },
    'isDisplayed': {
      'type': 'boolean',
    },
    'subProcs': {
      'type': 'json',
      'columnType': 'array',
    },
    'regionId': {
      'type': 'string',
    },
    'controllerId': {
      'type': 'string',
    },
    'driverType': {
      'type': 'string',
    },
    'plantCategories': {
      'type': 'json',
      'columnType': 'stringSet',
    },
  },
};

