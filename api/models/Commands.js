module.exports = {
  primaryKey: 'timestamp',
  attributes: {
    timestamp: {
      type: 'string',
    },
    deviceId: {
      type: 'string',
    },
    componentId: {
      type: 'string',
    },
    siteId: {
      type: 'string',
    },
    user: {
      type: 'string',
    },
    value: {
      type: 'string',
    },
    type: {
      type: 'string', // StartStop || SetValue
    },
    param: {
      type: 'string',
    },
    socketID: {
      type: 'string', // 0 for default value and ts for savedTime
    },
    reachedController: {
      type: 'number',
    },
    executed: {
      type: 'number',
    },
    mode: {
      type: 'string',
    },
    selectedJB: {
      type: 'string',
    },
    IPAddr: {
      type: 'string',
    },
    commandError: {
      type: 'number',
    },
    uniqId: {
      type: 'string',
    },
    component_abbr: {
      type: 'string',
    },
    controlAbbr: {
      type: 'string'
    },
    controlType: {
      type: 'string',
      isIn: ['BIT', 'VIT']
    }
  },
  beforeCreate: function (value, next) {
    value.createdAt = value.createdAt && new Date(value.createdAt).toISOString() || new Date().toISOString();
    value.updatedAt = value.createdAt && new Date(value.createdAt).toISOString() || new Date().toISOString();
    return next();
  },
  beforeUpdate: function (value, next) {
    value.updatedAt = new Date().toISOString();
    return next();
  }

};
