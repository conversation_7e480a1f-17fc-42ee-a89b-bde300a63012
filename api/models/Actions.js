module.exports = {
  primaryKey: 'triggerTime',
  attributes: {
    triggerTime: {
      'type': 'string',
      'description': 'hash',
      'example': '157492624300'//<Time stamp in unix>
    },
    sourceId: {
      type: 'string',
      description: `\n          uniqCmd from JT = site_2238.cmd\nuniqCmd from JR = site_alertId/2238.cmd/rId (rId here will be recipe to execute, in sourceInfo the rid will be recipe who asked to run this recipe )\n          in the case of schedule scheduling recipe this will be site_scheduleId\nsite_alertId/cmdId in case of recipe sei comming , else site_did.cmd`,
      example: 'ssh_ff1363f9-1beb-4a68-bfdc-32900cfb0fb7'
    },
    source: {
      type: 'string',
    },
    sourceInfo: {
      type: 'string',
      description: `
          in case of server cmds site_userId of user, else site_recipeId, dont know how to handle PID here, maybe
          only when this action is given by recipe this will be site_recipeId
          in case of schedule this will be site_userId
      `,
    },
    uniqId: {
      type: 'string',
      description: 'global-secondary##'
    },
    type: {
      type: 'string',
      description: '(alert/command/recipe=trigger the other recipe) on bases of this, info is opened'
    },
    response: {
      type: 'string',
      description: 'gets timestamp when action is done, false not done, -1 no feedback(default -1)'
    },
    runOn: {
      type: 'string',
      description: 'possible value can server or controller'
    },
    isInitiated: {
      type: 'string',
      description: 'true/false tells if this command is already sent to execution or not, just to avoid normal command and scheduler command collide'
    },
    rcCode: {
      type: 'string',
      description: 'Code received from controller about the state'
    },
    nest: {
      'type': 'string'
    },
    reachedJb: {
      type: 'string',
      description: '0 or timestamp'
    },
    reachedCtrl: {
      type: 'string',
      description: '0 or timestamp'
    },
    info: {
      type: 'string',
      description: 'now on the bases of type this is json accordingly'
    },
    siteId: {
      type: 'string',
      description: 'global-secondary##triggerTime'
    },
  },
  beforeCreate: function (value, next) {
    value.createdAt = value.createdAt && new Date(value.createdAt).toISOString() || new Date().toISOString();
    value.updatedAt = value.createdAt && new Date(value.createdAt).toISOString() || new Date().toISOString();
    return next();
  },
  beforeUpdate: function (value, next) {
    value.updatedAt = new Date().toISOString();
    return next();
  }
};
