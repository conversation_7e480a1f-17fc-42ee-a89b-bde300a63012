module.exports = {
  datastore: "postgres",
  tableName: "configurator_systems",
  primaryKey: "id",
  attributes: {
    id: {
      type: "number",
      autoIncrement: true,
    },
    name: {
      type: "string",
      columnType: "varchar(255)",
      example: "Floor",
      description: "Name of the node.",
      required: true,
    },
    order: {
      type: 'number',
      defaultsTo: 0
    },
    description: {
      type: "string",
      description: "description text",
    },
    data_tagger: {
      type: "json",
      description: "data tagger",
    },
    icon: {
      type: "string",
      description: "icon svg url",
    },
    created_by: {
      type: "string",
    },
    updated_by: {
      type: "string",
    },
    status: {
      type: "number",
      isIn: [0, 1],
      defaultsTo: 1,
      description: "0 -> deleted, 1 -> active",
    },
    system_id: {
      type: "number",
      required: true,
    },
    site_id: {
      type: "string",
      required: true,
    },
    svg_path: {
      type: "string",
      description: "rapid_plant_page.svg_path | svg_name",
    },
    createdAt: {
      type: "ref",
      columnType: "timestamp",
    },
    updatedAt: {
      type: "ref",
      columnType: "timestamp",
    },
  },

  beforeCreate: function (values, next) {
    const timestamp = sails.helpers.getCurrentTimestampIst.with({
      timestampFormat: "YYYY-MM-DD HH:mm:ss",
    });
    values.createdAt = timestamp;
    values.updatedAt = timestamp;
    return next();
  },
  beforeUpdate: function (values, next) {
    values.updatedAt = sails.helpers.getCurrentTimestampIst.with({
      timestampFormat: "YYYY-MM-DD HH:mm:ss",
    });
    return next();
  },
};
