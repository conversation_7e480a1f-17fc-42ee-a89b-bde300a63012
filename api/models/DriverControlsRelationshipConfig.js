/**
 * DriverControlsRelationshipConfig.js
 * @description :: Stores the history of Mode changes of each device
 */
module.exports = {
  datastore: "postgres",
  tableName: "driver_controls_relationship_config",
  primaryKey: "id",
  attributes: {
    id: {
      type: "number",
      autoIncrement: true,
    },
    deviceType: {
      type: "string",
      required: true,
      example: "chiller",
      columnName: "devicetype",
    },
    driverType: {
      type: "string",
      required: true,
      example: "0",
      columnName: "drivertype",
    },
    controlName: {
      type: "string",
      required: true,
      example: "Turn On/Off Chiller",
      columnName: "control_name",
    },
    controlAbbr: {
      type: "string",
      required: true,
      example: "turnOnOffChiller",
      columnName: "control_abbr",
    },
    controlType: {
      type: "string",
      required: true,
      isIn: ["BIT", "VIT"],
      description:
        "BIT=>Binary Value Command Type e.g turnOnOffChiller, BIT=>Variable Value Command Type e.g changeChillerSetPoint",
      columnName: "control_type",
    },
    controlClass: {
      type: "string",
      // required: true,
      // isIn:['controlChillerStatus'],
      description: "controlChillerStatus",
      columnName: "control_class",
    },
    description: {
      type: "string",
      required: false,
      description: "description about control",
      columnName: "description",
    },
    dataParamAbbr: {
      type: "string",
      required: true,
      columnName: "data_param_abbr",
      example: "status abbr is used to know the current state of chiller",
    },
    controlProperty: {
      type: "json",
      example: "{}",
      required: true,
      columnName: "control_property",
    },
    status: {
      type: "number",
      columnName: "status",
      defaultsTo: 1,
      description: "1=>active, 0=>inactive/deleted",
    },
    componentPageViewOrder: {
      type: "number",
      columnName: "component_page_view_order",
      example: 0,
    },
    createdBy: {
      type: "string",
      columnName: "created_by",
    },
    createdAt: {
      type: "ref",
      columnType: "timestamp",
    },
    updatedAt: {
      type: "ref",
      columnType: "timestamp",
    },
  },
  beforeCreate: function (values, next) {
    const timestamp = sails.helpers.getCurrentTimestampIst.with({
      timestampFormat: "YYYY-MM-DD HH:mm:ss",
    });
    values.createdAt = timestamp;
    values.updatedAt = timestamp;
    return next();
  },
  beforeUpdate: function (values, next) {
    values.updatedAt = sails.helpers.getCurrentTimestampIst.with({
      timestampFormat: "YYYY-MM-DD HH:mm:ss",
    });
    return next();
  },
};
