/**
 * MaintenanceModesAudit.js
 * @description :: Stores the history of Mainteannce Mode changes of each component/device
 */
module.exports = {
    datastore:'postgres',
    tableName: 'maintenance_modes_audit',
    primaryKey: 'id',
    attributes: {
      id: { type: 'number', autoIncrement: true, },
      deviceId: {
        type: 'string',
        required: true,
        example: 'mgch_1',
        columnName:'device_id'
      },
      deviceClass: {
        type: 'string',
        required: true,
        example: 'component',
        columnName:'device_class'
      },
      siteId: {
        type: 'string',
        required: true,
        example: 'mgch',
        columnName:'site_id'
      },
      startTime: {
        type: 'ref',
        columnType: 'timestamp',
        required: true,
        columnName:'start_time'
      },
      endTime: {
        type: 'ref',
        columnType: 'timestamp',
        required: false,
        columnName:'end_time'
      },
      startedBy: {
        type: 'string',
        required: true,
        columnName:'started_by',
      },
      stoppedBy: {
        type: 'string',
        required: false,
        columnName:'stopped_by',
      },
      createdAt: { type: 'ref', columnType: 'timestamp'},
      updatedAt: { type: 'ref', columnType: 'timestamp'},
  
    },
    beforeCreate: function (values, next) {
      values.createdAt = values.startTime;
      values.updatedAt = values.startTime;
      return next();
    },
    beforeUpdate: function (values, next) {
      values.updatedAt = values.endTime;
      return next();
    }
  };
  