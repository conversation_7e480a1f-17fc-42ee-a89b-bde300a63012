const moment = require('moment-timezone');
const { parseDateToUTC } = require('../../utils/globalhelper');
const alertService = require('../../services/alerts/alerts.service');

module.exports = {
  friendlyName: 'Count Alerts',
  description: 'Get count of alerts',

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: {
        id: 'userId',
        _role: 'role',
        _site: 'siteId'
      },
    },
    siteId: {
      type: 'string',
      required: true,
      description: 'Site ID to fetch alerts for'
    },
    startTime: {
      type: 'string',
      required: false
    },
    endTime: {
      type: 'string',
      required: false
    },
  },

  exits: {
    success: { description: 'Successfully retrieved alerts' },
    badRequest: {
      statusCode: 400,
      description: 'Invalid parameters'
    },
    serverError: {
      statusCode: 500,
      description: 'Server error occurred'
    }
  },

  fn: async function (inputs, exits) {
    try {
      const {
        siteId,
        startTime,
        endTime,
        _userMeta: { id: userId }
      } = inputs;
      if (!_validateTimeInputs(startTime, endTime)) {
        return exits.badRequest({ error: 'Both startTime and endTime must be provided together, and startTime must be before endTime' });
      }

      const timezone = await sails.helpers.getSiteTimezone.with({
        siteId,
        timezoneFormat: 'tz'
      });
      const {
        start,
        end
      } = _getTimeRange(startTime, endTime, timezone);

      const alertCount = await alertService.getAlertCount({
        siteId,
        start,
        end,
        userId
      });
      return exits.success({
        siteId,
        totalActiveAlerts: Number.isNaN(Number(alertCount?.total_alert_count)) ? 0 : Number(alertCount?.total_alert_count),
        myActiveAlerts: Number.isNaN(Number(alertCount?.alert_count_for_subscriber)) ? 0 : Number(alertCount?.alert_count_for_subscriber)
      });
    } catch (error) {
      sails.log.error(`Error in count Alerts: ${error}`);
      return exits.serverError(error);
    }
  }
};

function _validateTimeInputs(startTime, endTime) {
  if ((startTime && !endTime) || (!startTime && endTime)) return false;
  return !(startTime && endTime && parseDateToUTC(startTime) >= parseDateToUTC(endTime));
}

function _getTimeRange(startTime, endTime, timezone) {
  if (startTime && endTime) {
    return {
      start: parseDateToUTC(startTime),
      end: parseDateToUTC(endTime)
    };
  }
  return {
    start: moment()
      .tz(timezone)
      .startOf('day')
      .utc()
      .toISOString(),
    end: moment()
      .tz(timezone)
      .utc()
      .toISOString()
  };
}
