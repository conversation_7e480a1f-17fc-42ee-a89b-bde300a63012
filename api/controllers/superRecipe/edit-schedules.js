const { validateScheduleData } = require('../../utils/super_recipe/schedule.util');
const scheduleService = require('../../services/superRecipe/schedule/schedule.public');
const recipeService = require('../../services/superRecipe/recipe.public');
const RecipeIotCommunicationInterface = require('../../services/superRecipe/lib/recipe.iot.communication.interface');

module.exports = {
  friendlyName: 'Edit Schedules',
  description: 'Edit existing Super Recipe Schedules',
  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: {
        id: 'userId',
        _role: 'role',
        _site: 'siteId',
      },
      description: 'User meta information added by default to authenticated routes',
    },
    siteId: {
      type: 'string',
      required: true,
      example: 'gob-coi',
    },
    recipeId: {
      type: 'string',
      required: true,
      example: '42',
    },
    scheduleInfo: {
      type: 'json',
      required: true,
      description: 'Payload containing updated schedule data',
    },
  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[superRecipe -> edit-schedules] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[superRecipe -> edit-schedules] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[superRecipe -> edit-schedules] Forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[superRecipe -> edit-schedules] Not Found',
    },
    success: {
      statusCode: 200,
      description: '[superRecipe -> edit-schedules] Schedules updated successfully',
    },
  },

  fn: async (inputs, exits) => {
    try {
      const {
        recipeId,
        siteId,
        scheduleInfo
      } = inputs;

      if (!recipeId || !siteId) {
        return exits.badRequest({ message: 'recipeId and siteId are required' });
      }

      const recipe = await recipeService.findOne({
        id: recipeId,
        status: 1,
        is_recipe_template: 0,
        site_id: siteId,
      });

      if (!recipe) {
        return exits.badRequest({ message: `No active recipe found with id=${recipeId} and siteId=${siteId}` });
      }

      const scheduleInfoData = scheduleInfo.map((item) => ({
        ...item,
        recipe_id: +recipeId,
      }));

      /**Ensure all schedules have an ID*/
      const missingIds = scheduleInfoData.filter(item => !item.id);
      if (!_.isEmpty(missingIds)) {
        return exits.badRequest({ message: 'Each schedule must have a valid ID to be edited.' });
      }

      validateScheduleData(scheduleInfoData);

      const updatedSchedules = await Promise.all(
        scheduleInfoData.map(item => {
          RecipeIotCommunicationInterface.deleteSuperRecipeScheduleFromController(siteId, recipe?.run_on, recipe?.rid, item.id);
          return scheduleService.update(item.id, item);
        })
      );

      return exits.success({
        message: 'Schedules successfully updated',
        updatedSchedules,
      });

    } catch (error) {
      sails.log.error('[superRecipe -> edit-schedules]', error);
      switch (error.code) {
        case 'E_MISSING_FIELDS':
        case 'E_INPUT_VALIDATION':
        case 'E_INVALID_TIME_RANGE':
          return exits.badRequest({ message: error.message });
        case 'E_FORBIDDEN':
          return exits.forbidden({ message: 'You are not authorized to perform this action.' });
        case 'E_NOT_FOUND':
          return exits.notFound({ message: error.message });
        case 'E_DB_ERROR':
        default:
          return exits.serverError(error);
      }
    }
  }
};
