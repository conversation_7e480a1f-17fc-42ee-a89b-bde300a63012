const recipeService = require('../../services/superRecipe/recipe.public');
const scheduleService = require('../../services/superRecipe/schedule/schedule.public');
const RecipeIotCommunicationInterface = require('../../services/superRecipe/lib/recipe.iot.communication.interface');

module.exports = {
  friendlyName: 'delete-schedule-by-id',
  description: 'Deletes Schedule by id',
  inputs: {
    siteId: {
      type: 'string',
      required: true,
      example: 'gob-coi',
    },
    recipeId: {
      type: 'string',
      required: true,
      example: '123',
    },
    scheduleId: {
      type: 'string',
      required: true,
      example: '123',
    },
    recipeType: {
      type: 'string',
      required: true,
      example: 'action',
    }
  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[superRecipe -> delete-schedule-by-id] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[superRecipe -> delete-schedule-by-id] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[superRecipe -> delete-schedule-by-id] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[superRecipe -> delete-schedule-by-id] Not Found',
    },
    success: {
      statusCode: 200,
      description: '[superRecipe -> delete-schedule-by-id] Schedule Deleted successfully',
    },
  },
  fn: async (inputs, exits) => {
    try {
      const {
        scheduleId,
        siteId,
        recipeId,
        recipeType,
      } = inputs;

      if (!scheduleId || !siteId || !recipeId) {
        return exits.badRequest({ message: 'scheduleId/siteId/recipeId is required' });
      }
      if (!['action', 'alert'].includes(recipeType)) {
        return exits.badRequest({ message: 'Type of recipe can be action or alert only' });
      }
      const recipeData = await recipeService.getActionRecipeInfoById(recipeId, recipeType, siteId);
      const scheduleData = await scheduleService.findOne(scheduleId);
      if (recipeData?.error || _.isEmpty(scheduleData)) {
        return exits.badRequest({ message: 'Recipe/Schedule not found' });
      }

      const response = await scheduleService.delete({ uniqId: scheduleData?.uniqId });
      await RecipeIotCommunicationInterface.deleteSuperRecipeScheduleFromController(siteId, recipeData?.run_on, recipeData?.rid, scheduleId);
      return exits.success({
        message: `Schedule with id=${scheduleId} deleted`,
        response
      });
    } catch (error) {
      sails.log.error('[superRecipe -> delete-schedule-by-id]', error);
      if (error.code === 'E_NOT_FOUND') {
        return exits.badRequest({ message: error.message });
      }
      return exits.serverError(error);
    }

  }
};
