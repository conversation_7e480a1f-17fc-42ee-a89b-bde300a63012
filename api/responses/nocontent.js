/**
 * no-content.js
 *
 * A custom response that will be sent in case of when db.find found nothing
 *
 * Example usage:
 * ```
 *     return res.expired();
 * ```
 *
 * Or with actions2:
 * ```
 *     exits: {
 *       badToken: {
 *         description: 'Provided token was expired, invalid, or already used up.',
 *         responseType: 'expired'
 *       }
 *     }
 * ```
 */
module.exports = function noContent() {
  var req = this.req;
  var res = this.res;
  sails.log.verbose('Ran custom response: res.nocontent()');

  res.status(204);
  res.send();
};
