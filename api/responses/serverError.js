/**
 * expired.js
 *
 * A custom response that content-negotiates the current request to either:
 *  • serve an HTML error page about the specified token being invalid or expired
 *  • or send back 498 (Token Expired/Invalid) with no response body.
 *
 * Example usage:
 * ```
 *     return res.expired();
 * ```
 *
 * Or with actions2:
 * ```
 *     exits: {
 *       badToken: {
 *         description: 'Provided token was expired, invalid, or already used up.',
 *         responseType: 'expired'
 *       }
 *     }
 * ```
 */
const Sentry = require("../services/logTransport/sentry.service");

module.exports = function serverError(data) {
  let { req } = this;
  let { res } = this;
  sails.log.error(data);
  const url = req.originalUrl.split("?")[0];
  Sentry.setTag("url", url);
  const requestDetail = {
    method: req.method,
    body: JSON.stringify(req.body),
    queryParams: req.query,
    params: req.params,
    originalUri: req.originalUrl,
    referer: req.headers.referer || '',
    ua: req.headers['user-agent'],
  }
  if (req.headers['x-amzn-trace-id']) {
    requestDetail.x_amzn_trace_id = req.headers['x-amzn-trace-id'];
  }
  if (req.headers['x-amzn-request-id']) {
    requestDetail.x_amzn_request_id = req.headers['x-amzn-request-id'];
  }
  if (req.headers['x-transaction-id']) {
    requestDetail.transaction_id = req.headers['x-transaction-id'];
  }

  Sentry.setContext('Request Details', requestDetail);
  Sentry.setContext("Error Object", {
    errorObject: JSON.stringify(data),
    error: data
  });
  if (data instanceof Error) Sentry.captureException(data)
  else Sentry.captureException(Error(`Request URL: ${url}`, data || 'Empty Server Error captured'));
  sails.log.verbose('Ran custom response: res.serverError()');
  if (req.wantsJSON) {
    res.status(500);
    if (process.env.NODE_ENV === 'production') res.send();
    else res.json(data);
  } else {
    return res.status(500).send();
  }
};
