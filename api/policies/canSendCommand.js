module.exports = async function (req, res, next) {
  const {
    _userMeta: {
      _role: roleName,
    }
  } = req.body;
  const RoleDetail = await Roles.find({ roleName });
  if (!RoleDetail.length) {
    return res.status(401)
      .send({
        err: `This role ${roleName} does not exists`
      });
  }
  const policies = JSON.parse(RoleDetail[0].policies);
  if (policies['AC Plant']['subHeadings']['command']['policies']['write']['hasAccess']) {
    return next();
  } else {
    return res.status(401)
      .send({
        err: `You do not have access to give command`
      });
  }
};
