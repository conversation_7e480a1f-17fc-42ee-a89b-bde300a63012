const authService = require("../services/auth/auth.public");

module.exports = function (req, res, next) {
  if (!req.headers || !req.headers.authorization) {
    return res.forbidden({ problems: ["Unauthorized: Missing Authorization header"] });
  }

  if (!req.body) req.body = {};

  const authorizationToken = req.headers.authorization;
  const [authBearer, authToken] = authorizationToken.split(" ");

  if (authBearer.toLowerCase() !== "bearer") {
    return res.badRequest({ problems: ["Invalid Auth token: Bearer token expected"] });
  }

  try {
    const decodedAuthToken = authService.verifyJWTToken(authToken);
    if (decodedAuthToken) {
      req.body._userMeta = decodedAuthToken;
      return next();
    }
  } catch (e) {
    if (e.name === "TokenExpiredError") {
      return res.forbidden({ problems: ["Session Expired"] });
    }
    return res.forbidden({ problems: ["Invalid Token"] });
  }
};
