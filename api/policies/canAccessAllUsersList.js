module.exports = async function (req, res, next) {
    const {
      _userMeta: { _role: roleName },
    } = req.body;
    const roleDetails = await Roles.find({ roleName });
    if (!roleDetails || !roleDetails.length) {
      return res.status(401).send({
        err: `This role ${roleName} does not exists`,
      });
    }
    const rolePolicies = JSON.parse(roleDetails[0].policies);
  
    const userPolicies = rolePolicies?.User?.subHeadings?.user?.policies;
    if (!userPolicies || !rolePolicies?.User?.pageView || !userPolicies?.read?.hasAccess) {
      return res.status(403).send({
        message: `You don't have permission to access all users' list.`,
        errorCode: `READ_OPERATION_NOT_PERMITTED`,
      });
    }
    return next();
  };
  