module.exports = async function (req, res, next) {
  const {
    _userMeta: {
      _role: roleName,
    }
  } = req.body;
  const RoleDetail = await Roles.find({ roleName });
  if (!RoleDetail.length) {
    return res.status(401)
      .send({
        err: `This role ${roleName} does not exists`
      });
  }
  const rolePolicies = JSON.parse(RoleDetail[0].policies);
  const configPolicies = rolePolicies?.Configuration?.subHeadings?.config?.policies;
  if (configPolicies && configPolicies.create?.hasAccess) {
    return next();
  } else {
    return res.status(403)
      .send({
        message: `You don't have permission to perform this operation. Please contact PD team`,
        errorCode: `CREATE_OPERATION_NOT_PERMITTED`
      });
  }
};
