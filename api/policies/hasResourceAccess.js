const allPolicies = require("../../config/policy.json");
const roleService = require("../services/role/role.public");

module.exports = async function (req, res, next) {
  const {
    _userMeta: { _role: roleName, id: userId },
  } = req.body;

  const roleDetailsCache = await roleService.getRBACPolicyCache(roleName);
  let rolePolicies = {};
  if (!roleDetailsCache) {
    const roleDetails = await roleService.find({ roleName });
    if (!roleDetails.length) {
      return res.status(401).send({
        err: `This role ${roleName} does not exist.`,
      });
    }
    rolePolicies = JSON.parse(roleDetails[0].policies);
    await roleService.setRBACPolicyCache(roleName, rolePolicies);
  } else {
    rolePolicies = roleDetailsCache;
  }

  const requestedAction = req?.options?.action || null;

  if (!requestedAction) {
    return next();
  }

  if (isActionPermitted(allPolicies, requestedAction, roleName, userId, rolePolicies)) {
    return next();
  } else {
    return res.status(403).send({
      message: `You don't have permission to perform this operation. Please contact the support team.`,
      errorCode: `RBAC_NOT_ALLOWED`,
    });
  }
};

function isActionPermitted(deJouleRolePolicies, action, roleName, userId, userSiteRolePolicies) {
  for (const module in deJouleRolePolicies) {
    const subHeadings = deJouleRolePolicies[module]?.subHeadings || {};
    for (const subHeadingKey in subHeadings) {
      const featurePolicyGroup = subHeadings[subHeadingKey]?.policies || {};
      for (const policyKey in featurePolicyGroup) {
        const routePolicyMap = featurePolicyGroup?.[policyKey]?.routePolicyMap || {};
        const actions = routePolicyMap.actions || [];
        if (actions.includes(action)) {
          return userSiteRolePolicies?.[module]?.["subHeadings"]?.[subHeadingKey]?.["policies"]?.[
            policyKey
          ]?.["hasAccess"];
        }
      }
    }
  }
  sails.log(
    `[RBAC-ACTION-PERMISSION] ${action} is not exist in policy of role=${roleName} of userId=${userId}`
  );
  return true;
}
