
const configuratorPageService = require('../services/configuratorPage/configuratorPage.public')
module.exports = async function (req, res, next) {
  const {
    pageId,
  } = req.params;

  const isPageInEditableMode = await configuratorPageService.findOne({
    id: pageId,
    status: 1,
  })

  if (!isPageInEditableMode) {
    return res.status(400)
    .send({
      err: `Page does not exist`
    });
  }

  // isPublished [0-> draft, 1-> published]
  if (isPageInEditableMode.isPublished != 0) {
    return res.status(422)
      .send({
        err: `Page is not in editable mode`
      });
  }
  return next();
}
