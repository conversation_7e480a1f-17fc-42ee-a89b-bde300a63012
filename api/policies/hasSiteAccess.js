const authService = require('../services/auth/auth.public');

module.exports = function (req, res, next) {

  const userMeta = req.body._userMeta;
  if (!userMeta) {
    return res.forbidden({ problems: ['Incorrect middleware call. _userMeta not found in the request.'] });
  }
  const params = req.params;
  const { _site: tokenSiteId, id: userId } = userMeta;
  if (!params.siteId || tokenSiteId === params.siteId) {
    return next();
  }
  authService.userHasSiteAccess(userId, params.siteId).then(result => {
    if (result) {
      return next();
    }
    return res.forbidden({ problems: ['You do not have access of this site. Please login again'] });
  });
};
