/**
 * is-logged-in
 *
 * A simple policy that allows any request from an authenticated user.
 *
 * For more about how to use policies, see:
 *   https://sailsjs.com/config/policies
 *   https://sailsjs.com/docs/concepts/policies
 *   https://sailsjs.com/docs/concepts/policies/access-control-and-permissions
 */
module.exports = async function (req, res, proceed) {

  // If `req.me` is set, then we know that this request originated
  // from a logged-in user.  So we can safely proceed to the next policy--
  // or, if this is the last policy, the relevant action.
  // > For more about where `req.me` comes from, check out this app's
  // > custom hook (`api/hooks/custom/index.js`).
  if(req.body){
    req.body['_userMeta'] = { id: '-testing-' };
  }else{
    req.body = {'_userMeta' : '-testing-'};
  }

  return proceed();
  // let token = undefined;

  // if (req.headers && req.headers.authorization) {
  //   let parts = req.headers.authorization.split(' ');

  //   if (parts.length === 2) {

  //     let scheme = parts[0];
  //     let credentials = parts[1];

  //     if ('Bearer' === scheme) {
  //       token = credentials;
  //     }
  //   } else {
  //     return res.json(401, { 'err': 'Format is Authorization: Bearer [token]' });
  //   }
  // } else if (req.param('token')) {
  //   token = req.param('token');
  //   // We delete the token from param to not mess with blueprints
  //   let parts = req.headers.authorization.split(' ');
  //   if (parts.length === 2) {
  //     let scheme = parts[0];
  //     let credentials = parts[1];

  //     if ('Bearer' === scheme) {
  //       token = credentials;
  //     }
  //   } else {
  //     return res.json(401, { 'err': 'Format is Authorization: Bearer [token]' });
  //     //return res.unauthorized();
  //   }
  //   delete req.query.token;
  // } else {
  //   return res.json(401, { 'err': 'No Authorization header was found' });
  //   //return res.unauthorized();
  // }
  // jwtService.verify(token, function (err, verifiedToken) {
  //   if (err) {
  //     let decoded = jwt.decode(token);
  //     sails.log.error(err, req.method, req.path, decoded);
  //     if (err.name == 'TokenExpiredError') {
  //       //Todo : Remove this when new login mech implemented on frontend
  //       let decode = jwt.decode(token);
  //       req.user = decode.id;
  //       return res.json(401, { 'err': 'Token Expired' });
  //     } else {

  //       return res.json(401, { 'err': 'Invalid Token!' });
  //     }
  //   }

  //   let user = verifiedToken.id;
  //   req.user = user;
  //   req._userMeta = verifiedToken;
  //   next();
  // });
  // return proceed();

};
