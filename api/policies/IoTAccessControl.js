const jwt = require('jsonwebtoken');
const { tokenSecret, validSources, validAppIds } = sails.config.IOT_AUTH_CONFIG;

module.exports = async function (req, res, next) {
  const token = req.headers['x-authorization-token'];
  if (!token) return res.status(401).json({ message: 'No token provided' });
  jwt.verify(token, tokenSecret, (err, decoded) => {
    if (err) return res.status(401).json({ message: 'Failed to authenticate token.' });
    if (!validSources.includes(decoded.source) || !validAppIds.includes(decoded.appId)) return res.status(401).json({ message: 'Invalid app ID or source.' })
    req.user = decoded;
    next();
  });
}
