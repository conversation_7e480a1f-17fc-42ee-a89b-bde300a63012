const authService = require('../services/auth/auth.public');
module.exports = function (req, res, next) {
  let proxyHosts = sails.config.allowedHost ? sails.config.allowedHost : [];
  if (!req.headers || !req.headers.authorization) {
    return res.forbidden({ problems: ['Unauthorized Authorization'] });
  }

  if (!req.body) req.body = {};
  let decodedAuthToken;
  let authorizationToken = req.headers.authorization;
  let [authBearer, authToken] = authorizationToken.split(' ');
  if (authBearer.toLowerCase() !== 'bearer') return res.badRequest({ problems: ['Invalid Auth token'] });
  try {
    decodedAuthToken = authService.verifyJWTToken(authToken);
  } catch (e) {
    if (e.name === 'TokenExpiredError') {
      return res.forbidden({ problems: ['Session Expired'] });
    } else {
      return res.forbidden({ problems: ['Invalid Token!'] });
    }

  }
  req.body._userMeta = decodedAuthToken;
  next();
};
