const configuratorSystemService = require('../services/ConfiguratorSystem/configuratorSystem.public');

module.exports = async function (req, res, next) {
  const {
    siteId,
    systemId
  } = req.params;
  if (!await configuratorSystemService.isSystemIsInEditableMode(systemId, siteId)) {
    return res.status(422)
      .send({
        err: `System is not in editable mode`
      });
  }
  return next();
}
