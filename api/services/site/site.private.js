/* eslint-disable no-undef */
module.exports = {

  /**
   * Sites module private functions
   */
  create: async (params) => {
    return Sites.create(params);
  },
  find: async (searchParams) => {
    // Sites.find
    searchParams.status = 1
    return Sites.find(searchParams);
  },
  findOne: async (searchParams) => {
    // Sites.findone
    searchParams.status =  1
    return Sites.findOne(searchParams);
  },
  update: async (searchParams, updateValue) =>{
    return Sites.update(searchParams, updateValue);
  },
  delete: async (searchParams) => {
    return Sites.destroy(searchParams);
  },
};
