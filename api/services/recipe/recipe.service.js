const componentService = require('../component/component.public');
const createRecipeUtil = require('../../utils/recipe/createrecipe.util');
const createScheduleUtil = require('../../utils/recipe/createschedule.util');
const deviceService = require('../device/device.public');
const eventService = require('../event/event.public');
const processService = require('../../services/process/process.public');
const recipeService = require('./recipe.private');
const cacheService = require('../cache/cache.public');
const socketService = require('../socket/socket.public');
const rabbitMQService = require('../rabbitmq/rabbitmq.public');
const userService = require('../user/user.service');
const componentError = require('../../utils/component/error');
const recipeErrorUtils = require('../../utils/recipe/error');
const ThermostatRecipe = require('./lib/ThermostatRecipe');
const globalHelper = require('../../utils/globalhelper');
const utils = require('../../utils/recipe/utils');
const sentry = require('../../services/logTransport/sentry.service');
const flaverr = require('flaverr');
const { v4: uuidv4 } = require('uuid');
const iotCoreService = require("../iotCore/iotCore.public");

let syncService = null;
process.nextTick(() => {
  syncService = require('../sync/sync.public');
});

const RECIPE_QUEUE = new rabbitMQService.consumerWithAckQueue('RECIPE_QUEUE');
exports = {
  find: recipeService.find,
  findOne: recipeService.findOne,
  create: recipeService.create,
  update: recipeService.update,
  destroy: recipeService.destroy,
  schedules: recipeService.schedules,
  updateRecipeByRecipeId: recipeService.updateRecipeByRecipeId,
  RECIPE_QUEUE,

  /* on message received from the  RECIPE_QUEUE */
  async handleTopicFromRabbitMQ(data) {
    try {
      const {
        topic,
        message
      } = globalHelper.toJson(data.content.toString());
      eventHandler(topic, globalHelper.toJson(message));
      return true;
    } catch (e) {
      sails.log.error(e);
      throw e;
    }
  },

  /**
   * This function gets the recipeIsActive timestamp from the redis cache
   * @param {rid} rid Recipe ID
   */
  getRecipeIsActive: async function (rid) {
    try {
      return await cacheService.get(utils.getRecipeIsActiveKey(rid));
    } catch (e) {
      sails.log.error('Error in recipeService.getRecipeIsActive: ', e);
      return false;
    }
  },

  /**
   * This function sets the recipeIsActive timestamp in the redis cache
   * @param {rid} rid Recipe ID
   * @param {value} value Integer UNIX timestamp
   */
  setRecipeIsActive: async function (rid, value) {
    try {
      let isActiveKey = utils.getRecipeIsActiveKey(rid);
      await cacheService.set(isActiveKey, value);
      await cacheService.expire(isActiveKey, utils.LAST_IS_ACTIVE_DAYS * 24 * 60 * 60);
    } catch (e) {
      sails.log.error('Error in recipeService.setRecipeIsActive: ', e);
      return false;
    }
  },

  /**
   * Given list of devices and formula try to find best fit (controller) to run the recipe on (if preference is not server else just send to server)
   * MODIFICATION: runOn is no longer supposed to be "server". If component parameters are detected in the list, it finds the best controllerId based on the
   * most deviceId occurrences in the component parameter calculation.
   * It still returns "server" in case no devices are passed, ie the formula is empty. Case should be investigated, if such a recipe can actaully be configured.
   * @param {array} deviceIds List of deviceIds
   * @param {string} formula Formula string of recipe-logic
   * @param {string} runOnPreference enum(server, "") where server means run thos recipe on server else find from above device list
   * @param {object} params Same params as received in the recipe object. All values are a list of <deviceId>.<parameter> being used in the formula.
   */
  getRunOn: async function (deviceIds, params) {
    try {
      let $devices,
        devices,
        controllerIds,
        runon;

      // Commenting the following code to disallow server based recipes.
      // if (createRecipeUtil.isServerRecipe(runOnPreference, deviceIds, formula)) {
      //   return "server";
      // }

      // Returning "NA" in case no params have been passed. Code should ideally never execute this.
      if (!params) return 'NA';

      // Creating id - Params map. Eg: {mgch_1 : ["kw", "cwit"]} from the params passed.
      const idToParameterMap = Object.values(params)
        .reduce((idToParameterMap, parameterInfo) => {
          const [componentId, parameter] = parameterInfo.split('.');
          if (!idToParameterMap[componentId]) {
            idToParameterMap[componentId] = [parameter];
          } else {
            idToParameterMap[componentId].push(parameter);
          }
          return idToParameterMap;
        }, {});

      /*
        If deviceId belongs to a device add it to the device list.
        If it's a componentId, fetch relevant deviceIds of devices being used in that comoponent parameter calculation and add to list.
       */
      let deviceList = [];
      for (deviceId of deviceIds) {
        // Checking if parsed deviceId belongs to a device and not a component. Parsing a componentId (ash-che_1) would return NaN.
        if (!isNaN(parseInt(deviceId))) {
          deviceList.push(deviceId);
        } else {
          // Fetch deviceList for Component and Parameter
          const componentParameters = idToParameterMap[deviceId];
          const deviceListForComponent = await componentService.getDeviceListFromDataParameter(
            deviceId,
            componentParameters
          );
          deviceList = [...deviceList, ...deviceListForComponent];
        }
      }

      /* Fetching controllerId based on  deviceList*/
      $devices = deviceList.map((deviceId) => deviceService.findOne(deviceId));
      devices = await Promise.all($devices);
      controllerIds = createRecipeUtil.getControllerIds(devices);
      runon = controllerIds.length > 0 ? utils.getMaxCountElement(controllerIds) : 'server';

      // Checking is calculated runOn is a third party controller, if it is, returning it's parent controller.
      let verifiedRunOn = await deviceService.getParentControllerIdIfThirdPartyController(runon);

      return verifiedRunOn;
    } catch (e) {
      throw 'Error in recipeService.getRunOn: ' + e;
    }
  },
  /**
   * Given list of devices and formula try to find best fit (controller) to run the recipe on (if preference is not server else just send to server)
   * @param {array} deviceIds List of deviceIds
   * @param {string} formula Formula string of recipe-logic
   * @param {string} runOnPreference enum(server, "") where server means run thos recipe on server else find from above device list
   */
  getThermalRecipeRunOn: async function (deviceIds, formula, runOnPreference) {
    try {
      const devicePromises = deviceIds.map((deviceId) => deviceService.findOne(deviceId));
      const devices = await Promise.all(devicePromises);

      const controllerIds = createRecipeUtil.getControllerIds(devices);
      const runOn = controllerIds.length > 0 ? utils.getMaxCountElement(controllerIds) : 'server';

      /**Checking if the calculated runOn is a third-party controller; if so, return its parent controller.*/
      return await deviceService.getParentControllerIdIfThirdPartyController(runOn);
    } catch (error) {
      sails.log.error('getThRunOn');
      sails.log.error(error);
      throw `Error in recipeService.getRunOn: ${error?.message}`;
    }
  },
  /**
   * @description Give either deviceId or componentId. In case of componenID find the
   * @summary deviceId that will sends that "param" data. If that param is coming from single device
   * @returns return [deviceId, param, runOnServer=false], else if its some complex param coming from
   * @exceptions multiple devices return [componentId, param, runOnServer=true].
   * @condition In case deviceId is passed, simply return [deviceId, param, runOnServer=false]
   * @param {string} deviceId Device/componenetId
   * @param {string} param Device's parameter to observe
   */
  findDeviceForParam: async function (deviceId, param) {
    let runOnServer = false;

    try {
      /**Get if deviceId is componentId or deviceId*/
      let deviceIdType = createRecipeUtil.getDeviceIdType(deviceId);

      if (deviceIdType === 'compId') {
        return await this.processComponentId(deviceId, param);
      }

      return [deviceId, param, runOnServer];
    } catch (error) {
      sails.log.error(
        'Error in recipeservice.findDeviceForParam during Create/Update Recipe',
        error
      );
      throw flaverr(
        'E_PROCESS_RECIPE_ERROR',
        new Error(
          `Failed to process Create/Update Recipe for deviceId: ${deviceId} and param: ${param}. Details: ${error?.message || 'unable to extract deviceId from param'}`
        )
      );
    }
  },
  /**
   * @description Processes a component ID to determine its associated parameters and updates the deviceId and param if necessary.
   * @param {string} deviceId - The ID of the component or process to be processed.
   * @param {string} param - The parameter to be retrieved or checked.
   * @returns {Promise<Array>} - A promise that resolves to an array containing the updated deviceId, param, and a boolean indicating if it should run on the server.
   * @throws {Error} - Throws an error if any issue occurs during the processing of the component ID.
   */
  processComponentId: async function (deviceId, param) {
    let runOnServer = false;
    let data,
      dataList,
      expression;
    if (globalHelper.isComponentOfTypeProcess(deviceId)) {
      const process = await processService.findOne({ processId: deviceId });
      dataList = process ? globalHelper.toArray(process?.rawParams) : [];
      data = !_.isEmpty(dataList)
        ? createRecipeUtil.getParameterFromProcessData(dataList, param)
        : null;
    } else {
      const component = await componentService.findOne({ deviceId });
      dataList = component ? globalHelper.toArray(component?.data) : [];
      data = !_.isEmpty(dataList)
        ? createRecipeUtil.getParameterFromComponentsData(dataList, param)
        : null;
    }

    expression = data?.expression?.split('||');

    const componentTypeRecipeParams = ['wbt'];
    if (expression && utils.haveCalculatedParameter(expression)) {
      runOnServer = true;
    } else if (componentTypeRecipeParams.indexOf(param) !== -1) {
      return [deviceId, param, runOnServer];
    } else {
      const {
        newDeviceId,
        newParam
      } = _extractDeviceParamFromExpression(data);
      if (newDeviceId) deviceId = newDeviceId;
      if (newParam) param = newParam;
    }

    return [deviceId, param, runOnServer];
  },
  /**
   *
   * @param {string} formula ||#1||$1||0
   * @param {Object} observableParams {\"#1\":\"mgch_12.status\"}
   * @param {Object} operatorsDict {\"$1\":\"==\",\"$2\":\"(\",\"$3\":\")\"}
   * @param {int} size Amount of data required for this recipe
   */
  v1ParseFormula: async function (formula, observableParams, operatorsDict, size) {
    let $devicesInFormulaArray,
      deviceParamList;

    try {
      $devicesInFormulaArray = Object.values(observableParams)
        .map(async (deviceIdDotParam) => {
          let [deviceId, param] = deviceIdDotParam.split('.');
          return await this.findDeviceForParam(deviceId, param);
        });

      deviceParamList = await Promise.all($devicesInFormulaArray);
      deviceParamList = deviceParamList.filter(Boolean);

      let reqAttr = createRecipeUtil.createFormula(
        deviceParamList,
        observableParams,
        formula,
        size,
        operatorsDict
      );
      let {
        runOnServer,
        didArray,
        topicArray
      } = reqAttr;

      return {
        runOnServer,
        dids: didArray,
        devicesToSubscribe: topicArray,
        parsedFormula: reqAttr['formula'],
      };
    } catch (e) {
      throw 'Error in recipeService.v1parseFormula: ' + e;
    }
  },
  /**
   * Create a schedule for recipe
   * @param {object} schedule Schedule object
   * @param {string} schedule.siteId site id of schedule
   * @param {string} schedule.rid recipe id of new schedule
   * @param {string} schedule.sid unique schedule id
   */
  createScheduleForRecipe: async function (schedule) {
    try {
      let {
        siteId,
        rid
      } = schedule;
      let recipe = await this.findOne({
        rid: rid,
        siteId
      });

      if (!recipe) {
        return { problems: ['Recipe does not exist.'] };
      }

      switch (recipe.appType) {
        case 'thermal':
          let misc = globalHelper.toJson(recipe.misc);
          let sampleTime = parseInt(misc.controlSettings.sampleTime);
          // It will modify the schedule.ts property with new cron
          createScheduleUtil.addSampleTimeToCron(sampleTime, schedule);
          break;
        case 'recipe':
          let intervalTime = parseInt(recipe.runInterval);
          // It will modify the schedule.ts property with new cron
          createScheduleUtil.addSampleTimeToCron(intervalTime, schedule);
          break;
      }
      let allSchedule = globalHelper.toArray(recipe['scheduled']);
      if (!allSchedule) {
        allSchedule = [];
      }

      schedule['runOn'] = recipe['runOn']; // schedule should also know where its running on
      try {
        await this.schedules.create(schedule);
      } catch (e) {
        throw e; // 500 server error
      }

      if (schedule) {
        allSchedule.push(schedule['sid']);
      } else {
        return { problems: ['Cannot create schedule'] };
      }

      try {
        await this.update({
          rid: rid,
          siteId: siteId
        }, { scheduled: JSON.stringify(allSchedule) });
      } catch (e) {
        await this.schedules.destroy(schedule);
        throw e;
      }

      return schedule;
    } catch (e) {
      throw 'Error in recipeService.createSchedule: ' + e;
    }
  },
  /**
   * Send the configration of recipe with schedules to controller
   * @param {object} configObject Config Object to send to controller. If first time confiring will have recipe object else empty
   * @param {array} scheduleIds Array of schedule ids to deploy to controller along with other configration
   * @param {string} siteId site id
   * @param {boolean} sendRecipeConfig true/false depicting should we send recipe config to controller or not
   */
  sendRecipeToController: async function (configObject, scheduleIds, siteId, sendRecipeConfig) {
    try {
      let $schedules,
        schedules,
        controllerPacket = {};

      let {
        rid: recipeId,
        runOn: sendController,
        actionAlert
      } = configObject;
      let topic = `${siteId}/config/${sendController}/recipelogic`;

      controllerPacket['operation'] = 'recipeInit';
      const appType = configObject?.appType;
      if (appType != 'commandRetentionRecipe') {
        $schedules = scheduleIds.map((scheduleId) =>
          this.schedules.findOne({
            rid: recipeId,
            sid: scheduleId
          })
        );
        schedules = await Promise.all($schedules);

        if (schedules.indexOf(undefined) !== -1) {
          sails.log.error(`Inconsistent database state for recipe ${recipeId}`);
          return { problems: [`Inconsistent database state for ${recipeId}`] };
        }

        if (schedules.length > 0) {
          schedules = schedules.filter((schedule) => !utils.isScheduleDeployed(schedule)); // filter deplyed schedules, those are not needed
          let schedules2DArray = schedules.map(utils.flattenSchedule);
          let schedulesArray = [];
          for (let schedules of schedules2DArray) {
            schedulesArray.push(...schedules);
          }
          controllerPacket['scheduleInfo'] = schedulesArray;
        }

        if (controllerPacket['scheduleInfo'].length === 0) {
          return { problems: ['No schedules found'] };
        }
      } else {
        controllerPacket['scheduleInfo'] = [];
      }

      if (sendRecipeConfig) {
        controllerPacket['recipeInfo'] = configObject;
      }

      const {
        did,
        parent: componentId,
        command,
        type
      } = actionAlert?.[0] || {};
      if (
        sendRecipeConfig &&
        type == 'action' &&
        did &&
        componentId &&
        command &&
        !actionAlert?.[0].hasOwnProperty('controlsRelationship')
        // In case of comandretention recipe bypass it
      ) {
        try {
          const componentControlRelationship =
            await componentService.fetchControlRelationshipByComponent(siteId, componentId);
          const commandControlRelationship =
            utils.getComponentControlRelationshipByDeviceIdAndCommandAbbr(
              componentControlRelationship,
              {
                did,
                command
              }
            );
          actionAlert[0]['controlRelationship'] = utils.attachControlRelationship(
            commandControlRelationship,
            {
              did,
              command
            }
          );
        } catch (e) {
          sentry.setTag('siteId', siteId);
          sentry.captureException(
            new Error(
              '[Error > sendRecipeToController] Unable to enable control relationship in the recipe config.'
            ),
            {
              extra: {
                error: e,
                recipeConfig: JSON.stringify(configObject),
                scheduleIds,
                sendRecipeConfig,
              },
            }
          );
          sails.log('Error > recipe-control-relationship', e);
        }
      }

      //TODO: Remove it used for testing purpose
      // await publish(topic,JSON.stringify(controllerPacket))
      await eventService.publish(topic, JSON.stringify(controllerPacket));
      return true;
    } catch (e) {
      sails.log('Error ar recipeService.sendrecipetoController : ', e);
      return { problems: ['Unable to send to controller'] };
    }
  },
  /**
   * Function deployes the schedules of recipe to server
   * @param {object} recipe Recipe Object
   * @param {string} recipe.rid RecipeId for the recipe
   * @param {string} recipe.siteId site Id for the recipe
   * @param {Array} schedules Array of schedules to deploy
   */
  sendRecipeToserver: async function (recipe, schedules) {
    try {
      let updateList = [],
        {
          rid,
          siteId
        } = recipe;

      try {
        await this.update({
          rid,
          siteId
        }, { isStage: '0' });
      } catch (e) {
        throw e; // Server error if this fails
      }

      for (let schedule of schedules) {
        updateList.push(this.schedules.update({
          rid: rid,
          sid: schedule
        }, { isDeployed: '1' }));
      }
      try {
        await Promise.all(updateList);
      } catch (e) {
        await this.update({
          rid,
          siteId
        }, { isStage: '1' }); // try to reset the state to 1
        throw e;
      }

      return true;
    } catch (e) {
      sails.log.error('Error in recipeService.sendRecipeToserver :', e);
    }
  },

  /**
   * Given recipe, this function will change isDeployed status of all it schedules to deployedStatus
   * @param {object} recipe Recipe object
   * @param {string} deployedStatus enum('0','1'), the new status
   */
  changeDeployedStatusOfSchedulesOfRecipe: async (recipe, deployedStatus) => {
    let {
      rid,
      scheduled
    } = recipe;
    let schedules = globalHelper.toArray(scheduled);
    schedules = schedules ? schedules : [];
    if (deployedStatus !== '1' && deployedStatus !== '0') {
      throw new Error('Invalid isDeployed value. Expected 0 or 1');
    }

    let $promArr = schedules.map((schedule) =>
      recipeService.schedules.update({
        rid,
        sid: schedule
      }, { isDeployed: deployedStatus })
    ); // Unset deployed status of all schedules

    try {
      await Promise.all($promArr);
    } catch (e) {
      throw e;
    }
    return;
  },
  /**
   * Perform Any update/delete recipe request to controller defined by functionType
   * @param {object} data Data object to send to controller
   * @param {string} functionType What function (update/delete/other) of recipe to invoke on controller
   * @param {string} controllerId Id of controller to send the info to
   * @param {string} siteId site id
   */
  invokeFunctionOnController: async function (data, functionType, controllerId, siteId) {
    let objectForController = {
      data,
      func: functionType,
      operation: 'recipeControl',
    };
    let topic = `${siteId}/config/${controllerId}/recipelogic`;
    //TODO: Changed topic
    try {
      // await publish(topic,JSON.stringify(objectForController))
      await eventService.publish(topic, JSON.stringify(objectForController));
    } catch (e) {
      throw e; // 500 server error in this case when mqtt is down
    }
    return;
  },
  /**
   * Given objects of recipes and there state(0/1). 0 means recipe is not working else 1 means recipe is running for
   * which update its isActive to current timestamp in redis-cache
   * @param {string} site site Id
   * @param {string} deviceId controller Id of device  the event was generated
   * @param {object} data.id-N So object of rid's and there state (0/1) {rid1: 1, rid2 :1, rid3: 0}
   */
  updateRecipeIsActive: async function (siteId, deviceId, data) {
    let $promiseArray = [];

    try {
      let recipeIds = Object.keys(data);
      let recipes = await Promise.all(
        recipeIds.map((rid) => {
          return this.findOne({
            rid,
            siteId
          });
        })
      );

      for (let [index, rid] of recipeIds.entries()) {
        let isActive = data[rid];
        // TODO: send those recipes to slack
        // if (recipes[index] === undefined) $promiseArray.push(this.invokeFunctionOnController({ rid }, 'deleteRecipe', deviceId, siteId));
        if (isActive) {
          $promiseArray.push(this.setRecipeIsActive(rid, isActive));
        } else {
          continue;
        }
      }
      let recipeUpdateStateResponses = await Promise.allSettled($promiseArray);
      recipeUpdateStateResponses.forEach((recipeUpdateStateResponse) => {
        if (recipeUpdateStateResponse['status'] === 'rejected') {
          sails.log.error(
            'recipeservice::updateRecipeIsActive Error updating recipe active state',
            recipeUpdateStateResponse
          );
        }
      });
    } catch (e) {
      throw e;
    }
  },
  /**
   * Delete the list of schedules from database.
   * @param {string} rid Recipe id
   * @param {array} schedules Array of schedule id's to delete
   */
  deleteSchedules: async function (rid, scheduleids) {
    let $deleteSchedules = scheduleids.map((sid) => {
      this.schedules.destroy({
        rid,
        sid
      });
    });

    try {
      await Promise.all($deleteSchedules);
    } catch (e) {
      throw e;
    }
    return true;
  },
  /**
   * Completely delete the recipe from the server
   * @param {string} siteId Site id
   * @param {object} data
   * @param {string} data.rid Unique recipe Id to delete.
   */
  deleteRecipe: async function (siteId, data) {
    let recipe,
      scheduleIds;
    let { rid } = data;
    if (!rid) {
      sails.log.error('Required parameter recipeId missing recipsService.deleteRecipe');
      return;
    }

    try {
      recipe = await this.findOne({
        rid,
        siteId
      });
    } catch (e) {
      throw e;
    }
    if (recipe.hasOwnProperty('appType') && recipe.appType == 'commandretention') {
      await deleteCommandRetentionAndNotify(recipe);
      return;
    }

    if (!recipe) return;
    scheduleIds = globalHelper.toArray(recipe['scheduled']);
    scheduleIds = scheduleIds ? scheduleIds : [];
    try {
      await this.deleteSchedules(rid, scheduleIds);
      await this.destroy({
        rid,
        siteId
      });
      await this.deleteRecipeFromSyncTable(recipe);
      await socketService.notifyServiceOnJouleTrack(siteId, 'recipies', 'recipe', {
        data: { recipe: rid },
        event: 'deleteRecipe',
      });
    } catch (e) {
      throw e;
    }
    return;
  },
  /**
   * Start/stop a recipe isActive state in database
   * @param {string} siteId Site Id
   * @param {string} rid recipe Id
   * @param {string} switchOff Value of switchOff key
   */
  startStopRecipe: async function (siteId, rid, switchOff) {
    try {
      await recipeService.update({
        rid,
        siteId
      }, { switchOff });
      await socketService.notifyServiceOnJouleTrack(siteId, 'recipies', 'recipe', {
        data: {
          recipe: rid,
          switchOff
        },
        event: 'startStopRecipe',
      });
    } catch (e) {
      throw e;
    }
    return;
  },

  /**
   * @description Given the siteId and the list of recipes it creates and sends the startStop recipe command packet to controllers.
   * @param {string} siteId site ID
   * @param {Object} recipes {1234: [{rid: '2345-dfdv-3r23r', switchOff: '2'}]}
   * @param triggerRequestId
   */
  sendStartStopToControllers: async function (siteId, recipes, triggerRequestId) {
    let $publishArray = [];
    for (let ctrlId in recipes) {
      let updateObject = {
        func: 'startStopRecipe',
        operation: 'recipeControl',
        data: recipes[ctrlId],
        requestId: uuidv4(),
        triggerRequestId,
      };
      let topic = `${siteId}/config/${ctrlId}/recipecontrol`;
      $publishArray.push(iotCoreService.publish(topic, updateObject));
      sails.log.info(
        `recipeService.sendStartStopToControllers: Sending startStopRecipe to controller ${ctrlId} for site ${siteId} with triggerRequestId=${triggerRequestId}`,
      );
    }
    await Promise.all($publishArray);
  },

  deleteSchedule: async function (siteId, {
    rid,
    sid
  }) {
    try {
      let recipe = await this.findOne({
        rid,
        siteId
      });
      if (!recipe) {
        sails.log.error('Recipe not found');
        return false;
      }
      if (recipe.hasOwnProperty('appType') && recipe.appType == 'commandretention') {
        sails.log.info('commandretention type recipe can not be deleted');
        return;
      }
      let schedules = globalHelper.toArray(recipe['scheduled']);
      if (!schedules) schedules = [];
      schedules = schedules.filter((schedule) => schedule !== sid);
      await this.update({
        rid,
        siteId
      }, { scheduled: JSON.stringify(schedules) });
      await this.schedules.destroy({
        sid,
        rid
      });
      await socketService.notifyServiceOnJouleTrack(siteId, 'recipies', 'recipe', {
        data: {
          recipe: rid,
          sid
        },
        event: 'deleteSchedule',
      });
      return true;
    } catch (e) {
      sails.log.error('Error in recipe.srvice->deleteSchedule: ', e);
      return false;
    }
  },

  //TODO:
  // [] add support to store the OT control panel related keys
  // []
  updateRecipe: async function (siteId, data) {
    try {
      let {
        rid,
        runOn
      } = data;

      if (!rid) {
        return;
      }
      data['isStage'] = '1';
      data['failsafe'] = globalHelper.toString(data['failsafe']);
      data['actionable'] = globalHelper.toString(data['actionable']);
      data['recipelabel'] = globalHelper.toString(data['recipelabel']);
      data['notRun'] = globalHelper.toString(data['notRun']);
      data['scheduled'] = globalHelper.toString(data['scheduled']);
      data['operator'] = globalHelper.toString(data['operator']);
      data['params'] = globalHelper.toString(data['params']);
      data['everyMinuteTopics'] = globalHelper.toString(data['everyMinuteTopics']);
      data['dependentOnOthers'] = globalHelper.toString(data['dependentOnOthers']);
      data['misc'] = globalHelper.toString(data['misc']);
      data['componentsType'] = globalHelper.toString(data['componentsType']);
      delete data['rid'];
      delete data['siteId'];
      try {
        await this.update({
          rid,
          siteId
        }, data);
        let recipeObject = await this.findOne({
          rid,
          siteId
        });
        await this.changeDeployedStatusOfSchedulesOfRecipe(recipeObject, '0'); // Undeploying shedules here so that if the update packet
        await this.deleteRecipeFromSyncTable({
          rid,
          runOn
        }); // doesn't reach controller the recipe should not get staged.
        await socketService.notifyServiceOnJouleTrack(siteId, 'recipies', 'recipe', {
          data: {
            ...data,
            recipe: rid
          },
          event: 'updateRecipe',
        });
      } catch (e) {
        sails.log.error('recipeservice::updateRecipe ', e);
      }
      // await axios.post('http://localhost:1337/m2/recipe/v2/deploy', {
      //   'rid': rid,
      //   'siteId': siteId,
      // });
      return;
    } catch (e) {
      throw e;
    }
  },

  deleteRecipeFromSyncTable: async function (recipe) {
    try {
      let {
        runOn,
        rid
      } = recipe;
      if (runOn === undefined || rid === undefined) {
        sails.log.error('recipe.service::deleteRecipeFromSyncTable rid/runOn dosnt not exist');
        return false;
      }
      if (runOn === 'server') {
        return true;
      }

      await syncService.destroyRecipeState(runOn, rid);
      return true;
    } catch (e) {
      throw e;
    }
  },

  /**
   * Given the siteId it gets all deployed recipes of that site
   * @param {string} siteId site ID
   */
  getAllDeployedRecipesOfASite: async function (siteId) {
    try {
      let recipes = await this.find({
        siteId,
        isStage: '0',
        appType: { "!=": "commandretention" },
      });
      return recipes;
    } catch (e) {
      sails.log.error('Error in recipeService.getAllDeployedRecipesOfASite: ', e);
      throw e;
    }
  },

  changeDeployedStatusOfSpecificSchedules: async function (rid, schedules, deployedStatus) {
    schedules = schedules ? schedules : [];
    if (deployedStatus !== '1' && deployedStatus !== '0') {
      throw new Error('Invalid isDeployed value. Expected 0 or 1');
    }

    let $promArr = schedules.map((schedule) =>
      recipeService.schedules.update({
        rid,
        sid: schedule
      }, { isDeployed: deployedStatus })
    );

    try {
      await Promise.all($promArr);
    } catch (e) {
      throw e;
    }
    return;
  },

  unstageRecipe: async function (rid, siteId) {
    try {
      let recipe = await recipeService.findOne({
        rid,
        siteId
      });

      if (!recipe) return false;
      await recipeService.update({
        rid,
        siteId
      }, { isStage: '0' });
      await this.changeDeployedStatusOfSchedulesOfRecipe(recipe, '1'); // deploy all schedules
      return true;
    } catch (e) {
      sails.log.error('recipeservice::unstageRecipe ', e);
      return;
    }
  },
  updateCommandRetentionRecipe: async function (siteId, rid, feedback) {
    const recipe = await recipeService.findOne({
      rid,
      siteId
    });
    if (!recipe) return;
    const { status } = feedback;
    if (status && status == 2) {
      await recipeService.update(
        {
          rid,
          siteId,
        },
        {
          isStage: '0',
          commandRetentionRecipeStatus: 1,
        }
      );
    } else {
      await recipeService.update(
        {
          rid,
          siteId,
        },
        {
          commandRetentionRecipeStatus: 0,
        }
      );
    }
    const data = fetchEnableCommandRetentionNotifyObj(recipe, status);
    await socketService.notifyServiceOnJouleTrack(siteId, 'recipies', 'commandRetention', {
      data,
      event: 'updateCommandRetention',
    });
    return;
  },
  eventFeedbackFromController: async function (siteId, deviceId, message) {
    let {
      data,
      func,
      status
    } = message;

    if (func === 'startStopRecipe') {
      // status will be of type object
      //when multiple recipes are updated --> {'1234-85fsdf9-9adf5d6': true}  (status for each recipe)
      let tempStatus = globalHelper.toJson(status);
      if (!tempStatus) tempStatus = globalHelper.toBoolean(status); // Back compatibility

      let tempData = globalHelper.toArray(data);
      if (!tempData) tempData = globalHelper.toJson(data); // Back compatibility

      data = tempData;
      status = tempStatus;
    } else {
      status = globalHelper.toBoolean(status);
      data = globalHelper.toJson(data);
    }

    if (!data || !siteId) return;
    if (status === false && func !== 'saveRecipeActive') {
      // we dont need status key for func saveRecipeActive
      return;
    }

    try {
      if (func === 'saveRecipeActive') {
        await this.updateRecipeIsActive(siteId, deviceId, data);
      } else if (func === 'deleteRecipe') {
        await new Promise(resolve => setTimeout(resolve, 1000));
        await this.deleteRecipe(siteId, data);
      } else if (func === 'startStopRecipe') {
        let recipesToUpdate = [];
        if (data.constructor.name === 'Object') {
          // Back compatibility
          if (status) recipesToUpdate.push(data);
        } else {
          recipesToUpdate = data.filter((eachRecipe) => {
            eachRecipe = globalHelper.toJson(eachRecipe);
            let rid = eachRecipe.rid;
            if (globalHelper.toBoolean(status[rid])) return true;
            return false;
          });
        }
        await Promise.allSettled(
          recipesToUpdate.map((eachRecipe) => {
            return this.startStopRecipe(siteId, eachRecipe.rid, eachRecipe.switchOff);
          })
        );
      } else if (func === 'updateRecipe') {
        await this.updateRecipe(siteId, data);
      } else if (func === 'deleteSchedule') {
        await this.deleteSchedule(siteId, data);
      } else if (func === 'forceUpdateThermostatRecipeOTPC') {
        return await this.doHandleThermostatRecipeOTCPFeedback(siteId, data);
      } else if (func === 'forceUpdateRecipe') {
        return await this.doHandleForceUpdateRecipeFeedback(siteId, data);
      } else {
      }
    } catch (e) {
      sails.log.error(`recipe.service: ${e}`);
    }

    return;
  },
  /**
   * @description Publishes a list of recipe Ids that are supposed to be running on the controller.
   *
   * @param {string} message Example payload: {"controllerid" : <controllerid>, "operation" : "recipelist"}
   * @param {string} siteId
   * @returns
   */
  findRecipesForController: async function (message, siteId) {
    // sails.log.info("findRecipesForController >> Called with message", message);
    if (message === undefined) return;
    let { controllerId } = message;
    if (!controllerId) return;
    const responseTopic = `${siteId}/response/${controllerId}/recipesync`;
    let recipes = await this.find({ runOn: controllerId });
    // Filtering recipes based on `isStage` key. 1 means on DB only. 0 means deployed.
    let recipelist;
    if (!recipes || recipes.length == 0) {
      recipelist = [];
    } else {
      recipelist = recipes
        .filter((recipe) => recipe['isStage'] == '0')
        .filter(Boolean)
        .map((recipe) => recipe.rid);
    }
    let responseObject = {
      controllerId,
      operation: 'recipelist',
      recipelist,
    };
    eventService.publish(responseTopic, JSON.stringify(responseObject));
  },
  /**
   * @description Returns formatted list of recipe configurations based on recipe Ids requested.
   * Returns formatted list of schedule configurations based on the recipes found in the database.
   * Filters recipes based on `isStage` key. 1 = DB only. 0 = deployed.
   * Filters schedules based on 'isDeployed' key. 1 = deployed to controller.
   * @param {strng} message Example payload :
   * {"controllerid" : <controllerid>, "recipelist" : [<rid_1>, <rid2> ..], "operation" : "recipesync"}
   * @param {*} siteId
   * @returns
   */
  syncRecipesForController: async function (message, siteId) {
    // sails.log.info("syncRecipesForController >> Called with message", message);
    if (message === undefined) return;
    let {
      controllerId,
      recipelist
    } = message;
    if ((!controllerId, !recipelist)) return;
    let scheduleconfigs = [],
      infoconfigs = [];
    const responseTopic = `${siteId}/response/${controllerId}/recipesync`;

    let $recipes = recipelist.map((rid) => this.findOne({ rid }));
    let recipes = await Promise.all($recipes);
    // Filtering only those recipes which are deployed to controller:
    recipes = recipes.filter(Boolean)
      .filter((recipe) => recipe?.['isStage'] == '0');
    // Fetching all schedules and parsing them.
    for (let i in recipes) {
      let recipe = recipes[i];
      if (!recipe) continue; // In case recipe was not found in DB.
      let scheduleIds = globalHelper.toArray(recipe['scheduled']);
      if (!scheduleIds || scheduleIds.length == 0) continue;
      let $schedules = scheduleIds.map((sid) => recipeService.schedules.findOne({ sid }));
      let schedules = await Promise.all($schedules);
      schedules
        .filter(Boolean)
        .filter((schedule) => {
          sails.log.error(
            `method > syncRecipesForController > rid=${recipe?.rid} > sid=${
              schedule ? schedule?.sid : null
            }`
          );
          return schedule && schedule?.['isDeployed'] == '1';
        })
        .forEach((schedule) => {
          let flattenedSchdules = utils.flattenSchedule(schedule);
          scheduleconfigs.push({
            scheduleInfo: flattenedSchdules,
          });
        });
    }

    // Parsing all recipe objects based on Controller Requirement
    recipes.forEach((recipe) => {
      let { appType = 'recipe' } = recipe;
      let configObject;
      switch (appType) {
        case 'recipe':
          configObject = utils.parseRecipeObjectToSendToController(recipe);
          break;
        case 'thermal':
          configObject = utils.parseThermalObjectToSendToController(recipe);
          break;
      }
      infoconfigs.push({
        recipeInfo: configObject,
      });
    });

    const responseObject = {
      infoconfigs,
      scheduleconfigs,
      operation: 'recipesync',
    };
    eventService.publish(responseTopic, JSON.stringify(responseObject));
  },
  /**
   * @description Adds phone numbers in the configuration, if the recipe is hyper critical.
   * @param {object} actionable Stringified actionable object saved in recipe config
   */
  hypercriticalAlertConfigModification: async function (actionable) {
    for (let i in actionable) {
      const actionableObject = actionable[i];
      if (!actionableObject.isHypercritical) continue;
      const userList = actionableObject.smslist;
      const phoneNumbers = await userService.getMobileNumbersOfUsers(userList);
      actionableObject['numbers'] = phoneNumbers;
    }
  },
  fetchOTControlPanelModeRecipe: async function FetchOTControlPanelModeRecipe({
    componentId,
    siteId,
  }) {
    const thermostatRecipeInstance = new ThermostatRecipe({
      siteId,
      componentId
    });
    const recipeList = await thermostatRecipeInstance.fetchOTControlledActuatorRecipe(componentId);
    return recipeList;
  },
  /**
   * Send OTPC configuration to controller.
   *
   * @param {Object} param0 - Object with componentId, siteId, and isOTPanelControllingSetpoint properties
   * @return {Array} An array of recipe IDs that has accepted the change for OT Panel control
   */
  sendOTPCConfigToController: async function SendOTPCConfigToController({
    componentId,
    siteId,
    isOTPanelControllingSetpoint,
  }) {
    const $componentDetail = Components.findOne({ deviceId: componentId });

    const thermostatRecipeInstance = new ThermostatRecipe({
      siteId,
      componentId
    });
    const $recipeList = thermostatRecipeInstance.fetchActiveActuatorRecipes(componentId);
    const [componentDetail, recipeList] = await Promise.all([$componentDetail, $recipeList]);
    componentDetail.data = JSON.parse(componentDetail.data);

    if (_.isEmpty(componentDetail)) {
      return componentError.throwComponentNotExist(this.componentId);
    }

    recipeErrorUtils.throwErrorComponentIsNotValid(componentDetail);

    if (_.isEmpty(recipeList)) {
      return recipeErrorUtils.throwActuatorThermostatConfigExists();
    }
    const dataParamExpressionMap = componentDetail.data.reduce((acm, curr) => {
      const {
        key,
        expression
      } = curr;
      acm[key] = expression;
      return acm;
    }, {});
    for (const recipe of recipeList) {
      const {
        runOn: controllerId,
        rid,
        siteId
      } = recipe;
      const {
        misc: oldMisc,
        everyMinuteTopics: oldEveryMinuteTopics,
        dependentOnOthers: oldDependentOnOthers,
      } = recipe;
      const newMisc = Object.assign({}, globalHelper.toJson(oldMisc));
      const newEveryMinuteTopics = new Set();
      const newDependentOnOthers = new Set();

      //build new recipe configuration
      newMisc.dataExpression.otThermostatSwitchStatus = dataParamExpressionMap.otthmswstatus;
      newMisc.dataExpression.otSetpointTemperature = dataParamExpressionMap.otsettemp;

      let deviceParamArray = utils.getDeviceParamListFromThermostatExpression(
        newMisc.dataExpression
      );
      if (deviceParamArray.length > 0) {
        let devicesArray = deviceParamArray.map((deviceParam) => deviceParam['deviceId']);
        let topicsArray = utils.getTopicsArray(deviceParamArray);
        topicsArray.forEach((it) => newEveryMinuteTopics.add(it));
        devicesArray.forEach((it) => newDependentOnOthers.add(it));
      }

      const recipeUpdateObject = {
        misc: globalHelper.toString(newMisc),
        everyMinuteTopics: globalHelper.toString(Array.from(newEveryMinuteTopics)),
        dependentOnOthers: globalHelper.toString(Array.from(newDependentOnOthers)),
      };
      await this.updateRecipeByRecipeId(rid, siteId, recipeUpdateObject);

      //merge new config in existing recipe
      recipe.misc = recipeUpdateObject.misc;
      recipe.everyMinuteTopics = recipeUpdateObject.everyMinuteTopics;
      recipe.dependentOnOthers = recipeUpdateObject.dependentOnOthers;

      const recipeObject = utils.thermalRecipeToControllerObject(recipe);
      recipeObject.isOTPanelControllingSetpoint = Number.parseInt(isOTPanelControllingSetpoint, 10);
      this.invokeFunctionOnController(
        recipeObject,
        'forceUpdateThermostatRecipeOTPC',
        controllerId,
        siteId
      );
      sails.log.info(`module="thermostatRecipeOTCP" message=recipe information shared to IoT`);
    }
    return recipeList.map((it) => {
      return it.rid;
    });
  },
  doHandleThermostatRecipeOTCPFeedback: async function forceUpdateThermostatRecipeOTPC(
    siteId,
    recipeObject
  ) {
    const { rid } = recipeObject;
    sails.log.info(
      `recipeId=${recipeObject.rid} operation='doHandleThermostatRecipeOTCPFeedback',message='updated packet received from IoT'`
    );
    const recipe = await this.findOne({ rid });
    if (!recipe) return;
    if (!recipeObject.hasOwnProperty('isOTPanelControllingSetpoint')) {
      sails.log.info(
        `recipeId=${recipeObject.rid} operation='doHandleThermostatRecipeOTCPFeedback',message='isOTPanelControllingSetpoint key is missing'`
      );
      return;
    }
    const { isOTPanelControllingSetpoint } = recipeObject;
    await this.updateRecipeByRecipeId(rid, siteId, {
      isOTPanelControllingSetpoint: String(isOTPanelControllingSetpoint),
    });
    await socketService.notifyServiceOnJouleTrack(siteId, 'recipies', 'recipe', {
      data: {
        ...recipeObject,
        recipe: rid
      },
      event: 'forceUpdateThermostatRecipeOTPC',
    });
  },
  initiateThermostatActuatorSetPointChangeRequest:
    async function initiateThermostatActuatorSetPointChangeRequest({
      rid,
      setPoint,
      siteId,
      componentId,
    }) {
      const thermostatRecipeInstance = new ThermostatRecipe({
        siteId,
        componentId
      });
      const recipe = await thermostatRecipeInstance.fetchActiveActuatorRecipeById(
        rid,
        siteId,
        componentId
      );
      if (!recipe) {
        return recipeErrorUtils.throwActuatorThermostatConfigExists();
      }
      const { runOn: controllerId } = recipe;
      const recipeObject = utils.thermalRecipeToControllerObject(recipe);
      recipeObject.misc.appSettings.setpoint = Number.parseFloat(setPoint, 10);
      await this.invokeFunctionOnController(
        recipeObject,
        'forceUpdateRecipe',
        controllerId,
        siteId
      );
      sails.log.info(
        `module="initiateThermostatActuatorSetPointChangeRequest" message=recipe information shared to IoT`
      );
      return rid;
    },
  doHandleForceUpdateRecipeFeedback: async function doHandleForceUpdateRecipeFeedback(
    siteId,
    recipeObject
  ) {
    const { rid } = recipeObject;
    const recipe = await this.findOne({ rid });
    if (!recipe) return;

    sails.log.info(
      `recipeId=${recipeObject.rid} operation='doHandleForceUpdateRecipeFeedback',message='updated packet received from IoT'`
    );
    let { misc: existingMisc } = recipe;
    existingMisc = JSON.parse(existingMisc);
    existingMisc.appSettings.setpoint = recipeObject.misc.appSettings.setpoint;
    await this.updateRecipeByRecipeId(rid, siteId, { misc: JSON.stringify(existingMisc) });
    await socketService.notifyServiceOnJouleTrack(siteId, 'recipies', 'recipe', {
      data: {
        ...recipeObject,
        recipe: rid
      },
      event: 'forceUpdateRecipe',
    });
  },
  deployCommandRetention: deployCommandRetention,
  createRecipe: async function (appType, recipeObject) {
    if (appType == 'commandretention') {
      return recipeService.create(_buildCommandRetentionRecipeObject(recipeObject));
    }
  },
  updateRecipeProperty: async function (appType, recipeObject) {
    if (appType == 'commandretention') {
      const {
        rid,
        siteId
      } = recipeObject;
      const recipeProperty = _buildCommandRetentionRecipeObject(recipeObject);
      delete recipeProperty.rid;
      delete recipeProperty.siteId;
      return recipeService.update({
        rid,
        siteId
      }, recipeProperty);
    }
  },
};
module.exports = exports;

async function eventHandler(topic, message) {

  let {
    siteId,
    deviceId
  } = globalHelper.parseMQTTTopic(topic);

  if (/.*\/feedback\/\d+\/recipelogicconfig$/.test(topic)) {
    try {
      if (message.recipeInfo) {
        if (message.recipeInfo.appType === 'commandRetentionRecipe') {
          const rid = message.recipeInfo.rid;
          sails.log('IOT feedback: enableCommandRetentionRecipe');
          sails.log({
            rid,
            requestId: message.recipeInfo.requestId,
            func: 'updateCommandRetentionRecipe',
            message: 'update command retention recipe',
            payload: message,
            topic,
            operation: 'enableCommandRetention',
            appType: 'commandretention',
            siteId,
            deviceId
          });
          await exports.updateCommandRetentionRecipe(siteId, rid, message);
        } else {
          let rid = message['scheduleInfo']?.[0]?.['rid'];
          await exports.unstageRecipe(rid, siteId);
          await socketService.notifyServiceOnJouleTrack(siteId, 'recipies', 'recipe', {
            data: { recipe: rid },
            'event': 'deployRecipe'
          });
        }

      } else {
        const rid = message.scheduleInfo?.[0]?.['rid'];
        let scheduleIds = message.scheduleInfo.map(schedule => schedule.id);
        await exports.changeDeployedStatusOfSpecificSchedules(rid, scheduleIds, '1');
        await socketService.notifyServiceOnJouleTrack(siteId, 'recipies', 'recipe', {
          data: {
            recipe: rid,
            sid: scheduleIds[0]
          },
          'event': 'deploySchedule'
        });

      }
    } catch (e) {
      sails.log.error(`[${new Date().toISOString()}] ERROR in recipeservice.eventHandler feedback/recipeconfig: ${e?.message || e}`);
      sails.log.error(e);
      return;
    }
  } else if (/.*\/feedback\/\d+\/recipeControl$/.test(topic)) {
    try {
      sails.log('IOT feedback: disableCommandRetention');
      sails.log({
        rid: message.data.rid,
        func: 'deleteRecipe',
        message: 'disable command retention recipe',
        payload: message,
        topic,
        operation: 'disableCommandRetention',
        appType: 'commandretention',
        siteId,
        deviceId
      });
      await exports.eventFeedbackFromController(siteId, deviceId, message);
    } catch (e) {
      sails.log.error(`recipeservice.eventHandler feedback/recipecontrol: ${e}`);
      return;
    }
  } else if (/.*\/request\/\d+\/recipesync$/.test(topic)) {
    // Recipe Sync Service
    const { operation } = message;
    try {
      switch (operation) {
        case 'recipesync':
          // Operation for requesting recipes and schedules
          exports.syncRecipesForController(message, siteId);
          break;
        case 'recipelist':
          // Operation for requesting list of recipes for a controllerId.
          exports.findRecipesForController(message, siteId);
          break;
        default:
          throw Error({
            message: 'Invalid operation found in payload',
            topic,
            payload
          });
      }

    } catch (error) {
      sails.log.error(`recipeservice.eventHandler request/recipesync: ${error}`);
      return;
    }
  } else {
    sails.log(`Topic not configured ${topic}`);
  }
}

async function deployCommandRetention() {
  let {
    actionable,
    isSchedule,
    appType,
    componentId,
    isHypercritical,
  } = recipeObject;
  actionable = globalHelper.toArray(actionable);
  if (actionable === undefined || actionable.length == 0) {
    return exits.badRequest({
      problems: ['Actionable array cannot be empty']
    });
  }
  const devicesIncluded = utils.getDevicesInsideActionable(actionable);
  if (isHypercritical) {
    try {
      await hypercriticalAlertConfigModification(actionable);
      recipeObject['actionable'] = JSON.stringify(actionable);
    } catch (error) {
      sails.log.error('[createRecipe] >> Error attaching phone numbers in case of hyper critical alerts!', error);
    }
  }

  recipeObject.isSchedule = 'true';
  const runOn = await getRunOn(devicesIncluded, recipeObject.params);
  recipeObject.runOn = runOn;
  recipeObject.dependentOnOthers = globalHelper.toString(devicesIncluded);

  // send topic to iot deploy recipe
  await sendRecipeToserver();
  let sendRecipeConfig = true; // send full configObject to controller
  let response = await sendRecipeToController(configObject, schedules, siteId, sendRecipeConfig);
  return response;

}

async function deleteCommandRetentionAndNotify(recipInfo) {
  const {
    rid,
    siteId
  } = recipInfo;
  await recipeService.update({
    rid,
    siteId
  }, {
    commandRetentionRecipeStatus: '0' // TODO: Variable name change
  });
  const data = fetchDisableCommandRetentionNotifyObj(recipInfo);
  await socketService.notifyServiceOnJouleTrack(siteId, 'recipies', 'commandRetention', {
    data,
    event: 'disableCommandRetention',
  });
  return;
}

function fetchDisableCommandRetentionNotifyObj(recipeInfo) {
  const message = 'Control retention deleted successfully';
  const state = 0;

  const {
    siteId,
    componentId,
    actionable,
    rid: recipeId
  } = recipeInfo;
  const controlAbbr = JSON.parse(actionable)[0].controlsRelationship.controlabbr;
  const payload = {
    siteId,
    componentId,
    state,
    status: 'success',
    controlAbbr,
    message,
    recipeId
  };
  return payload;
}

function fetchEnableCommandRetentionNotifyObj(recipeInfo, status) {
  let state;
  if (status == 2) {
    message = 'Control retention enabled successfully';
    state = 1;
  } else {
    message = 'Control retention can not be enabled. Please try again.';
    state = 0;
  }
  const {
    siteId,
    componentId,
    actionable,
    rid: recipeId
  } = recipeInfo;
  const controlAbbr = JSON.parse(actionable)[0].controlsRelationship.controlabbr;
  const payload = {
    siteId,
    componentId,
    state,
    status: status == 2 ? 'success' : 'failure',
    controlAbbr,
    message,
    recipeId
  };
  return payload;
}

function _buildCommandRetentionRecipeObject(recipeObj) {
  return {
    isSchedule: true,
    isStage: 1,
    switchOff: 0,
    isActive: 0,
    rid: recipeObj.rid,
    siteId: recipeObj.siteId,
    componentId: recipeObj.componentId,
    label: `${recipeObj.assetName} ${recipeObj.controlAbbr} Command Retention`,
    runOn: recipeObj.controllerId,
    startNow: true,
    maxDataNeeded: 1,
    notRun: '[]',
    appType: 'commandretention',
    actionable: JSON.stringify(recipeObj.actionable),
    user: recipeObj.userId,
  };
}

function _extractDeviceParamFromExpression(data) {
  const deviceParamList = data?.expression
    ? Components.getDeviceParamListFromDataExpression(data.expression)
    : [];

  if (deviceParamList.length > 0) {
    const {
      deviceId: newDeviceId,
      param: newParam
    } = deviceParamList[0]; // Recipe does not support multiple devices in expression
    return {
      newDeviceId,
      newParam
    };
  }

  return {};
}
