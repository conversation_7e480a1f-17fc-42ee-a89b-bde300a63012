/* eslint-disable no-undef */
module.exports = {

  /**
   * recipe Service Private functions
   */
  find: async (searchParams) => {
    // Recipes.find
    return Recipes.find(searchParams);
  },
  findOne: async (searchParams) => {
    // Recipes.findOne
    let recipes = await Recipes.find(searchParams).limit(1);
    return recipes[0];
  },
  update: async (searchParams, updateValue) => {
    return Recipes.update(searchParams, updateValue);
  },
  create: async (searchParams) => {
    return Recipes.create(searchParams);
  },
  destroy: async (searchParams) => {
    return Recipes.destroy(searchParams);
  },
  updateRecipeByRecipeId: async (rid, siteId, updateValue) => {
    if (_.isEmpty(rid)) throw new Error(`rid is required to update it`)
    if (_.isEmpty(siteId)) throw new Error(`siteId is required to update it`)
    if (_.isEmpty(updateValue)) throw new Error(`updateValue can be empty object`)
    return Recipes.update({ rid, siteId }, updateValue)
  },

  schedules: {
    find: async (searchParams) => {
      return Schedules.find(searchParams);
    },
    findOne: async (searchParams) => {
      let schedules = await Schedules.find(searchParams).limit(1);
      return schedules[0];
    },
    update: async (searchParams, updateValue) => {
      return Schedules.update(searchParams, updateValue);
    },
    create: async (searchParams) => {
      return Schedules.create(searchParams);
    },
    destroy: async (searchParams) => {
      return Schedules.destroy(searchParams);
    }
  }
};
