const recipeService = require('./recipe.service');

module.exports = {
  find: recipeService.find,
  findOne: recipeService.findOne,
  startStopRecipe: recipeService.startStopRecipe,
  getAllDeployedRecipesOfASite: recipeService.getAllDeployedRecipesOfASite,
  sendStartStopToControllers: recipeService.sendStartStopToControllers,
  create: recipeService.create,
  createRecipe: recipeService.createRecipe,
  sendRecipeToController: recipeService.sendRecipeToController,
  invokeFunctionOnController: recipeService.invokeFunctionOnController,
  update: recipeService.update,
  updateRecipeProperty: recipeService.updateRecipeProperty,
  deleteRecipe: recipeService.deleteRecipe,
};
