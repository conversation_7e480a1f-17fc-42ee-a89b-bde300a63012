const flaverr = require("flaverr");
const { 
  throwExceptionEmptyControlsList, 
  validateEnableSchema,
  throwExceptionInvalidSiteAsset,
  fetchCommandRetentionRecipes,
  validateCommands,
  throwExceptionInvalidControlState,
  fetchRecipeByControlAbbr,
  COMMAND_RETENTION_STATUS,
  formateResponse,
  componentModeResponseBuilder
} = require("../../../utils/recipe/command-retention.util");
const recipeUtils = require("../../../utils/recipe/utils");
const componentService = require("../../component/component.public");
const { getDeviceControllerMap } = require("../../device/device.public");
const { isControlConfiguredToAcceptCommand, filterCommandHistorySchema, buildCommandHistoryObj } = require("../../../utils/controls");
const recipePublic = require("../recipe.public");
const influxService = require("../../influx/influx.service");
const { fetchComponentLastCommand } = require("../../controls/control.public");
class CommandRetention {


  constructor() {
    this.siteId = null;
    this.componentId = null
    this.controlAbbr = null;
    this.appType = 'commandretention'
    this.assetName = null;
    this.userId = null;
  }

  setSiteId(siteId) {
    this.siteId = siteId;
    return this
  }

  setUserId(userId) {
    this.userId = userId;
    return this;
  }

  async setAssetInfoById(compId) {
    const componentDetails = await componentService.findOne({
      deviceId:compId,
      siteId: this.siteId
    })
    if (!componentDetails) {
      throwExceptionInvalidSiteAsset(this.siteId, compId)
    }
    this.componentId = compId;
    this.assetName = componentDetails?.name || compId;
    return this;
  }

  /**
   * 
   * @param {*} siteId 
   * @param {*} componentId 
   * @description {['turnOnOff', 'setFrequency']} controlAbbr
   */
  async fetchCommandAbbrInfoByControlAbbr(enableControlAbbrList, disableControlAbbrList) {
    const enableCommandAbbrInfo = [];
    const disableCommandAbbrInfo = [];
    const componentControlRelationship = await componentService.fetchControlRelationshipByComponent(this.siteId, this.componentId);

    const deviceIds = [];
    componentControlRelationship.forEach((componentData)=>{
      enableControlAbbrList.forEach((controlAbbr) => {
        const title = `Command Retention (${this.assetName}):- ${controlAbbr}`
        if(componentData.controlAbbr == controlAbbr){
          if(componentData.controlType == 'VIT'){
            const controlProperty = componentData.controlProperty;
            const info = {
              controlAbbr: componentData.controlAbbr,
              deviceId: controlProperty.deviceId,
              commandAbbr: controlProperty.commandAbbr,
              deviceAbbr: controlProperty.device_abbr,
              commandType: 'VIT',
              label: title,
              controllerId: null,
              dataParamAbbr: componentData.dataParamAbbr
            };

            deviceIds.push(controlProperty.deviceId);
    
            enableCommandAbbrInfo.push(info);
          }else{
            const controlPropertyRight = componentData.controlProperty.right;
            const controlInfo = {
                controlAbbr: componentData.controlAbbr,
                commandType: 'BIT',
            }
            const dataParamAbbr = componentData.dataParamAbbr
            const rightInfo = {
              deviceId: controlPropertyRight.deviceId,
              commandAbbr: controlPropertyRight.commandAbbr,
              deviceAbbr: controlPropertyRight.device_abbr,
              label: title,
              controllerId: null,
              dataParamAbbr
            }
            deviceIds.push(controlPropertyRight.deviceId);
            controlInfo.right = rightInfo
            const controlPropertyLeft = componentData.controlProperty.left;

            const leftInfo = {
              deviceId: controlPropertyLeft.deviceId,
              commandAbbr: controlPropertyLeft.commandAbbr,
              deviceAbbr: controlPropertyLeft.device_abbr,
              label: title,
              controllerId: null,
              dataParamAbbr
            }
            deviceIds.push(controlPropertyLeft.deviceId);
            controlInfo.left = leftInfo
            enableCommandAbbrInfo.push(controlInfo);                   
          }
        }
     })
     disableControlAbbrList.forEach((controlAbbr) => {
      if (componentData.controlAbbr == controlAbbr) {
         if (componentData.controlType == 'BIT')  {
          disableCommandAbbrInfo.push({
            controlAbbr,
            deviceId: componentData.controlProperty.left.deviceId,
            controllerId: null
          })
          deviceIds.push(componentData.controlProperty.left.deviceId)
         } else {
          disableCommandAbbrInfo.push({
            controlAbbr,
            deviceId: componentData.controlProperty.deviceId,
            controllerId: null
          })
          deviceIds.push(componentData.controlProperty.deviceId);
         }
      }
     })

    })

    const controllerDeviceMapping = await getDeviceControllerMap(deviceIds);

    const updatedControllerIdInEnableCommand = enableCommandAbbrInfo.map((info) => {
      const {commandType} = info;
      if (commandType == 'BIT') {
        info.left.controllerId = controllerDeviceMapping[info.left.deviceId] || info.left.deviceId; 
        info.right.controllerId = controllerDeviceMapping[info.right.deviceId] || info.right.deviceId; 
      } else {
        info.controllerId =  controllerDeviceMapping[info.deviceId] || info.deviceId; 
      }
      return info;
    });

    const updateControllerIdInDisableCommand = disableCommandAbbrInfo.map((info) => {
      info.controllerId =  controllerDeviceMapping[info.deviceId]
      return info;
    })

    //TODO: Throw an error in case of controllerID is null
    return { 
      enableCommandRetention: updatedControllerIdInEnableCommand, 
      disableCommandRetention: updateControllerIdInDisableCommand
    };
  }

  async enable(commandParamsToEnable) {
    if (!commandParamsToEnable.length) return;
    const recipes = await this.fetch();
    const $enableCommandRetention = commandParamsToEnable.map((commandRetention) =>
       enableCommandRetention(commandRetention, recipes, {
        siteId: this.siteId, 
        componentId:this.componentId, 
        assetName: this.assetName,
        appType: this.appType,
        userId: this.userId
      }));
    const result = await Promise.all($enableCommandRetention);
    return formateResponse(result);
    async function enableCommandRetention(enableCommandParam, recipes, {siteId, componentId, assetName,appType, userId}) {
      const {
          controlAbbr,
        } = enableCommandParam
      
      validateEnableSchema(enableCommandParam)

      // Either disabled or pending state
      let existingRecipe = recipes.filter((r)=> r.controlAbbr == controlAbbr && [ 
          COMMAND_RETENTION_STATUS.Pending,
          COMMAND_RETENTION_STATUS.Disabled
        ].includes(r.commandRetentionStatus)
      )
      existingRecipe = existingRecipe.length > 0 ? existingRecipe[0] : {};
      if (_.isEmpty(existingRecipe)) throwExceptionInvalidControlState(controlAbbr);      
      const recipeId = recipeUtils.uuid.v4()
      const sendCommandRetentionRecipeConfig = recipeUtils.getIOTDataPacketToEnableCommandRetention({
        ...enableCommandParam,
        siteId,
        componentId,
        rid: recipeId, // Check once again 
        recipeId,
        uniqId: recipeUtils.uuid.v4(),
      });

      if (existingRecipe.commandRetentionStatus == COMMAND_RETENTION_STATUS.Pending) {
        const existingRecipes = await recipePublic.find({siteId, commandRetentionRecipeStatus: '1', appType});
        const existingRecipeId = fetchRecipeByControlAbbr(existingRecipes, controlAbbr);
        sendCommandRetentionRecipeConfig.recipeInfo.rid = existingRecipeId;
        sendCommandRetentionRecipeConfig.recipeInfo.requestId = existingRecipeId;
        await recipePublic.sendRecipeToController(sendCommandRetentionRecipeConfig.recipeInfo, [], siteId, true);
        return sendCommandRetentionRecipeConfig
      }
      
      if (
        existingRecipe.commandRetentionStatus == COMMAND_RETENTION_STATUS.Disabled && 
        existingRecipe.rid
      ) {
        const oldRecipeId = existingRecipe.rid
        sendCommandRetentionRecipeConfig.recipeInfo.rid = oldRecipeId;
        sendCommandRetentionRecipeConfig.recipeInfo.requestId = oldRecipeId;
        await recipePublic.updateRecipeProperty('commandretention', {
            controlAbbr,
            assetName,
            siteId,
            componentId,
            rid: oldRecipeId,
            actionable: sendCommandRetentionRecipeConfig.recipeInfo.actionAlert,
            userId
        })
      } else { 
        await recipePublic.createRecipe(appType, {
            controlAbbr,
            assetName,
            siteId,
            componentId,
            rid: recipeId ,
            actionable: sendCommandRetentionRecipeConfig.recipeInfo.actionAlert,
            userId
        })
     }
     await recipePublic.sendRecipeToController(sendCommandRetentionRecipeConfig.recipeInfo, [], siteId, true);
     return sendCommandRetentionRecipeConfig
    }
  }


  async save(controls) {
    try {
    const enableCommand = [];
    const disableCommand = [];
    controls.forEach((control) => {
      if (control.commandRetentionStatus == 1) enableCommand.push(control.controlAbbr);
      if (control.commandRetentionStatus == 0) disableCommand.push(control.controlAbbr);
    });

    if(!enableCommand.length && !disableCommand.length){
      throwExceptionEmptyControlsList();
    }
    
    const {
      enableCommandRetention, 
      disableCommandRetention
    } = await this.fetchCommandAbbrInfoByControlAbbr(enableCommand, disableCommand)
    validateCommands(enableCommandRetention, enableCommand);
    validateCommands(disableCommandRetention, disableCommand);

    const [enableCommands, disableCommands] = await Promise.all([
      this.enable(enableCommandRetention),
      this.disable(disableCommandRetention)
    ])
    return {
      enableCommands,
      disableCommands,
    };
  } catch(e) {
    if (e.code == 'COMPONENT_ID_NOT_EXIST') {
      throw flaverr({
        code: e.code,
        message: e.message,
        HTTP_STATUS_CODE: 400
       })
    } else if (e.code == 'E_DEVICE_NOT_FOUND') {
      throw flaverr({
        code: e.code,
        message: e.message,
        HTTP_STATUS_CODE: 400
       })
    } else if (e.code == 'E_CONTROLLER_NOT_ATTACHED_TO_DEVICE') {
      throw flaverr({
        code: e.code,
        message: e.message,
        HTTP_STATUS_CODE: 400
       })
    } else if (e.code =='E_CONTROLLER_NOT_FOUND'){
      throw flaverr({
        code: e.code,
        message: e.message,
        HTTP_STATUS_CODE: 400
       })
    } else {
      throw e;
    }
  }
  }

  async fetch() {
    try {
    const [
      recipes,
      controlRelationship
      ] = await Promise.all([
        recipePublic.find({
        siteId: this.siteId,
        componentId: this.componentId,
        appType: this.appType,
      }),
      componentService.fetchControlRelationshipByComponent(this.siteId, this.componentId)
    ]);
    const configuredParamsControlRelationship = controlRelationship.filter(isControlConfiguredToAcceptCommand);
    const commandRetentionList = fetchCommandRetentionRecipes(recipes, configuredParamsControlRelationship);
    const $isRetentionActive = commandRetentionList.map((command) => {
      const {commandRetentionStatus, controlAbbr} = command;
      if (commandRetentionStatus == 1) return this.checkIsCommandRetentionExist(this.siteId, this.componentId, controlAbbr)
      return false
    });

    const isRetentionActiveOnAbbr = await Promise.all($isRetentionActive);
    return commandRetentionList.map((command, index) => {
      command.commandRetentionIsActive = isRetentionActiveOnAbbr[index];
      return command;
    })
  } catch (e) {
     if (e.code === 'COMPONENT_ID_NOT_EXIST') {
      throw flaverr({
        code: 'COMPONENT_ID_NOT_EXIST',
        message: e.message,
        HTTP_STATUS_CODE: 400
       })
     } else {
      throw e
     }
  }
  }


  async disable(controls) {
    if (!controls.length) return
    const recipes = await recipePublic.find({
      siteId: this.siteId,
      componentId: this.componentId,
      appType: this.appType,
      commandRetentionRecipeStatus: '1'
    });

    const recipeToDisable = recipeUtils.fetchDisableCommandInfo(recipes, controls);
    const $disableRecipes = recipeToDisable.map((disableRecipe) => {
        const  {rid, controllerId} = disableRecipe;
        const data = {
          rid : rid,
        }
        const functionType = 'deleteRecipe';
       return recipePublic.invokeFunctionOnController(data, functionType, controllerId, this.siteId)
    })

    await Promise.all($disableRecipes);
    return componentModeResponseBuilder(recipeToDisable);
  }

  async checkIsCommandRetentionExist(siteId, componentId, controlAbbr) {
    try {
      const retentionRecord = await fetchComponentLastCommand(
        siteId,
        componentId,
        controlAbbr
      );
      return !_.isEmpty(retentionRecord) ? true: false;
    } catch(e) {
      sails.log('Error > checkIsCommandRetentionExist', e)
      return false
    }
  }
}

module.exports = CommandRetention;