
class ThermostatRecipe {
    appType = "thermal"
    constructor({siteId}) {
        this.siteId = siteId
    }
    
    /**
     * Fetches active actuator recipes based on the component ID.
     * @param {string} componentId - componentId
     * @return {Promise<Array>} A list of active actuator recipes
     */
    async fetchActiveActuatorRecipes(componentId) {
        const recipeList = await Recipes.find({ siteId: this.siteId, componentId, appType: this.appType, isStage: '0', switchOff: '0', type:'setpoint' })
        return recipeList;
    }
    async fetchOTControlledActuatorRecipe(componentId) {
        return await Recipes.find({ siteId: this.siteId, componentId, appType: this.appType, isStage: '0', switchOff: '0', type: 'setpoint', isOTPanelControllingSetpoint:'1' })
    }
    async fetchActiveActuatorRecipeById(rid){
        const [recipeDetail] = await Recipes.find({ rid,appType: this.appType, isStage: '0', switchOff: '0', type: 'setpoint' })
        return recipeDetail;

    }
}
module.exports = ThermostatRecipe