const jwt = require('jsonwebtoken');
const secret = 'b52831dd10cbdff819e6635c82c9dd83';

module.exports = {
  issueJWTToken: function(payload, ts = 172800) { //default 48 hours Token Expiry time
    let tokenMeta = {
      expiresIn: ts
    };
    return jwt.sign(payload, secret, tokenMeta);
  },
  verifyJWTToken: function(token) {
    return jwt.verify(token, secret);
  },
  decodeJWTToken: function(token) {
    return jwt.decode(token);
  },
}
