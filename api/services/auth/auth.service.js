const authService = require('./auth.private');
const authUtils = require('../../utils/auth/utils');
const globalHelpers = require('../../utils/globalhelper');
const cacheService = require('../cache/cache.public');
const userService = require('../user/user.public');
const userSiteMapService = require('../userSiteMap/userSiteMap.service');

module.exports = {

  verifyJWTToken: authService.verifyJWTToken,
  issueJWTToken: authService.issueJWTToken,
  /**
   * Get List of Dejoule Services this userRole can access.
   * @param {string} roleName User role name
   */
  getAccessibleServicesByRoleName: async function (roleName) {
    try {
      let services = new Set(['public']); // default room for any user

      let userPolicies = await userService.getOldPoliciesByRoleName(roleName);  // This function is querying the policies from the role table
      if (globalHelpers.isNullish(userPolicies)) {                              // and it is in user service only.
        return services; // only access to public service
      }
      let policyServiceMap = authUtils.getPoliciesRoomMap();      // Getting this policy room map from dejoulePolicy.json should be the responsibility
      for (let policyName in userPolicies) {                      // of auth service only as it knows the resepective rooms for each policy.
        let policyAccess = userPolicies[policyName];
        if (policyAccess === 1 || policyAccess === '1') {
          if (policyServiceMap[policyName]) {
            let roomName = policyServiceMap[policyName];
            services.add(roomName);
          } else {
            sails.log.error(`Not defined policy ${policyName}`);
          }
        }
      }
      return Array.from(services);

    } catch (e) {
      sails.log.error('auth.service::getAccessibleServicesByRoleName:', e);
      throw e;
    }

  },
  /**
   * Check is user session exist in redis cache by browser hash
   * @param {string} clientToken secret browser hash of user
   * @return {Promise} true
   */
  getUserInfoFromClientToken: async function (clientToken) {
    try {
      const userId = await cacheService.get('u_' + clientToken);
      if (!userId) {
        sails.log.info(`[ISSUE_TOKEN_MYSTERY] Unable to fetch  u_${clientToken} from redis`)
      }
      let _socketId = await cacheService.smembers(clientToken);
      if(_.isEmpty(_socketId)){
        sails.log.info(`[ISSUE_TOKEN_MYSTERY] Unable to fetch socket Id by client Token from redis.Client Token is ${clientToken}`)
      }
    }
    catch (e) {
        sails.log.error('Error while fetching data from redis', e);
      }
  },
  /**
   * @description Is user has site access
   * @param userId
   * @param {String} siteId
   * @returns {Boolean}
   */
  userHasSiteAccess: async function(userId, siteId) {
      return userSiteMapService.checkSiteIdInUserSite(userId, siteId);
  }


};
