const notification = require("../notification/notification.service");
const diagnostic = require("../diagnostic/diagnostic.public");
const recipe = require("../recipe/recipe.service");
const modeService = require('../mode/mode.public')

module.exports = {
  init: () => {
    if (process.env.RABBITMQ_CONNECTION !== "FALSE") {
      diagnostic.DIAGNOSTIC_QUEUE.initConnection().then((_) => {
        diagnostic.DIAGNOSTIC_QUEUE.onMessage(diagnostic.eventHandler);
      });
      notification.RECIPE_ALERT_QUEUE.initConnection().then((_) => {
        notification.RECIPE_ALERT_QUEUE.onMessage(
          notification.handleRecipeAlert
        );
      });
      notification.NUDGE_ALERT_QUEUE.initConnection().then((_) => {
        notification.NUDGE_ALERT_QUEUE.onMessage(notification.handleNudgeAlert);
      });
      notification.EMAIL_QUEUE.initConnection().then((_) => {
        notification.EMAIL_QUEUE.onMessage(notification.handleEmail);
      });
      if(process.env.RABBITMQ_SMS_QUEUE == "TRUE"){
        notification.SMS_QUEUE.initConnection().then((_) => {
          notification.SMS_QUEUE.onMessage(notification.handleSMS);
        });
      }
      notification.NOTIFICATION_QUEUE.initConnection().then((_) => {
        notification.NOTIFICATION_QUEUE.onMessage(
          notification.handleDejouleNotification
        );
      });

      /* listening for new message from the RECIPE_QUEUE */
      recipe.RECIPE_QUEUE.initConnection().then((_) => {
        recipe.RECIPE_QUEUE.onMessage(recipe.handleTopicFromRabbitMQ);
      });
      /* listening for new message from the NOTIFICATION_QUEUE */
      notification.NOTIFICATION.initConnection().then((_) => {
        notification.NOTIFICATION.onMessage(
          notification.handleTopicFromRabbitMQ
        );
      });
      modeService.MODE_FEEDBACK_QUEUE.initConnection().then(()=>{
        modeService.MODE_FEEDBACK_QUEUE.onMessage(modeService.modeFeedbackMessageHandler)

      })
    }
  },
};
