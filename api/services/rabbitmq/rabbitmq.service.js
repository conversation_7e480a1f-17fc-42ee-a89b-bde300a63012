const amqp = require("amqplib");
const globalhelper = require("../../utils/globalhelper");

class consumerWithAckQueue {
  constructor(queueName) {
    this.amqp = amqp;
    this.queueName = queueName;

    sails.log("Setting Up Queue " + queueName);
  }

  async initConnection() {
    try {
      this.connection = await this.amqp.connect(sails.config.RABBITMQ_URL);
      this.channel = await this.connection.createChannel();
      await this.channel.assertQueue(this.queueName, {
        durable: true,
        exclusive: false,
      }); // create queue if not exists
      sails.log.info(
        `RabbitMQ Connection made with queue: ${this.queueName}`
      );
    } catch (e) {
      sails.log.error("consumerWithAckQueue::initConnection ", e);
      throw e;
    }
  }

  async onMessage(messageHandler) {
    let channel = this.channel;

    try {
      this.channel.consume(this.queueName, handleMessageAndSendAck, {
        noAck: false,
      }); // on message from queue call eventHandler
    } catch (err) {
      sails.log.error("consumerWithAckQueue::ONMESSAGE ", err);
    }

    try {
      this.connection.on("close", async () => {
        let interval = setInterval(reconnectStrategy, 10000);
        let that = this;

        async function reconnectStrategy() {
          try {
            sails.log("Connection Lost. Trying to reconnect");
            await that.initConnection();
            that.onMessage(handleMessageAndSendAck);
            clearInterval(interval);
          } catch (e) {
            sails.log.error(e);
          }
        }
      });
    } catch (err) {}

    async function handleMessageAndSendAck(message) {
      try {
        await messageHandler(message);
        channel.ack(message);
      } catch (e) {
        sails.log.error("consumerWithAckQueue:onMessage : ", e);
        channel.nack(message);
      }
    }
  }

  async sendMessage(message) {
    try {
      let payload = globalhelper.toString(message);
      this.channel.sendToQueue(this.queueName, Buffer.from(payload));
    } catch (e) {
      sails.log.error("consumerWithAckQueue::sendMessage ", e);
      return;
    }
  }
}

class consumeriWithoutAckQueue {
  constructor(queueName) {
    this.amqp = amqp;
    this.queueName = queueName;
    sails.log("Setting Up Queue " + queueName);
  }

  async onMessage(messageHandler) {
    let connectionPromise = this.amqp.connect(sails.config.RABBITMQ_URL);

    try {
      let conn = await connectionPromise;
      let channel = await conn.createChannel();
      await channel.assertQueue(this.queueName, {
        durable: true,
        exclusive: false,
      }); // create queue if not exists
      channel.consume(this.queueName, messageHandler, { noAck: true }); // on message from queue call eventHandler
    } catch (err) {
      sails.log.error("consumeriWithoutAckQueue::ONMESSAGE ", err);
    }
  }

  async sendMessage(message) {
    try {
      if (this.channel === undefined) {
        let conn = await amqp.connect(sails.config.RABBITMQ_URL);
        this.channel = await conn.createChannel();
        await this.channel.assertQueue(this.queueName, {
          durable: true,
          exclusive: false,
        }); // create queue if not exists
      }
      let payload = globalhelper.toString(message);

      this.channel.sendToQueue(this.queueName, Buffer.from(payload));
    } catch (e) {
      sails.log.error("consumeriWithoutAckQueue::sendMessage ", e);
      return;
    }
  }
}

module.exports = {
  consumerWithAckQueue,
  consumeriWithoutAckQueue,
};
