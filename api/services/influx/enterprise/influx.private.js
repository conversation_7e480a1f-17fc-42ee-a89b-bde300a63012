const Axios = require('axios');
const Joi = require('joi');
const CSVToJSON = require('csvtojson');
module.exports =(()=> {
    const config = {
        host: process.env.INFLUX_ENT_URL || 'http://timeseries.smartjoules.org',
        username: process.env.INFLUX_ENT_USERNAME,
        password: process.env.INFLUX_ENT_PASSWORD
    };
    const configSchema = Joi.object()
    .keys({
        host: Joi.string().required(),
        username: Joi.string().required(),
        password: Joi.string().required(),
    })
    .unknown(true);

    const { error } = configSchema.validate(config);
    if (error && process.env.NODE_ENV !== "development") throw new Error(`INFLUX CONFIG FOR ENTERPRISE IS NOT VALID - ${error}`);

    /**
     * @description Get data from influxdb enterprise using a flux query.
     * @param {string} query - The flux query to run for enterprise version.
     * @returns {Object} - The csv data returned from influxdb enterprise filtered to the json format.
    */
    async function runQuery(query,opts = {}) {
        try {

          let _parsedQuery = query;
          if (opts && opts.replacements) {
            let _replacements = opts.replacements;
            for (let key in _replacements) {
              let regEx = `{{${key}}}`
              let re = new RegExp(regEx, "g");
              _parsedQuery = _parsedQuery.replace(re, _replacements[key])
            }
          }
          if (opts && opts.debug && process.env.NODE_ENV !== 'production') {
            sails.log(_parsedQuery)
          }

        const requestObject = {
          url: `${config.host}/api/v2/query?pretty=true`,
          headers: {
            'Accept': 'application/csv',
            'Content-type': 'application/vnd.flux',
            'Authorization': `Token ${config.username}:${config.password}`
          },
          data : _parsedQuery
        };

        const response = await Axios.post(requestObject.url, requestObject.data, {headers: requestObject.headers});
        const jsonObj = await CSVToJSON({
         flatKeys: true
        }).fromString(response.data)

        return jsonObj;
      } catch (error) {
        sails.log.error(error);
           sails.log.error('[Service > influx] Error: ');
           throw new Error(error);
        }
    }


    async function runInfluxQLQuery(query) {
      try {
        const influxQlSchema = Joi.object()
          .keys({
            host: Joi.string().required(),
            username: Joi.string().required(),
            password: Joi.string().required(),
          })
          .unknown(true)
        const influxServerDetail = { ...config}
        const { error } = influxQlSchema.validate(config)
        if (error) throw new Error(`INVALID INFLUX QL REQUEST -${error}`)
        const requestInfo = {
          method: 'get',
          url: `${influxServerDetail.host}/query`,
          headers: { 
            'Accept': 'application/csv', 
            'Content-type': 'application/vnd.flux', 
            'Authorization': `Token ${influxServerDetail.username}:${influxServerDetail.password}`
          },
          params: {
            q: query
          }
        };
        const response = await retryOperation(()=> Axios.request(requestInfo));
        const jsonObj = await CSVToJSON({
          flatKeys: true
          }).fromString(response.data)

        return jsonObj;
      } catch (error) {
        sails.log.error(error);
        sails.log.error('[Service > influx OSS] InfluxQL query error: ' + error);
        throw new Error(error);
      }
    }

    async function retryOperation(fn, retries=5, delay= 1000) {
      const attempt = async (retryCount) => {
        try {
          const result = await fn();
          return result;
        } catch(e) {
          if (retryCount <= 0) {
            throw e
          }
          await new Promise((resolve)=> setTimeout(resolve, delay));
          return attempt(retryCount-1);
        }
      }
      return attempt(retries)
     }
    
    return {
        runQuery,
        runInfluxQLQuery
    }
})()
