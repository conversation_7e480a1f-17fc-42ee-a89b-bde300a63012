const Joi = require("joi");
const _ = require("lodash");
const moment = require('moment')
const qs = require('qs');

const {
  InfluxDB,
  Point,
  HttpError,
  fluxDuration,
  DEFAULT_WriteOptions,
  FluxTableMetaData
} = require("@influxdata/influxdb-client");
const { default: axios } = require("axios");

module.exports = (() => {
  const config = {
    host: process.env.INFLUX_URL,
    token: process.env.INFLUX_TOKEN,
    org: process.env.INFLUX_ORG,
    orgID: process.env.INFLUX_ORG_ID,
  };

  const configSchema = Joi.object()
    .keys({
      host: Joi.string().required(),
      token: Joi.string().required(),
      org: Joi.string().required(),
      orgID: Joi.string().required(),
    })
    .unknown(true);

  const { error } = configSchema.validate(config);
  // if (error && process.env.NODE_ENV !== "development") throw new Error(`INFLUX CONFIG IS NOT VALID - ${error}`);

  async function checkConnection() {
    //implement connection check functionality
  }
  function query(query, opts = {}, configName) {
    const influxServerConfig = _.isEmpty(configName) ? config: getInfluxConfig(configName);
    if (_.isEmpty(influxServerConfig)) {
      throw new Error(`INVALID_CONFIG_FOR_INFLUX - ${configName}. Influxdb configuration does not exist.`)
    }
    return new Promise((resolve, reject) => {
      const queryResult = []
      let _parsedQuery = query;
      if (opts && opts.replacements) {
        let _replacements = opts.replacements;
        for (let key in _replacements) {
          let regEx = `{{${key}}}`
          let re = new RegExp(regEx, "g");
          _parsedQuery = _parsedQuery.replace(re, _replacements[key])
        }
      }
      if (opts && opts.debug) {
        sails.log(_parsedQuery)
      }
      const queryApi = new InfluxDB({ url: influxServerConfig.host, token: influxServerConfig.token, timeout: 60000 }).getQueryApi(influxServerConfig.org)
      queryApi.queryRows(_parsedQuery, {
        next(row, tableMeta) {
          // console.log(JSON.stringify(o, null, 2))
          const rows = tableMeta.toObject(row)
          queryResult.push(rows);

        },
        error(error) {
          reject(error)
        },
        complete() {
          resolve(queryResult);
        },
      })
    })
  }
  async function write(params, configName) {
    const influxServerConfig = _.isEmpty(configName) ? config : getInfluxConfig(configName);
    if (_.isEmpty(influxServerConfig)) {
      throw new Error(`INVALID_CONFIG_FOR_INFLUX - ${configName}. Influxdb configuration does not exist.`);
    }

    const writeRequestSchema = Joi.object()
      .keys({
        data: Joi.object().required(),
        bucket: Joi.string().required(),
        measurement: Joi.string().required(),
      })
      .unknown(true)
    const { error } = writeRequestSchema.validate(params)
    if (error) throw new Error(`INVALID WRITE REQUEST -${error}`)
    const { data, bucket, measurement } = params
    // eslint-disable-next-line camelcase
    const flushBatchSize = DEFAULT_WriteOptions.batchSize

    const timePrecision = params.timePrecision || 's'
    // create points
    const { tags, fields, timestamp: defaultTimestamp } = params.data
    let _point = new Point(measurement)
    const timestamp = defaultTimestamp || moment().unix();
    // sorting tags by their keys name to optimize the performance
    //setting up those variable for detailed logging in writeSuccess.
    let _packetTimestamp = moment(timestamp * 1000).utcOffset(330).format('YYYY-MM-DDTHH:mm:ssZ')// formatting packet to GMT for easy testing. In docker, moment is using the UTC timezone instead of local
    let _connectionUrl = influxServerConfig.host
    let _org = influxServerConfig.org
    let _orgId = influxServerConfig.orgID
    tags.sort((a, b) =>
      a.key > b.key ? 1 : b.key > a.key ? -1 : 0
    )
    tags.forEach((tag) => {
      _point = _point.tag(tag.key, tag.value.toString())
    })
    fields.forEach((field) => {
      if (field.type === "float") {
        try {
          _point = _point.floatField(field.key, field.value);
        } catch (e) {
          sails.log.error(
            `[InfluxDB] operation=line_point_assignment msg="${e.message
            }" connectionUrl=${_connectionUrl} org=${_org} orgId=${_orgId} bucket=${params.bucket
            } measurement=${params.measurement
            } packet="${lines.toString()}" packet_timestamp=${_packetTimestamp}`
          );
        }
      } else if (field.type === "string") {
        try {
          _point = _point.stringField(field.key, field.value);
        } catch (e) {
          sails.log.error(
            `[InfluxDB] operation=line_point_assignment msg="${e.message
            }" connectionUrl=${_connectionUrl} org=${_org} orgId=${_orgId} bucket=${params.bucket
            } measurement=${params.measurement
            } packet="${lines.toString()}" packet_timestamp=${_packetTimestamp}`
          );
        }
      } else if (field.type === "integer") {
        try {
          _point = _point.intField(field.key, field.value);
        } catch (e) {
          sails.log.error(
            `[InfluxDB] operation=line_point_assignment msg="${e.message
            }" connectionUrl=${_connectionUrl} org=${_org} orgId=${_orgId} bucket=${params.bucket
            } measurement=${params.measurement
            } packet="${lines.toString()}" packet_timestamp=${_packetTimestamp}`
          );
        }
      } else if (field.type === "boolean") {
        try {
          _point = _point.booleanField(field.key, field.value);
        } catch (e) {
          sails.log.error(
            `[InfluxDB] operation=line_point_assignment msg="${e.message
            }" connectionUrl=${_connectionUrl} org=${_org} orgId=${_orgId} bucket=${params.bucket
            } measurement=${params.measurement
            } packet="${lines.toString()}" packet_timestamp=${_packetTimestamp}`
          );
        }
      } else {
        _point = _point.stringField(field.key, field.value);
      }
    });
    _point.timestamp(timestamp)
    const writeOptions = {
      /* the maximum points/line to send in a single batch to InfluxDB server */
      batchSize: flushBatchSize + 1, // don't let automatically flush data
      /* maximum time in millis to keep points in an unflushed batch, 0 means don't periodically flush */
      flushInterval: 0,
      /* maximum size of the retry buffer - it contains items that could not be sent for the first time */
      maxBufferLines: 30000,
      /* the count of retries, the delays between retries follow an exponential backoff strategy if there is no Retry-After HTTP header */
      maxRetries: 3,
      /* maximum delay between retries in milliseconds */
      maxRetryDelay: 15000,
      /* minimum delay between retries in milliseconds */
      minRetryDelay: 1000, // minimum delay between retries
      /* a random value of up to retryJitter is added when scheduling next retry */
      retryJitter: 1000,
      // ... or you can customize what to do on write failures when using a writeFailed fn, see the API docs for details
      writeFailed: function (error, lines, failedAttempts) {
        sails.log.error(`[InfluxDB] operation="write"  message="Unable to write data" connectionUrl=${_connectionUrl} org=${_org} orgId=${_orgId} bucket=${params.bucket} measurement=${params.measurement} packet="${lines.toString()}" packet_timestamp=${_packetTimestamp}`);
      },
      writeSuccess: function (lines) {
        sails.log.info(`[InfluxDB] operation="write"  message="successfully write data" connectionUrl=${_connectionUrl} org=${_org} orgId=${_orgId} bucket=${params.bucket} measurement=${params.measurement} packet="${lines.toString()}" packet_timestamp=${_packetTimestamp}`);
      }
    }

    const writeApi = new InfluxDB({ url: influxServerConfig.host, token: influxServerConfig.token }).getWriteApi(influxServerConfig.org, bucket, timePrecision, writeOptions)
    writeApi.writePoint(_point)
    sails.log(` ${_point.toLineProtocol(writeApi)}`)
    try {
      const response = await writeApi.close()
      return {
        status: 200,
        packet: _point.toString()
      };
    } catch (e) {
      if (e instanceof HttpError) {
        return {
          status: e.statusCode,
          message: e.message
        }
      } else if (e.message.includes("ECONNREFUSED")) {
        return {
          status: 504,
          message: e.message
        }
      }
      else {
        throw new Error(e)
      }
    }

  }
  function getInfluxConfig(configName) {
    const customConfiguration = {
      "iot_influxdb": {
        host: process.env.INFLUX_OSS_IOT_METRICS_URL,
        token: process.env.INFLUX_OSS_IOT_METRICS_TOKEN,
        org: process.env.INFLUX_OSS_IOT_METRICS_ORG, //used in flux query
        orgID: process.env.INFLUX_OSS_IOT_METRICS_ORG_ID, //used in flux query
      }
    }
    if (customConfiguration.hasOwnProperty(configName)) {
      return customConfiguration[configName];
    }
    return null;
  }
  async function queryUsingInfluxQl(query, configName, databaseName) {
    const influxServerConfig = getInfluxConfig(configName);
    if (_.isEmpty(influxServerConfig)) {
      throw new Error(`INVALID_CONFIG_FOR_INFLUX - ${configName}. Influxdb configuration does not exist.`)
    }
    try {
      const influxQlSchema = Joi.object()
        .keys({
          host: Joi.string().required(),
          database: Joi.string().required(),
          token: Joi.string().required(),
        })
        .unknown(true)
      const influxServerDetail = { ...influxServerConfig, database: databaseName }
      const { error } = influxQlSchema.validate(influxServerDetail)
      if (error) throw new Error(`INVALID INFLUX QL REQUEST -${error}`)
      const { host, database, token } = influxServerDetail
      let data = qs.stringify({
        'q': query
      });
      const requestObject = {
        method: 'get',
        url: `${host}/query?db=${database}&${data}`,
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json'
        },
      };

      const response = await retryOperation(()=> axios.request(requestObject));
      let resultObject = []
      if (response.data && response.data.results && response.data.results[0].series && response.data.results[0].series[0]) {
        const seriesData = response.data.results[0].series[0]
        const columns = seriesData.columns
        for (const values of seriesData.values) {
          resultObject.push(_.zipObject(columns, values));
        }
      }
      return resultObject
    } catch (error) {
      sails.log.error(error);
      sails.log.error('[Service > influx OSS] Error: ');
      throw new Error(error);
    }
  }


 async function retryOperation(fn, retries=5, delay= 1000) {
  const attempt = async (retryCount) => {
    try {
      const result = await fn();
      return result;
    } catch(e) {
      if (retryCount <= 0) {
        throw e
      }
      await new Promise((resolve)=> setTimeout(resolve, delay));
      return attempt(retryCount-1);
    }
  }
  return attempt(retries)
 }

  return {
    query,
    write,
    queryUsingInfluxQl,
  }
})();
