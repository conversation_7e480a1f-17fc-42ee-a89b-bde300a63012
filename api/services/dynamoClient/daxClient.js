const AmazonDaxClient = require("amazon-dax-client");
const AWS = require("aws-sdk");

const awsRegion = sails.config.REGION;
const dynamoDbCacheEndpoint = sails.config.dynamoDBCache;

AWS.config.update({
  region: awsRegion,
});

const dynamoDbClient = new AWS.DynamoDB.DocumentClient();
let daxDocumentClient = null;

if (!_.isEmpty(dynamoDbCacheEndpoint)) {
  const daxClient = new AmazonDaxClient({
    endpoints: [`${dynamoDbCacheEndpoint}:8111`],
    region: awsRegion,
    maxConnections: 50,
  });
  daxDocumentClient = new AWS.DynamoDB.DocumentClient({ service: daxClient });
}

module.exports = daxDocumentClient !== null ? daxDocumentClient : dynamoDbClient;
