const flaverr = require("flaverr");
const configuratorPageService = require("./configuratorPage.private");
const configuratorSystemService = require("../ConfiguratorSystem/configuratorSystem.public");
const errorHandler = require("../../utils/configuratorPage/errorHandler");
const ConfiguratorNode = require("../ConfiguratorNode/ConfiguratorNode");
const s3Service = require("../s3/s3.service");
const siteService = require("../site/site.public");
const cacheService = require("../cache/cache.public");
const ConfiguratorGraphFactory = require("../configuratorGraph/ConfiguratorGraphFactory");
const { attributes: page } = require('../../models/Nodes');
const { isValidSvgContent } = require('../../utils/globalhelper');
const fs = require("fs");

const TABLE_PAGE_TYPE = 2;
module.exports = {
  findOne: configuratorPageService.findOne,
  async createPage(params) {
    const { siteId, subsystemId, type, title, userId } = params;
    const subsystem = await configuratorSystemService.isSystemExist(siteId, subsystemId);
    if (!subsystem) {
      throw flaverr("E_SYSTEM_NOT_FOUND", new Error(`System not found`));
    }
    const newOrder = await this._getNextPageOrder(subsystemId);
    const pageType = type;
    const newPageObj = {
      subsystemId,
      pageType,
      title: title.trim(),
      order: newOrder,
      created_by: userId,
    };
    const page = await configuratorPageService.create(newPageObj);
    return page;
  },
  async _getNextPageOrder(subsystemId) {
    const activePages = await configuratorPageService.find({
      where: {
        subsystemId,
        status: 1,
      },
      sort: "order DESC",
      limit: 1,
    });
    if (activePages.length > 0) {
      return activePages[0].order + 1;
    }
    return 1;
  },
  async updatePage(params) {
    const { siteId, subsystemId, pageId, title, userId } = params;
    const subsystem = await configuratorSystemService.isSystemExist(siteId, subsystemId);
    if (!subsystem) {
      throw flaverr("E_SYSTEM_NOT_FOUND", new Error(`System not found`));
    }
    const updateData = {
      title: title.trim(),
      last_updated_by: userId,
    };
    const updatedPage = await configuratorPageService.updateOne(
      {
        id: pageId,
        status: 1,
      },
      updateData,
    );
    if (!updatedPage) {
      throw flaverr("E_PAGE_NOT_FOUND", new Error(`Page not found`));
    }
    return updatedPage;
  },

  async deletePage(params) {
    const { siteId, subsystemId, pageId, userId } = params;
    const subsystem = await configuratorSystemService.isSystemExist(siteId, subsystemId);
    if (!subsystem) {
      throw flaverr("E_SYSTEM_NOT_FOUND", new Error(`System not found`));
    }
    const deletedPage = await configuratorPageService.updateOne(
      { id: pageId },
      {
        status: 0,
        last_updated_by: userId,
      },
    );
    if (!deletedPage) {
      throw flaverr("E_PAGE_NOT_FOUND", new Error(`Page not found`));
    }
  },
  async fetchPageDetails(page) {
    const query = `
    SELECT cps.svg_path AS "svgPath"
    FROM sub_system_pages AS ssp
    LEFT JOIN configurator_page_svg cps ON cps.pages_ref_id = ssp.id AND cps.status = 1
    WHERE ssp.status = 1 AND ssp.id = $1
  `;
    const svgPathResult = await sails.getDatastore("postgres").sendNativeQuery(query, [page.id]);
    const svgPath = svgPathResult.rows[0]?.svgPath || null;

    let svgUrl = null;
    if (svgPath) {
      svgUrl = await s3Service.getSignedUrl(process.env.CONFIGURATOR_BUCKET, svgPath);
    }

    return {
      id: page.id,
      title: page.title,
      type: page.pageType,
      isPublished: page.isPublished,
      svgUrl,
      order: page.order,
    };
  },
  async getPages(params) {
    const { siteId, subsystemId } = params;
    const subsystem = await configuratorSystemService.isSystemExist(siteId, subsystemId);
    if (!subsystem) {
      throw flaverr("E_SYSTEM_NOT_FOUND", new Error(`System not found`));
    }
    const pages = await configuratorPageService.find({
      subsystemId,
      status: 1,
    });
    return pages.sort((a, b) => {
      if (a.order == 0 && b.order == 0) {
        return a.id - b.id;
      }
      if (a.order == 0) return 1;
      if (b.order == 0) return -1;
      if (a.order == b.order) {
        return a.id - b.id;
      }
      return a.order - b.order;
    });
  },
  fetchAllPageDetails: async (subsystemId) => {
    const query = `
  WITH ranked_pages AS (
    SELECT
      ssp.id AS "id",
      ssp.title AS "title",
      ssp.page_type AS "type",
      ssp.is_published AS "isPublished",
      ssp."order" AS "order",
      cps.svg_path AS "svgUrl",
      ROW_NUMBER() OVER (PARTITION BY ssp.id ORDER BY ssp."order") AS rn
    FROM
      sub_system_pages ssp
    LEFT JOIN
      configurator_page_svg cps
    ON
      pages_ref_id = ssp.id
    WHERE
      sub_system_id = $1
      AND ssp.status = 1
  )
  SELECT
    "id", "title", "type", "isPublished", "order", "svgUrl"
  FROM
    ranked_pages
  WHERE
    rn = 1
  ORDER BY
    "order";
`;

    const pageDetails = await sails.getDatastore("postgres").sendNativeQuery(query, [subsystemId]);

    if (_.isEmpty(pageDetails?.rows)) return [];

    const pagesData = pageDetails.rows;

    return Promise.all(
      pagesData.map(async (page) => {
        if (page.svgUrl) {
          page.svgUrl = await s3Service.getSignedUrl(process.env.CONFIGURATOR_BUCKET, page.svgUrl);
        }
        return page;
      })
    );
  },
  async publishPage(params) {
    const { siteId, pageId, userId } = params;

    const isSiteValid = await siteService.isValidSite(siteId);
    if (!isSiteValid) {
      throw flaverr("E_SITE_NOT_FOUND", new Error("Site does not exists"));
    }

    const updatedPage = await configuratorPageService.updateOne(
      {
        id: pageId,
        status: 1,
      },
      {
        isPublished: 1,
        last_updated_by: userId,
      },
    );

    if (!updatedPage) {
      throw flaverr("E_PAGE_NOT_FOUND", new Error(`Page does not exists`));
    }
    return updatedPage;
  },
  async unpublishPage(params) {
    const { siteId, pageId, userId } = params;

    if (!(await siteService.isValidSite(siteId))) {
      throw flaverr("E_SITE_NOT_FOUND", new Error("Site not found"));
    }

    const updatedPage = await configuratorPageService.updateOne(
      {
        id: pageId,
        status: 1,
      },
      {
        isPublished: 0,
        last_updated_by: userId,
      },
    );

    if (!updatedPage) {
      throw flaverr("E_PAGE_NOT_FOUND", new Error(`Page does not exists`));
    }
    await this.invalidatePageCache(pageId);
    return updatedPage;
  },
  async validatePage(pageId) {
    const isValidPage = await configuratorPageService.findOne({
      id: pageId,
      status: 1,
    });

    if (_.isEmpty(isValidPage)) {
      errorHandler.throwExceptionInvalidPageId(pageId);
    }
  },
  setPageCache: async function (pageId, data) {
    const cacheKey = `configurator:page:${pageId}`;
    const cacheTTL = sails.config.custom.configuratorPageDataTTL;
    try {
      await cacheService.set(cacheKey, JSON.stringify(data));
      await cacheService.expire(cacheKey, cacheTTL);
    } catch (error) {
      sails.log.error(`Error setting up the cached data for pageId: ${pageId}`);
      sails.log.error(error);
    }
  },
  async invalidatePageCache(pageId) {
    try {
      const cacheKey = `configurator:page:${pageId}`;
      await cacheService.delete(cacheKey);
    } catch (error) {
      sails.log.error(`Error deleting cached data for pageId: ${pageId}`);
      sails.log.error(error);
    }
  },
  async validateSVGPage(pageId) {
    const isValidPage = await configuratorPageService.findOne({
      id: pageId,
      status: 1,
      pageType: 1,
    });

    if (_.isEmpty(isValidPage)) {
      errorHandler.throwExceptionInvalidPage(pageId);
    }
  },

  uploadSVGtoGraphicTypePage: async function (param) {
    const { siteId, pageId, userId, svgFilePath } = param;
    let { filename: fileName, fd: fileLocation, type: fileType } = svgFilePath;
    const isSiteExist = await siteService.siteExists(siteId);
    if (!isSiteExist) {
      errorHandler.throwExceptionInvalidSite(siteId);
    }

    await this.validateSVGPage(pageId);
    await this.validatePageSiteMapping(pageId, siteId);

    const { ALLOWED_GRAPHIC_BASED_SVG_EXTENSIONS } = sails.config.custom;
    const fileExtension = fileName.split(".").pop().toLowerCase();
    if (!ALLOWED_GRAPHIC_BASED_SVG_EXTENSIONS.includes(fileExtension)) {
      throw flaverr(
        "E_INVALID_FILE_FORMAT",
        new Error(
          `File should be only in these ${ALLOWED_GRAPHIC_BASED_SVG_EXTENSIONS.join(",")} format`,
        ),
      );
    }

    const fileContent = await fs.promises.readFile(fileLocation, fileExtension === "ico" ? null : "utf-8");
      if (
        (fileExtension === "svg" && !isValidSvgContent(fileContent))) {
        throw flaverr(
          "E_INVALID_FILE_CONTENT",
          new Error("The content of the file is invalid or not an allowed format"),
        );
      }

    const fileUID = `${fileName.split(".")[0]}_${fileLocation.split("/").pop()}`;  
    const { Key: svgFileKey } = await s3Service.upload(
      fileLocation,
      fileUID,
      fileType,
      process.env.CONFIGURATOR_BUCKET,
    );
    if (!svgFileKey) {
      throw flaverr("E_FILE_NOT_UPLOADED", new Error("File not uploaded please try again later"));
    }
    const { id: svgGraphicTypePageId } = await createConfiguratorPage(svgFileKey, pageId, userId);
    return {
      configuratorPageSVGId: svgGraphicTypePageId,
      svgURL: await s3Service.getSignedUrl(process.env.CONFIGURATOR_BUCKET, svgFileKey),
    };

    async function createConfiguratorPage(svgPath, pageRefId) {
      return sails.getDatastore("postgres").transaction(async (dbTransactionObj) => {
        await ConfiguratorPageSVG.update(
          {
            pageRefId,
            updatedBy: userId,
          },
          {
            status: 0,
          },
        ).usingConnection(dbTransactionObj);

        const newPageRecord = await ConfiguratorPageSVG.create({
          svgPath,
          pageRefId,
          createdBy: userId,
        })
          .fetch()
          .usingConnection(dbTransactionObj);
        return newPageRecord;
      });
    }
  },
  async updatePageCustomConfig(pageId, customConfig, userId) {
    const serializedCustomConfig = JSON.stringify(customConfig);

    const query = `UPDATE sub_system_pages SET misc = $1,last_updated_by = $2 WHERE id = $3`;

    const updatedPage = await sails
      .getDatastore("postgres")
      .sendNativeQuery(query, [serializedCustomConfig, userId, pageId]);

    if (!updatedPage) {
      throw flaverr("E_PAGE_NOT_FOUND", new Error(`Page does not exists`));
    }
    return `Custom config of page with id ${pageId} updated successfully`;
  },
  getCachedDataByPageId: async function (pageId) {
    try {
      const cacheKey = `configurator:page:${pageId}`;
      const cachedData = await cacheService.get(cacheKey);
      if (!cachedData) return null;
      return JSON.parse(cachedData);
    } catch (error) {
      sails.log.error(`Error retrieving cached data for pageId: ${pageId}`);
      sails.log.error(error);
      return null;
    }
  },
  fetchPageDetailById: async function (siteId, pageId) {
    try {
      const isSiteExist = await siteService.siteExists(siteId);
      if (!isSiteExist) {
        errorHandler.throwExceptionInvalidSite(siteId);
      }

      await this.validatePage(pageId);
      await this.validatePageSiteMapping(pageId, siteId);
      let query = `
    SELECT
    ssp.title,
    ssp.id,
    cps.svg_path AS "svgPath",
    ssp.is_published AS "status",
    ssp.page_type AS "pageType",
    ssp.misc AS "customConfig"
    FROM sub_system_pages as ssp
    LEFT JOIN configurator_page_svg cps on cps.pages_ref_id = ssp.id and cps.status = 1
    WHERE ssp.status = 1 and ssp.id = $1
    `;
      let systemDetailRaw = await sails.getDatastore("postgres").sendNativeQuery(query, [pageId]);
      const { rows } = systemDetailRaw;
      if (!rows.length) {
        throw flaverr({
          code: "E_SYSTEM_NOT_FOUND",
          message: `Subsystem page not found`,
        });
      }

      const CONFIGURATOR_PAGE_TYPE = this.PAGE_TYPE;
      const INVERTED_CONFIGURATOR_PAGE_TYPE = _.invert(CONFIGURATOR_PAGE_TYPE);
      const pageDetail = rows[0];

      let response = [];

      if (!INVERTED_CONFIGURATOR_PAGE_TYPE.hasOwnProperty(pageDetail.pageType)) {
        errorHandler.throwExceptionInvalidPage(pageId);
      }
      if (pageDetail.pageType == CONFIGURATOR_PAGE_TYPE.SVG) {
        response = await getSVGPageDetails(pageDetail);
      }
      if (pageDetail.pageType == CONFIGURATOR_PAGE_TYPE.TABLE) {
        response = await getTablePageDetails(pageDetail);
      }
      if (pageDetail.pageType == CONFIGURATOR_PAGE_TYPE.GRAPH) {
        const graphRecords = await ConfiguratorGraphs.find({
          subsystemPageId: pageId,
          status: 1,
        }).sort("id DESC");
        if (_.isEmpty(graphRecords)) {
          return {
            pageId: pageDetail.id,
            pageType: CONFIGURATOR_PAGE_TYPE.GRAPH,
            pageTypeDescription: "GraphType",
            type: null,
            id: null,
            status: pageDetail.status,
          };
        }
        const graphRecord = graphRecords[0];
        const graph = new ConfiguratorGraphFactory({
          pageId,
          graphId: graphRecord.id,
          type: graphRecord.type,
          siteId,
        });
        response = await graph.fetch();
      }
      if (pageDetail.customConfig) response.customConfig = pageDetail.customConfig;
      return response;

      async function getTablePageDetails(pageDetail) {
        return [];
      }

      async function getSVGPageDetails(pageDetails) {
        const { title: pageTitle, id: pageId, status, svgPath } = pageDetails;

        let presignedSVGURI = null;
        let svgXmlContent = null;

        if (svgPath && !_.isEmpty(svgPath)) {
          presignedSVGURI = await s3Service.getSignedUrl(process.env.CONFIGURATOR_BUCKET, svgPath);
        }

        const data = await ConfiguratorNode.fetchAllConfiguredNodes(siteId, pageId);

        const response = {
          pageId,
          pageTitle,
          systemDataTagger: data,
          status,
          svgURL: presignedSVGURI,
        };

        if (presignedSVGURI) {
          const svgObjectContent = await s3Service.get(svgPath, process.env.CONFIGURATOR_BUCKET);
          const { Body: svgBuffer, ContentType: objectContentType } = svgObjectContent;

          if (objectContentType === "image/svg+xml") {
            svgXmlContent = svgBuffer.toString();
          }
        }

        response.svgXml = svgXmlContent;
        return response;
      }
    } catch (e) {
      if (e.statusCode === 400) {
        throw flaverr({
          code: "INVALID_GRAPH",
          message: e.msg,
        });
      } else {
        throw e;
      }
    }
  },
  validatePageSiteMapping: async (pageId, siteId) => {
    const mappingQuery = `
        SELECT site_id AS "siteId"
        FROM configurator_systems cs
        WHERE cs.id = (
            SELECT sub_system_id
            FROM sub_system_pages ssp
            WHERE id = $1 AND status = 1
            LIMIT 1
        )`;

    try {
      const mappingResult = await sails
        .getDatastore("postgres")
        .sendNativeQuery(mappingQuery, [pageId]);
      const pageMappedWithSiteId = mappingResult.rowCount ? mappingResult.rows[0].siteId : null;

      if (!pageMappedWithSiteId || pageMappedWithSiteId !== siteId) {
        errorHandler.throwExceptionInvalidSiteMapping(siteId);
      }
    } catch (error) {
      throw error;
    }
  },
  validateTablePage: async (pageId) => {
    const page = await configuratorPageService.findOne({
      id: pageId,
      status: 1,
    });
    if (!page) {
      errorHandler.throwExceptionInvalidPageId(pageId);
    }
    if (page.pageType != TABLE_PAGE_TYPE) {
      errorHandler.throwExceptionInvalidPageType(pageId);
    }
    return true;
  },
  getSiteIdFromPage: async (pageId) => {
    const query = `
    SELECT
    site_id AS "siteId"
    FROM configurator_systems cs
    WHERE cs.id = (
      SELECT sub_system_id
      FROM sub_system_pages ssp
      WHERE id = $1 AND status = 1
      LIMIT 1
    ) and cs.status != 0`;
    const result = await sails.getDatastore("postgres").sendNativeQuery(query, [pageId]);
    return result.rowCount ? result.rows[0].siteId : null;
  },
  TABLE_PAGE_TYPE,
  PAGE_TYPE: {
    SVG: 1,
    TABLE: 2,
    GRAPH: 3,
  },
  async savePageOrder({ siteId, subsystemId, pageIds }, userId) {
    const subsystem = await configuratorSystemService.isSystemExist(siteId, subsystemId);
    if (!subsystem) {
      throw flaverr(
        "E_SYSTEM_NOT_FOUND",
        new Error(`Subsystem not found for the given siteId: ${siteId}`),
      );
    }
    const uniquePageIds = [...new Set(pageIds)];
    const pages = await configuratorPageService.find({
      where: { id: { in: uniquePageIds } },
      select: ["id", "subsystemId"],
    });
    const validPages = pages.filter((page) => page.subsystemId === subsystemId);
    if (validPages.length !== uniquePageIds.length) {
      const invalidPageIds = uniquePageIds.filter(
        (id) => !validPages.some((page) => page.id === id),
      );
      throw flaverr(
        "E_INVALID_SUBSYSTEM",
        new Error(
          `Some pages(${invalidPageIds.join(", ")}) do not belong to the specified subsystem.`,
        ),
      );
    }
    await sails.getDatastore("postgres").transaction(async (db) => {
      const updatePromises = validPages.map((page) => {
        const order = uniquePageIds.indexOf(page.id) + 1;
        return ConfiguratorPages.updateOne(
          {
            id: page.id,
            status: 1,
          },
          { order, last_updated_by:userId },
        ).usingConnection(db);
      });
      await Promise.all(updatePromises);
    });
  },
  isPagePublished: async (pageId) => {
    const isPublished = await configuratorPageService.findOne({
      id: pageId,
      status: 1,
      isPublished: 1,
    });
    return isPublished ? true : false;
  },
};
