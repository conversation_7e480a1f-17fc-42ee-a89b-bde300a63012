const configuratorPageService = require('./configuratorPage.service');

module.exports = {
  fetchPageDetails: configuratorPageService.fetchPageDetails.bind(configuratorPageService),
  getPages: configuratorPageService.getPages.bind(configuratorPageService),
  fetchAllPageDetails: configuratorPageService.fetchAllPageDetails,
  findOne: configuratorPageService.findOne,
  validateTablePage: configuratorPageService.validateTablePage,
  getSiteIdFromPage: configuratorPageService.getSiteIdFromPage,
  getCachedDataByPageId:configuratorPageService.getCachedDataByPageId,
  setPageCache:configuratorPageService.setPageCache,
  TABLE_PAGE_TYPE: configuratorPageService.TABLE_PAGE_TYPE,
  isPagePublished: configuratorPageService.isPagePublished
};
