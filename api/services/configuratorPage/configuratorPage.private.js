module.exports = {
  find: async (searchParams) => {
    return ConfiguratorPages.find(searchParams);
  },
  findOne: async (searchParams) => {
    return ConfiguratorPages.findOne(searchParams);
  },
  update: async (searchParams, updateValue) => {
    return ConfiguratorPages.update(searchParams, updateValue);
  },
  updateOne: async (searchParams, updateValue) => {
    return ConfiguratorPages.updateOne(searchParams, updateValue);
  },
  create: async (param) => {
    return ConfiguratorPages.create(param)
      .fetch();
  },
  delete: async (searchParams) => {
    return ConfiguratorPages.destroy(searchParams);
  },
};
