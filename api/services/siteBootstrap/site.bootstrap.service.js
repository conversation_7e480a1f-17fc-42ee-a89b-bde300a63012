const roleService = require("../role/role.public");
const siteService = require("../site/site.public");
const deviceService = require("../device/device.public");
const componentService = require("../component/component.public");
const deviceTypeService = require("../devicetype/devicetype.public");
const userSiteMapService = require("../userSiteMap/userSiteMap.public");
const flaverr = require("flaverr");

class SiteBootstrapFacade {
  constructor(userId, role, siteId) {
    this.userId = userId;
    this.role = role;
    this.siteId = siteId;
  }

  async bootstrap() {
    const isValidSite = await siteService.isValidSite(this.siteId);
    if (!isValidSite) {
      throw flaverr("E_SITE_NOT_FOUND", new Error("Invalid siteId!"));
    }

    const [site, policy, usersites, devices, componentList, deviceTypes] = await Promise.all([
      this.getCurrentSiteDetails(),
      this.getUserPolicy(),
      this.getUserSites(),
      this.getDevices(),
      this.getComponents(),
      this.getDeviceTypes(),
    ]);
    const sitesList = await this.getSitesList(usersites);

    return {
      devices,
      componentList,
      sitesList,
      currentSite: site,
      plants: [],
      policy,
      params: deviceTypes,
    };
  }

  async getDevices() {
    return deviceService.getDevicesBySiteId(this.siteId);
  }
  async getUserPolicy() {
    return roleService.getRoleBootstrapDetail(this.role);
  }

  async getCurrentSiteDetails() {
    return siteService.getCurrentSiteDetail(this.siteId);
  }

  async getUserSites() {
    return userSiteMapService.getUserSiteIds(this.userId);
  }

  async getComponents() {
    return componentService.getComponents(this.siteId);
  }

  async getDeviceTypes() {
    return deviceTypeService.getDeviceTypes();
  }

  async getSitesList(usersites) {
    const getSiteNamePromises = usersites.map((usersite) => siteService.getSiteName(usersite));
    return Promise.all(getSiteNamePromises);
  }
}

module.exports = SiteBootstrapFacade;
