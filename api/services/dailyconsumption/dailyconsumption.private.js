/* eslint-disable no-undef */
module.exports = {

  /**
   * Dailyconsumption module private functions
   */
  create: async (params) => {
    return DailyConsumptions.create(params);
  },
  find: async (searchParams) => {
    // Dailyconsumption.find
    return DailyConsumptions.find(searchParams);
  },
  findOne: async (searchParams) => {
    // Dailyconsumption.findone
    let dailyconsumptions = await DailyConsumptions.find(searchParams).limit(1);
    return dailyconsumptions[0];
  },
  update: async (searchParams, updateValue) =>{
    return DailyConsumptions.update(searchParams, updateValue);
  },
  delete: async (searchParams) => {
    return DailyConsumptions.destroy(searchParams);
  },
};
