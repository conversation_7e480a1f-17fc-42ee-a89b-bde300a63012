/* eslint-disable no-undef */
const dailyconsumption = require('./dailyconsumption.private');
const globalHelper = require('../../utils/globalhelper');
const baselineUtils = require('../../utils/baseline/utils');
const selfUtils = require('../../utils/dailyconsumption/utils');
const influxDBEnterpriseService = require('../influx/enterprise/influx.public');
const BUCKET = 'device_component/autogen';
const moment = require('moment');
const deviceService = require('../device/device.service');
const energyConsumptionService = require('../energyConsumption/energyConsumption.service');
const MEASUREMENT = 'device';
module.exports = {
  create: dailyconsumption.create,
  find: dailyconsumption.find,
  findOne: dailyconsumption.findOne,
  update: dailyconsumption.update,
  delete: dailyconsumption.delete,

  /**
   * This function returns day wise daily consumption of a site between
   * start and endtime from daily consumption table.
   * @param {string} siteId unique siteId
   * @param {string} startTime Start time 
   * @param {string} endTime end time
   * @returns {array} Array of Objects from dailyconsumption table
   */
  async getDataBetween2Timestamp(
    siteId,
    startTime,
    endTime,

  ) {
    if (
      !globalHelper.isValidDateTime(startTime)
      || !globalHelper.isValidDateTime(endTime)
    ) {
      return { problems: ['Invalid start/endtime'] };
    }
    const startTimeMoment = globalHelper.toMoment(startTime);
    const endTimeMoment = globalHelper.toMoment(endTime);
    const differenceInDays = endTimeMoment.diff(startTimeMoment, 'days');

    if (startTimeMoment > endTimeMoment) {
      return { problems: ['start cannot be greater than endtime'] };
    }
    if (differenceInDays > baselineUtils.MAX_ALLOWED_DAYS) {
      return {
        problems: [
          `Cannot query data more than ${baselineUtils.MAX_ALLOWED_DAYS} days`,
        ],
      };
    }
    const dailyConsumptionbetween2Timestamps = await dailyconsumption.find({
      siteId,
      timestamp: { in: [startTime, endTime] },
    });

    return dailyConsumptionbetween2Timestamps;
  },
  /**
   * This function returns day wise daily consumption of a site between
   * start and endtime from daily consumption table In User perferred Unit
   * @param {string} siteId unique siteId
   * @param {string} startTime Start time 
   * @param {string} endTime end time
   * @param {string} preferredUnit User unit prefernce
   * @param {string} currUnit unit of consumption stored in dailyconsumption unit
   * @returns {array} Array of Objects from dailyconsumption table
   */
  async getDayWiseConsumptionOfSiteBetween2TSInPreferredUnit(
    siteId,
    startDate,
    endDate,
    preferredUnit,
  ) {
    try {
      const timezoneOffset = await sails.helpers.getSiteTimezone.with({siteId, timezoneFormat:'Z'})
      const formattedStartDate = moment(startDate).tz(timezoneOffset).format('YYYY-MM-DDT00:00:00Z');
      let formattedEndDate = moment(endDate).tz(timezoneOffset).format('YYYY-MM-DDT00:00:00Z');
      const currentMonth = moment().tz(timezoneOffset).month();
      const endDateMonth = moment(endDate).tz(timezoneOffset).month()
      if (endDateMonth == currentMonth) {
        formattedEndDate = moment().tz(timezoneOffset).startOf().format('YYYY-MM-DDT00:00:00Z');
      }
  
       let emList = await deviceService.fetchMainMetersByScanningSiteConfiguration(siteId);
       if (_.isEmpty(emList)) {
          sails.log.info(`[dashboard > dashboardConsumptionLoadaverage] siteId=${siteId} userId=${inputs._userMeta.id} message="Unable to find mainMeter list for site" mainMeterKey=${siteId}_mainMeter`);
          return []
       }

      const {dailyConsumption} = await energyConsumptionService.fetchDailyConsumptionForMultipleMeters(
        emList, 
        formattedStartDate, 
        formattedEndDate, 
        preferredUnit
      );
      return dailyConsumption.map(([time,val]) => {
        return  {
              siteId,
              "timestamp": moment(time).format('YYYY-MM-DD'),
              "actual": val,
          }
      } );

      
    } catch (e) {
      sails.log.error('dailyconsumtion::getDayWiseConsumptionOfSiteBetween2TSInPreferredUnit', e);
      throw (e);
    }
  },
  /**
   * This function returns day wise daily consumption of a site for
   * a day from daily consumption table In User unit perferred Unit
   * @param {string} siteId unique siteId
   * @param {string} timestamp Time/Date for which we need dailyconsumption of
   * @param {string} preferredUnit User unit prefernce
   * @param {string} currUnit unit of consumption stored in dailyconsumption unit
   * @returns {object} Objects from dailyconsumption table
   */
  async findOneInPreferredUnit(
    siteId,
    timestamp,
    preferredUnit,
    currUnit,
  ) {
    try {
      const consumption = await dailyconsumption.findOne({ siteId, timestamp });
      if (consumption === undefined) {
        return undefined;
      }
      if (preferredUnit === 'kvah') {
        return consumption;
      } else if (preferredUnit === 'kwh') {
        return selfUtils.getDailyConsumptionInKWH(consumption);
      } else {
        // Invalid case
        return undefined;
      }

      return consumption;
    } catch (e) {
      sails.log.error('dailyconsumptionservice::fineOneInPreferedUnit ', e);
      throw (e);
    }
  },
  /**
   * @description Get weekly consumption raw data from influxDB enterprise
   * @param {Array} devices
   * @param {Object} siteId 
   * @param {Object} filter startTime and endTime
   * @returns {Array}
   */
  async getWeeklyConsumptionExpertPro(devices, siteId,  filter) {
    try {
      const {startTime, endTime} = filter;
      const query = `
      import "math"
      import "timezone"
      option location = timezone.fixed(offset: {{timeZoneOffset}})
      from(bucket: "{{bucket}}")
      |> range(start: {{startTime}}, stop: {{endTime}})
      |> filter(fn: (r) => r["_measurement"] == "{{measurement}}")
      |> filter(fn: (r) => {{deviceIds}})
      |> filter(fn: (r) => r["siteId"] == "{{siteId}}")
      |> filter(fn: (r) =>  r["_field"] == "corrected_kvah" or r["_field"] == "corrected_ebkwh")
      |> group(columns: ["_field"], mode:"by")
      |> difference(nonNegative: false, columns: ["_value"])
      |> aggregateWindow(every: 1d, fn: sum, createEmpty: true,timeSrc:"_start")
      |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
      |> yield(name: "weeklyConsumptionData")
      `;
      const deviceIds = devices.map(id => `r["deviceId"] == "${id}"`).join(' or '); 
      const timeZoneOffset = await sails.helpers.getSiteTimezone.with({siteId, timezoneFormat:'utcOffsetInMinute'})
      let _weeklyConsumption = await influxDBEnterpriseService.runQuery(query,{
        replacements: {
          bucket: BUCKET,
          measurement: MEASUREMENT,
          deviceIds,
          siteId,
          startTime,
          endTime,
          timeZoneOffset
        },
        debug: true,
      })
      _weeklyConsumption = _weeklyConsumption.map((cons)=> {
        cons.corrected_kvah = Math.round(Math.abs(cons.corrected_kvah));
        cons.corrected_ebkwh = Math.round(Math.abs(cons.corrected_ebkwh));
        delete cons._start;
        delete cons._stop;
        delete cons.field1;
        delete cons.result;
        delete cons.table;
        return cons
      })
      return _weeklyConsumption;
    } catch (e) {
      sails.log('Error > dailyConsumptionData')
      sails.log.error(e);
      throw e;
    }
  },
  async getWeeklyConsumptionNF29(devices, siteId, filter) {
    try {
      const {startTime, endTime} = filter;
      
      const query = `
      import "math"
      import "timezone"
      option location = timezone.fixed(offset: {{timeZoneOffset}})
      from(bucket: "{{bucket}}")
      |> range(start: -8d)
      |> filter(fn: (r) => r["_measurement"] == "{{measurement}}")
      |> filter(fn: (r) => {{deviceIds}})
      |> filter(fn: (r) => r["siteId"] == "{{siteId}}")
      |> filter(fn: (r) =>  r["_field"] == "corrected_kvah" or r["_field"] == "corrected_kwh")
      |> group(columns: ["_field"], mode:"by")
      |> difference(nonNegative: false, columns: ["_value"])
      |> aggregateWindow(every: 1d, fn: sum, createEmpty: true,timeSrc:"_start")      
      |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
      |> yield(name: "weeklyConsumptionData")
      `;
      const deviceIds = devices.map(id => `r["deviceId"] == "${id}"`).join(' or ');
      const timeZoneOffset = await sails.helpers.getSiteTimezone.with({siteId, timezoneFormat:'utcOffsetInMinute'})
      let _weeklyConsumption = await influxDBEnterpriseService.runQuery(query,{
        replacements: {
          bucket: BUCKET,
          measurement: MEASUREMENT,
          deviceIds,
          siteId,
          startTime,
          endTime,
          timeZoneOffset
        },
        debug: true,
      })
      _weeklyConsumption.splice(0,1)
       _weeklyConsumption.pop()
      _weeklyConsumption = _weeklyConsumption.map((cons)=> {
        cons.corrected_kvah = Math.round(Math.abs(cons.corrected_kvah));
        cons.corrected_kwh = Math.round(Math.abs(cons.corrected_kwh));
        delete cons._start;
        delete cons._stop;
        delete cons.field1;
        delete cons.result;
        delete cons.table;
        return cons
      })
      return _weeklyConsumption;
    } catch (e) {
      sails.log('Error > dailyConsumptionData')
      sails.log.error(e);
      throw e;
    }
  }
};
