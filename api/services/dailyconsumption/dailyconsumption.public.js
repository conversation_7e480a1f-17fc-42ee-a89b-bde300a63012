/* eslint-disable no-undef */

const dailyconsumption = require('./dailyconsumption.service');

module.exports = {
  // Instead of find and findOne please use the 'getDayWiseConsumptionOfSiteBetween2TSInPreferredUnit' 
  // and 'findOneInPreferredUnit' functions.
  create: dailyconsumption.create,
  findOneInPreferredUnit: dailyconsumption.findOneInPreferredUnit,

  async getDayWiseConsumptionOfSiteBetween2TSInPreferredUnit(
    siteId,
    startDate,
    endDate,
    preferredUnit,
  ) {
    return dailyconsumption
      .getDayWiseConsumptionOfSiteBetween2TSInPreferredUnit(
        siteId, startDate, endDate, preferredUnit,
      );
  },
  find: dailyconsumption.find,
  getWeeklyConsumptionNF29: dailyconsumption.getWeeklyConsumptionNF29,
  getWeeklyConsumptionExpertPro: dailyconsumption.getWeeklyConsumptionExpertPro
};
