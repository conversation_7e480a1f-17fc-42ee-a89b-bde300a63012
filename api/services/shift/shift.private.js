const globalHelpers = require('../../utils/globalhelper');
const moment = require('moment');
const Joi = require('joi');
module.exports = {
  create: async (params) => {
    const schema = Joi.object().keys({
      siteId:Joi.string().required(),
      shift_start_time: Joi.string().required(),
      shift_end_time: Joi.string().required(),
      created_by: Joi.string().required()
    }).unknown()
    const { error } = schema.validate(params);
    if( error ) throw new Error(`INVALID_INPUT`);

    const _shiftId = `Shift-${params.shift_start_time}-${params.shift_end_time}`;
    const existingShift = await ShiftProductionData.findOne({
      pk:params.siteId,
      sk:_shiftId
    })
    if(existingShift){
      return null;
    }
    const payload = {
      pk:params.siteId,
      sk:_shiftId,
      shift_start_time: params.shift_start_time,
      shift_end_time: params.shift_end_time,
      shift_name: `Shift ${params.shift_start_time}-${params.shift_end_time}`,
      createdAt: moment().format('YYYY-DD-MMTHH:mm:ssZ'),
      createdBy:params.created_by
    }
    // noinspection JSUnresolvedVariable
    const result = await ShiftProductionData.create(payload);
    return {
      ...params,
      shiftId:_shiftId

    };
  },
  find: async (searchParams) => {
    //TODO: implementation pendind
  },
  findOne: async (searchParams) => {
    //TODO: implementation pendind
  },
  update: async (searchParams, updateValue) =>{
    //TODO: implementation pendind
  },
  delete: async (param) => {
    //TODO: implementation pendind
  },
};
