const ShiftService = require('./shift.private')
const globalutil = require('../../utils/globalhelper')
const AWS = require("aws-sdk");
const Joi = require('joi');
const moment = require('moment');
module.exports = {
  create:ShiftService.create,
  // update:ShiftService.update,
  // find:ShiftService.find,
  // findOne:ShiftService.findOne,
  // delete:ShiftService.delete,
  getShiftBySite:async (siteId)=>{
    const documentClient = new AWS.DynamoDB.DocumentClient({ });
    const params = {
      TableName:"ShiftProductionData",
      KeyConditionExpression:"#pk = :siteId and begins_with (#sk, :pattern)",
      ExpressionAttributeNames: {
        "#pk":"pk",
        "#sk":"sk"
      },
      ExpressionAttributeValues: {
        ":siteId":siteId,
        ":pattern": "Shift-",
      }
    };
    const result = await documentClient.query(params).promise()
    return result.Items
  },
}
