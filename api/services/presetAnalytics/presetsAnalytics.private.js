const RunHourAnalytics = require('./lib/RunHourAnalytics');
const PressureDropAnalysis = require('./lib/PressureDropAnalysis')
const StakedGraph = require('./lib/Graph/StakedGraph');
const Graph = require('./lib/Graph/Graph')
async function runHourAnalysis(siteId, filter, graphType) {
  let runHourAnalytics = await RunHourAnalytics.Build(siteId,filter);
  let rawData = await runHourAnalytics.fetch();
  let graph = new Graph()
  if(siteId === "lmw-coi"){
    graph = new StakedGraph();
  }
  return graph.draw(rawData)
}
async function pressureDropAnalysis(siteId,filter,unitPref,graphType) {
  let pressureDropAnalytics = await PressureDropAnalysis.Build(siteId,filter,unitPref);
  let rawData = await pressureDropAnalytics.fetch();
  return rawData;
}
module.exports = {
  runHourAnalysis,
  pressureDropAnalysis
}
