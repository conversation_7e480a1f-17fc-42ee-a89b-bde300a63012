class Graph {
  constructor(props = {}) {
    this.x_axis = props.x_axis || '_value';
    this.y_axis = props.y_axis || '_time';
  }
  draw(data){
    return data;
  }
  formatValue(value, decimalValue=2 ) {
    if(Number.isNaN(value)) {
      sails.log.warn(`${value} is not a number`);
      return value;
    }
    return Number.parseFloat(value.toFixed(decimalValue));
  }


}
module.exports = Graph;
