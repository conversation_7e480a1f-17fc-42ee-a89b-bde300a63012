const Graph = require('./Graph');
class StakedGraph extends Graph {
  constructor() {
    super();
  }
  draw(data){
    let graphData = { }
    for(let stakeKey  in data) {
      data[stakeKey].reduce((acm,curr)=>{
        let { deviceId, _value, _time } = curr;
        _value = this.formatValue(_value);
        if(acm.hasOwnProperty(deviceId)){
          acm[deviceId][stakeKey].push([_time,_value])
        } else {
          acm[deviceId] = this._point(Object.keys(data));
          acm[deviceId][stakeKey].push([_time,_value])
        }
        return acm;
      },graphData)

    }
    return graphData;
  }
  _validateRawData(rawData) {
    // TODO: implement validator function
    return true;
  }
  _point(stakeKeys){
    return stakeKeys.reduce((acm,curr)=>{
      acm[curr] = [];
      return acm;
    },{ })
    }
}
module.exports = StakedGraph;
