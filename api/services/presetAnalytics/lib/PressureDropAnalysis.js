const Analytics = require('./Analytics')
const Joi = require('joi');
const moment = require('moment')
const influx = require('../../influx/enterprise/influx.service')
const utils = require('../../../utils/globalhelper')
class pressureDropAnalysis extends Analytics {
  constructor(props) {
    super(props);
    this.siteId = props.siteId;
    this.debugFluxQuery = true;
    this.interval = {
      startTime: moment(props.startTime).utcOffset(props.timeZoneOffSet).format('YYYY-MM-DDT00:00:00Z'),
      endTime:moment(props.endTime).utcOffset(props.timeZoneOffSet).format('YYYY-MM-DDT00:00:00Z')
    }
    this.groupBy = props.groupBy || 'd';
    this.timeShift = '0m'
    this.assetList = props.assetList;
    this.siteConfigMap = {
      "lmw-coi": this._lmwCoiData
    }
    this.timeZoneOffSet = props.timeZoneOffSet || '330m';
    this.unitPref = props.unitPref
  }
  static async Build(siteId, filter, unitPref) {
    //TODO : validate all the input like group by,
    //TODO : limit the interval difference
    const timeZoneOffSet = await sails.helpers.getSiteTimezone.with({siteId, timezoneFormat:'utcOffsetInMinute'})
    return new pressureDropAnalysis({
      siteId,
      ...filter,
      unitPref,
      timeZoneOffSet
    })
  }
  async fetch(){
    let queryBuilder = this.siteConfigMap.hasOwnProperty(this.siteId) ? this.siteConfigMap[this.siteId] : this._defaultQueryBuilder;
    queryBuilder = queryBuilder.bind(this)
    return await queryBuilder(this.assetList);
  }
  async generateGraph(data, graphType){
    return data;
  }
  async _defaultQueryBuilder() {


  }

  // Start: custom calculation for specific site
  async _lmwCoiData(assetIds) {
    this.interval.startTime = moment(this.interval.startTime).format('YYYY-MM-DDT01:00:00Z');
    this.interval.endTime = moment(this.interval.endTime).format('YYYY-MM-DDT01:00:00Z');
    if(this.groupBy === 'd') {
      this.timeShift = '-60m';
    }

    const data = assetIds.reduce((acm,curr)=>{
      acm[curr]=[]
      return acm;
    },{ })
    const flowMeterWiseQueryMap = {
      "8642":{
        label:'Shop Floor',
        query:`
        import "timezone"
        option location = timezone.fixed(offset: {{timeZoneOffset}})
        from(bucket: "device_component/autogen")
          |> range(start: {{startTime}}, stop: {{endTime}})
          |> filter(fn: (r) => r["siteId"] == "lmw-coi")
          |> filter(fn: (r) =>  (r["_measurement"] == "components" or r["_measurement"] == "device") )
          |> filter(fn: (r) => (r["componentId"] == "lmw-coi_9" or r["deviceId"] == "8642"))
          |> filter(fn: (r) => r["_field"] == "recpres" or r["_field"] == "pressure")
          |> drop(columns: ["topic","_measurement","deviceId","componentId"])
          |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
          |> map(fn: (r) => ({ r with _value: r.recpres - r.pressure }))
          |> drop(columns: ["recpres","pressure"])
          |> filter(fn: (r) => r._value > 0.0)
          |> map(fn: (r) => ({ r with _field: "pressure_drop" }))
          |> timeShift(duration: {{timeShift}}, columns: ["_start", "_stop", "_time"])
          |> aggregateWindow(every: 1{{groupBy}}, fn: mean,timeSrc: "_start", createEmpty: false)
          |> yield(name: "8642")
          `
      },
      "8564":{
        label:'BTD JM',
        query:`
        import "timezone"
        option location = timezone.fixed(offset: {{timeZoneOffset}})
        from(bucket: "device_component/autogen")
          |> range(start: {{startTime}}, stop: {{endTime}})
          |> filter(fn: (r) => r["siteId"] == "lmw-coi")
          |> filter(fn: (r) =>  (r["_measurement"] == "components" or r["_measurement"] == "device") )
          |> filter(fn: (r) => r["componentId"] == "lmw-coi_9" or r["deviceId"] == "8564")
          |> filter(fn: (r) => r["_field"] == "recpres" or r["_field"] == "pressure")
          |> drop(columns: ["topic","_measurement","deviceId","componentId"])
          |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
          |> map(fn: (r) => ({ r with _value: r.recpres - r.pressure }))
          |> drop(columns: ["recpres","pressure"])
          |> filter(fn: (r) => r._value > 0.0)
          |> map(fn: (r) => ({ r with _field: "pressure_drop" }))
          |> timeShift(duration: {{timeShift}}, columns: ["_start", "_stop", "_time"])
          |> aggregateWindow(every: 1{{groupBy}}, fn: mean,timeSrc: "_start", createEmpty: false)
          |> yield(name: "8564")
          `
      },
      "8565": {
        label:'KV-16',
        query:`
        import "timezone"
        option location = timezone.fixed(offset: {{timeZoneOffset}})
        from(bucket: "device_component/autogen")
          |> range(start: {{startTime}}, stop: {{endTime}})
          |> filter(fn: (r) => r["siteId"] == "lmw-coi")
          |> filter(fn: (r) =>  (r["_measurement"] == "components" or r["_measurement"] == "device") )
          |> filter(fn: (r) => r["componentId"] == "lmw-coi_9" or r["deviceId"] == "8565")
          |> filter(fn: (r) => r["_field"] == "recpres" or r["_field"] == "pressure")
          |> drop(columns: ["topic","_measurement","deviceId","componentId"])
          |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
          |> map(fn: (r) => ({ r with _value: r.recpres - r.pressure }))
          |> drop(columns: ["recpres","pressure"])
          |> filter(fn: (r) => r._value > 0.0)
          |> map(fn: (r) => ({ r with _field: "pressure_drop" }))
          |> timeShift(duration: {{timeShift}}, columns: ["_start", "_stop", "_time"])
          |> aggregateWindow(every: 1{{groupBy}}, fn: mean,timeSrc: "_start", createEmpty: false)
          |> yield(name: "8565")
          `
      },
    }
    let promiseHolder= [];
    assetIds.forEach(assetId=>{
      let replacements = {
        startTime: this.interval.startTime,
        endTime: this.interval.endTime,
        timeZoneOffset: this.timeZoneOffSet,
        groupBy: this.groupBy,
        timeShift:this.timeShift

      };
      if(flowMeterWiseQueryMap[assetId]){
        let _data = influx.runQuery(flowMeterWiseQueryMap[assetId].query, {
          replacements,
          debug:this.debugFluxQuery
        });
        promiseHolder.push(_data);
      }
    })
    let _result = await Promise.allSettled(promiseHolder);
    let _dataHolder = [];
    _result.forEach(it=>{
      if(it.status === "fulfilled"){
        _dataHolder.push(...it.value);
      } else{
        sails.log.error(it.reason);
      }
    })
    _dataHolder.forEach(it=>{
      const { _time, _value, result } = it;
      let formattedValue =  parseFloat(Number(utils.pressureUnitConversion(_value,this.unitPref)).toFixed(2));
      data[result].push([moment(_time).unix()*1000, formattedValue])
    })
    return data;
  }
  // End: Custom queries specific to particular sites
}
module.exports = pressureDropAnalysis;
