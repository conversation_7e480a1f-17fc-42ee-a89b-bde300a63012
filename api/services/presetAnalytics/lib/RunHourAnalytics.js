const Analytics = require('./Analytics')
const Joi = require('joi');
const moment = require('moment')
// const influx = require('../../influx/influx.service');
const influx = require('../../influx/enterprise/influx.service');
class RunHourAnalytics extends Analytics {
  constructor(props) {
    super(props);
    this.siteId = props.siteId;
    this.debugFluxQuery = true;
    this.interval = {
      startTime: moment(props.startTime).format('YYYY-MM-DDTHH:mm:00Z'),
      endTime:moment(props.endTime).format('YYYY-MM-DDTHH:mm:00Z')
    }
    this.groupBy = props.groupBy || 'd';
    this.timeShift = '0m'
    this.assetList = props.assetList;
    this.siteConfigMap = {
      "lmw-coi": this._lmwCoiData
    }
    this.timeZoneOffSet = props.timeZoneOffSet || '330m';
  }
  static async Build(siteId, filter) {
    //TODO : validate all the input like group by,
    //TODO : limit the interval difference
    const timeZoneOffSet = await sails.helpers.getSiteTimezone.with({siteId, timezoneFormat:'utcOffsetInMinute'})
    return new RunHourAnalytics({
      siteId, ...filter, timeZoneOffSet
    })
  }
  async fetch(){
    let queryBuilder = this.siteConfigMap.hasOwnProperty(this.siteId) ? this.siteConfigMap[this.siteId] : this._defaultQueryBuilder;
    queryBuilder = queryBuilder.bind(this)
    return await queryBuilder(this.assetList);
  }
  async generateGraph(data, graphType){
    return data;
  }
  async _defaultQueryBuilder() {


  }

  // Start: custom calculation for specific site
  async _lmwCoiData(assetIds) {
    if(this.groupBy === 'd') {
      this.timeShift = '-60m';
      this.interval.startTime = moment(this.interval.startTime).format('YYYY-MM-DDT01:00:00Z');
      this.interval.endTime = moment(this.interval.endTime).format('YYYY-MM-DDT01:00:00Z');
    }
    function loadHourAssetQueryMap(assetId) {
      let _queryMap = {
        "lmw-coi_15": {
          label:"compressor_6",
          query:` import "timezone"
                  option location = timezone.fixed(offset: {{timeZoneOffset}})
                  from(bucket: "device_component/autogen")
                    |> range(start: {{startTime}}, stop: {{endTime}})
                    |> filter(fn: (r) => r["_measurement"] == "components")
                    |> filter(fn: (r) => r["componentId"] == "lmw-coi_15" and ( r["_field"] == "kw" or r["_field"] == "status" ) )
                    |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
                    |> filter(fn: (r) => r["status"] == 1.0 and r["kw"] >= 47.0 )
                    |> drop(columns: ["status"])
                    |> timeShift(duration: {{timeShift}}, columns: ["_start", "_stop", "_time"])
                    |> aggregateWindow(every: 1{{groupBy}}, fn: count, createEmpty: true,timeSrc:"_start",column:"kw")
                    |> drop(columns: ["status", "_start","_stop","_measurement"])
                    |> sort(columns: ["_time"], desc: false)
                    |> rename(columns: {componentId: "deviceId"})
                    |> yield(name: "loadHourAnalysis_compressor_6")
                 `,
        },
        "lmw-coi_16": {
          label:"compressor_6",
          query: `
                  import "timezone"
                  option location = timezone.fixed(offset: {{timeZoneOffset}})
                  from(bucket: "device_component/autogen")
                    |> range(start: {{startTime}}, stop: {{endTime}})
                    |> filter(fn: (r) => r["_measurement"] == "components")
                    |> filter(fn: (r) => r["componentId"] == "lmw-coi_16" and ( r["_field"] == "kw" or r["_field"] == "status" ) )
                    |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
                    |> filter(fn: (r) => r["status"] == 1.0 and r["kw"] >= 80.0 )
                    |> drop(columns: ["status"])
                    |> timeShift(duration: {{timeShift}}, columns: ["_start", "_stop", "_time"])
                    |> aggregateWindow(every: 1{{groupBy}}, fn: count, createEmpty: true,timeSrc:"_start",column:"kw")
                    |> drop(columns: ["status", "_start","_stop","_measurement"])
                    |> sort(columns: ["_time"], desc: false)
                    |> rename(columns: {componentId: "deviceId"})
                    |> yield(name: "loadHourAnalysis_compressor_6")
                 `,
        },
        "lmw-coi_17": {
          label:"compressor_6",
          query: `
                  import "timezone"
                  option location = timezone.fixed(offset: {{timeZoneOffset}})
                  from(bucket: "device_component/autogen")
                    |> range(start: {{startTime}}, stop: {{endTime}})
                    |> filter(fn: (r) => r["_measurement"] == "components")
                    |> filter(fn: (r) => r["componentId"] == "lmw-coi_17" and ( r["_field"] == "kw" or r["_field"] == "status" ) )
                    |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
                    |> filter(fn: (r) => r["status"] == 1.0 and r["kw"] >= 35.0 )
                    |> drop(columns: ["status"])
                    |> timeShift(duration: {{timeShift}}, columns: ["_start", "_stop", "_time"])
                    |> aggregateWindow(every: 1{{groupBy}}, fn: count, createEmpty: true,timeSrc:"_start",column:"kw")
                    |> drop(columns: ["status", "_start","_stop","_measurement"])
                    |> sort(columns: ["_time"], desc: false)
                    |> rename(columns: {componentId: "deviceId"})
                    |> yield(name: "loadHourAnalysis_compressor_6")
                 `,
        },
        "lmw-coi_10": {
          label:"compressor_1",
          query: `
                  import "timezone"
                  option location = timezone.fixed(offset: {{timeZoneOffset}})
                  from(bucket: "device_component/autogen")
                    |> range(start: {{startTime}}, stop: {{endTime}})
                    |> filter(fn: (r) => r["_measurement"] == "components")
                    |> filter(fn: (r) => r["componentId"] == "lmw-coi_10" and ( r["_field"] == "kw" or r["_field"] == "status" ) )
                    |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
                    |> filter(fn: (r) => r["status"] == 1.0 and r["kw"] >= 145.0 )
                    |> drop(columns: ["status"])
                    |> timeShift(duration: {{timeShift}}, columns: ["_start", "_stop", "_time"])
                    |> aggregateWindow(every: 1{{groupBy}}, fn: count, createEmpty: true,timeSrc:"_start",column:"kw")
                    |> drop(columns: ["status", "_start","_stop","_measurement"])
                    |> sort(columns: ["_time"], desc: false)
                    |> rename(columns: {componentId: "deviceId"})
                    |> yield(name: "loadHourAnalysis_compressor_1")
                 `,
        },
        "lmw-coi_11": {
          label:"compressor_2",
          query: `
                  import "timezone"
                  option location = timezone.fixed(offset: {{timeZoneOffset}})
                  from(bucket: "device_component/autogen")
                    |> range(start: {{startTime}}, stop: {{endTime}})
                    |> filter(fn: (r) => r["_measurement"] == "components")
                    |> filter(fn: (r) => r["componentId"] == "lmw-coi_11" and ( r["_field"] == "kw" or r["_field"] == "status" ) )
                    |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
                    |> filter(fn: (r) => r["status"] == 1.0 and r["kw"] >= 145.0 )
                    |> drop(columns: ["status"])
                    |> timeShift(duration: {{timeShift}}, columns: ["_start", "_stop", "_time"])
                    |> aggregateWindow(every: 1{{groupBy}}, fn: count, createEmpty: true,timeSrc:"_start",column:"kw")
                    |> drop(columns: ["status", "_start","_stop","_measurement"])
                    |> sort(columns: ["_time"], desc: false)
                    |> rename(columns: {componentId: "deviceId"})
                    |> yield(name: "loadHourAnalysis_compressor_2")
                 `,
        },
        "lmw-coi_12": {
          label:"compressor_3",
          query: `
                  import "timezone"
                  option location = timezone.fixed(offset: {{timeZoneOffset}})
                  from(bucket: "device_component/autogen")
                    |> range(start: {{startTime}}, stop: {{endTime}})
                    |> filter(fn: (r) => r["_measurement"] == "components")
                    |> filter(fn: (r) => r["componentId"] == "lmw-coi_12" and ( r["_field"] == "kw" or r["_field"] == "status" ) )
                    |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
                    |> filter(fn: (r) => r["status"] == 1.0 and r["kw"] >= 145.0 )
                    |> drop(columns: ["status"])
                    |> timeShift(duration: {{timeShift}}, columns: ["_start", "_stop", "_time"])
                    |> aggregateWindow(every: 1{{groupBy}}, fn: count, createEmpty: true,timeSrc:"_start",column:"kw")
                    |> drop(columns: ["status", "_start","_stop","_measurement"])
                    |> sort(columns: ["_time"], desc: false)
                    |> rename(columns: {componentId: "deviceId"})
                    |> yield(name: "loadHourAnalysis_compressor_3")
            `,
        },
        "lmw-coi_13": {
          label:"compressor_4",
          query: `
                  import "timezone"
                  option location = timezone.fixed(offset: {{timeZoneOffset}})
                  from(bucket: "device_component/autogen")
                    |> range(start: {{startTime}}, stop: {{endTime}})
                    |> filter(fn: (r) => r["_measurement"] == "components")
                    |> filter(fn: (r) => r["componentId"] == "lmw-coi_13" and ( r["_field"] == "kw" or r["_field"] == "status" ) )
                    |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
                    |> filter(fn: (r) => r["status"] == 1.0 and r["kw"] >= 145.0 )
                    |> drop(columns: ["status"])
                    |> timeShift(duration: {{timeShift}}, columns: ["_start", "_stop", "_time"])
                    |> aggregateWindow(every: 1{{groupBy}}, fn: count, createEmpty: true,timeSrc:"_start",column:"kw")
                    |> drop(columns: ["status", "_start","_stop","_measurement"])
                    |> sort(columns: ["_time"], desc: false)
                    |> rename(columns: {componentId: "deviceId"})
                    |> yield(name: "loadHourAnalysis_compressor_4")
            `,
        },
        "lmw-coi_14": {
          label: "compressor_5",
          query: `
                import "timezone"
                option location = timezone.fixed(offset: {{timeZoneOffset}})
                from(bucket: "device_component/autogen")
                  |> range(start: {{startTime}}, stop: {{endTime}})
                  |> filter(fn: (r) => r["_measurement"] == "components")
                  |> filter(fn: (r) => r["componentId"] == "lmw-coi_14" and ( r["_field"] == "kw" or r["_field"] == "status" ) )
                  |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
                  |> filter(fn: (r) => r["status"] == 1.0 and r["kw"] >= 19.0 )
                  |> drop(columns: ["status"])
                  |> timeShift(duration: {{timeShift}}, columns: ["_start", "_stop", "_time"])
                  |> aggregateWindow(every: 1{{groupBy}}, fn: count, createEmpty: true,timeSrc:"_start",column:"kw")
                  |> drop(columns: ["status", "_start","_stop","_measurement"])
                  |> sort(columns: ["_time"], desc: false)
                  |> rename(columns: {componentId: "deviceId"})
                  |> yield(name: "loadHourAnalysis_compressor_5")
                 `,
        },
      }
      return _queryMap[assetId] && _queryMap[assetId].query
    }
    function unloadHourAssetQueryMap(assetId) {
      let _queryMap = {
        "lmw-coi_15": {
          label:"compressor_6",
          query:`
                import "timezone"
                option location = timezone.fixed(offset: {{timeZoneOffset}})
                from(bucket: "device_component/autogen")
                  |> range(start: {{startTime}}, stop: {{endTime}})
                  |> filter(fn: (r) => r["_measurement"] == "components")
                  |> filter(fn: (r) => r["componentId"] == "lmw-coi_15" and ( r["_field"] == "kw" or r["_field"] == "status" ) )
                  |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
                  |> filter(fn: (r) => r["status"] == 1.0 and (r["kw"] >= 12.0 and r["kw"] < 47.0) )
                  |> drop(columns: ["status"])
                  |> timeShift(duration: {{timeShift}}, columns: ["_start", "_stop", "_time"])
                  |> aggregateWindow(every: 1{{groupBy}}, fn: count, createEmpty: true,timeSrc:"_start",column:"kw")
                  |> drop(columns: ["status", "_start","_stop","_measurement"])
                  |> sort(columns: ["_time"], desc: false)
                  |> rename(columns: {componentId: "deviceId"})
                  |> yield(name: "unloadhour_compressor_6")
            `,
        },
        "lmw-coi_16": {
          label:"compressor_7",
          query: `
                import "timezone"
                option location = timezone.fixed(offset: {{timeZoneOffset}})
                from(bucket: "device_component/autogen")
                  |> range(start: {{startTime}}, stop: {{endTime}})
                  |> filter(fn: (r) => r["_measurement"] == "components")
                  |> filter(fn: (r) => r["componentId"] == "lmw-coi_16" and ( r["_field"] == "kw" or r["_field"] == "status" ) )
                  |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
                  |> filter(fn: (r) => r["status"] == 1.0 and (r["kw"] > 0.0 and r["kw"] <  80.0) )
                  |> drop(columns: ["status"])
                  |> timeShift(duration: {{timeShift}}, columns: ["_start", "_stop", "_time"])
                  |> aggregateWindow(every: 1{{groupBy}}, fn: count, createEmpty: true,timeSrc:"_start",column:"kw")
                  |> drop(columns: ["status", "_start","_stop","_measurement"])
                  |> sort(columns: ["_time"], desc: false)
                  |> rename(columns: {componentId: "deviceId"})
                  |> yield(name: "unloadhour_compressor_7")
                 `,
        },
        "lmw-coi_17": {
          label:"compressor_6",
          query: `
                import "timezone"
                option location = timezone.fixed(offset: {{timeZoneOffset}})
                from(bucket: "device_component/autogen")
                  |> range(start: {{startTime}}, stop: {{endTime}})
                  |> filter(fn: (r) => r["_measurement"] == "components")
                  |> filter(fn: (r) => r["componentId"] == "lmw-coi_17" and ( r["_field"] == "kw" or r["_field"] == "status" ) )
                  |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
                  |> filter(fn: (r) => r["status"] == 1.0 and (r["kw"] > 0.0 and r["kw"] <  35.0) )
                  |> drop(columns: ["status"])
                  |> timeShift(duration: {{timeShift}}, columns: ["_start", "_stop", "_time"])
                  |> aggregateWindow(every: 1{{groupBy}}, fn: count, createEmpty: true,timeSrc:"_start",column:"kw")
                  |> drop(columns: ["status", "_start","_stop","_measurement"])
                  |> sort(columns: ["_time"], desc: false)
                  |> rename(columns: {componentId: "deviceId"})
                  |> yield(name: "unloadhour_compressor_6")
                 `,
        },
        "lmw-coi_10": {
          label:"compressor_1",
          query: `
                import "timezone"
                option location = timezone.fixed(offset: {{timeZoneOffset}})
                from(bucket: "device_component/autogen")
                  |> range(start: {{startTime}}, stop: {{endTime}})
                  |> filter(fn: (r) => r["_measurement"] == "components")
                  |> filter(fn: (r) => r["componentId"] == "lmw-coi_10" and ( r["_field"] == "kw" or r["_field"] == "status" ) )
                  |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
                  |> filter(fn: (r) => r["status"] == 1.0 and (r["kw"] > 0.0 and r["kw"] < 145.0) )
                  |> drop(columns: ["status"])
                  |> timeShift(duration: {{timeShift}}, columns: ["_start", "_stop", "_time"])
                  |> aggregateWindow(every: 1{{groupBy}}, fn: count, createEmpty: true,timeSrc:"_start",column:"kw")
                  |> drop(columns: ["status", "_start","_stop","_measurement"])
                  |> sort(columns: ["_time"], desc: false)
                  |> rename(columns: {componentId: "deviceId"})
                  |> yield(name: "unloadhour_compressor_1")
            `,
        },
        "lmw-coi_11":{
          label: "compressor_2",
          query: `
                import "timezone"
                option location = timezone.fixed(offset: {{timeZoneOffset}})
                from(bucket: "device_component/autogen")
                  |> range(start: {{startTime}}, stop: {{endTime}})
                  |> filter(fn: (r) => r["_measurement"] == "components")
                  |> filter(fn: (r) => r["componentId"] == "lmw-coi_11" and ( r["_field"] == "kw" or r["_field"] == "status" ) )
                  |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
                  |> filter(fn: (r) => r["status"] == 1.0 and (r["kw"] > 0.0 and r["kw"] < 145.0) )
                  |> drop(columns: ["status"])
                  |> timeShift(duration: {{timeShift}}, columns: ["_start", "_stop", "_time"])
                  |> aggregateWindow(every: 1{{groupBy}}, fn: count, createEmpty: true,timeSrc:"_start",column:"kw")
                  |> drop(columns: ["status", "_start","_stop","_measurement"])
                  |> sort(columns: ["_time"], desc: false)
                  |> rename(columns: {componentId: "deviceId"})
                  |> yield(name: "unloadhour_compressor_2")
            `,
        },
        "lmw-coi_12":{
          label: "compressor_3",
          query: `
                import "timezone"
                option location = timezone.fixed(offset: {{timeZoneOffset}})
                from(bucket: "device_component/autogen")
                  |> range(start: {{startTime}}, stop: {{endTime}})
                  |> filter(fn: (r) => r["_measurement"] == "components")
                  |> filter(fn: (r) => r["componentId"] == "lmw-coi_12" and ( r["_field"] == "kw" or r["_field"] == "status" ) )
                  |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
                  |> filter(fn: (r) => r["status"] == 1.0 and (r["kw"] > 0.0 and r["kw"] < 145.0) )
                  |> drop(columns: ["status"])
                  |> timeShift(duration: {{timeShift}}, columns: ["_start", "_stop", "_time"])
                  |> aggregateWindow(every: 1{{groupBy}}, fn: count, createEmpty: true,timeSrc:"_start",column:"kw")
                  |> drop(columns: ["status", "_start","_stop","_measurement"])
                  |> sort(columns: ["_time"], desc: false)
                  |> rename(columns: {componentId: "deviceId"})
                  |> yield(name: "unloadhour_compressor_3")
            `,
        },
        "lmw-coi_13":{
          label: "compressor_4",
          query: `
              import "timezone"
              option location = timezone.fixed(offset: {{timeZoneOffset}})
              from(bucket: "device_component/autogen")
                |> range(start: {{startTime}}, stop: {{endTime}})
                |> filter(fn: (r) => r["_measurement"] == "components")
                |> filter(fn: (r) => r["componentId"] == "lmw-coi_13" and ( r["_field"] == "kw" or r["_field"] == "status" ) )
                |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
                |> filter(fn: (r) => r["status"] == 1.0 and (r["kw"] > 0.0 and r["kw"] < 145.0) )
                |> drop(columns: ["status"])
                |> timeShift(duration: {{timeShift}}, columns: ["_start", "_stop", "_time"])
                |> aggregateWindow(every: 1{{groupBy}}, fn: count, createEmpty: true,timeSrc:"_start",column:"kw")
                |> drop(columns: ["status", "_start","_stop","_measurement"])
                |> sort(columns: ["_time"], desc: false)
                |> rename(columns: {componentId: "deviceId"})
                |> yield(name: "unloadhour_compressor_4")
            `,
        },
        "lmw-coi_14":{
          label: "compressor_5",
          query: `
                import "timezone"
                option location = timezone.fixed(offset: {{timeZoneOffset}})
                from(bucket: "device_component/autogen")
                  |> range(start: {{startTime}}, stop: {{endTime}})
                  |> filter(fn: (r) => r["_measurement"] == "components")
                  |> filter(fn: (r) => r["componentId"] == "lmw-coi_14" and ( r["_field"] == "kw" or r["_field"] == "status" ) )
                  |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
                  |> filter(fn: (r) => r["status"] == 1.0 and (r["kw"] > 0.0 and r["kw"] < 19.0) )
                  |> drop(columns: ["status"])
                  |> timeShift(duration: {{timeShift}}, columns: ["_start", "_stop", "_time"])
                  |> aggregateWindow(every: 1{{groupBy}}, fn: count, createEmpty: true,timeSrc:"_start",column:"kw")
                  |> drop(columns: ["status", "_start","_stop","_measurement"])
                  |> sort(columns: ["_time"], desc: false)
                  |> rename(columns: {componentId: "deviceId"})
                  |> yield(name: "unloadhour_compressor_5")
            `,
        },
      }
      return _queryMap[assetId] && _queryMap[assetId].query
    }
    let loadHourData = [];
    let unloadHourData = [];
    let replacements = {
      startTime: this.interval.startTime,
      endTime: this.interval.endTime,
      timeZoneOffset: this.timeZoneOffSet,
      groupBy: this.groupBy,
      timeShift: this.timeShift
    };
    for (const asset of assetIds) {
      if(loadHourAssetQueryMap(asset)){
        let _data = influx.runQuery(loadHourAssetQueryMap(asset), {
          replacements,
          debug: this.debugFluxQuery
        });
        loadHourData.push(_data)
      }
      if(unloadHourAssetQueryMap(asset)){
        let _data = influx.runQuery(unloadHourAssetQueryMap(asset), {
          replacements,
          debug:this.debugFluxQuery
        });
        unloadHourData.push(_data)
      }
    }
    let $loadHourData =  Promise.allSettled(loadHourData);
    let $unloadHourData = Promise.allSettled(unloadHourData);
    let [ loadHourAnalysis, unloadHourAnalysis ] = await Promise.all([$loadHourData, $unloadHourData]);

    let returnObject = {
      loadHourData: [],
      unloadHourData: [],
    }
    loadHourAnalysis.forEach(it=>{
      if(it.status === "fulfilled"){
        returnObject.loadHourData.push(...it.value);
      } else {
        sails.log.error(`message="error while fetching load hour data" siteId:${this.siteId} debug=${it.reason}`)
      }
    })
    unloadHourAnalysis.forEach(it=>{
      if(it.status === "fulfilled"){
        returnObject.unloadHourData.push(...it.value);
      } else {
        sails.log.error(`message="error while fetching unload hour data" siteId:${this.siteId} debug=${it.reason}`)
      }
    })

    //data processing
    returnObject.loadHourData.forEach(data=>{
      delete data.result;
      delete data.table;
      delete data._field;
      data._value = data["kw"]/60;
      data._time = moment(data._time).unix()*1000;
    })
    returnObject.unloadHourData.forEach(data=>{
      delete data.result;
      delete data.table;
      delete data._field;
      data._value = data["kw"]/60;
      data._time = moment(data._time).unix()*1000;
    })
    return returnObject;
  }
  // End: Custom queries specific to particular sites
}
module.exports = RunHourAnalytics;
