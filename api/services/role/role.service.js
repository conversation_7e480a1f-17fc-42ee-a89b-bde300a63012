const RoleService = require("./role.private");

const _capitalizeFirstLetter = (string) => string.charAt(0).toUpperCase() + string.slice(1);
const _flattenPolicies = (policies) => {
  const flattenedPolicies = {};
  /**Safely parse policies JSON*/
  try {
    policies = JSON.parse(policies);
  } catch (error) {
    sails.log.error("Invalid policies JSON");
    sails.log.error(error);
    throw new Error("Invalid policies JSON");
  }

  for (const [policyKey, policyValue] of Object.entries(policies)) {
    const subHeadings = policyValue.subHeadings || {};
    flattenedPolicies[`${policyKey}_View`] = policyValue.pageView ? "1" : "0";

    for (const [subHeadKey, subHeadValue] of Object.entries(subHeadings)) {
      const camelCaseSubHead = _capitalizeFirstLetter(subHeadKey);
      const innerPolicies = subHeadValue.policies || {};

      for (const [innerPolicyKey, innerPolicyValue] of Object.entries(innerPolicies)) {
        const innerPolicyCamelCase = _capitalizeFirstLetter(innerPolicyKey);
        const key = `${camelCaseSubHead}_${innerPolicyCamelCase}`;
        flattenedPolicies[key] = innerPolicyValue.hasAccess ? "1" : "0";
      }
    }
  }

  return JSON.stringify(flattenedPolicies);
};

const getRoleBootstrapDetail = async (roleName) => {
  const userPolicyCache = await RoleService.getUserPolicyCache(roleName);
  if (userPolicyCache) return userPolicyCache;
  const { createdAt, updatedAt, ...policy } = await RoleService.findOne({ roleName });
  policy.policies = _flattenPolicies(policy.policies);
  await RoleService.setUserPolicyCache(roleName, policy);
  return policy;
};

module.exports = {
  create: RoleService.create,
  find: RoleService.find,
  findOne: RoleService.findOne,
  update: RoleService.update,
  delete: RoleService.delete,
  getUserPolicyCacheKey: RoleService.getUserPolicyCacheKey,
  getUserPolicyCache: RoleService.getUserPolicyCache,
  setUserPolicyCache: RoleService.setUserPolicyCache,
  getRoleBootstrapDetail,
  getRBACPolicyCache: RoleService.getRBACPolicyCache,
  setRBACPolicyCache: RoleService.setRBACPolicyCache,
};
