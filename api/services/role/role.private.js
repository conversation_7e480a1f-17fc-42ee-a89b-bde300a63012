const cacheService = require('../cache/cache.public');

const getUserPolicyCacheKey = (roleName) => `Role-${roleName}`;

const getUserPolicyCache = async (roleName) => {
  try {
    const cacheKey = getUserPolicyCacheKey(roleName);
    const cachedData = await cacheService.get(cacheKey);
    return cachedData ? JSON.parse(cachedData) : null;
  } catch (error) {
    sails.log.error(`Error retrieving cached data for User Policy Details: ${roleName}`);
    sails.log.error(error);
    return null;
  }
};

const setUserPolicyCache = async (roleName, roleData) => {
  try {
    const cacheKey = getUserPolicyCacheKey(roleName);
    const roleDataCacheTTL = 24 * 60;
    await cacheService.set(cacheKey, JSON.stringify(roleData));
    await cacheService.expire(cacheKey, roleDataCacheTTL);
  } catch (error) {
    sails.log.error(`Unable to set cache for Role Data: ${roleName}`);
    sails.log.error(error);
  }
};

const _invalidateUserPolicyCache = async (roleName) => {
  try {
    if (!roleName) return;
    const cacheKey = getUserPolicyCacheKey(roleName);
    await cacheService.delete(cacheKey);
  } catch (error) {
    sails.log.error(`Unable to delete cached data for Role Data: ${roleName}`);
    sails.log.error(error);
  }
};

const getRBACKey = (roleName) => `RBAC-${roleName}`;

const getRBACPolicyCache = async (roleName) => {
  try {
    const cacheKey = getRBACKey(roleName);
    const cachedData = await cacheService.get(cacheKey);
    return cachedData ? JSON.parse(cachedData) : null;
  } catch (error) {
    sails.log.error(`Error retrieving cached data for User Policy Details: ${roleName}`);
    sails.log.error(error);
    return null;
  }
};

const setRBACPolicyCache = async (roleName, roleData) => {
  try {
    const cacheKey = getRBACKey(roleName);
    const roleDataCacheTTL = 24 * 60;
    await cacheService.set(cacheKey, JSON.stringify(roleData));
    await cacheService.expire(cacheKey, roleDataCacheTTL);
  } catch (error) {
    sails.log.error(`Unable to set cache for Role Data: ${roleName}`);
    sails.log.error(error);
  }
};

const _invalidateRBACPolicyCache = async (roleName) => {
  try {
    if (!roleName) return;
    const cacheKey = getRBACKey(roleName);
    await cacheService.delete(cacheKey);
  } catch (error) {
    sails.log.error(`Unable to delete cached data for Role Data: ${roleName}`);
    sails.log.error(error);
  }
};

module.exports = {
  /**
   * @description role module private functions
   */
  create: async (params) => {
    const { roleName } = params;
    await _invalidateUserPolicyCache(roleName);
    await _invalidateRBACPolicyCache(roleName);
    return Roles.create(params);
  },
  find: async (searchParams) => {
    return Roles.find(searchParams)
      .then((data) => {
        delete data.isDeleted;
        return data;
      });
  },
  findOne: async (searchParams) => {
    return Roles.findOne(searchParams);
  },
  update: async (searchParams, updateValue) => {
    const { roleName } = searchParams;
    await _invalidateUserPolicyCache(roleName);
    await _invalidateRBACPolicyCache(roleName);
    return Roles.update(searchParams, updateValue);
  },
  delete: async (searchParams) => {
    const { roleName } = searchParams;
    await _invalidateUserPolicyCache(roleName);
    await _invalidateRBACPolicyCache(roleName);
    return Roles.destroy(searchParams);
  },
  getUserPolicyCacheKey,
  setUserPolicyCache,
  getUserPolicyCache,
  getRBACPolicyCache,
  setRBACPolicyCache
};

