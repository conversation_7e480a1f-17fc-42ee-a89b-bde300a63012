/* eslint-disable no-undef */
module.exports = {

  /**
   * Baselines module private functions
   */
  create: async (params) => {
    return Baselines.create(params);
  },
  find: async (searchParams) => {
    // Baselines.find
    return Baselines.find(searchParams);
  },
  findOne: async (searchParams) => {
    // Baselines.findone
    let baseliness = await Baselines.find(searchParams).limit(1);
    return baseliness[0];
  },
  update: async (searchParams, updateValue) => {
    return Baselines.update(searchParams, updateValue);
  },
  delete: async (searchParams) => {
    return Baselines.destroy(searchParams);
  },

  Adjustment: {
    create: async (params) => {
      return Adjustments.create(params);
    },
    find: async (searchParams) => {
      // Adjustments.find
      return Adjustments.find(searchParams);
    },
    findOne: async (searchParams) => {
      // Adjustments.findone
      let adjustments;
      if (searchParams.sort) {
        const tempSearchParams = { ...searchParams };
        delete tempSearchParams.sort;
        adjustments = await Adjustments.find(tempSearchParams)
          .sort(searchParams.sort)
          .limit(1);
      } else {
        adjustments = await Adjustments.find(searchParams);
      }

      return adjustments[0];
    },
    update: async (searchParams, updateValue) => {
      return Adjustments.update(searchParams, updateValue);
    },
    delete: async (searchParams) => {
      return Adjustments.destroy(searchParams);
    },
  },

  DynamicTarget: {
    create: async (params) => {
      return DynamicTargets.create(params);
    },
    find: async (searchParams) => {
      // DynamicTargets.find
      return DynamicTargets.find(searchParams);
    },
    findOne: async (searchParams) => {
      // DynamicTargets.findone
      let dynamictargets = await DynamicTargets.find(searchParams).limit(1);
      return dynamictargets[0];
    },
    update: async (searchParams, updateValue) => {
      return DynamicTargets.update(searchParams, updateValue);
    },
    delete: async (searchParams) => {
      return DynamicTargets.destroy(searchParams);
    },
  }
};
