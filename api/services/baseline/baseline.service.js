const baselineService = require("./baseline.private");
const globalhelper = require("../../utils/globalhelper");
const moment = require("moment");
const ProductionService = require("../productionData/productionData.service");
const flaverr = require("flaverr");
const userSiteMapService = require("../userSiteMap/userSiteMap.public");
const baselineUtils = require("../../utils/baseline/utils");
const { getCurrentDateBaseline } = require('../../utils/baseline/utils');

const BASELINE_INPUT_DATE_FORMAT = baselineUtils.BASELINE_INPUT_DATE_FORMAT ;
const DYNAMODB_START_DATE_BASELINE_FORMAT = baselineUtils.DYNAMODB_START_DATE_BASELINE_FORMAT;
const DYNAMODB_END_DATE_BASELINE_FORMAT = baselineUtils.DYNAMODB_END_DATE_BASELINE_FORMAT;


const _saveBaseline = async (baselineRows) => {
  try {
    const batchInsert = baselineRows.map((row) => {
      const { siteId, startDate, target, consumptionValue, endDate } = row;
      return {
        Put: {
          TableName: "baselines",
          Item: {
            siteId: siteId,
            startDate: startDate.format(DYNAMODB_START_DATE_BASELINE_FORMAT),
            endDate: endDate.format(DYNAMODB_END_DATE_BASELINE_FORMAT),
            consumptionValue: consumptionValue,
            target: target,
            createdAt: moment().unix() * 1000,
            updatedAt: moment().unix() * 1000,
          },
        },
      };
    });
    const transactionParams = {
      TransactItems: batchInsert,
    };
    const DynamoDocClient = await sails.helpers.getDynamoDocClient();
    await DynamoDocClient.transactWrite(transactionParams).promise();
  } catch (e) {
    console.error(e);
    throw flaverr("E_UNABLE_TO_SAVE_BASELINE", new Error(e.message));
  }
};


module.exports = {
  create: baselineService.create,
  find: baselineService.find,
  findOne: baselineService.findOne,
  update: baselineService.update,
  delete: baselineService.delete,

  Adjustment: baselineService.Adjustment,
  DynamicTarget: baselineService.DynamicTarget,

  /**
   * -
   * @param {string} siteId site id to get baseline of
   * @param {string} currentDate Current date in YYYY-MM-DD format
   * @returns baseline Object, adding currentYearStartDate & currentYearSEndDate
   */
  async getCurrentBaseline(siteId,currentDate) {
    const timezoneOffset =  await sails.helpers.getSiteTimezone.with({siteId, timezoneFormat:'utcOffsetInMinute'})
    const date = currentDate && moment(currentDate, BASELINE_INPUT_DATE_FORMAT).utcOffset(timezoneOffset).date()
    const month =  currentDate &&  moment(currentDate, BASELINE_INPUT_DATE_FORMAT).utcOffset(timezoneOffset).month(); 
    const year = currentDate && moment(currentDate, BASELINE_INPUT_DATE_FORMAT).utcOffset(timezoneOffset).year();
    let baseline = await baselineService.find({
      siteId,
    });
    if (_.isEmpty(baseline)) { 
      throw flaverr('E_BASELINE_NOT_FOUND', new Error(`current date ${moment().date(date).month(month).format(BASELINE_INPUT_DATE_FORMAT)} is not lie in any baseline. Please go to  baseline page to configure it`))
    }

    baseline = baseline.map((cycle) => {
      let {startDate, endDate} = cycle
      startDate = moment(startDate, DYNAMODB_START_DATE_BASELINE_FORMAT).utcOffset(timezoneOffset)
      endDate = moment(endDate, DYNAMODB_END_DATE_BASELINE_FORMAT).utcOffset(timezoneOffset);
      cycle.startDate = startDate
      cycle.endDate = endDate
      return cycle
    })
    baselineUtils.sortBaseline(baseline)

    const targetBaselines = getCurrentDateBaseline(baseline, {date, month})
    if (_.isEmpty(targetBaselines)) {
      throw flaverr('E_BASELINE_NOT_FOUND', new Error(`current date ${moment().date(date).month(month).format(BASELINE_INPUT_DATE_FORMAT)} is not lie in any baseline cycle. Please go to baseline page to configure it`))
    }

    //if request date is exists in more than
    if(targetBaselines.length > 2) {
      const baselineNotValid = targetBaselines.map(baseline => {
        let {startDate, endDate} = baseline
        baseline.startDate = moment(startDate).format(BASELINE_INPUT_DATE_FORMAT);
        baseline.endDate = moment(endDate).format(BASELINE_INPUT_DATE_FORMAT);
        return baseline;
      });
      const dateOverlapping = baselineNotValid.map((baseline) => {
        let {startDate, endDate} = baseline
        return `${startDate} to ${endDate}` 
      })
      throw flaverr({
        code: 'E_MULTIPLE_BASELINE_EXIST',
        raw: baselineNotValid
      }, new Error(`Baseline of current date ${moment().date(date).month(month).format(BASELINE_INPUT_DATE_FORMAT)} exist in multiple baseline cycle ${dateOverlapping.join(', ')}`));
    }

    const [prevBaseline,endBaseline ] = targetBaselines;
    if(_.isEmpty(endBaseline)) return baselineUtils.formattedBaselineVO(prevBaseline, year)
    if (!prevBaseline.endDate.isSame(endBaseline.startDate) || moment().utcOffset(timezoneOffset).hours() >= 12) return baselineUtils.formattedBaselineVO(endBaseline, year)
    return baselineUtils.formattedBaselineVO(prevBaseline, year)
  },
  /**
   *
   * @param {string} siteId unique site id
   * @param {Object} baseline Baseline object from baseline table after adjusting dates to
   * current year
   * @param {Object} monthsBaselineAfterAdjustments Object of {date1: baselineAfterAdjustment, ...}.
   * The baselineAfterAdjustment is float value in user preferred consumption unit
   * @param {string} unitPreference User unit preference for consumption
   * @param {string} sitesConsumptionUnit Sites unit of storing consumption
   * @returns
   */
  async getTargetConsumptionBetween2TSInPreferredUnit(
    siteId,
    baseline,
    monthsBaselineAfterAdjustments,
    unitPreference,
    sitesConsumptionUnit
  ) {
    const dynamicTargetsConsumption = [];
    const dynamicTargets = await baselineService.DynamicTarget.find({
      siteId,
      timestamp: {
        in: [baseline.startDate, baseline.endDate],
      },
    });
    const defaultPercent = baseline.target;
    const dynamicTargetsMap = dynamicTargets.reduce((acc, dynamicTarget) => {
      acc[dynamicTarget.timestamp] = dynamicTarget;
      return acc;
    }, {});

    // monthsBaselineAfterAdjustments are alredy in user pref. unit
    // & dynamicTargetsMap are in site cons. unit
    for (let date in monthsBaselineAfterAdjustments) {
      let dynamicTarget = dynamicTargetsMap[date];
      let actualTarget, target;
      if (dynamicTarget !== undefined) {
        ({ actualTarget, target } = dynamicTarget);
        actualTarget = globalhelper.convertConsumptionToPreferredUnit(
          actualTarget,
          sitesConsumptionUnit,
          unitPreference
        );
        target = globalhelper.convertConsumptionToPreferredUnit(
          target,
          sitesConsumptionUnit,
          unitPreference
        );
      } else {
        actualTarget = monthsBaselineAfterAdjustments[date];
        target = (defaultPercent / 100) * monthsBaselineAfterAdjustments[date];
        target = globalhelper.roundNumber(target); // here baseline is already in preferred unit
      }

      dynamicTargetsConsumption.push({ date, actualTarget, target });
    }
    return dynamicTargetsConsumption;
  },

  /*
   * @description - Generating efficieny for LMW
   * @param {date} currentDate | last date of month
   * @param {array} actualConsumptionInBaselineDates | actual consumption for lmw-coi
   *
   *
   * */
  async generatingMovingEfficiencyGraphForLMW(
    startDate,
    endDate,
    actualConsumptionInBaselineDates
  ) {
    let _endDate = moment(endDate).add(1, "d").format("YYYYMMDD");
    let _startDate = moment(startDate).startOf("month").format("YYYYMMDD");
    let _productionData = await ProductionService.getProductionData(
      "lmw-coi",
      _startDate,
      _endDate
    );
    let _productionDataMap = _productionData.reduce((acm, curr) => {
      acm[curr.production_date] = curr.production_value;
      return acm;
    }, {});
    return actualConsumptionInBaselineDates
      .filter((it) => {
        if (_productionDataMap.hasOwnProperty(it.timestamp)) {
          return true;
        } else {
          return false;
        }
      })
      .map((it) => {
        it.actual = it.actual / _productionDataMap[it.timestamp];
        it.actualkwh = it.actualkwh / _productionDataMap[it.timestamp];
        return it;
      });
  },

  async checkUserSiteAccess(userId, siteId) {
    const userHasAccess = await userSiteMapService.findOne({
      userId,
      siteId,
    });
    return userHasAccess ? true : false;
  },

  async addOrUpdateBaselineCycle(siteId, baselineObj) {
    const { prevBaseline, newBaseline } = baselineObj;
    const baseline = Object.assign({}, newBaseline);
    const _prevBaseline = Object.assign({}, prevBaseline);
    if (baselineUtils.validateMaxDaysExceedInCycle(baseline)) {
      throw flaverr('E_DAYS_LIMIT_EXCEEDED', new Error('Baseline cycle limit exceeded'))
    };
    if (baselineUtils.validateInconsistentSiteId(prevBaseline, newBaseline, siteId)) {
      throw flaverr('INCONSISTENT_SITE_ID_NOT_ALLOWED', new Error('Inconsistent site id found'))
    }
    let updateBaselineCycle;
    if (prevBaseline) {
      updateBaselineCycle= await this._updateExistingBaseline(siteId, _prevBaseline, baseline);
    } else {
      updateBaselineCycle = await this._addNewBaselineRow(siteId, baseline);
    }
    return this.getBaselineBySiteId(siteId)
  },

  async deleteBaseline(siteId, startDate) {
    startDate = moment(startDate, BASELINE_INPUT_DATE_FORMAT).format(
      DYNAMODB_START_DATE_BASELINE_FORMAT
    );
    const baseline = await Baselines.findOne({ siteId, startDate });
    if (_.isEmpty(baseline)) {
      throw flaverr("E_BASELINE_NOT_FOUND", new Error(`Baseline not found`));
    }
    try {
      return Baselines.destroy({ siteId, startDate });
    } catch (e) {
      sails.log("[baseline > deleteBaseline] unable to delete baseline", e);
      throw flaverr(
        "E_BASELINE_NOT_DELETABLE",
        new Error("Baseline not deletable")
      );
    }
  },
  async _addNewBaselineRow(siteId, baseline) {
    let _existingBaseline = await Baselines.find({ siteId });

    //Gaurd clause to validate more than 12 cycle is not allowed
    if (_existingBaseline.length + 1 > 12) {
      throw flaverr(
        {
          code: "MORE_THAN_12_BASELINE_NOT_ALLOWED",
          raw: baseline,
        },
        new Error(`baseline can not have more than 12 cycle.`)
      );
    }

    const timezoneOffset = Number(await sails.helpers.getSiteTimezone.with({siteId,  timezoneFormat:'utcOffsetInNumber'}))
    _existingBaseline = _existingBaseline.map((it) => {
      const { endDate, startDate } = it;
      it.endDate = moment(endDate, DYNAMODB_END_DATE_BASELINE_FORMAT).utcOffset(
        timezoneOffset
      );
      it.startDate = moment(
        startDate,
        DYNAMODB_START_DATE_BASELINE_FORMAT
      ).utcOffset(timezoneOffset);
      return it;
    });
    let _newBaselines = [baseline].map((it) => {
      const { endDate, startDate } = it;
      it.endDate = moment(endDate, BASELINE_INPUT_DATE_FORMAT).utcOffset(timezoneOffset);
      it.startDate = moment(startDate, BASELINE_INPUT_DATE_FORMAT).utcOffset(
        timezoneOffset
      );
      return it;
    });
    let mergedBaselineList = _existingBaseline.concat(_newBaselines);
    baselineUtils.sortBaseline(mergedBaselineList);

    const _overlapBaseline =
      baselineUtils.getOverlappedBaselineCycle(mergedBaselineList);
    if (!_.isEmpty(_overlapBaseline)) {
      throw flaverr(
        {
          code: "OVERLAPPED_BASELINE_ROW_DETECTED",
          raw: _overlapBaseline,
        },
        new Error(
          `baseline for cycle  ${_overlapBaseline[1].startDate.format(
            BASELINE_INPUT_DATE_FORMAT
          )} and ${_overlapBaseline[1].endDate.format(
            BASELINE_INPUT_DATE_FORMAT
          )} is overlapping between ${_overlapBaseline[0].startDate.format(
            BASELINE_INPUT_DATE_FORMAT
          )} and ${_overlapBaseline[0].endDate.format(
            BASELINE_INPUT_DATE_FORMAT
          )}`
        )
      );
    }
    await _saveBaseline(_newBaselines);
    return mergedBaselineList
  },
  async _updateExistingBaseline(siteId, prevBaseline, baseline) {
    let { startDate, endDate } = prevBaseline;
    const timezoneOffset = Number(await sails.helpers.getSiteTimezone.with({siteId,  timezoneFormat:'utcOffsetInNumber'}))
    startDate = moment(startDate, "YYYY-MM-DD").utcOffset(timezoneOffset);
    endDate = moment(endDate, "YYYY-MM-DD").utcOffset(timezoneOffset);

    let _existingBaseline = await Baselines.find({ siteId });

    _existingBaseline = _existingBaseline.map((it) => {
      const { endDate, startDate } = it;
      it.endDate = moment(endDate, DYNAMODB_END_DATE_BASELINE_FORMAT).utcOffset(
        timezoneOffset
      );
      it.startDate = moment(
        startDate,
        DYNAMODB_START_DATE_BASELINE_FORMAT
      ).utcOffset(timezoneOffset);
      return it;
    });

    if (
      !_existingBaseline.filter(
        (it) => it.startDate.isSame(startDate) && it.endDate.isSame(endDate)
      ).length
    ) {
      throw flaverr(
        {
          code: "E_PREV_BASELINE_NOT_FOUND",
          raw: { prevBaseline, baseline },
        },
        new Error(
          `previous baseline not exist. please add it to the baseline first`
        )
      );
    }

    _existingBaseline = _existingBaseline.filter(
      (it) => !it.startDate.isSame(startDate)
    );

    let _newBaselines = [baseline].map((it) => {
      const { endDate, startDate } = it;
      it.endDate = moment(endDate, BASELINE_INPUT_DATE_FORMAT).utcOffset(timezoneOffset);
      it.startDate = moment(startDate, BASELINE_INPUT_DATE_FORMAT).utcOffset(
        timezoneOffset
      );
      return it;
    });
    let mergedBaselineList = _existingBaseline.concat(_newBaselines);
    baselineUtils.sortBaseline(mergedBaselineList);

    const _overlapBaseline =
      baselineUtils.getOverlappedBaselineCycle(mergedBaselineList);
    if (!_.isEmpty(_overlapBaseline)) {
      throw flaverr(
        {
          code: "OVERLAPPED_BASELINE_ROW_DETECTED",
          raw: _overlapBaseline,
        },
        new Error(
          `baseline for cycle  ${_overlapBaseline[1].startDate.format(
            BASELINE_INPUT_DATE_FORMAT
          )} and ${_overlapBaseline[1].endDate.format(
            BASELINE_INPUT_DATE_FORMAT
          )} is overlapping between ${_overlapBaseline[0].startDate.format(
            BASELINE_INPUT_DATE_FORMAT
          )} and ${_overlapBaseline[0].endDate.format(
            BASELINE_INPUT_DATE_FORMAT
          )}`
        )
      );
    }

    await Baselines.destroy({
      siteId,
      startDate: moment(startDate).format(DYNAMODB_START_DATE_BASELINE_FORMAT),
    });
    await _saveBaseline([baseline]);
    return mergedBaselineList;
  },
  async getBaselineBySiteId(siteId){
    let baselines = await Baselines.find({ siteId });
    const timezoneOffset = Number(await sails.helpers.getSiteTimezone.with({siteId,  timezoneFormat:'utcOffsetInNumber'}))
    baselineUtils.sortBaseline(baselines)
    return baselines.map((baseline) => {
      baseline.startDate = moment(baseline.startDate,DYNAMODB_START_DATE_BASELINE_FORMAT).utcOffset(timezoneOffset).format(
        BASELINE_INPUT_DATE_FORMAT
      );
      baseline.endDate = moment(baseline.endDate,DYNAMODB_END_DATE_BASELINE_FORMAT).utcOffset(timezoneOffset).format(
        BASELINE_INPUT_DATE_FORMAT
      );
      return baseline;
    });
  }
};
