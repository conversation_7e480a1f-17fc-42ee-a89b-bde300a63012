const cacheService = require('./cache.service');

/**
 * <PERSON>ot add any other get (like smembers, lrange etc.) as the .get function is
 * thin wrapper on all these.
 */

module.exports = {
  set: cacheService.set,
  sadd: cacheService.sadd,
  expire: cacheService.expire,
  get: cacheService.get,
  listAddAndTrim: cacheService.listAddAndTrim,
  hmset: cacheService.hmset,
  hset: cacheService.hset,
  hgetall: cacheService.hgetall,
  smembers: cacheService.smembers,
  _con: cacheService._con,
  acquireLock: async function (lockKey, identifier, expirationInSecond) {
    const result = await cacheService.set(lockKey, identifier, 'NX', 'EX', expirationInSecond,);
    if (result === 'OK') {
      return true;
    }
    return false;
  },
  releaseLock: async function (lockKey, uniqueIdentifier) {
    const currentIdentifier = await cacheService.get(lockKey);
    if (currentIdentifier === uniqueIdentifier) {
      await cacheService.delete(lockKey);
    }
  },
  delete: cacheService.delete,
  srem: cacheService.srem,
  zadd: cacheService.zadd,
  mget: cacheService.mget
};
