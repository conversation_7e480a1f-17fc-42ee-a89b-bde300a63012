const os = require('os');
const hostname = os.hostname();
const redis = require('redis');
const { promisify } = require('util');
const Sentry = require('../../services/logTransport/sentry.service');

const redisCommandDefaultValues = {
  set: 'OK',
  expire: 0,
  del: 0,
  get: null,
  sadd: 0,
  lpush: 0,
  ltrim: 'OK',
  type: 'none',
  lrange: [],
  smembers: [],
  srem: 0,
  hmset: 'OK',
  hgetall: {},
  hset: 0,
  exists: 0,
  sismember: 0,
};

let client = redis.createClient(sails.config.cachePort, sails.config.cacheHost, {
  retry_strategy: function (options) {
    /**Log and reconnect after a delay*/
    sails.log.error(
      `[Redis] Reconnecting to Redis,message="${options?.error?.message}" and errorCode="${options?.error?.code}"`
    );
    Sentry.setTag('hostname', hostname);
    Sentry.captureException(new Error(options));
    /**Retry after 30 seconds*/
    return 1000 * 30;
  },
});

client.on('ready', function () {
  sails.log.info('[Redis] Redis connection established successfully');
});

client.on('connect', function () {
  client.client('SETNAME', hostname);
});

client.on('error', function (err) {
  sails.log.error('[Redis] Redis error:', err);
  throw err;
});

const handleRedisCommand = async (command, ...args) => {
  if (client.connected) {
    return promisify(command)
      .bind(client)(...args);
  } else {
    sails.log.error(`[Redis] Client is not connected. Returning null for command: ${command.name}`);
    return undefined;
  }
};

const set = (...args) => handleRedisCommand(client.set, ...args);
const expire = (...args) => handleRedisCommand(client.expire, ...args);
const del = (...args) => handleRedisCommand(client.del, ...args);
const get = (...args) => handleRedisCommand(client.get, ...args);
const sadd = (...args) => handleRedisCommand(client.sadd, ...args);
const lpush = (...args) => handleRedisCommand(client.lpush, ...args);
const ltrim = (...args) => handleRedisCommand(client.ltrim, ...args);
const type = (...args) => handleRedisCommand(client.type, ...args);
const lrange = (...args) => handleRedisCommand(client.lrange, ...args);
const smembers = (...args) => handleRedisCommand(client.smembers, ...args);
const srem = (...args) => handleRedisCommand(client.srem, ...args);
const hmset = (...args) => handleRedisCommand(client.hmset, ...args);
const hgetall = (...args) => handleRedisCommand(client.hgetall, ...args);
const hset = (...args) => handleRedisCommand(client.hset, ...args);
const exists = (...args) => handleRedisCommand(client.exists, ...args);
const sismember = (...args) => handleRedisCommand(client.sismember, ...args);
const mget = (...args) => handleRedisCommand(client.mget, ...args);

module.exports = {
  set,
  expire,
  delete: del,
  get,
  sadd,
  lpush,
  ltrim,
  type,
  lrange,
  smembers,
  hmset,
  hgetall,
  hset,
  exists,
  sismember,
  _con: client,
  srem,
  mget
};
