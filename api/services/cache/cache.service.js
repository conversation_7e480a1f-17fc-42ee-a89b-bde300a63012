const cacheService = require('./cache.private');
const globalHelpers = require('../../utils/globalhelper');
const Sentry = require('../logTransport/sentry.service');
const os = require('os');

module.exports = {
  // set a {key , value } pair
  set: cacheService.set,
  get: async function (key) {

    if (typeof key !== 'string' || key === '') {
      throw new Error('Invalid key');
    }

    let keyType = await cacheService.type(key);
    let value;

    if (keyType === 'string') {
      value = await cacheService.get(key);
    } else if (keyType === 'list') {
      value = await cacheService.lrange(key, 0, -1);
    } else if (keyType === 'set') {
      value = await cacheService.smembers(key);
    }
    return value;
  },

  lpush: cacheService.lpush,

  // set-add: add multiple values to key in a set,
  sadd: async function (key, values) {
    if (!key || !values) {
      throw new Error('Required key and [Array]value.');
    }
    let valuesArray;
    if (Array.isArray(values)) {
      valuesArray = values;
    } else if (typeof values === 'string') {
      valuesArray = [values];
    } else {
      throw new Error('value should be array or string');
    }
    let promiseArr = valuesArray.map(value => cacheService.sadd(key, value));
    try {
      await Promise.all(promiseArr);
    } catch (e) {
      throw e;
    }
    return true;
  },
  /**
   * Insert all the specified values at the head of the list stored at key.
   * Length of List is mainted to be that of trimcount. So if list exceeds
   * the length of trimcount, the values that were inserted 'first' will be
   * deleted.. [FIFO]
   * @param {string} key List Name in cache
   * @param {Array} values Array of values to add into list
   * @param {integer} trimcount trim list to specified length
   */
  listAddAndTrim: async function (key, values, trimcount) {
    if (globalHelpers.isNullish(key) || globalHelpers.isNullish(values)) {
      throw new Error('Required key and [Array]value.');
    }
    let valuesArray;
    if (Array.isArray(values)) {
      valuesArray = values;
    } else if (typeof values === 'string') {
      valuesArray = [values];
    } else {
      throw new Error('value should be array or string');
    }
    if (Number.isInteger(trimcount) === false) {
      throw new Error('trimcount should be valid integer');
    }

    let promiseArr = valuesArray.map(value => cacheService.lpush(key, value));
    try {
      await Promise.all(promiseArr);
    } catch (e) {
      throw e;
    }
    try {
      await cacheService.ltrim(key, 0, trimcount);
    } catch (e) {
      throw e;
    }
    return true;

  },

  /**
   * Save user token to Redis
   * @param {string} userId - Unique user identifier
   * @param {string} siteId - Unique site identifier
   * @param {string} token - JWT token
   */
  setUserAuthToken: async function (userId, siteId, token) {
    const ttl = 172800;
    if (!userId || !siteId || !token) {
      throw new Error('All parameters (userId, siteId, token) are required');
    }

    const setName = `userAuth:${userId}`;
    const key = `auth:${userId}:${siteId}`;
    try {
      await cacheService.sadd(setName, key);
      await cacheService.set(key, token);
      await cacheService.expire(key, ttl);
    } catch (error) {
      sails.log.error('[setUserAuthToken] Error:', error);
    }
  },
  /**
   * Check if a user token is valid in the cache
   * @param {string} userId - Unique user identifier
   * @param {string} siteId - Unique site identifier
   * @param {string} token - JWT token to validate
   * @returns {Promise<boolean>} - True if valid, false otherwise
   */
  isUserAuthTokenValid: async function (userId, siteId, token) {
    if ([userId, siteId, token].some(param => !param)) {
      throw new Error('Missing required parameters: userId, siteId, and token are all required.');
    }

    const key = `auth:${userId}:${siteId}`;
    try {
      const storedTokenInCache = await cacheService.get(key);
      if (storedTokenInCache === undefined) {
        sails.log.error('[isUserAuthTokenValid] Token not found due to Redis Connectivity:', {
          userId,
          siteId
        });

        Sentry.setTag('hostname', os.hostname());
        Sentry.setContext('Redis Connectivity', {
          errorMessage: `Token not found for isUserAuthTokenValid due to redis connectivity: ${userId}, siteId: ${siteId}`,
          userId,
          siteId
        });
        Sentry.captureException(new Error(`Token not found for isUserAuthTokenValid due to redis connectivity: ${userId}, siteId: ${siteId}`));
        return true;
      }
      return !!storedTokenInCache;
    } catch (error) {
      sails.log.error('[isUserAuthTokenValid] Error:', error);
      throw Error(error);
    }
  },

  /**
   * Deletes all tokens for a user from Redis.
   * @param {string} userId - The unique user identifier.
   */
  deleteAllUserTokens: async function (userId) {
    if (!userId) {
      throw new Error('UserId is required');
    }

    const setName = `userAuth:${userId}`;
    try {
      // Fetch all keys associated with the user
      const keys = await cacheService.smembers(setName);

      if (keys && keys.length > 0) {
        // Delete all keys associated with the user
        const deletePromises = keys.map((key) => cacheService.delete(key));
        await Promise.all(deletePromises);
      }

      // Delete the set as well
      await cacheService.delete(setName);

      sails.log.info(`[deleteAllUserTokens] All tokens for userId=${userId} have been deleted.`);
    } catch (error) {
      sails.log.error('[deleteAllUserTokens] Error:', error);
    }
  },

  /**
   * Expire the given key after given number of seconds
   * @param {string} key cache key to expire
   * @param {Number} seconds number of seconds
   */
  expire: cacheService.expire,
  hmset: cacheService.hmset,
  hset: cacheService.hset,
  hgetall: cacheService.hgetall,
  exists: cacheService.exists,
  sadd: cacheService.sadd,
  smembers: cacheService.smembers,
  sismember: cacheService.sismember,
  delete: cacheService.delete,
  _con: cacheService._con,
  srem: cacheService.srem,
  mget: cacheService.mget
};
