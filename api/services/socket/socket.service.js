const socketService = require('./socket.private');
const cacheService = require('../cache/cache.public');
const globalhelper = require('../../utils/globalhelper');
const axios = require('axios');
const secret = 'e9caf762-31d0-4643-b8ce-24902c3a7781';
const socketBroadcastURL = sails.config.monolithURL + '/v1/auth/socketBroadcast'
// Topics list SERVICENAME to FRONTEND topic
const SOCKET_FRONTEND_TOPICS = {
  'NOTIFICATION': 'dj-notification'
};

module.exports = {

  SOCKET_FRONTEND_TOPICS,

  /**
   * Send a socket response to public channel to frontend with given siteId & topic
   * @param {string} siteId site id
   * @param {string} topic A mutially agreed feed name between FrontEnd and Backend to
   * communicate on for a particular feed
   * @param {object} data Object to send to
  */
  notifyJouleTrackPublicRoom: async (siteId, topic, data) => {
    // let roomName = `jt_${siteId}_public`;
    let room = 'public';
    if (globalhelper.isNullish(topic)) {
      return {problems: ['Topic cannot be empty']};
    }
    try {
      await axios.post(socketBroadcastURL, {
        siteId,
        room,
        topic,
        data,
        secret
      });
      // await socketService.broadcast(roomName, topic, data);
    } catch (e) {
      sails.log.error(e);
      throw 'Unable to broadcast to socket';
    }
  },

  /**
   * Send a socket response to frontend with given siteId & topic
   * @param {string} siteId site id
   * @param {sting} serviceName Enum ["recipe", "diagnostic", "acplant"]
   * @param {string} topic A mutually agreed feed name between FrontEnd and Backend to
   * communicate on for a particular feed
   * @param {object} data Object to send to
   */
  notifyServiceOnJouleTrack: async function(siteId, serviceName, topic, data) {
    if (globalhelper.isNullish(topic)) {
      return {problems: ['Topic cannot be empty']};
    }
    try {
      await axios.post(socketBroadcastURL, {
        siteId,
        room: serviceName,
        topic,
        data,
        secret
      });
    } catch (e) {
      sails.log.error(e);
      throw 'Unable to broadcast to socket';
    }

  },
  /**
   * Add socket Id to a room which can be used to group socketIds
   * on some measure and use it as broadcast channel to
   * send message to mass socketIds grouped in that room
   * @param {string} socketId unique Id of frontend socket connection
   * @param {string} room Room to add given socketId to( If room doesnt exist it will create
   * that room and add socketId to that room)
   */
  joinJouleTrack: async (socketId, room) => {
    // just add socketId to room
    sails.sockets.join(socketId, room);
  },
  sendSocketMessageToSocketIds: socketService.broadcast,
  /**
   *  Send a socket response to frontend to specific users
   * @param {array} userIds List of user Ids to send message to
   * @param {string} topic A mutually agreed feed name between FrontEnd and Backend to
   * communicate on for a particular feed
   * @param {string} siteId User can be logged into multiple sites. So this makes sure 
   * user on correct site gets the alert
   * @param {any} data JSON/String data to send to Frontend.
   */
  sendSocketMessageToUserIds: async function(
    userIds = [],
    siteId,
    topic,
    data,
  ) {

    data = data || '';

    if (globalhelper.isNullish(topic)) {
      return {problems: ['Topic cannot be empty']};
    }
    if (globalhelper.isNullish(siteId)) {
      return {problems: ['siteId is required key']};
    }
    if (userIds.constructor.name !== 'Array') {
      return {problems: ['user Ids passed should be of type Array']};
    }

    let $socketIds = [], socketIds;
    userIds.forEach(userId => {
      let key = `usersockets_${siteId}_${userId}`;
      $socketIds.push(cacheService.get(key));
    });
    socketIds = (await Promise.all($socketIds)).flat().filter(Boolean);

    if (socketIds.length === 0) {
      return true; // no user online
    }
    try {
      await socketService.broadcast(socketIds, topic, data);
    } catch (e) {
      sails.log.error(e);
      throw 'Unable to broadcast to socket';
    }

    return true;
  }

};
