const AWS = require('aws-sdk');
const moment = require('moment');
const flaverr = require('flaverr');
const deviceservice = require("./device.private");
const dynamokeystoreservice = require("../dynamokeystore/dynamokeystore.public");
const dynamoKeyStore = require("../dynamokeystore/dynamokeystore.public");
const addDeviceService = require('./add-device.service');
const globalhelper = require("../../utils/globalhelper");
const { notifyJouleTrackPublicRoom } = require("../socket/socket.service");
const parameterService = require('../parameter/parameter.private');
const siteUtil = require('../../utils/site/utils');
const selfUtils = require('../../utils/device/utils');
const cacheService = require('../cache/cache.public');

module.exports = {
  find: deviceservice.find,
  findOne: deviceservice.findOne,
  update: deviceservice.update,
  create: deviceservice.create,
  getDeviceListBySiteId: deviceservice.getDeviceListBySiteId,
  getEM: async function (siteId) {
    let key = `${siteId}_em`;
    return dynamoKeyStore.findOne(key);
  },
  /**
   * If the device is main meter add it to em-list in dyanmoKeystore
   * @param {string} siteId site id
   * @param {string} deviceId device id
   * @param {boolean} isMainMeter is it main meter
   */
  updateMainMeterDevice: async function (siteId, deviceId, isMainMeter) {
    let mainMeterUpdateResponse = false;
    // https://github.com/airbnb/javascript/issues/1281 how to write multiline conditional operators
    try {
      mainMeterUpdateResponse = isMainMeter
        ? await dynamokeystoreservice.addDeviceToSitesMainMeterList(
          siteId,
          deviceId
        )
        : await dynamokeystoreservice.deleteDeviceFromSitesMainMeterList(
          siteId,
          deviceId
        );
    } catch (e) {
      sails.log.error(e);
      return false;
    }
    return mainMeterUpdateResponse;
  },
  /**
   *
   * @param {string} controllerId Controller Id being checked
   * @description If controller Id being passed is a 3rd party controller, eg, Masibus, it returns the parent controller Id.
   * If not, it returns the same controller Id.
   * @returns
   */
  getParentControllerIdIfThirdPartyController: async function (controllerId) {
    // sails.log.info(`ControllerId sent to check Fucntion: ${controllerId}`);
    try {
      let controllerConfiguration = await deviceservice.findOne({ deviceId: controllerId });
      if ( controllerConfiguration["isSlaveController"] === undefined || controllerConfiguration["isSlaveController"] === false ){
        console.log("Found regular controller returning same controllerId");
        return controllerId; // If key is not present, it is not a 3rd party controller as old configurations do not have this key.
      }
      if (controllerConfiguration["isSlaveController"] === true || controllerConfiguration["isSlaveController"] === 1){
        console.log("Found 3rd party controller, returning parent controllerId");
        return controllerConfiguration["controllerId"]; // This is the parent/master controllerId on which the topic needs to be broadcasted.
      }
    } catch (error) {
      sails.log.error("Error either fetching device configuration or while searching for key. Returning old controllerId.");
      return controllerId;
    }
  },
  addDevice: addDeviceService.addDevice,
  getDeviceConfiguration: addDeviceService.getDeviceConfiguration,
  getDeviceListByDriverAndDeviceType: async function(siteId, driverType, deviceType, abbr) {
    let deviceList =  await deviceservice.find({
      siteId,
      driverType,
      deviceType,
    })
    return deviceList.map((device)=> {
      if (device.deviceId && device.name) {
        return {
          deviceId: device.deviceId,
          name: device.name,
          deviceType:device.deviceType,
          driverType:device.driverType,
          abbr
        }
      }
    })
      .filter(Boolean)
      .sort((a, b) => a.name.localeCompare(b.name))
      ;

  },
  getDeviceList: async function(siteId) {
    let deviceList =  await deviceservice.find({
      siteId,
    })
    return deviceList.map((device)=> {
      if (device.deviceId && device.name) {
        return {
          deviceId: device.deviceId,
          name: device.name,
          deviceType:device.deviceType,
          driverType:device.driverType
        }
      }
    })
      .filter(Boolean)
      .sort((a, b) => a.name.localeCompare(b.name))
      ;

  },
  /**
   * @description Fetch controller by controllerId and validating isValid controller
   * @param {String} siteId
   * @param {String} controllerId
   * @returns {Object} controller
   */
  getControllerById: async function(siteId, controllerId){
    const _controller = await Devices.findOne({deviceId: controllerId});
    if(_.isEmpty(_controller)) return null;

    const {deviceType:controllerDeviceType, siteId:controllerSiteId} = _controller;
    if(controllerSiteId != siteId) return null;

    const {list: controllerDeviceTypeMap} = await dynamoKeyStore.findOne({key: 'controllerDeviceTypeMap'});
    if (_.isEmpty(controllerDeviceType)) throw new flaverr('E_CONTROLLER_CATEGORY_MISSING', new Error('controller category missing '));
    if(!controllerDeviceTypeMap.hasOwnProperty(controllerDeviceType)) return null;
    return _controller
  },
  /**
   * @description Delete controller when there is no devices attached to the controller
   * @param {String} siteId
   * @param {String} controllerId
   * @param {String} industryType
   * @returns {Void}
   */
  deleteControllerIfNoDeviceAttached: async function(siteId, controllerId, industryType= undefined) {
    const _controller = await this.getControllerById(siteId,controllerId)
    if(!_controller) throw new flaverr('E_CONTROLLER_NOT_FOUND', new Error('controller not found'));

    const [
      _attachedDevice,
      _isComponentExist,
      _RawSiteData
    ] = await Promise.all([
      Devices.find({
        siteId,
        controllerId
      }),
      Components.find({
        siteId,
        controllerId
      }),
      Sites.findOne({ siteId, status: 1 })
    ])
    if (!_.isEmpty(_attachedDevice)) {
      throw new flaverr({
          code :'E_DEVICE_ATTACHED_TO_CONTROLLER',
          devicesFound: _attachedDevice.map(d => d.deviceId),
        },
        new Error('Please delete the attached devices first.'),
      )
    }

    if (!_.isEmpty(_isComponentExist)) {
      throw new flaverr({
          code :'E_CONTROLLER_COMPONENT_EXIST',
          devicesFound: _isComponentExist.map(d => d.deviceId),
        },
        new Error('This controller is primary controller of component.Please delete the attached component first.'),)
    }
    if (_.isEmpty(_RawSiteData)) {
      throw new flaverr({
          code: 'E_SITE_NOT_FOUND',
        },
        new Error('Invalid Site ID'),);
    }
    const _site = siteUtil.convertUnparsedDynamoSiteToJSON(_RawSiteData);

    const dynamoTxnQuery = [{
      Delete: {
        ConditionExpression: "attribute_exists(#deviceId) AND #deviceId = :expectedDeviceId AND attribute_exists(#siteId) AND #siteId = :expectedSiteId",
        ExpressionAttributeNames: {
          "#siteId": "siteId",
          "#deviceId": "deviceId",
        },
        ExpressionAttributeValues: {
          ":expectedDeviceId": { S: controllerId },
          ":expectedSiteId": { S: siteId },
        },
        TableName: "devices",
        Key: {
          "deviceId": { S: controllerId },
          "siteId": { S: siteId },
        },
      }
    },{
      Update: {
        Key: {
          key: { S: `${siteId}_configTS` }
        },
        ExpressionAttributeNames: {
          "#U": "updatedAt",
          "#value" : "value"
        },
        ExpressionAttributeValues: {
          ":u": { S: moment().utc().format() },
          ":v": { S: globalhelper.getCurrentUnixTs().toString() },
        },
        UpdateExpression: "SET #U = :u, #value = :v",
        TableName: 'dyanmokeystores'
      },
    }];

    let updatedSite;
    if (!industryType || industryType != 'ibms') {
      const { regionId } = _controller;
      //write delete controllerId dynamodb query and put into your transaction variable
      updatedSite = selfUtils.removeControllerFromSiteRegion(_site, regionId, controllerId); // remove this code only
      const { regions: updatedSiteRegion } = updatedSite;
      dynamoTxnQuery.push({
        Update: {
          Key: {
            "siteId": { S: siteId }
          },
          ExpressionAttributeNames: {
            "#R": "regions",
            "#U": "updatedAt",
          },
          ExpressionAttributeValues: {
            ":r": { S: JSON.stringify(updatedSiteRegion) },
            ":u": { N: globalhelper.getCurrentUnixTs().toString() },
          },
          UpdateExpression: "SET #R = :r, #U = :u",
          TableName: 'sites'
        },
      })
    }
    try {
      const documentClient = new AWS.DynamoDB();
      await documentClient.transactWriteItems({
        TransactItems: dynamoTxnQuery
      }).promise();

      if (updatedSite) {
        notifyJouleTrackPublicRoom(siteId, 'sites', {
          event: "update",
          data: updatedSite
        });
      }


      notifyJouleTrackPublicRoom(
        siteId,
        "devices",
        {
          event: "delete",
          data: [controllerId],
        });
      return;
    } catch(e) {
      sails.log('Error > deleteController', e)
      throw new flaverr('E_UNABLE_TO_DELETE_CONTROLLER', new Error('Unable to delete controller'));
    }
  },
  /*
     * @description Fetches main energy meters of a site by scanning device configurations on a site
     * instead of depending on <siteId>_mainMeter key in dyanmokeystore as that key is undependable
     * @param {string} siteId siteId
     */
  fetchMainMetersByScanningSiteConfiguration: async function (siteId) {
    if (!siteId) throw flaverr("E_SITE_ID_MISSING", new Error("SiteID invalid"));
    let mainMeterConfigurations = await deviceservice.find({
      siteId,
      isMainMeter: true
    });
    let mainMeterList = mainMeterConfigurations.map(deviceConfiguration => deviceConfiguration.deviceId);
    return mainMeterList;
  },
  fetchParameterListByDeviceIds:async function(siteId,deviceIds){
    const result= await Promise.all(deviceIds.map(deviceId=>this.fetchParameterListDeviceId(siteId, deviceId)))
    return result
  },
  fetchParameterListDeviceId:async function(siteId,deviceId){
    const params = await parameterService.getDeviceParameter(siteId,deviceId)
    return {
      deviceId,
      parameters:params
    }
  },
  getDeviceCatBySiteId: async function(siteId) {
    let indexName;
    if (process.env.NODE_ENV == 'development') {
      indexName = `siteId_deviceId_global_index`
    } else {
      indexName = 'siteId-deviceId-index';
    }
    const params = {
      TableName: 'devices',
      IndexName: indexName,
      KeyConditionExpression: '#siteId = :siteId',
      ExpressionAttributeNames: {
        '#siteId': 'siteId',
      },
      ExpressionAttributeValues: {
        ':siteId': siteId,
      },
      ProjectionExpression: 'deviceType, driverType',
    };
    try {
      const dynamoDbDocClient = new AWS.DynamoDB.DocumentClient({
        region: process.env.REGION,
      });
      const queryResult = await dynamoDbDocClient.query(params).promise();
      const uniqueCat =  queryResult.Items.reduce((acc, curr) => {
        const { driverType, deviceType} = curr;
        if (!driverType || !deviceType) return acc;
        const key = `${driverType}_${deviceType}`;
        acc[key] = curr;
        return acc;
      },{});
      return Object.values(uniqueCat);
    } catch (error) {
      sails.log.error('Error querying devices from DynamoDB using aws-sdk: fetchConfiguratorTableDeviceComponentDeviceType', error);
      throw error;
    }
  },
  isDeviceExist: async (deviceId, siteId, deviceType) => {
    const device = await deviceservice.findOne({deviceId, siteId, deviceType})
    if (_.isEmpty(device)) return false;
    return true;
  },
  async getDeviceControllerMap(deviceIds) {
    const _deviceIds = deviceIds
      .filter(deviceId => deviceId && String(deviceId).trim() !== '');
    const devicePromises = _deviceIds.map(deviceId => Devices.findOne({ deviceId }));
    let devices = await Promise.all(devicePromises);
    devices = devices.filter(device => device !== undefined);
    const devicesMap = new Map(devices.map(device => [device.deviceId, device]));
    const deviceControllerMap = new Map();
    for (const deviceId of _deviceIds) {
      const deviceDetail = devicesMap.get(deviceId);
      if (!deviceDetail) {
        throw flaverr({
          code: 'E_DEVICE_NOT_FOUND',
          message: `Device ID ${deviceId} does not exist`,
        });
      }
      const { controllerId } = deviceDetail;
      if (!controllerId) {
        throw flaverr({
          code: 'E_CONTROLLER_NOT_ATTACHED_TO_DEVICE',
          message: `Controller ID is missing for this device ${deviceId}`,
        });
      }
      const controllerDetail = await Devices.findOne({ deviceId: controllerId });
      if (!controllerDetail) {
        throw flaverr({
          code: 'E_CONTROLLER_NOT_FOUND',
          message: `Controller ID ${controllerId} does not exist for device ${deviceId}`,
        });
      }

      const finalControllerId = controllerDetail.isSlaveController && controllerDetail.controllerId ? controllerDetail.controllerId : controllerId;
      deviceControllerMap.set(deviceId, finalControllerId);
    }
    return Object.fromEntries(deviceControllerMap);
  },
  removeCachedDeviceCategories:function(siteId) {
    try {
      const cacheKey = selfUtils.getDeviceCategoryCacheKey(siteId);
      return cacheService.delete(cacheKey)
    } catch(e) {
      sails.log.error('Error while deleting cache [cachedDeviceCategories] ', e);
      return null;
    }
  },
  async getAllFlowBasedDevices(deviceDriverDetails, siteId) {
    const devices = [];
    for(const { abbr, driverType, deviceType } of deviceDriverDetails) {
      let _devices = await module.exports.getDeviceListByDriverAndDeviceType(siteId, driverType, deviceType, abbr);
      _devices = _devices.map(device => ({ ...device, class: 'device' }));
      devices.push(..._devices);
    }
    return devices;
  },
  getDevicesBySiteId: async function (siteId) {
    const [devices, parameters] = await Promise.all([
      deviceservice.getDeviceListBySiteId(siteId),
      parameterService.getParametersBySiteId(siteId),
    ]);
    const deviceMap = devices.reduce(
      (acc, { deviceId, isVirtualDevice = "0", createdAt, updatedAt, ...rest }) => {
        acc[deviceId] = {deviceId, ...rest, isVirtualDevice, param: {} };
        return acc;
      },
      {}
    );

    const parameterMap = parameters.reduce(
      (acc, { abbr, displayName, utilityType, paramGroup, dau, errOffset, deviceId }) => {
        acc[`${abbr}_${deviceId}`] = { abbr, displayName, utilityType, paramGroup, dau, errOffset };
        return acc;
      },
      {}
    );

    for (const { abbr, deviceId } of parameters) {
      if (deviceMap[deviceId]) {
        deviceMap[deviceId].param[abbr] = parameterMap[`${abbr}_${deviceId}`];
      }
    }

    return _.sortBy(Object.values(deviceMap), 'name');
  },
  updateMainMeterList: updateMainMeterList,

  async getUpdatedDevices(deviceId, siteId, deviceConfig) {
    const searchParams = { deviceId, siteId };
    await deviceservice.update(searchParams, deviceConfig)
    const updatedRecords = await deviceservice.find(searchParams);
    return updatedRecords[0]
  },
  getAvailableModbusSlaveIds: async function (devicesList, controllerId, siteId) {
        // Fetch devices from DB if not provided
        if (!devicesList) {
          devicesList = await deviceservice.find({ siteId });
        }
  
        let slaveArr = new Array(255).fill(false); // Modbus slaves addresses can only be from 1-255
        let slaveIdToDeviceTypeMap = {};
        let availableIndexes = [];
  
        // Process each device
        devicesList.forEach(device => {
          if (device.controllerId === controllerId && device.communicationType === 'MB') {
            slaveArr[device.slaveId] = true;
            slaveIdToDeviceTypeMap[device.slaveId] = device.deviceType;
          }
        });
  
        // Collect available slave IDs
        for (let i = 1; i < 255; i++) { // Slave IDs range from 1-255
          if (!slaveArr[i]) {
            availableIndexes.push(i.toString());
          }
        }
  
        return { availableIndexes, slaveIdToDeviceTypeMap };
  }, 
};

async function updateMainMeterList(isMainMeter, deviceId, siteId) {
  let key = `${siteId}_mainMeter`;
  let value;

    // Fetch existing main meters for the site
    let mainMeters = await dynamokeystoreservice.findOne({ key });
    if (!mainMeters) mainMeters = { value: "" }; // Initialize if not found

    if (isMainMeter === "false" || isMainMeter === false) {
      // Remove the device from the main meter list
      let meterList = mainMeters.value || "";
      let meters = new Set(meterList.split(",").filter(Boolean)); // Handle empty strings gracefully
      meters.delete(deviceId); // Remove the device
      value = [...meters].join(","); // Convert back to a comma-separated string
    } else if (isMainMeter === "true" || isMainMeter === true) {
      // Add the device to the main meter list
      let meterList = mainMeters.value || "";
      let meters = new Set(meterList.split(",").filter(Boolean)); // Handle empty strings gracefully
      meters.add(deviceId); // Add the device
      value = [...meters].join(","); // Convert back to a comma-separated string
    } else {
      throw flaverr('E_INVALID_MAIN_METER', new Error(`Invalid value for isMainMeter: ${isMainMeter}`));
    }

    // Perform the update or delete based on the final value
    if (value.length !== 0) {
      await dynamoKeyStore.update({ key }, { value });
    } else {
      await dynamoKeyStore.destroy({ key });
    }

    return true; // Operation succeeded
}
