/**
 * @description: Contains service funtions specific to route : bulk-component-generation.js ONLY.
 */
const deviceService = require("./device.service");
const parameterService = require("../parameter/parameter.service");
const componentService = require("../component/component.service");
const configurationHierarchyService = require("../configurationHierarchy/configurationHierarchy.service");

module.exports = {
    areValidControllerIds: async function (controllerIdAssetIdMap, siteId) {

        let $deviceConfigs = [];
        Object.keys(controllerIdAssetIdMap).forEach(controllerId => {
            const assetId = controllerIdAssetIdMap[controllerId];
            const $query = deviceService.findOne({ deviceId: controllerId, siteId })
                .then(deviceConfig => {
                    if (deviceConfig == undefined) return {
                        "status": false,
                        "message": `ControllerId: ${controllerId} not found in DB mentioned for AssetID: ${assetId}`
                    }
                    return {
                        "status": true,
                        "config": deviceConfig
                    }
                })
                .catch(error => {
                    sails.log.error("Error querying controller config from DB!");
                    sails.log.error(error);
                    return {
                        "status": false,
                        "message": `ControllerId ${controllerId} could not be queried from the DB for AssetId: ${assetId}`,
                        "assetId": assetId,
                    }
                })
            $deviceConfigs.push($query);
        })
        const promiseResults = await Promise.allSettled($deviceConfigs);
        let errors = [];
        promiseResults.forEach(promiseResult => {
            if (promiseResult.status == "fulfilled") {
                if (!promiseResult.value.status) errors.push(promiseResult.value.message)
            } else throw new Error(JSON.stringify(promiseResult))
        });
        return errors;
    },
    /**
     * 
     * @param {object} componentIdToDeviceParameterAbbrMap 
     * @param {string} siteId 
     * @returns returnObject.status : true if no existing componentIds found in the parameter configuration.
     */
    checkComponentsAlreadyExistForParameter: async function (componentIdToDeviceParameterAbbrMap, siteId) {
        let returnObject = {
            status: true,
            errors: []
        };
        let allPromises = [];
        for (let componentId in componentIdToDeviceParameterAbbrMap) {
            let parameterList = componentIdToDeviceParameterAbbrMap[componentId];
            let $parameterConfigs = [];
            for (let deviceId_abbr of parameterList) {
                $parameterConfigs.push(parameterService.findOne({
                    siteId,
                    deviceId_abbr
                }));
            }
            allPromises.push(Promise.all($parameterConfigs));
        }
        try {
            let allResults = await Promise.all(allPromises);
            for (let parameterConfigs of allResults) {
                for (let config of parameterConfigs) {
                    if (config.componentId) {
                        returnObject.status = false;
                        returnObject.errors.push(`Parameter configuration already exists for assetId: ${config.assetId} and deviceId_abbr: ${config.deviceId_abbr} with componentId: ${config.componentId}`);
                    }
                }
            }
        } catch (error) {
            sails.log.error("checkComponentsAlreadyExistForParameter >> Error fetching parameter configurations");
            sails.log.error(error);
            returnObject.status = false;
            returnObject.errors.push("Error fetching parameter configurations");
            return returnObject;
        }
        return returnObject
    },
    /**
     * Creates components for all component Configurations passed. 
     * On successfull component creation, updates the device parameter configuration with the
     * componentId.
     */
    createComponentsAndModifyParameterConfigurations: async function (componentConfigWithDataParams, componentIdToDeviceParameterAbbrMap, siteId) {
        const $createQueries = componentConfigWithDataParams.map(componentConfig => {
            delete componentConfig.assetId; // Was being used to generate data parameters.
            const hierarchyInfo = {
                name: componentConfig.name,
                layerId: componentConfig.layerId,
                deviceId: componentConfig.deviceId
            };
            delete componentConfig.layerId; 
            return componentService.create(componentConfig)
                .then(_ => {
                    const parameterList = componentIdToDeviceParameterAbbrMap[componentConfig.deviceId];
                    const $parameterCreateQueries = parameterList.map(deviceId_abbr => {
                        parameterService.update({
                            siteId,
                            deviceId_abbr
                        }, { componentId: componentConfig.deviceId });
                    })
                    return Promise.all($parameterCreateQueries);
                })
                .then(_ => {
                    const hierarchyCreateObject = {
                        system_id: "5", // Hard coded for Fire Alarm System currently.
                        parent_id: hierarchyInfo.layerId,
                        level_type: "component",
                        name: hierarchyInfo.name,
                        site_id: siteId, 
                        device_id: hierarchyInfo.deviceId
                    };
                    return configurationHierarchyService.nodes.create(hierarchyCreateObject);
                });
        });
        const createQueries = await Promise.allSettled($createQueries);
        const createQueryErrors = createQueries.map(createQuery => {
            if (createQuery.status != "fulfilled") return createQuery.reason;
            else return null;
        }).filter(Boolean);
        if (createQueryErrors.length != 0)
            return {
                "status": false,
                "code": "E_CREATE_COMPONENT_ERROR",
                "problems": createQueryErrors
            }
        return {
            "status": true,
        }
    },

    updateComponentNames: async function(componentIdToComponentNameMapping){
        const componentIds = Object.keys(componentIdToComponentNameMapping);
        const $componentConfigs = componentIds.map(async componentId => {
            return componentService.findOne({ deviceId: componentId });
        });
        const componentConfigs = await Promise.all($componentConfigs);
        const componentsToUpdateMap = componentConfigs.reduce((componentsToUpdateMap, componentConfig) => {
            const { deviceId, name, siteId } = componentConfig;
            if (componentIdToComponentNameMapping[deviceId] != name)
                componentsToUpdateMap[deviceId] = {
                    name: componentIdToComponentNameMapping[deviceId],
                    siteId: siteId
                };
            return componentsToUpdateMap;
        }, {});
        const $updateQueries = Object.keys(componentsToUpdateMap).map(componentId => {
            const componentConfig = componentsToUpdateMap[componentId];
            return componentService.update({
                deviceId: componentId,
                siteId: componentConfig.siteId
            }, {
                name: componentConfig.name
            })
        });
        const updateQueries = await Promise.allSettled($updateQueries);
        const updateErrors = updateQueries.map(updateQuery => {
            if(updateQuery.status != "fulfilled" ) return updateQuery.reason;
            else return null;
        }).filter(Boolean);
        if(updateErrors.length != 0){
            return {
                "status": false,
                "code": "E_UPDATE_COMPONENT_ERROR",
                "problems": updateErrors
            }
        }
        return {
            "status": true
        }
    },
}