/**
 * @description: Contains service funtions specific to route : generate-parameter-sheet.js ONLY.
 */
 const componentService = require("../component/component.service");
 const configurationHierarchyService = require("../configurationHierarchy/configurationHierarchy.service");
 
 module.exports = {
     /**
      * @description Fetches component names for all component IDs present in device parameters
      * @param {*} existingDeviceParameters 
      * @returns 
      */
    fetchComponentNamesForComponentIds: async function(existingDeviceParameters){
        let componentIdSet = new Set();
        existingDeviceParameters.forEach(parameterConfig => {
            if(parameterConfig.componentId){
                componentIdSet.add(parameterConfig.componentId);
            }
        });
        let componentIdList = Array.from(componentIdSet);
        let $componentConfigurations = componentIdList.map(componentId => componentService.findOne({
            deviceId: componentId,
        }));
        let componentConfigurations = await Promise.all($componentConfigurations);
        let componentIdToNameMap = componentConfigurations.reduce((componentIdToNameMap, componentConfiguration) => {
            if(!componentConfiguration) return componentIdToNameMap;
            const { deviceId: componentId, name } = componentConfiguration;
            componentIdToNameMap[componentId] = name;
            return componentIdToNameMap;
        }, {});
        return componentIdToNameMap;
    },
    /**
     * @description Fetches layer information for all component IDs present in device parameters. 
     * Fetches component information first, and then the layer information based on the parentId in the component configuiration
     * @param {*} existingDeviceParameters 
     */
    fetchLayerInformationForComponentIds: async function(existingDeviceParameters){
        // Fetching componentIdList from device parameter configuration
        let componentIdSet = new Set();
        existingDeviceParameters.forEach(parameterConfig => {
            if(parameterConfig.componentId){
                componentIdSet.add(parameterConfig.componentId);
            }
        });
        const componentIdList = Array.from(componentIdSet);
        if (componentIdList.length == 0) return {}; // Skipping querying DB is no components in device parameter configuration

        // Querying DB for layer names underwhich the component is configured.
        const componentIdsInquery = componentIdList.map(id => `'${id}'`).join(",");
        const query = `
            SELECT 
            n1.device_id, 
            n2.name
            FROM 
            public.nodes n1
            JOIN 
            public.nodes n2 ON n1.parent_id = n2.id
            WHERE 
            n1.device_id IN (${componentIdsInquery});
        `;
        const queryResult = await sails.getDatastore('postgres')
            .sendNativeQuery(query); 
        const componentIdToLayerNameMap = queryResult.rows.reduce((componentIdToLayerNameMap, row) => {
            const { device_id: componentId, name } = row;
            componentIdToLayerNameMap[componentId] = name;
            return componentIdToLayerNameMap;
        }, {});
        return componentIdToLayerNameMap;
    },

    fetchLayerNamesToIdMap: async function(systemId, siteId){
        // const hardCodedsiteId = "suh-hyd"; // Hardcoded for Sunshine. Remove to fetch siteId from controller instead. 
        const nodes = await configurationHierarchyService.nodes.find({
            site_id: siteId,
            is_deleted: false,
            system_id: systemId,
            layer_type: "floor" // Hardcoded for Sunshine iBMS. 
        });
        const layerNameToIdMap = nodes.reduce((layerNameToIdMap, node) => {
            const { name, id } = node;
            layerNameToIdMap[name] = id;
            return layerNameToIdMap;
        }, {});
        
        const layerNameList = Object.keys(layerNameToIdMap);
        const layerNameToIdRows = nodes.reduce((layerNameToIdRows, node) => {
            const { name, id } = node;
            layerNameToIdRows.push({
                layerName: name,
                layerId: id
            });
            return layerNameToIdRows;
        }, []);
        
        return { layerNameToIdMap, layerNameList, layerNameToIdRows };
    }
 }