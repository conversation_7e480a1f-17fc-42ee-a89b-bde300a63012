/* eslint-disable no-undef */
const dynamoDBClient = require("../dynamoClient/daxClient");

module.exports = {
  /**
   * Device Service Private functions
   */
  find: async (searchParams) => {
    // Device.find
    return Devices.find(searchParams);
  },
  getDeviceListBySiteId: async (siteId) => _getDeviceListBySiteId(siteId, null, []),
  findOne: async (searchParams) => {
    // Device.findOne
    return Devices.findOne(searchParams);
  },
  update: async (searchParams, updateValue) => {
    delete updateValue["deviceId"];
    delete updateValue["siteId"];
    return Devices.update(searchParams, updateValue);
  },
  create: async (searchParams) => {
    return Devices.create(searchParams);
  }
};

/**This is a recursive function call*/
async function _getDeviceListBySiteId(siteId, lastEvaluatedKey = null, devices = []) {
  const params = {
    TableName: "devices",
    IndexName: "siteId_deviceId_global_index",
    KeyConditionExpression: "#siteId = :siteId",
    ExpressionAttributeNames: {
      "#siteId": "siteId",
    },
    ExpressionAttributeValues: {
      ":siteId": siteId,
    },
  };

  if (lastEvaluatedKey) {
    params.ExclusiveStartKey = lastEvaluatedKey;
  }

  try {
    const queryResult = await dynamoDBClient.query(params).promise();

    const allDevices = devices.concat(queryResult?.Items || []);

    if (queryResult.LastEvaluatedKey) {
      return _getDeviceListBySiteId(siteId, queryResult.LastEvaluatedKey, allDevices);
    }

    return allDevices;
  } catch (error) {
    sails.log.error(
      `Error querying devices from DynamoDB using aws-sdk: getDeviceListBySiteId for siteId ${siteId}`,
      error,
    );
    throw error;
  }
}
