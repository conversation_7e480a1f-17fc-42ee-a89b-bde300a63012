const moment = require('moment');
const AWS = require('aws-sdk');
const dynamoKeyStorePublic = require('../dynamokeystore/dynamokeystore.public');
const globalHelper = require('../../utils/globalhelper');
const deviceServicePrivate = require('./device.private');
const flaverr = require('flaverr');
const siteUtil = require('../../utils/site/utils');
module.exports = {
    /**
   * @description Create a new device
   * Tables used: [dynamoKeyStore{configTs, totalDeviceCount}, devices]
   * @param {*} deviceConfig 
   * //TODO:
   * @returns Void
   */
  addDevice: async (deviceConfig, addDeviceParameter, dynamoKeystoreKeys) => {
    const {
      siteId,
      deviceId
    } = deviceConfig;
    const {
      em: emKeystoreData,
      mainMeter:mainMeterKeystoreData
    } = dynamoKeystoreKeys;
    const timezoneOffset = await sails.helpers.getSiteTimezone.with({siteId, timezoneFormat:'Z'})
    const epochTimestamp = moment().utcOffset(timezoneOffset).unix()*1000;
    const documentClient = new AWS.DynamoDB();
    const  transactionItems = [
      {
        Put: {
          Item: {
            key: {
              S: 'totalDeviceCount'
            },
            value: {
              S: deviceId.toString()
            },
            updatedAt: {
              S: epochTimestamp.toString()
            }
          },
          TableName: 'dyanmokeystores'
        },
      },
      {
        Put: {
          Item: {
            ...addDeviceParameter,
            updatedAt: {
              S: moment().utc().format().toString()
            },
            createdAt: {
              S: moment().utc().format().toString()
            }
          },
          TableName: 'devices'
        },
      },
      {
        Put: {
          Item: {
            key: {
              S: `${siteId}_configTS`
            },
            value: {
              S: globalHelper.getCurrentUnixTs().toString()
            },
            updatedAt: {
              S: epochTimestamp.toString()
            },
          },
          TableName: 'dyanmokeystores'
        },
      },
    ]
    if (emKeystoreData) {
      transactionItems.push({
        Put: {
          Item: {
            key: {
              S: emKeystoreData.key
            },
            list: {
              SS: emKeystoreData.list
            },
            updatedAt: {
              S: moment().utc().format().toString()
            },
            createdAt: {
              S: moment().utc().format().toString()
            }
          },
          TableName: 'dyanmokeystores'
        }
      })
    }
    /**
     * Make entry im site_mainMeter if a device request to mainMeter
     */
    if(!_.isEmpty(mainMeterKeystoreData)) {
      transactionItems.push({
        Put: {
          Item: {
            key: {
              S: mainMeterKeystoreData.key
            },
            value: {
              S: mainMeterKeystoreData.value
            },
            updatedAt: {
              S: moment().utc().format().toString()
            },
            createdAt: {
              S: moment().utc().format().toString()
            }
          },
          TableName: 'dyanmokeystores'
        }
      })
    }
    return documentClient.transactWriteItems({
      TransactItems: transactionItems
    }).promise();
  },
  /**
   * @description To get all the configuration required for creating a device
   * @param {String} siteId 
   * @param {Boolean} isMainMeter
   * @param {Boolean} isEnergyMeter
   * @returns {Object}
   * @example  {
      totalDevicesCount,
     _emKeystoreData,
      _mainMeterKeystoreData,
      _siteInfo,
      _deviceList,
    }
   */
  getDeviceConfiguration: async function(siteId, isMainMeter, isEnergyMeter) {
    const [
      totalDevicesCount,
      _siteRawInfo,
      _deviceList,
      _emKeystoreData,
      _mainMeterKeystoreData,
    ] = await Promise.all([
      dynamoKeyStorePublic.findOne({key: "totalDeviceCount"}),
      Sites.findOne({ siteId, status: 1 }),
      deviceServicePrivate.find({siteId}),
      isEnergyMeter && dynamoKeyStorePublic.findOne({key: `${siteId}_em`}),
      isEnergyMeter && isMainMeter && dynamoKeyStorePublic.findOne({key: `${siteId}_mainMeter`}),
    ])
    if (!_siteRawInfo) {
      throw flaverr('E_NOT_FOUND', new Error('Site not found'));
    }
    const _siteInfo = siteUtil.convertUnparsedDynamoSiteToJSON(_siteRawInfo);
    if (!_deviceList || _.isEmpty(_deviceList)) {
      throw flaverr('E_NOT_FOUND', new Error('Device list not found'));
    }

    return {
      totalDevicesCount,
      _emKeystoreData,
      _mainMeterKeystoreData,
      _siteInfo,
      _deviceList,
    }
  }
}