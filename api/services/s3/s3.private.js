const AWS = require('aws-sdk')
const fs = require('fs');
module.exports = (() => {
    const config = {
        s3: new AWS.S3({
            signatureVersion: 'v4',
            region: process.env.REGION
        }),
        signedUrlExpireSeconds: 60 * 20,//20 minutes
    }

    async function upload(fileLocation, fileUID, fileType, bucket, region = process.env.REGION,) {
        if (!bucket) throw new Error(`bucket name is required to upload file to s3`)

        const fileContent = fileType === "application/json" ? fileLocation : fs.readFileSync(fileLocation);
        const params = {
            Bucket: bucket,
            Key: fileUID,
            Body: fileContent,
            LOCATION_CONSTRAINT: region,
            ContentType: fileType
        };
        let _uploadedResponse = await config.s3.upload(params).promise();
        if (fileType !== "application/json") {
            fs.unlink(fileLocation, function (err) {
                if (err) throw err;
            });
        }
        return _uploadedResponse;
    }

    async function get(Key, Bucket) {
        return await config.s3.getObject({
            Bucket: Bucket,
            Key: Key,
        }).promise();
    }

    async function getSignedUrl(bucket, key) {
        if (!bucket) throw new Error(`Bucket name is required while fetching signed url`);
        if (!key) throw new Error(`key name is required while fetching signed url`);
        return config.s3.getSignedUrl('getObject', {
            Bucket: bucket,
            Key: key,
            Expires: config.signedUrlExpireSeconds
        })
    }
    return {
        upload,
        get,
        getSignedUrl
    }
})();
