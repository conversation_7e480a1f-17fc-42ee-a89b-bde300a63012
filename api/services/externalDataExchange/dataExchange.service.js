const ComponentService = require("../component/component.public");
const DeviceService = require("../device/device.public");
const ParameterService = require("../parameter/parameter.public");
const siteUtil = require("../../utils/site/utils");

async function fetchComponentByParams({ siteId, deviceType, param }) {
  const [siteDetailResult, componentsDetailResult] = await Promise.allSettled([
    Sites.findOne({ siteId }),
    ComponentService.find({ siteId, deviceType }),
  ]);
  let siteDetail;
  let componentList;
  if (siteDetailResult.status === "fulfilled") {
    siteDetail = siteDetailResult.value;
  } else {
    sails.log.error("Error fetching siteDetail:", siteDetailResult.reason);
  }

  if (componentsDetailResult.status === "fulfilled") {
    componentList = componentsDetailResult.value;
  } else {
    sails.log.error("Error fetching devices:", componentsDetailResult.reason);
  }
  if (_.isEmpty(componentList)) return [];
  const _compList = [];
  const locationMap = siteUtil.buildRegionAreaMap(siteDetail.areas, siteDetail.regions);
  for (const component of componentList) {
    const { data: dataParams } = component;
    if (_.isEmpty(dataParams) || !Array.isArray(dataParams)) continue;

    for (let dataParam of dataParams) {
      if (
        dataParam.key === param &&
        dataParam.expression &&
        dataParam.expression.trim().length > 0
      ) {
        _compList.push({
          // regionId: component.regionId,
          // location: `${locationMap?.[component.regionId]?.area?.name || 'NA'}->${locationMap?.[component.regionId]?.name || 'NA'}`,
          name: component.name,
          deviceId: component.deviceId,
          paramName: dataParam.displayName,
          unit: dataParam.dau,
        });
        break;
      }
    }
  }
  return _compList;
}

async function fetchDeviceListByParams({ siteId, deviceType, param }) {
  const [siteDetailResult, devicesResult] = await Promise.allSettled([
    Sites.findOne({ siteId }),
    DeviceService.find({ siteId, deviceType }),
  ]);
  let siteDetail;
  let devices;
  if (siteDetailResult.status === "fulfilled") {
    siteDetail = siteDetailResult.value;
  } else {
    sails.log.error("Error fetching siteDetail:", siteDetailResult.reason);
  }

  if (devicesResult.status === "fulfilled") {
    devices = devicesResult.value;
  } else {
    sails.log.error("Error fetching devices:", devicesResult.reason);
  }

  if (_.isEmpty(devices)) return [];
  const locationMap = siteUtil.buildAreaRegionMap(siteDetail.areas, siteDetail.regions);

  const deviceParamMap = (
    await Promise.all(
      devices.map((device) =>
        ParameterService.findOne({ siteId, deviceId_abbr: `${device.deviceId}_${param}` })
      )
    )
  ).filter(Boolean);

  const deviceParamConfigMap = deviceParamMap.reduce((acm, curr) => {
    acm[curr.deviceId] = curr;
    return acm;
  }, {});
  const deviceMap = devices.reduce((acm, curr) => {
    if (deviceParamConfigMap.hasOwnProperty(curr.deviceId)) {
      acm[curr.deviceId] = curr;
    }
    return acm;
  }, {});
  // const deviceControllerMap = await DeviceService.getDeviceControllerMap(Object.keys(deviceMap));
  const finalDeviceResponse = [];
  // eslint-disable-next-line guard-for-in
  for (const deviceId in deviceMap) {
    finalDeviceResponse.push({
      // areaId: `${locationMap?.[deviceMap[deviceId].areaId]?.name || 'NA'}->${locationMap?.[deviceMap[deviceId].areaId]?.region?.[deviceMap[deviceId].regionId]?.name || 'NA'}`,
      name: deviceMap[deviceId].name,
      deviceId: deviceMap[deviceId].deviceId,
      paramName: deviceParamConfigMap[deviceId].displayName,
      unit: deviceParamConfigMap[deviceId].dau,
    });
  }
  return finalDeviceResponse;
  //fetch controllerId also to find out the location
}

module.exports = {
  fetchAssetsList: async ({ siteId, deviceType, param, deviceClass }) => {
    if (deviceClass === "component") {
      return fetchComponentByParams({ siteId, param, deviceType });
    }
    if (deviceClass === "device") {
      return fetchDeviceListByParams({ siteId, param, deviceType });
    }
  },
};
