class Logger {
    constructor(loggerInstance){
        this.logger = loggerInstance;
    }
    log(params){
        this.logger.log(params);
    }
    warn(params){
        this.logger.warn(params);
    }
    error(params){
        this.logger.error(params);
    }
    info(params){
        this.logger.info(params);
    }
    silly(params){
        this.logger.silly(params);
    }
}
module.exports = Logger;