class PinoLoggerWrapper {
    constructor(config){
        this.logger = require('pino')();
        let logLevel = config.level || 'info'
        this.logger.level = logLevel;
    }
    log(obj){
        this.logger.info(obj);
    }
    warn(params){
        this.logger.warn(params);
    }
    error(params){
        this.logger.error(params);
    }
    info(params){
        this.logger.info(params);
    }
    silly(params){
        this.logger.silly(params);
    }
    trace(params){
        this.logger.trace(params);
    }
}
module.exports = {
    PinoLoggerWrapper
}