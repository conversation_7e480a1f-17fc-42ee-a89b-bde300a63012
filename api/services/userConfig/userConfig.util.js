/**
 * @module userConfig.util.js
 * @description Contains all/ most sync functions to help support userConfig.service.js
 */

const helper = require('../../utils/globalhelper');

module.exports = {

  getParsedDefaultPreferences: function(adminRoleResult){
    // Storing default preferences if role found.
    let parsedDefaultPreferences = helper.toJson(adminRoleResult.defpref);
    if (parsedDefaultPreferences === undefined)
      throw new Error('Could not parse \'defpref\' fetched from \'admin\' role');
    else {
      for (let key in parsedDefaultPreferences){
        parsedDefaultPreferences[key] = JSON.stringify(parsedDefaultPreferences[key]);
      }
      return parsedDefaultPreferences;
    }
  },

};
