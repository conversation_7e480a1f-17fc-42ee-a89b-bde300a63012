/**
 * @module userConfig
 * @description This module is responsible for high level functions related to User Configuration and permissions. It imports data from Users, Roles and UserSiteMaps.
 */

const roleService = require('../role/role.public');
const DynamoKeyStore = require('../dynamokeystore/dynamokeystore.public');
const userService = require('../user/user.public');
const userSiteMapService = require('../userSiteMap/userSiteMap.public');
const util = require('./userConfig.util');

module.exports = {
  /**
	 * @function giveAccessToDevelopers
	 * @param {string} siteId The new siteId being generated.
	 * @summary A select list of developers / users have been stored in the Dyanmokeystore. It gives admin access to all those users to the new site after fetching the default admin preferences from the Roles table.
	 * @returns {object} A Promise which resolves to : {"status": boolean, "message": "Error message if any."}
	 */
  giveAccessToDevelopers: async function (siteId) {
    let defaultPreferences, $defaultPreferences, developerList, $developerList, $createUserAccessList;
    try {
      // Fetch default preferences for admin role.
      $defaultPreferences = fetchDefaultPreferences();
      // Fetch list of configured developers in DynamoKeyStore who should automatically get access.
      $developerList = fetchDeveloperList();
      defaultPreferences = await $defaultPreferences;
      developerList = await $developerList;
      // Fetch User Details.
      $createUserAccessList = developerList.map(fetchUserDetails).map(giveAccessToUser);
      await Promise.all($createUserAccessList);
      return {
        status: true
      };
      // Give access to users.
    } catch (error) {
      sails.log.error('[giveAccessToDevelopers] Error!');
      return {
        status: false,
        message: error
      };
    }

    // Helper functions
    /**
    * @function fetchDefaultPreferences
    * @summary Fetches default preferences stored in the "defpref" key of the object with "roleName":"admin" in the "Role" Table.
    */
    async function fetchDefaultPreferences(){
      try {
        let adminRoleResult = await roleService.findOne({
          'roleName': 'admin'
        });
        if (adminRoleResult === undefined)
          throw new Error('\'admin\' role not found in \'Roles\' Table. Could not allocate permission to admins.');
        else
          return util.getParsedDefaultPreferences(adminRoleResult);
      } catch (error) {
        sails.log.error('[giveAccessToDevelopers] Error getting default preferences from \'role\' table.');
        throw new Error(error);
      }
    }
    /**
		 * @function fetchDeveloperList
		 * @summary Fetches configured developerList in DyanmoKeyStore Table to who the admin access needs to be given.
		 */
    async function fetchDeveloperList(){
      let developerListResult = await DynamoKeyStore.findOne({
        'key': 'developerList'
      });
      if (developerListResult === undefined)
        throw new Error('\'developerList\' not found in DyanmoKeyStore');
      else
        // return developerListResult.list; // #ToReviewAmit Commented till Amit resolves the adapter.
        return developerListResult.list.values;
    }
    /**
		 * @function fetchUserDetails
		 * @summary Fetches user details, ie, is "phone" required for the userSiteMap entry.
		 * @returns {Promise} Return a promise which resolves to the user record in the Users table.
		 */
    async function fetchUserDetails(user){
      let userResult = await userService.findOne({
        'userId': user
      });
      if (userResult === undefined){
        sails.log.error(`[giveAccessToDevelopers] Error! ${user} not found in users tables. Not allocating access to new site.`);
      } else return userResult;
    }
    /**
		 * @function giveAccessToUser
		 * @summary Creates a userSiteMap object required to give one user admin access to a specified site. Uses user details fetched in the previous promise.
		 * @param {promise} $user User search promise
		 * @returns {Promise} Which resolved when the permission record has been succesfully created in userSiteMap table.
		 */
    async function giveAccessToUser($user){
      try {
        let user = await $user;
        if (user === undefined) return;
        let newUserSiteMapsObject = {
          'phone': user.phone,
          'role': 'admin',
          'userId': user.userId,
          siteId
        };
        newUserSiteMapsObject = {...defaultPreferences, ...newUserSiteMapsObject};
        return userSiteMapService.create(newUserSiteMapsObject);
      } catch (error) {
        sails.log.error('[giveAccessToDevelopers] Error creating access entry in userSiteMap.');
        throw new Error(error);
      }
    }
  },
};
