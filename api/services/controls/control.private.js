const flaverr = require('flaverr');
const ComponentService = require('../component/component.public')
const ControlState = require('./lib/ControlStateService')
require('./JouleTrackCommandTimeoutHandler');
const { DeviceControlRelationshipConfig } = require('../controlRelationshipConfig/controlsRelationshipConfig.public');

module.exports = {
  getControlStateByControlAbbr: async function (componentId, controlAbbr) {
    const controlState = new ControlState({ componentId, controlAbbr })
    return controlState.fetchCurrentControlState()
  },
  fetchControlStateByComponents: async function (siteId, componentIds) {
    const deviceControls = await DeviceControlRelationshipConfig.find({
      where: {
        siteId,
        deviceId: {
          'in': componentIds,
        },
        status: 1,
      },
    });

    const deviceStatesPromises = await Promise.all(deviceControls.map(async ({ controlAbbr, deviceId }) => {
      const controlState = await this.getControlStateByControlAbbr(deviceId, controlAbbr);
      return { deviceId, state: controlState };
    }));

    return deviceStatesPromises.reduce((groupedDevices, { deviceId, state }) => {
      let deviceGroup = groupedDevices.find(device => device.deviceId === deviceId);

      if (!deviceGroup) {
        deviceGroup = { deviceId, states: [] };
        groupedDevices.push(deviceGroup);
      }

      deviceGroup.states.push(state);

      return groupedDevices;
    }, []);
  },
  fetchControlStateByComponentId: async function (componentId) {
    const controls = await ComponentService.fetchConfiguredControlsByComponentId(componentId);
    if (_.isEmpty(controls)) {
      throw new flaverr({
        code: 'E_CONTROl_CONFIG_MISSING',
        HTTP_STATUS_CODE: 400,
        message: `there is no control config exist for this component. Please check again`
      })
    }
    const batchStateHolder = [];
    for (const { controlAbbr } of controls) {
      batchStateHolder.push(this.getControlStateByControlAbbr(componentId, controlAbbr))
    }
    const controlsState = await Promise.all(batchStateHolder)
    return { controlsState, componentId };
  }
}
