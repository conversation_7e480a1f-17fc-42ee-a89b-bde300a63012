const controlService = require("./control.private");
const controlRelationship = require("../../services/controlRelationshipConfig/controlsRelationshipConfig.service");
const utils = require("../../utils/controls/index");
const errorHandler = require("../../utils/controls/error");
const requestValidator = require("../../utils/controls/requestValidator");
const influxService = require('../../services/influx/influx.public');
const componentService = require("../component/component.public");
const siteService = require("../../services/site/site.public");
const { DeviceControlRelationshipConfig } = require("../controlRelationshipConfig/controlsRelationshipConfig.public");


module.exports = {
  getControlStateByControlAbbr: controlService.getControlStateByControlAbbr,
  fetchControlStateByComponentId: controlService.fetchControlStateByComponentId,
  fetchControlStateByComponents:controlService.fetchControlStateByComponents,
  getCommandHistoryMetaInfo: async function (siteId, componentId) {
    const [componentInfo, componentControlsInfo] = await Promise.all([
      componentService.fetchComponentById(componentId),
      controlRelationship.DeviceControlRelationshipConfig.getComponentControlRelationship({
        siteId,
        componentId,
      }),
    ]);
    if (componentInfo.deviceId != componentId) errorHandler.throwExceptionComponentIdNotFound(componentId);
    return utils.buildCommandHistoryMetaInfoObj(componentInfo, componentControlsInfo.controls);
  },
  fetchComponentCommandList: async function (siteId, componentId, filterObject) {
    const { rows, offset, commandSource, controlAbbr } = filterObject;
    const [
      commandHistoryRawData,
      componentControlRelationship,
    ] =
      await Promise.allSettled([
        fetchCommandHistoryFromInflux(siteId, componentId, {rows, offset, commandSource, controlAbbr}),
        componentService.fetchControlRelationshipByComponent(siteId, componentId)
    ]);
    const controlsRelationship = (componentControlRelationship?.value || []).find(control => control.controlAbbr == controlAbbr);
    if (_.isEmpty(commandHistoryRawData?.value) || _.isEmpty(controlsRelationship)) return []
    return utils.buildCommandHistoryObj(commandHistoryRawData.value, controlsRelationship)

    async function fetchCommandHistoryFromInflux(siteId, componentId, {rows, offset, commandSource, controlAbbr})  {
      requestValidator.validateCommandHistoryQueryParam({
        siteId,
        componentId,
        rows,
        offset,
        commandSource,
        controlAbbr
       })
       const deviceConfig =  await DeviceControlRelationshipConfig.findOne({
          deviceId: componentId,
          siteId: siteId,
          controlAbbr,
          status: 1
      });
      const controlType = deviceConfig?.controlType;
      const commandHistoryQuery = utils.getCommandHistoryQueryBasedOnFilter(commandSource, controlType);
      const replacements = {
        siteId,
        componentId,
        rows,
        offset,
        commandSource,
        controlAbbr,
        journeyStep:5,
        bucket: 'iot-metrics',
        measurement: 'commandstatus',
        offset
      }
      if (['recipe', 'cpa'].includes(commandSource)) {
        /**
       * In this case, we need to include an additional record because the algorithm used in CPA and recipe calculates the differences.
       * For example, if we have 1-2-3-4 records, after applying the difference, we get 2-3-4.
       * I want to check how many times the 4th record is repeated.
       * I also need the 5th record so that I can calculate 5th - 4th and determine the total number of occurrences of the 4th record.
       */

       replacements.rows += 1;
      }
      const commandHistoryRawData = await influxService.runQuery(commandHistoryQuery,{
        replacements,
        debug: true
       },"iot_influxdb");
      const timezoneOffset = await sails.helpers.getSiteTimezone.with({siteId, timezoneFormat:'utcOffsetInMinute'})
      const commandHistoryWithTimezoneOffset = commandHistoryRawData.map(commandHistory => {
        commandHistory.timezoneFormat = timezoneOffset
        if (commandSource) commandHistory.commandsource = commandSource
        return commandHistory;
      })
      return utils.filterCommandHistorySchema(commandHistoryWithTimezoneOffset, commandSource);
    };


  },
  fetchComponentLastCommand: async function(
    siteId,
    componentId,
    controlAbbr
  ) {

    if (!(await siteService.isValidSite(siteId))) errorHandler.throwExceptionInvalidSite(siteId)
    const isValidComponent = await componentService.fetchComponentById({siteId, deviceId: componentId})
    if (!isValidComponent) errorHandler.throwExceptionComponentIdNotFound(componentId)

    const [
      commandHistoryRawData,
      componentControlRelationship,
    ] =
      await Promise.allSettled([
        fetchCommandHistoryLastCommandFromInflux(siteId, componentId, controlAbbr),
        componentService.fetchControlRelationshipByComponent(siteId, componentId)
    ]);
    const controlsRelationship = (componentControlRelationship?.value || []).find(control => control.controlAbbr == controlAbbr);
    if (_.isEmpty(commandHistoryRawData?.value) || _.isEmpty(controlsRelationship)) return {}
    return utils.buildCommandRetentionObj(commandHistoryRawData.value, controlsRelationship);

    async function fetchCommandHistoryLastCommandFromInflux(siteId, componentId, controlAbbr)  {
      requestValidator.validateFetchLastCommand({
        siteId,
        componentId,
        controlAbbr
       })
      const getCommandHistoryQuery = getLastCommandQuery();
      const replacements = {
        siteId,
        componentId,
        controlAbbr,
        journeyStep:5,
        bucket: 'iot-metrics',
        measurement: 'retentionrecipecommandslog',
        rows: 1,
        offset: 0
      }
      const commandHistoryRawData = await influxService.runQuery(getCommandHistoryQuery,{
        replacements,
        debug: true
       },"iot_influxdb");
      if (commandHistoryRawData && !commandHistoryRawData.length || commandHistoryRawData[0]?.retentionstatuscode != 1) return [];
      const timezoneOffset = await sails.helpers.getSiteTimezone.with({siteId, timezoneFormat:'utcOffsetInMinute'})
      const commandHistoryWithTimezoneOffset = commandHistoryRawData.map(commandHistory => {
        commandHistory.timezoneFormat = timezoneOffset
        if (commandHistory.commandsource == 'joulerecipe') {
          if (commandHistory.commandcategory != 'cpa') commandHistory.commandsource = 'recipe'
          else commandHistory.commandsource = 'cpa'
        }
        return commandHistory;
      })
      return utils.filterCommandRetentionSchema(commandHistoryWithTimezoneOffset)


      function getLastCommandQuery() {
        return `
         from(bucket: "{{bucket}}")
        |> range(start: -180d)
        |> filter(fn: (r) => r["_measurement"] == "{{measurement}}")
        |> filter(fn: (r) => r["siteid"] == "{{siteId}}")
        |> filter(fn: (r) => r["controlabbr"] == "{{controlAbbr}}")
        |> filter(fn: (r) => r["componentid"] == "{{componentId}}")
        |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
        |> sort(columns: ["_time"], desc: true)
        |> limit(n:{{rows}}, offset: {{offset}})
        `
      }
    }
  },
};
