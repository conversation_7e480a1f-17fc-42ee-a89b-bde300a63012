const os = require('os');

const CommandRepository = require("./lib/CommandRepository");
const RedisNotification = require("../RedisNotification");
const JouleTrackCommandFeedbackBuilder = require("./lib/JouleTrackCommandFeedbackBuilder");
const { notifyJouleTrackPublicRoom } = require("../socket/socket.public");
const RedisClient = require('../cache/cache.public');

function isCommandExpiryNotification(key) {
  //example pattern command:c8e445a3-4970-4b23-9413-3a34ea236270
  const regex = /^command:[a-f\d]{8}-[a-f\d]{4}-[a-f\d]{4}-[a-f\d]{4}-[a-f\d]{12}$/i;
  return regex.test(key);
}

(async () => {
  RedisNotification.on("message", async (pattern, channel, expiredKey) => {
    try {
      if (!isCommandExpiryNotification(expiredKey)) return;
      const commandUid = expiredKey.split(':')[1];
      const isLockAcquired = await RedisClient.acquireLock(`timeout:event:lock:${commandUid}`, os.hostname(), 3);
      if (!isLockAcquired) {
        sails.log.error(`Message is already processing by other server`);
        return;
      }

      const commandRepositoryInstance = new CommandRepository();
      const commandDetail = await commandRepositoryInstance._getCommandDetailFromDynamoByUid(
        commandUid
      );
      if (!commandDetail) {
        sails.log(`commandUid:${commandUid} does not exist`);
        return;
      }
      const { timestamp } = commandDetail;
      const [siteId, commandExecutionTsInUnix] = timestamp.split('_');
      const feedbackBuilderInstance = new JouleTrackCommandFeedbackBuilder();
      const feedbackPayload = feedbackBuilderInstance
        .setComponentId(commandDetail.componentId)
        .setSiteId(siteId)
        .setCommandDetail({
          value: commandDetail.value,
          commandAbbr: commandDetail.component_abbr,
          controlAbbr: commandDetail.controlAbbr,
          executionTimestamp: commandExecutionTsInUnix,
          executedBy: commandDetail.user,
          commandUid: commandUid,
          userId: commandDetail.user,
        })
        .setControlState('-1')
        .build();
      await notifyJouleTrackPublicRoom(siteId, 'JouleTrackCommandFeedback', {
        data: feedbackPayload,
      });
      await RedisClient.releaseLock(`timeout:event:lock:${commandUid}`, os.hostname());
    } catch (e) {
      sails.log.error('CommandFeedback>Timeout Event Handling');
      sails.log.error(e.message || e);
    }
  });
})();
