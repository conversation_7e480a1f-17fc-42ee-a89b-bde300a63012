const influxService = require("../../influx/influx.public");
const { validateCommandHistoryBuilderObj, validateCommandHistoryDirector, throwExceptionInvalidInitialization } = require("../../../utils/controls/command-history-utils");

class CommandHistoryBuilder {
    constructor(command) {
        validateCommandHistoryBuilderObj(command)
        this.bucket = command.bucket;
        this.measurement = command.measurement;
        this.siteId = null;
        this.source = null;
        this.uniqId = null;
        this.controllerId  = null;
        this.componentId  = null;
        this.controlAbbr  = null;
        this.commandAbbr = null;
        this.deviceId = null;
        this.userId = null;
        this.dataParamAbbr = null;
        this.value = null;
        this.commandType = null;
        this.controllerDeviceType = null
        this.controllerHardwareVer = null;
        this.param = null;
        this.journeyStep = null;
        this.success = null;
        this.status = null;
        this.commandValue = null;
    }


    setJourneyStep(journeyStepValue){
        this.journeyStep = Number.parseInt(journeyStepValue);
        return this;
    }
    setIoTStateCode(stateCode){
        this.status = Number.parseInt(stateCode)
        return this;
    }
    setSuccessFlag(successFlag){
        this.success = Number.parseInt(successFlag)
        return this;
    }
    build(commandObj){
        validateCommandHistoryDirector(commandObj);
        const {
            siteId,
            source,
            uniqId,
            device_abbr: param,
            controllerId, 
            componentId, 
            controlAbbr, 
            commandParam: commandAbbr,
            deviceId,
            userId,
            dataParamAbbr,
            commandValue,
            controlType: commandType,
            controllerDeviceType,
            controllerHardwareVer
        } = commandObj
        this.siteId =  siteId
        this.source = source
        this.uniqId = uniqId
        this.controllerId = controllerId
        this.componentId = componentId
        this.controlAbbr = controlAbbr
        this.commandAbbr = commandAbbr
        this.deviceId = deviceId
        this.userId = userId
        this.dataParamAbbr = dataParamAbbr
        this.commandValue = Number.parseFloat(commandValue)
        this.commandType = commandType
        this.param = param
        this.controllerDeviceType = controllerDeviceType
        this.controllerHardwareVer = controllerHardwareVer
        this.setIoTStateCode(17)
        .setJourneyStep(1)
        .setSuccessFlag(1)
        Object.freeze(
            this.siteId,
            this.source,
            this.uniqId,
            this.controllerId, 
            this.componentId, 
            this.controlAbbr, 
            this.commandAbbr, //
            this.deviceId,
            this.userId,
            this.dataParamAbbr,
            this.value,
            this.commandType,
            this.param,
            this.journeyStep,
            this.status,
            this.success,
            this.controllerDeviceType, 
            this.controllerHardwareVer,
        )
        return this

    }

    async logHistory() {
        const command = this.commandHistoryStateObject;
        await influxService.write(command, "iot_influxdb");
    }
    
    get commandHistoryStateObject() {
        const payload = {
            data: {
                tags: [
                    { key: 'siteid', value: this.siteId },
                    { key: 'controllerid', value: this.controllerId },
                    { key: 'controllertype', value: `${this.controllerDeviceType}_${this.controllerHardwareVer}` },
                    { key: 'componentid', value: this.componentId },
                    { key: 'deviceid', value: this.deviceId },
                    { key: 'controlabbr', value: this.controlAbbr },
                    { key: 'commandabbr', value: this.commandAbbr },
                    { key: 'commandsource', value: this.source },
                    { key: 'journeystep', value: this.journeyStep }
                ],
                fields: [
                    {
                        type: 'string',
                        key: 'devicecommandparam',
                        value: this.param
                    },
                    {
                        type: 'string',
                        key: 'commandcategory',
                        value: 'no_category'
                    },
                    {
                        type: 'string',
                        key: 'commandkey',
                        value: this.uniqId
                    },
                    {
                        type: 'integer',
                        key: 'status',
                        value: this.status
                    }, 
                    {
                        type: 'string',
                        key: 'userid',
                        value: this.userId,
                    }, 
                    {
                        type: 'string',
                        key: 'dataparamabbr',
                        value: this.dataParamAbbr
                    },
                    {
                        type: 'float',
                        key: 'commandvalue',
                        value: this.commandValue
                    },
                    {
                        type: 'string',
                        key: 'commandtype',
                        value: this.commandType
                    }, {
                        type: 'integer',
                        key: 'success',
                        value: this.success
                    }
                ],
            },
            bucket: this.bucket,
            measurement: this.measurement
        }
        return payload;
    }
}

/**
 * Command History Director Class
 */
class CommandHistory {
    constructor() {
      throwExceptionInvalidInitialization()
    }  

    static async logOriginatedCommand(commandObj) {
        try {
        const commandHistoryBuilder = new CommandHistoryBuilder({
            bucket:'iot-metrics',
            measurement: 'commandstatus'
        })
        .build(commandObj);
      
        await commandHistoryBuilder.logHistory();
        return ;
        } catch (e) {
            sails.log('Error: Command History Originate Command uuid:', commandObj.uniqId, e);
        }
    }


}



module.exports = CommandHistory