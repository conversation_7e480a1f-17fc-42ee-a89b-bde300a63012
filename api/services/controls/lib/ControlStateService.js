/**
 * this class is responsible for fetching current command state. If there is no command state exist, then it will
 * return the LISTENING_MODE state.
 * Client code need to run all the validation check like siteId, componentId and controlAbbr need to accurate
 * otherwise they will receive the LISTENING_MODE state for invalid command
 * Client code is written in Component config
 */
const Error = require('../../../utils/controls/error');
const CommandRepository = require('./CommandRepository');
const Util = require('../../../utils/controls');

class ControlStateService {
  constructor({
    componentId,
    controlAbbr
  }) {
    if (_.isEmpty(componentId)) return Error.throwsExceptionComponentIdRequired();
    if (_.isEmpty(controlAbbr)) return Error.throwsExceptionControlAbbrRequired();
    this.componentId = componentId;
    this.controlAbbr = controlAbbr;
  }

  async fetchCurrentControlState() {
    const repository = new CommandRepository();
    const currentState = await repository
      .fetchCurrentStateByControlAbbr(
        this.componentId,
        this.controlAbbr
      );
    if (currentState.state == 2) {
      return {
        controlAbbr: this.controlAbbr,
        state: Util.ControlStateDict[currentState.state],
        commandDetail: JSON.parse(currentState.commandDetail)
      }
    }
    if (currentState.state == 3) {
      return {
        controlAbbr: this.controlAbbr,
        state: Util.ControlStateDict[currentState.state],
        commandDetail: {}
      };
    }
    return {
      controlAbbr: this.controlAbbr,
      state: Util.ControlStateDict['1'],
    };
  }
}

module.exports = ControlStateService;
