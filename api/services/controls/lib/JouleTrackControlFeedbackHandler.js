const CommandCodeDict = require('./IoTFeedbackControlStateDict.json')
const CommandRepository = require('./CommandRepository')

class JouleTrackControlFeedbackHandler {
  constructor() {
    this.CommandRepository = new CommandRepository();
    this.SUCCESS_STATUS_CODE_SEQUENCE = ["4", "1"];
    this.FAILED_FEEDBACK_CODE_ORDER = ["3", "8", "9", "13", "0", "6"];
    this.IN_PROGRESS_SEQUENCE = ["7", "2"]
  }

  async fetchCurrentState(commandUid) {
    const commandData = await this.CommandRepository.fetchJouleTrackCommandDetailByCommandId(commandUid);
    if (!commandData) return null;
    const { state } = commandData
    return state
  }

  async changeControlState(IoTCommandStatus, currentCommandUid, rawIoTPacket) {
    const newControlState = JouleTrackControlFeedbackHandler.getControlStateByIoTStatus(IoTCommandStatus)
    if (!newControlState) {
      sails.log.warn(`incorrect status received from IoT. Command status ${IoTCommandStatus} does not exist in map`);
      return null;
    }
    const { stateCode } = newControlState
    if (stateCode == 1) {
      return this.doHandleSuccessFeedback(IoTCommandStatus, currentCommandUid);
    } if (stateCode == 2) {
      return this.doHandleInProgressState(IoTCommandStatus, currentCommandUid, rawIoTPacket)
    } if (stateCode == 3) {
      return this.doHandleInFailedFeedback(IoTCommandStatus, currentCommandUid)
    }
    return null;
  }

  async doHandleInProgressState(inProgressStatusCode, currentCommandUid, rawIoTPacket) {
    sails.log(`doHandleInProgressState currentCommandUid=${currentCommandUid} rawIoTPacket=${JSON.stringify(rawIoTPacket)}`)
    //doHandleInProgressState this will refactor later
    return;
    const { status: rawStatus } = rawIoTPacket
    const existingControlStateData = await this.CommandRepository.fetchJouleTrackCommandDetailByCommandId(currentCommandUid);
    if (!existingControlStateData) return null;
    const { commandDetail: controlStateData } = existingControlStateData
    if (!controlStateData.hasOwnProperty('prevRawFeedback')) {
      controlStateData.prevRawFeedback = rawIoTPacket;
      await this.CommandRepository
        .updateCommandProperty(controlStateData.componentId, controlStateData.controlAbbr, controlStateData)
      return controlStateData
    }

    const upcomingStatusIndex = this.IN_PROGRESS_SEQUENCE.indexOf(rawStatus)
    const existingStatusIndex = this.IN_PROGRESS_SEQUENCE.indexOf(controlStateData.prevRawFeedback.status)
    if (upcomingStatusIndex > existingStatusIndex) {
      controlStateData.prevRawFeedback = rawIoTPacket;
      await this.CommandRepository
        .updateCommandProperty(controlStateData.componentId, controlStateData.controlAbbr, controlStateData)
      return controlStateData;
    }
    return null;
  }

  async doHandleInFailedFeedback(failedStatusCode, currentCommandUid) {
    const commandData = await this.CommandRepository.fetchJouleTrackCommandDetailByCommandId(currentCommandUid);
    if (!commandData) return null;
    await this.CommandRepository.deleteCommand(currentCommandUid);
    return commandData.commandDetail;
  }

  async doHandleSuccessFeedback(successStatusCode, currentCommandUid) {
    const commandData = await this.CommandRepository.fetchJouleTrackCommandDetailByCommandId(currentCommandUid);
    if (!commandData) return null;
    await this.CommandRepository.deleteCommand(currentCommandUid);
    return commandData.commandDetail;
  }

  static getControlStateByIoTStatus(RawStatusCode) {
    return CommandCodeDict.hasOwnProperty(RawStatusCode) ? CommandCodeDict[RawStatusCode] : null
  }
}

module.exports = JouleTrackControlFeedbackHandler
