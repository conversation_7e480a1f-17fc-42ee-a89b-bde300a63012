{"0": {"message": "Failed After Verification", "stateCode": "3"}, "1": {"message": "command successfully executed", "stateCode": "1"}, "2": {"message": "command has been reached to controller", "stateCode": "2"}, "3": {"message": "Error while processing the command", "stateCode": "3"}, "4": {"message": "Same Command is already executing on the controller", "stateCode": "1"}, "6": {"message": "Unable to evaluate feedback due to null data exist or no data found", "stateCode": "3"}, "7": {"message": "command has been reached to gateway", "stateCode": "2"}, "8": {"message": "Command failed due mode mismatch", "stateCode": "3"}, "9": {"message": "Command value is outside the range of allowed values", "stateCode": "3"}, "13": {"message": "Wrong Syntax in feedback expression", "stateCode": "3"}, "-1": {"message": "Command feedback timeout", "stateCode": "3"}, "17": {"message": "Command originated", "stateCode": "1"}, "18": {"message": "Command executed without error", "stateCode": "1"}, "19": {"message": "Error in command execution", "stateCode": "3"}, "11": {"message": "Invalid packet", "stateCode": "3"}, "15": {"message": "Stale command", "stateCode": "3"}, "20": {"message": "Invalid command value type", "stateCode": "3"}, "16": {"message": "No expression configured", "stateCode": "3"}, "14": {"message": "Unknown error while evaluating expression", "stateCode": "3"}}