// noinspection JSCheckFunctionSignatures
const AWS = require('aws-sdk');

const RedisClient = require('../../cache/cache.public');
const ActionService = require('../../action/action.public');
const CommandService = require('../../command/command.public');
const CommandHistory = require('./CommandHistory');


/**
 * @class
 * @classdesc this command class is exposing function to interact with database
 */
class CommandRepository {
  constructor() {
    this.documentClient = new AWS.DynamoDB.DocumentClient({
      region: sails.config.REGION,
    });
  }

  static getControlKey(componentId, controlAbbr) {
    return `control:component:${componentId}:controlAbbr:${controlAbbr}`;
  }

  /**
   * @param {JouleTrackCommand} Command
   * @return {Promise<void>}
   */
  async save(Command) {
    const actionCreateObject = this._createActionObject(Command);
    const commandCreateObject = this._createCommandObject(Command);

    await Promise.all([
      ActionService.create(actionCreateObject),
      CommandService.create(commandCreateObject),
      CommandHistory.logOriginatedCommand(Command)
    ]);
  }

  _createActionObject(commandObj) {
    const actionObject = {
      triggerTime: String(commandObj.triggerTime),
      siteId: commandObj.siteId,
      sourceId: commandObj.sourceId,
      source: 'jouletrack',
      sourceInfo: commandObj.sourceInfo,
      uniqId: commandObj.uniqId,
      type: 'command',
      response: '0',
      runOn: 'server',
      isInitiated: 'true',
      nest: JSON.stringify({}),
      reachedJb: '0',
      reachedCtrl: '0',
      operation: 'command',
      info: JSON.stringify({
        deviceId: commandObj.deviceId,
        param: commandObj.device_abbr,
        uniqId: commandObj.uniqId,
        value: commandObj.commandValue,
        component: commandObj.componentId,
        type: 'command',
        controlAbbr: commandObj.controlAbbr,
        commandAbbr: commandObj.commandParam,
        controllerId: commandObj.controllerId,
        componentId: commandObj.componentId,
        userId: commandObj.userId,
        commandType: commandObj.controlType,
        dataParamAbbr: commandObj.dataParamAbbr,
      }),
    };
    return actionObject;
  }

  _createCommandObject(commandObj) {
    const commandObject = {
      deviceId: `${commandObj.deviceId}_jt`,
      timestamp: `${commandObj.siteId}_${commandObj.commandExecutionTsInUnix}`,
      user: commandObj.userId,
      socketID: commandObj.socketId,
      uniqId: commandObj.uniqId,
      value: String(commandObj.commandValue),
      param: commandObj.commandParam,
      componentId: commandObj.componentId,
      component_abbr: commandObj.commandParam,
      controlAbbr: commandObj.controlAbbr,
      controlType: commandObj.controlType,
    };
    return commandObject;
  }

  async fetchCurrentStateByControlAbbr(componentId, controlAbbr) {
    const currentState = await RedisClient.hgetall(CommandRepository.getControlKey(componentId, controlAbbr));
    if (_.isEmpty(currentState)) {
      return {
        state: 1,
      };
    }
    return currentState;
  }

  async saveControlState(componentId, controlAbbr, commandDetail, stateCode, stateTimeoutInSec) {
    sails.log.info(`saveControlState=Saving state for componentId-${componentId} ${controlAbbr} with state code ${stateCode} and timeout ${stateTimeoutInSec}`);
    const transactionClient = RedisClient._con.multi();
    const commandId = CommandRepository.getControlKey(componentId, controlAbbr);
    transactionClient.hset(commandId, 'commandDetail', JSON.stringify(commandDetail));
    transactionClient.hset(commandId, 'state', stateCode);
    transactionClient.expire(commandId, stateTimeoutInSec);

    const commandTrackingId = `command:${commandDetail.commandUid}`;
    transactionClient.set(commandTrackingId, commandId);
    transactionClient.expire(commandTrackingId, stateTimeoutInSec);
    await transactionClient.exec();
  }

  async fetchJouleTrackCommandDetailByCommandId(commandUid) {
    return this._getCommandDetailFromCacheByUid(commandUid);
  }

  _queryToFetchCommandDetailByDynamo(uniqId) {
    const query = {
      TableName: 'commands',
      IndexName: 'uniqId_timestamp_global_index',
      KeyConditionExpression: '#uniqId = :uniqId',
      ExpressionAttributeNames: {
        '#uniqId': 'uniqId'
      },
      ExpressionAttributeValues: {
        ':uniqId': uniqId
      }
    };
    return query;
  }

  async _getCommandDetailFromDynamoByUid(commandUid) {
    const query = this._queryToFetchCommandDetailByDynamo(commandUid)
    try {
      const data = await this.documentClient
        .query(query)
        .promise();
      if (!data.Items.length) return null;
      const [commandDetail] = data.Items;
      return commandDetail;
    } catch (e) {
      sails.log.error(`[CommandRepository > _getCommandDetailFromDynamoByUid ]Unable to Query Data`);
      sails.log.error(`Query - ${JSON.stringify(query)}`)
      sails.log.error(e);
      return null;
    }
  }

  async _getCommandDetailFromCacheByUid(commandUid) {
    const commandTrackingId = `command:${commandUid}`;
    const commandRefKey = await RedisClient.get(commandTrackingId);
    if (!commandRefKey) return null;
    const commandExecutionDetail = await RedisClient.hgetall(commandRefKey);
    if (_.isEmpty(commandExecutionDetail)) return null;
    const {
      state,
      commandDetail
    } = commandExecutionDetail;
    return {
      state,
      commandDetail: JSON.parse(commandDetail)
    };
  }

  async deleteCommand(commandUid) {
    const commandControlKey = await RedisClient.get(`command:${commandUid}`);
    if (!commandControlKey) return false;
    const transactionClient = RedisClient._con.multi();
    transactionClient.del(`command:${commandUid}`);
    transactionClient.del(commandControlKey);
    await transactionClient.exec();
    return true;
  }

  async updateCommandProperty(componentId, controlAbbr, commandDetail) {
    return RedisClient.hset(CommandRepository.getControlKey(componentId, controlAbbr), 'commandDetail', JSON.stringify(commandDetail));
  }
}

module.exports = CommandRepository;
