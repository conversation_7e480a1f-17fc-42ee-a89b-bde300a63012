/**
 *
 */
const moment = require("moment");
const CommandRepository = require("./CommandRepository");
const IotCoreService = require("../../iotCore/iotCore.public");

class CommandExecutionService {
  /**
   *
   * @param commandInstance
   */
  /**
   *
   * @param { JouleTrackCommand } commandInstance
   */
  constructor(commandInstance) {
    this.command = commandInstance;
    this.BUFFER_TIMEOUT_IN_SECOND = 5;
  }

  async execute() {
    const commandRepository = new CommandRepository();
    const commandDetail = {
      commandUid: this.command.uniqId,
      userId: this.command.userId,
      timestamp: moment(this.command.commandExecutionTsInUnix * 1000).toISOString(),
      commandAbbr: this.command.commandParam,
      controlAbbr: this.command.controlAbbr,
      value: this.command.commandValue,
      deviceId: this.command.deviceId,
      device_abbr: this.command.device_abbr,
      siteId: this.command.siteId,
      componentId: this.command.componentId,
      commandExecutionTsInUnix: this.command.commandExecutionTsInUnix,
    };
    const commandTimeoutInSec =
      Number.parseInt(this.command.commandTimeoutInSec) + this.BUFFER_TIMEOUT_IN_SECOND || 5;
    await commandRepository.saveControlState(
      this.command.componentId,
      this.command.controlAbbr,
      commandDetail,
      2,
      commandTimeoutInSec,
    );
    try {
      await IotCoreService.publish(this.getIoTTopic(), this.buildCommandPayload());
    } catch (e) {
      await commandRepository.deleteCommand(commandDetail.commandUid);
      throw e;
    }
  }

  buildCommandPayload() {
    return {
      triggerTime: Number(this.command.triggerTime),
      sourceId: this.command.sourceId,
      source: this.command.source,
      sourceInfo: this.command.sourceInfo,
      uniqId: this.command.uniqId,
      type: this.command.type,
      response: this.command.response,
      runOn: this.command.runOn,
      isInitiated: this.command.isInitiated,
      nest: this.command.nest,
      reachedJb: this.command.reachedJb,
      reachedCtrl: this.command.reachedCtrl,
      operation: this.command.operation,
      extra: {
        socketId: this.command.socketId,
      },
      info: {
        deviceId: String(this.command.deviceId),
        param: this.command.device_abbr,
        uniqId: this.command.uniqId,
        value: Number(this.command.commandValue),
        component: this.command.componentId,
        type: this.command.type,
        controlAbbr: this.command.controlAbbr,
        commandAbbr: this.command.commandParam,
        controllerId: this.command.controllerId,
        componentId: this.command.componentId,
        userId: this.command.userId,
        dataParamAbbr: this.command.dataParamAbbr,
        commandType: this.command.controlType,
      },
      siteId: this.command.siteId,
    };
  }

  getIoTTopic() {
    return `${this.command.siteId}/command/${this.command.deviceId}/jouletrack`;
  }

  getCommandKey() {
    return `siteId:${this.command.siteId}:componentId:${this.command.componentId}:control:${this.command.controlAbbr}`;
  }

  getCommandTimeoutInSec() {
    return Number(this.command.commandTimeoutInSec) + this.BUFFER_TIMEOUT_IN_SECOND;
  }
}

module.exports = CommandExecutionService;
