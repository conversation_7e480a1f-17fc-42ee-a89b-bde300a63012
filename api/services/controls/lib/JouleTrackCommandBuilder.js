/* eslint-disable camelcase */
/* eslint-disable no-multi-assign */
const uuid = require('uuid');
const moment = require('moment-timezone');

moment.tz.add('Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6');
moment.tz.setDefault('Asia/Kolkata');

const privateDataHolder = new WeakMap();
const JouleTrackCommand = require('./JouleTrackCommand');
const ComponentService = require("../../component/component.public");
const CommandErrors = require('../../../utils/controls/error');
const deviceService = require('../../device/device.public');

/**
 * @class
 * @classdesc this class is responsible to create a jouletrack command object.
 * <AUTHOR> Kumar
 */
class JouleTrackCommandBuilder {
  constructor() {
    /**
     *
     * @type {JouleTrackCommand}
     */
    this.command = new JouleTrackCommand();
    this.controlParam = null;
    this.siteId = null;
    this.commandParam = null;
    this.commandValue = null;
    this.componentId = null;
    this.uniqId = null;
    this.sourceInfo = null;
    this.userId = null;
    this.socketId = null;
    this.device_abbr = null;
    this.commandExecutionTsInUnix = null;
    this.feedbackExpression = null;
    this.minValue = null;
    this.maxValue = null;
    this.commandTimeoutInSec = null;
    privateDataHolder.set(this, {
      componentConfig: null,
    });
  }

  static async start(componentId) {
    const _builderInstance = new JouleTrackCommandBuilder();
    const componentConfig = await ComponentService.getComponentConfig(componentId);
    privateDataHolder.get(_builderInstance).componentConfig = componentConfig;
    _builderInstance.command = new JouleTrackCommand();
    _builderInstance.componentId = componentId;
    return _builderInstance;
  }

  reset() {
    this.command = new JouleTrackCommand();
  }

  selectAsset(componentId) {
    this.command.componentId = componentId;
    return this;
  }

  selectControl(controlParam) {
    const { componentConfig } = privateDataHolder.get(this);
    if (!componentConfig) throw new Error('E_COMPONENT_NOT_SET_YET');
    if (!componentConfig.isValidControlAbbr(controlParam)) {
      return CommandErrors.throwsExceptionControlNotFound(controlParam);
    }
    const controlConfig = this.getControlConfigByControlParam(controlParam);
    if (!controlConfig) {
      return CommandErrors.throwsExceptionControlNotFound(controlParam);
    }
    // await RedisClient.get(this.command.getCommandKey())
    // eslint-disable-next-line no-multi-assign
    this.command.controlAbbr = this.controlParam = controlParam;
    return this;
  }

  setCommand({
    commandParam,
    commandValue
  }) {
    if (!this.isValidCommandParam(commandParam)) {
      return CommandErrors.throwsExceptionCommandNotFoundInControl(commandParam);
    }
    if (!this.isControlConfiguredToAcceptCommand()) {
      return CommandErrors.throwsExceptionControlNotConfigured(this.commandParam);
    }
    this.command.commandParam = this.commandParam = commandParam;
    this.command.commandValue = this.commandValue = commandValue;

    const commandExecutionProperty = this.getCommandProperty(this.commandParam)
    const { controlType, dataParamAbbr } = this.getControlConfigByControlParam(this.controlParam)
    this.command.controlType = controlType;
    this.command.dataParamAbbr = dataParamAbbr
    this.command.deviceId = this.deviceId = commandExecutionProperty.deviceId;
    this.command.device_abbr = this.device_abbr = commandExecutionProperty.device_abbr
    this.command.commandTimeoutInSec = this.commandTimeoutInSec = commandExecutionProperty.timeout || 90
    this.command.minValue = this.minValue = commandExecutionProperty.min
    this.command.maxValue = this.maxValue = commandExecutionProperty.max
    this.command.feedbackExpression = this.feedbackExpression = commandExecutionProperty.expression
    return this;
  }

  setExecutorDetail({
    userId,
    socketId
  }) {
    this.command.socketId = this.socketId = socketId;
    this.command.userId = this.userId = userId;
    return this;
  }

  isControlConfiguredToAcceptCommand() {
    const {
      controlProperty,
      controlType,
    } = this.getControlConfigByControlParam(this.controlParam);
    function isCommandConfigured(commandParameterRow) {
      const {
        expression,
        device_abbr,
        key,
        deviceId,
      } = commandParameterRow;
      if (_.isEmpty(expression) || _.isEmpty(deviceId) || (_.isEmpty(key) && _.isEmpty(device_abbr))) return false;
      return true;
    }
    if (controlType === 'BIT') {
      return isCommandConfigured(controlProperty.left) && isCommandConfigured(controlProperty.right);
    }
    if (controlType == 'VIT') {
      return isCommandConfigured(controlProperty);
    }
    return false;
  }

  isValidCommandParam(commandParam) {
    const {
      controlProperty,
      controlType,
    } = this.getControlConfigByControlParam(this.controlParam);

    if (controlType === 'BIT' && controlProperty.left.commandAbbr === commandParam) {
      return controlProperty.left;
    }
    if (controlType === 'BIT' && controlProperty.right.commandAbbr === commandParam) {
      return controlProperty.right;
    }
    if (controlType === 'VIT' && controlProperty.commandAbbr === commandParam) {
      return controlProperty;
    }
    return false
  }

  getControlConfigByControlParam(controlParam) {
    const { componentConfig } = privateDataHolder.get(this);
    if (!componentConfig) throw new Error('E_COMPONENT_NOT_SET_YET');

    const { controlRelationshipMap: { controls } } = componentConfig;
    const [ControlConfig] = controls.filter((it) => it.controlAbbr === controlParam);
    return ControlConfig;
  }

  build() {
    const { componentConfig } = privateDataHolder.get(this);
    this.command.siteId = this.siteId = componentConfig.siteId;
    this.command.uniqId = this.uniqId = uuid.v4();

    this.command.sourceId = this.sourceId = `${this.siteId}_${this.deviceId}.${this.device_abbr}`;
    this.command.triggerTime = this.trigerTime = String(moment()
      .startOf('m')
      .unix() * 1000);
    this.command.sourceInfo = this.sourceInfo = `${this.siteId}_${this.userId}`;
    this.command.commandExecutionTsInUnix = this.commandExecutionTsInUnix = moment()
      .unix();
    const _command = this.command;
    this.reset();
    return _command;
  }

  getCommandProperty(commandParam) {
    const {
      controlProperty,
      controlType,
    } = this.getControlConfigByControlParam(this.controlParam);
    if (controlType === 'BIT') {
      if (controlProperty.left.commandAbbr === commandParam) return controlProperty.left;
      if (controlProperty.right.commandAbbr === commandParam) return controlProperty.right;
    }
    if (controlType === 'VIT') {
      return controlProperty;
    }
    return null;
  }

   static async setCommandDeviceControllerId(jouleTrackCommandObj) {
    try {
    const { deviceId, siteId} = jouleTrackCommandObj;
    const controllerId = (await deviceService.getDeviceControllerMap([deviceId]))[deviceId];
    const controllerInfo =  await deviceService.findOne({deviceId: controllerId, siteId});
    jouleTrackCommandObj.controllerId = controllerId;
    jouleTrackCommandObj.controllerDeviceType = controllerInfo && controllerInfo.hasOwnProperty('deviceType') ? controllerInfo.deviceType : null;
    jouleTrackCommandObj.controllerHardwareVer = controllerInfo && controllerInfo.hasOwnProperty('hardwareVer') ? controllerInfo.hardwareVer : null;
    return;
    } catch(e) {
      if (e.code === 'E_DEVICE_NOT_FOUND') {
        return CommandErrors.throwExceptionDeviceNotExist(e)
      } else if (e.code === 'E_CONTROLLER_NOT_ATTACHED_TO_DEVICE') {
        return  CommandErrors.throwExceptionControllerNotAttached(e)
      } else if (e.code === 'E_CONTROLLER_NOT_FOUND') {
        return CommandErrors.throwExceptionControllerNotExist(e)
      } else {
        throw e;
      }
    }
  }
}

module.exports = JouleTrackCommandBuilder;
