const CommandRepository = require('./CommandRepository');

/**
 * @class
 */
class JouleTrackCommand {
  constructor() {
    this.siteId = null;
    this.componentId = null;
    this.deviceId = null;
    this.commandParam = null;
    this.commandValue = null;
    this.uniqId = null;
    this.sourceInfo = null;
    this.triggerTime = null;
    this.sourceId = null;
    this.controlAbbr = null;
    this.socketId = null;
    this.userId = null;
    this.commandExecutionTsInUnix = null;
    this.device_abbr = null;
    this.feedbackExpression = null;
    this.minValue = null;
    this.maxValue = null;
    this.commandTimeoutInSec = null;
    this.source = 'jouletrack';
    this.type = 'command';
    this.response = '0';
    this.runOn = 'server';
    this.isInitiated = 'true';
    this.nest = {};
    this.reachedJb = "0";
    this.reachedCtrl = "0";
    this.operation = "command";
    this.dataParamAbbr = null;
    this.controllerHardwareVer = null;
    this.controllerDeviceType = null;
  }

  async saveCommand() {
    const commandRepository = new CommandRepository();
    await commandRepository.save(this);
  }

  async getCurrentState() {
    const commandRepository = new CommandRepository();
    const currentState = await commandRepository.fetchCurrentStateByControlAbbr(this.componentId, this.controlAbbr)
    return currentState;
  }

  async isControlInReadyState() {
    const currentState = await this.getCurrentState();
    return currentState.state == 1
  }

  getCommandKey() {
    return `siteId:${this.siteId}:componentId:${this.componentId}:control:${this.controlAbbr}`;
  }
}

module.exports = JouleTrackCommand;
