/* eslint-disable max-classes-per-file */
const flaverr = require('flaverr');
const JTControlFeedbackService = require('./JouleTrackControlFeedbackHandler');

class JouleTrackCommandFeedback {
  constructor() {
    this.componentId = null;
    this.siteId = null;
    this.controlAbbr = null;
    this.userId = null;
    this.commandDetail = {
      initialCommandValue: null,
      commandAbbr: null,
      executionTimestamp: null
    };
    this.currentTimestamp = null;
    this.currentFeedbackDetail = {
      stateCode: null,
      message: null
    };
    this.rawIoTFeedback = {};
  }
}

class JouleTrackCommandFeedbackBuilder {
  constructor() {
    this.JouleTrackCommandFeedback = new JouleTrackCommandFeedback();
    this.JTControlFeedbackService = new JTControlFeedbackService();
  }

  setComponentId(componentId) {
    this.JouleTrackCommandFeedback.componentId = componentId;
    return this;
  }

  setSiteId(siteId) {
    this.JouleTrackCommandFeedback.siteId = siteId;
    return this;
  }

  setCommandDetail({
    value,
    commandAbbr,
    controlAbbr,
    executionTimestamp,
    userId,
    commandUid
  }) {
    this.JouleTrackCommandFeedback.controlAbbr = controlAbbr;
    this.JouleTrackCommandFeedback.commandDetail.initialCommandValue = value;
    this.JouleTrackCommandFeedback.commandDetail.commandAbbr = commandAbbr;
    this.JouleTrackCommandFeedback.commandDetail.executionTimestamp = executionTimestamp * 1000;
    this.JouleTrackCommandFeedback.userId = userId;
    this.JouleTrackCommandFeedback.commandUid = commandUid
    return this;
  }

  setControlState(rawCommandState) {
    const currentControlState = JTControlFeedbackService.getControlStateByIoTStatus(rawCommandState);
    if (!currentControlState) {
      throw flaverr({
        code: 'E_UNREGISTERED_COMMAND_FEEDBACK_RECEIVED',
        message: `command feedback "${rawCommandState}" is not registered. Please contact to PD team for registration`,
        data: {
          commandUid: this.JouleTrackCommandFeedback.commandUid
        },
        HTTP_STATUS_CODE: 400
      });
    }
    this.JouleTrackCommandFeedback.currentFeedbackDetail.stateCode = Number.parseInt(currentControlState.stateCode, 10);
    this.JouleTrackCommandFeedback.currentFeedbackDetail.message = currentControlState.message;
    return this;
  }

  setRawFeedback(rawMessage) {
    this.JouleTrackCommandFeedback.rawIoTFeedback = rawMessage;
    return this;
  }

  build() {
    this.JouleTrackCommandFeedback.currentTimestamp = new Date().getTime()
    return { ...this.JouleTrackCommandFeedback };
  }
}
module.exports = JouleTrackCommandFeedbackBuilder
