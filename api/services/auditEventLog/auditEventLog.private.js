const EventEmitter = require('events');
const async = require('async');
const { v4: uuidv4 } = require('uuid');
const moment = require('moment');
const kafkaProducerService = require('../../services/kafka/lib/kafka.producer.service');
const { validateAuditPayload } = require('../../utils/auditEventLog/auditEventLog.util');

class AuditEventLogService extends EventEmitter {
  constructor() {
    super();
    this.queue = [];
  }

  log(payload) {
    sails.log.info('[AuditEventLogService]', payload);
  }

  async emit(payload) {
    const auditLogPayload = await this._prepareAuditLogPayload(payload);
    if (_.isEmpty(auditLogPayload)) return;
    this.log(auditLogPayload);
    if (sails.config.kafka.kafkaEnabled) {
      this.queue.push(auditLogPayload);
      await this.processQueue();
    }
  }

  /**
   * @description Controls the concurrency of tasks to ensure optimal resource usage.
   * @detail Parallel Queue Processor with Concurrency Control
   * @overview Limits the number of tasks running concurrently to a maximum of 50.
   * @summary Processes tasks in a rolling window, meaning as soon as one task finishes, the next task starts.
   * @example For example, with 100 tasks:
   *   - Initially, 50 tasks start running simultaneously.
   *   - As each task completes, the next task from the queue begins immediately.
   *   - This ensures continuous processing without exceeding the limit of 50 concurrent tasks.
   * - Prevents system overload and respects external service rate limits.
   */
  async processQueue() {
    await async.eachLimit(this.queue, 50, async (payload) => {
      try {
        const message = JSON.stringify(payload);
        const auditLogTopic = sails.config?.kafka?.topics?.auditLogTopic;
        sails.log.info('[AuditEventLogService] Topic=' + auditLogTopic);
        await kafkaProducerService.send(auditLogTopic, [
          { value: message },
        ]);
        sails.log.info('[AuditEventLogService] Audit log published to Kafka:', message);
      } catch (error) {
        sails.log.error('[AuditEventLogService] Failed to publish audit log:', error);
      }
    });

    this.queue = [];
  }

  /**
   * @description Prepares the audit log payload by validating it against the schema,
   * and assigning required fields like `api_end_point`, `timestamp`, and `request_id`.
   */
  async _prepareAuditLogPayload(payload) {
    const {
      req,
      ...rest
    } = payload;

    const timeZoneOffset = await sails.helpers.getSiteTimezone.with({
      siteId: payload?.site_id,
      timezoneFormat: 'utcOffsetInMinute',
    });

    const auditLogPayload = {
      ...rest,
      api_end_point: req?.baseUrl + req?.url || 'unknown',
      timestamp: moment()
        .utcOffset(timeZoneOffset)
        .toISOString(),
      request_id: req?.headers?.['x-transaction-id'] || uuidv4(),
    };

    try {
      validateAuditPayload(auditLogPayload);
    } catch (error) {
      sails.log.error('[AuditEventLogService] Invalid audit log payload:', error?.message);
      sails.log.error(error);
      return null;
    }
    return auditLogPayload;
  }
}

module.exports = new AuditEventLogService();
