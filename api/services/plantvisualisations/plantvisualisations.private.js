/* eslint-disable no-undef */
module.exports = {

  /**
   * Plantvisualisations module private functions
   */
  create: async (params) => {
    return Plantvisualisations.create(params);
  },
  find: async (searchParams) => {
    // Plantvisualisations.find
    return Plantvisualisations.find(searchParams);
  },
  findOne: async (searchParams) => {
    // Plantvisualisations.findone
    let plantvisualisationss = await Plantvisualisations.find(searchParams).limit(1);
    return plantvisualisationss[0];
  },
  update: async (searchParams, updateValue) =>{
    return Plantvisualisations.update(searchParams, updateValue);
  },
  delete: async (searchParams) => {
    return Plantvisualisations.destroy(searchParams);
  },
};
