module.exports = {
  configuratorTaggedDevices: {
    create: async (params) => {
      return ConfiguratorTaggedDevices.create(params);
    },
    find: async (searchParams) => {
      return ConfiguratorTaggedDevices.find(searchParams);
    },
    update: async (searchParams, updateValue) => {
      return ConfiguratorTaggedDevices.update(searchParams, updateValue);
    },
    delete: async (searchParams) => {
      return ConfiguratorTaggedDevices.destroy(searchParams);
    },
    findOne: async (searchParams) => {
      return ConfiguratorTaggedDevices.findOne(searchParams);
    },
  },
  configuratorTaggedDevicesParam: {
    create: async (params) => {
      return ConfiguratorTaggedDevicesParam.create(params);
    },
    find: async (searchParams) => {
      return ConfiguratorTaggedDevicesParam.find(searchParams);
    },
    update: async (searchParams, updateValue) => {
      return ConfiguratorTaggedDevicesParam.update(searchParams, updateValue);
    },
    delete: async (searchParams) => {
      return ConfiguratorTaggedDevicesParam.destroy(searchParams);
    },
    findOne: async (searchParams) => {
      return ConfiguratorTaggedDevicesParam.findOne(searchParams);
    },
  },
};
