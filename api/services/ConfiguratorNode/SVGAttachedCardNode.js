const ErrorHandler = require("../../utils/configuratorTaggedDevices/ErrorHandler");

class SVGAttachedCardNode {
  constructor(obj) {
    this._rawObj = obj;
  }

  static ObjectBuilder(raw) {
    const object = {
      id: raw.id,
      elementId: raw.elementId,
      deviceId: raw.deviceId,
      deviceClass: "component",
      uiElementType: "svgAttachedCard",
      svgLocationId: raw.svgLocationId,
      offset: raw.offset,
      isInMaintenanceMode: raw.isInMaintenanceMode,
    };

    if (raw.data) {
      object.data = {
        parameters: (raw.data && raw.data.parameters) || [],
        position: (raw.data && raw.data.position) || {},
        style: (raw.data && raw.data.style) || {},
      };
    }
    if (raw.controls) {
      object.controls = {
        controlAbbr: raw.controls.controlAbbr || [],
        position: raw.controls.position || {},
        style: raw.controls.style || {},
        controlRelationshipData: raw.controls.controlRelationshipData || [],
      };
    }
    return object;
  }

  // eslint-disable-next-line class-methods-use-this
  async build() {}

  async createNewNodeWithTransaction(subSystemPageId, node, userId, dbTransactionObj) {
    // eslint-disable-next-line max-len
    const configuratorTaggedNode = JSON.parse(JSON.stringify(node)); // making new copy of node object
    delete configuratorTaggedNode.id;
    // nodeObject.
    configuratorTaggedNode.subSystemPageId = subSystemPageId;
    const {
      svgLocationId,
      deviceId,
      data: dataAbbrCollection,
      controls: controlAbbrCollection,
    } = configuratorTaggedNode;
    await this.validateDuplicateSvgLocationIdTagging(
      {
        svgLocationId,
        deviceId,
        subSystemPageId,
      },
      dbTransactionObj,
    );

    configuratorTaggedNode.createdBy = userId;
    const newConfiguratorTaggedDevice = await ConfiguratorTaggedDevices.create(
      configuratorTaggedNode,
    )
      .fetch()
      .usingConnection(dbTransactionObj);

    const { id: configuratorTaggedDevicesId } = newConfiguratorTaggedDevice;

    const taggedParams = [];
    taggedParams.push(
      ...this._returnDataParamBatchInsertQueries(
        configuratorTaggedDevicesId,
        dataAbbrCollection,
        userId,
        "createdBy",
      ),
    );

    if (controlAbbrCollection && Array.isArray(controlAbbrCollection.controlAbbr)) {
      taggedParams.push(
        ...this._returnControlParamBatchInsertQueries(
          configuratorTaggedDevicesId,
          controlAbbrCollection,
          userId,
          "createdBy",
        ),
      );
    }

    await ConfiguratorTaggedDevicesParam.createEach(taggedParams).usingConnection(dbTransactionObj);
    return configuratorTaggedDevicesId;
  }

  async updateNodeWithTransaction(subSystemPageId, _updateNodeObj, userId, dbTransactionObj) {
    const updateNodeObj = JSON.parse(JSON.stringify(_updateNodeObj));
    const { id: taggedDeviceId } = updateNodeObj;
    // noinspection JSUnresolvedReference
    // eslint-disable-next-line no-undef
    const taggingExist = await ConfiguratorTaggedDevices.findOne({
      id: taggedDeviceId,
      subSystemPageId,
      status: 1,
    }).usingConnection(dbTransactionObj);
    if (_.isEmpty(taggingExist)) {
      ErrorHandler.throwExceptionInvalidConfiguratorTaggedDevices(taggedDeviceId);
    }
    const UpdateConfiguratorTaggedDevicesObj = {};
    const allowedUpdateKeys = ["deviceId", "offset", "data", "controls"];
    for (const [key, value] of Object.entries(updateNodeObj)) {
      if (allowedUpdateKeys.includes(key)) {
        UpdateConfiguratorTaggedDevicesObj[key] = value;
      }
    }

    UpdateConfiguratorTaggedDevicesObj.updatedBy = userId;
    await ConfiguratorTaggedDevices.update({
      id: taggedDeviceId,
      status: 1,
    })
      .set(UpdateConfiguratorTaggedDevicesObj)
      .fetch()
      .usingConnection(dbTransactionObj);
    // Disable data params and create new record
    let { data: dataAbbrCollection, controls: controlAbbrCollection } = updateNodeObj;
    const taggedParams = [];
    if (!dataAbbrCollection) dataAbbrCollection = { parameters: [] };
    if (!controlAbbrCollection) controlAbbrCollection = { controlAbbr: [] };
    if (dataAbbrCollection && Array.isArray(dataAbbrCollection.parameters)) {
      await ConfiguratorTaggedDevicesParam.update({
        configuratorTaggedDevicesId: taggedDeviceId,
        paramType: "data",
        status: 1,
      })
        .set({
          status: 0,
          updatedBy: userId,
        })
        .usingConnection(dbTransactionObj);
      taggedParams.push(
        ...this._returnDataParamBatchInsertQueries(
          taggedDeviceId,
          dataAbbrCollection,
          userId,
          "updatedBy",
        ),
      );
    }

    if (controlAbbrCollection && Array.isArray(controlAbbrCollection.controlAbbr)) {
      await ConfiguratorTaggedDevicesParam.update({
        configuratorTaggedDevicesId: taggedDeviceId,
        paramType: "control",
        status: 1,
      })
        .set({ status: 0 })
        .usingConnection(dbTransactionObj);
      taggedParams.push(
        ...this._returnControlParamBatchInsertQueries(
          taggedDeviceId,
          controlAbbrCollection,
          userId,
          "updatedBy",
        ),
      );
    }
    if (taggedParams.length) {
      await ConfiguratorTaggedDevicesParam.createEach(taggedParams).usingConnection(
        dbTransactionObj,
      );
    }
  }

  async validateDuplicateSvgLocationIdTagging(
    { deviceId, svgLocationId, subSystemPageId },
    dbTransactionObj,
  ) {
    // noinspection JSUnresolvedReference
    // eslint-disable-next-line no-undef
    const ConfiguratorTaggedDevice = await ConfiguratorTaggedDevices.find({
      deviceId,
      svgLocationId,
      subSystemPageId,
      status: 1,
    }).usingConnection(dbTransactionObj);
    if (!_.isEmpty(ConfiguratorTaggedDevice)) {
      ErrorHandler.throwsExceptionSvgLocationOccupied({
        newComponentId: deviceId,
        existingComponentId: ConfiguratorTaggedDevice[0].deviceId,
        svgLocationId,
      });
    }
  }

  _returnDataParamBatchInsertQueries(
    configuratorTaggedDevicesId,
    { parameters, style, position },
    userId,
    operation,
  ) {
    const _taggedParams = [];
    const paramSet = new Set();
    if (parameters.length) {
      let index = 0;
      for (const paramAbbr of parameters) {
        if (paramSet.has(paramAbbr)) continue;
        _taggedParams.push({
          configuratorTaggedDevicesId,
          paramType: "data",
          paramAbbr,
          position,
          style,
          status: 1,
          order: index,
          [operation]: userId,
        });
        index++;
      }
    }
    return _taggedParams;
  }

  _returnControlParamBatchInsertQueries(
    configuratorTaggedDevicesId,
    { controlAbbr, style, position },
    userId,
    operation,
  ) {
    const paramSet = new Set();
    const _taggedParams = [];
    let index = 0;
    for (const paramAbbr of controlAbbr) {
      if (paramSet.has(paramAbbr)) continue;
      _taggedParams.push({
        configuratorTaggedDevicesId,
        paramType: "control",
        paramAbbr,
        position,
        style,
        order: index,
        [operation]: userId,
      });
      paramSet.add(paramAbbr);
      index++;
    }
    return _taggedParams;
  }
}

module.exports = SVGAttachedCardNode;
