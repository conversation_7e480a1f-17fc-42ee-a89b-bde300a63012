/* eslint-disable max-len */
const ErrorHandler = require("../../utils/configuratorTaggedDevices/ErrorHandler");
const {
  attributes: updatedConfiguratorTaggedDevicesObj,
} = require("../../models/ConfiguratorTables");

/*
// eslint-disable-next-line max-len
* FloatingComponentControlCardNode is data taggable class
to generate a block of component's control parameter
* */
class FloatingComponentControlCardNode {
  constructor(obj) {
    this._rawObj = obj;
  }

  static ObjectBuilder(raw) {
    const object = {
      id: raw.id,
      elementId: raw.elementId,
      deviceId: raw.deviceId,
      deviceClass: "component",
      isInMaintenanceMode: raw.isInMaintenanceMode,
      uiElementType: "floatingControl",
      offset: raw.offset || {},
      displayFormat: raw.displayFormat,
      controls: {
        controlAbbr: (raw.controls && raw.controls.controlAbbr) || [],
        position: (raw.controls && raw.controls.position) || {},
        style: (raw.controls && raw.controls.style) || {},
        controlRelationshipData: (raw.controls && raw.controls.controlRelationshipData) || [],
      },
      position: raw.position || {},
      style: raw.style || {},
    };
    return object;
  }

  async build() {}

  async createNewNodeWithTransaction(subSystemPageId, node, userId, dbTransactionObj) {
    const configuratorTaggedNode = JSON.parse(JSON.stringify(node)); // making new copy of node object
    delete configuratorTaggedNode.id;
    // nodeObject.
    configuratorTaggedNode.subSystemPageId = subSystemPageId;
    const { controls: controlAbbrCollection } = configuratorTaggedNode;
    // noinspection JSUnresolvedReference
    configuratorTaggedNode.createdBy = userId;
    const newConfiguratorTaggedDevice = await ConfiguratorTaggedDevices.create(
      configuratorTaggedNode,
    )
      .fetch()
      .usingConnection(dbTransactionObj);
    const { id: configuratorTaggedDevicesId } = newConfiguratorTaggedDevice;

    const taggedParams = [];
    taggedParams.push(
      ...this._returnControlParamBatchInsertQueries(
        configuratorTaggedDevicesId,
        controlAbbrCollection,
        userId,
        "createdBy",
      ),
    );

    await ConfiguratorTaggedDevicesParam.createEach(taggedParams).usingConnection(dbTransactionObj);
    return configuratorTaggedDevicesId;
  }

  async updateNodeWithTransaction(subSystemPageId, _updateNodeObj, userId, dbTransactionObj) {
    const updateNodeObj = JSON.parse(JSON.stringify(_updateNodeObj));
    const { id: taggedDeviceId } = updateNodeObj;
    // noinspection JSUnresolvedReference
    // eslint-disable-next-line no-undef
    const taggingExist = await ConfiguratorTaggedDevices.findOne({
      id: taggedDeviceId,
      subSystemPageId,
      status: 1,
    }).usingConnection(dbTransactionObj);
    if (_.isEmpty(taggingExist)) {
      ErrorHandler.throwExceptionInvalidConfiguratorTaggedDevices(taggedDeviceId);
    }
    const UpdateConfiguratorTaggedDevicesObj = {};
    const allowedUpdateKeys = [
      "displayFormat",
      "offset",
      "controls",
      "style",
      "deviceId",
      "position",
    ];
    for (const [key, value] of Object.entries(updateNodeObj)) {
      if (allowedUpdateKeys.includes(key)) {
        UpdateConfiguratorTaggedDevicesObj[key] = value;
      }
    }
    updatedConfiguratorTaggedDevicesObj.updatedBy = userId;
    // noinspection JSUnresolvedReference
    // eslint-disable-next-line no-undef
    await ConfiguratorTaggedDevices.update({
      id: taggedDeviceId,
      status: 1,
    })
      .set(UpdateConfiguratorTaggedDevicesObj)
      .fetch()
      .usingConnection(dbTransactionObj);
    // Disable data params and create new record
    let { controls: controlAbbrCollection } = updateNodeObj;
    if (!controlAbbrCollection) controlAbbrCollection = { controlAbbr: [] };
    const taggedParams = [];
    if (controlAbbrCollection && Array.isArray(controlAbbrCollection.controlAbbr)) {
      // noinspection JSUnresolvedReference
      // eslint-disable-next-line no-undef
      await ConfiguratorTaggedDevicesParam.update({
        configuratorTaggedDevicesId: taggedDeviceId,
        paramType: "control",
        status: 1,
      })
        .set({
          status: 0,
          updatedBy: userId,
        })
        .usingConnection(dbTransactionObj);
      taggedParams.push(
        ...this._returnControlParamBatchInsertQueries(
          taggedDeviceId,
          controlAbbrCollection,
          userId,
          "updatedBy",
        ),
      );
    }

    // noinspection JSUnresolvedReference
    // eslint-disable-next-line no-undef
    if (taggedParams.length) {
      await ConfiguratorTaggedDevicesParam.createEach(taggedParams).usingConnection(
        dbTransactionObj,
      );
    }
  }

  _returnControlParamBatchInsertQueries(
    configuratorTaggedDevicesId,
    { controlAbbr, style, position },
    userId,
    operation,
  ) {
    const paramSet = new Set();
    const _taggedParams = [];
    let index = 0;
    for (const paramAbbr of controlAbbr) {
      if (paramSet.has(paramAbbr)) continue;
      _taggedParams.push({
        configuratorTaggedDevicesId,
        paramType: "control",
        paramAbbr,
        position,
        style,
        order: index,
        [operation]: userId,
      });
      paramSet.add(paramAbbr);
      index++;
    }
    return _taggedParams;
  }
}

module.exports = FloatingComponentControlCardNode;
