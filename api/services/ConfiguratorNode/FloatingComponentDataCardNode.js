/* eslint-disable no-prototype-builtins */
/* eslint-disable max-len */
const ErrorHandler = require("../../utils/configuratorTaggedDevices/ErrorHandler");
const {
  attributes: updateConfiguratorTaggedDevicesObj,
} = require("../../models/ConfiguratorTables");

/*
 * FloatingComponentDataCardNode is data taggable class to generate a block of component's data parameter
 * */
class FloatingComponentDataCardNode {
  constructor(obj) {
    this._rawObj = obj;
  }

  static ObjectBuilder(raw) {
    const object = {
      id: raw.id,
      elementId: raw.elementId,
      deviceId: raw.deviceId,
      deviceClass: raw.deviceClass,
      uiElementType: "floatingCard",
      offset: raw.offset || {},
      position: raw.position || {},
      style: raw.style || {},
      isInMaintenanceMode: raw.isInMaintenanceMode,
    };
    if (raw.hasOwnProperty("controls")) {
      object.controls = {
        controlAbbr: raw.controls.controlAbbr || [],
        position: raw.controls.position || {},
        style: raw.controls.style || {},
        controlRelationshipData: raw.controls.controlRelationshipData || [],
      };
    }
    if (raw.hasOwnProperty("data")) {
      object.data = {
        parameters: raw.data.parameters || [],
        position: raw.data.position || {},
        style: raw.data.style || {},
      };
    }
    return object;
  }

  async build() {}

  async createNewNodeWithTransaction(subSystemPageId, node, userId, dbTransactionObj) {
    const configuratorTaggedNode = JSON.parse(JSON.stringify(node)); //making new copy of node object
    delete configuratorTaggedNode.id;
    // nodeObject.
    configuratorTaggedNode.subSystemPageId = subSystemPageId;
    const { data: dataAbbrCollection, controls: controlAbbrCollection } = configuratorTaggedNode;
    // noinspection JSUnresolvedReference
    configuratorTaggedNode.createdBy = userId;
    const newConfiguratorTaggedDevice = await ConfiguratorTaggedDevices.create(
      configuratorTaggedNode,
    )
      .fetch()
      .usingConnection(dbTransactionObj);
    const { id: configuratorTaggedDevicesId } = newConfiguratorTaggedDevice;

    const taggedParams = [];

    taggedParams.push(
      ...this._returnDataParamBatchInsertQueries(
        configuratorTaggedDevicesId,
        dataAbbrCollection,
        userId,
        "createdBy",
      ),
    );
    if (controlAbbrCollection)
      taggedParams.push(
        ...this._returnControlAbbrBatchInsertQueries(
          configuratorTaggedDevicesId,
          controlAbbrCollection,
          userId,
          "createdBy",
        ),
      );

    // noinspection JSUnresolvedReference
    await ConfiguratorTaggedDevicesParam.createEach(taggedParams).usingConnection(dbTransactionObj);
    return configuratorTaggedDevicesId;
  }

  async updateNodeWithTransaction(subSystemPageId, _updateNodeObj, userId, dbTransactionObj) {
    const updateNodeObj = JSON.parse(JSON.stringify(_updateNodeObj));
    const { id: taggedDeviceId } = updateNodeObj;
    // noinspection JSUnresolvedReference
    const taggingExist = await ConfiguratorTaggedDevices.findOne({
      id: taggedDeviceId,
      subSystemPageId,
      status: 1,
    }).usingConnection(dbTransactionObj);
    if (_.isEmpty(taggingExist)) {
      ErrorHandler.throwExceptionInvalidConfiguratorTaggedDevices(taggedDeviceId);
    }
    const UpdateConfiguratorTaggedDevicesObj = {};
    const allowedUpdateKeys = [
      "displayFormat",
      "offset",
      "data",
      "controls",
      "style",
      "position",
      "deviceId",
    ];
    for (const [key, value] of Object.entries(updateNodeObj)) {
      if (allowedUpdateKeys.includes(key)) {
        UpdateConfiguratorTaggedDevicesObj[key] = value;
      }
    }
    updateConfiguratorTaggedDevicesObj.updatedBy = userId;
    // noinspection JSUnresolvedReference
    await ConfiguratorTaggedDevices.update({
      id: taggedDeviceId,
      status: 1,
    })
      .set(UpdateConfiguratorTaggedDevicesObj)
      .fetch()
      .usingConnection(dbTransactionObj);

    const taggedParams = [];

    //Disable data params and create new record
    let { data: dataAbbrCollection, controls: controlAbbrCollection } = updateNodeObj;
    if (!dataAbbrCollection) dataAbbrCollection = { parameters: [] };
    if (!controlAbbrCollection) controlAbbrCollection = { controlAbbr: [] };
    if (dataAbbrCollection && Array.isArray(dataAbbrCollection.parameters)) {
      // noinspection JSUnresolvedReference
      await ConfiguratorTaggedDevicesParam.update({
        configuratorTaggedDevicesId: taggedDeviceId,
        paramType: "data",
        status: 1,
      })
        .set({
          status: 0,
          updatedBy: userId,
        })
        .usingConnection(dbTransactionObj);
      taggedParams.push(
        ...this._returnDataParamBatchInsertQueries(
          taggedDeviceId,
          dataAbbrCollection,
          userId,
          "updatedBy",
        ),
      );
    }

    if (controlAbbrCollection && Array.isArray(controlAbbrCollection.controlAbbr)) {
      // noinspection JSUnresolvedReference
      await ConfiguratorTaggedDevicesParam.update({
        configuratorTaggedDevicesId: taggedDeviceId,
        paramType: "control",
        status: 1,
      })
        .set({
          status: 0,
          updatedBy: userId,
        })
        .usingConnection(dbTransactionObj);
      taggedParams.push(
        ...this._returnControlAbbrBatchInsertQueries(
          taggedDeviceId,
          controlAbbrCollection,
          userId,
          "updatedBy",
        ),
      );
    }

    if (!taggedParams.length) return;
    // noinspection JSUnresolvedReference
    await ConfiguratorTaggedDevicesParam.createEach(taggedParams).usingConnection(dbTransactionObj);
  }

  _returnDataParamBatchInsertQueries(
    configuratorTaggedDevicesId,
    { parameters, style, position },
    userId,
    operation,
  ) {
    const paramSet = new Set();
    const _taggedParams = [];
    let index = 0;
    for (const paramAbbr of parameters) {
      if (paramSet.has(paramAbbr)) continue;
      _taggedParams.push({
        configuratorTaggedDevicesId,
        paramType: "data",
        paramAbbr,
        position,
        style,
        status: 1,
        order: index,
        [operation]: userId,
      });
      paramSet.add(paramAbbr);
      index++;
    }
    return _taggedParams;
  }

  _returnControlAbbrBatchInsertQueries(
    configuratorTaggedDevicesId,
    { controlAbbr, style, position },
    userId,
    operation,
  ) {
    const paramSet = new Set();
    const _taggedParams = [];
    let index = 0;
    for (const paramAbbr of controlAbbr) {
      if (paramSet.has(paramAbbr)) continue;
      _taggedParams.push({
        configuratorTaggedDevicesId,
        paramType: "control",
        paramAbbr,
        position,
        style,
        status: 1,
        order: index,
        [operation]: userId,
      });
      paramSet.add(paramAbbr);
      index++;
    }
    return _taggedParams;
  }
}

module.exports = FloatingComponentDataCardNode;
