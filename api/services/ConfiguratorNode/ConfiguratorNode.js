const SVGAttachedCardNode = require("./SVGAttachedCardNode");
const FloatingComponentDataCardNode = require("./FloatingComponentDataCardNode");
const FloatingComponentControlCardNode = require("./FloatingComponentControlCardNode");
const FloatingDataParameterCardNode = require("./FloatingDataParameterCardNode");
const FloatingLabelCardNode = require("./FloatingLabelCardNode");
const DeviceService = require("../device/device.public");
const ComponentService = require("../component/component.service");
const modeServicePublic = require("../mode/mode.public");
const errorHandler = require("../../utils/configuratorPage/errorHandler");

class ConfiguratorNode {
  constructor(nodeType) {
    if (nodeType === "svgAttachedCard") {
      this.instance = new SVGAttachedCardNode();
    } else if (nodeType === "floatingCard") {
      this.instance = new FloatingComponentDataCardNode();
    } else if (nodeType === "floatingControl") {
      this.instance = new FloatingComponentControlCardNode();
    } else if (nodeType === "floatingParameter") {
      this.instance = new FloatingDataParameterCardNode();
    } else if (nodeType === "floatingLabel") {
      this.instance = new FloatingLabelCardNode();
    } else {
      throw new Error("INVALID_CONFIGURATOR_NODE_TYPE");
    }
  }

  static async fetchAllConfiguredNodes(siteId, pageId) {
    const rawRecord = await fetchRawRecord(siteId, pageId);

    const componentIds = extractComponentIds(rawRecord);

    const componentConfig = (
      await Promise.allSettled(componentIds.map(ComponentService.getComponentConfig))
    )
      .filter((it) => it.status == "fulfilled")
      .map((it) => it.value);

    const ComponentConfigMap = componentConfig.reduce((acm, curr) => {
      acm[curr.deviceId] = curr;
      return acm;
    }, {});

    const formattedRecord = merger(
      buildElementIdWiseDataControlMap(rawRecord),
      buildElementWiseTaggableItemMap(rawRecord),
    );
    const ConfiguredNodeWithControlConfig = mapControlConfig(formattedRecord, ComponentConfigMap);
    return ConfiguredNodeWithControlConfig.map((it) => {
      const { uiElementType } = it;
      if (uiElementType === "svgAttachedCard") {
        return SVGAttachedCardNode.ObjectBuilder(it);
      } else if (uiElementType === "floatingCard") {
        return FloatingComponentDataCardNode.ObjectBuilder(it);
      } else if (uiElementType === "floatingControl") {
        return FloatingComponentControlCardNode.ObjectBuilder(it);
      } else if (uiElementType === "floatingParameter") {
        return FloatingDataParameterCardNode.ObjectBuilder(it);
      } else if (uiElementType === "floatingLabel") {
        return FloatingLabelCardNode.ObjectBuilder(it);
      }
    });

    async function fetchRawRecord(siteId, pageId) {
      const query = `
      SELECT
          ctd.id AS "taggedId",
          ctd.device_id AS "deviceId",
          ctd."createdAt" AS "timestamp",
          ctd.svg_location_id AS "svgLocationId",
          ctd.display_format AS "displayFormat",
          ctd."device_Type" AS "deviceClass",
          ctd."elementStyle" AS "style",
          ctd.element_id AS "elementId",
          ctd.text_label_data AS "label",
          ctd.position AS "position",
          ctd."uiElementType" AS "uiElementType",
          ctd.offset,
          ctdp.param_abbr AS "parameterAbbr",
          ctdp.position AS "paramPosition",
          ctdp.style AS "paramStyle",
          ctdp.param_type AS "paramType",
          ctd.order AS "paramOrder",
          ctdp.id AS "paramId"
      FROM
          configurator_tagged_devices AS ctd
      Left join configurator_tagged_devices_param ctdp on ctdp.configurator_tagged_devices_id = ctd.id and ctdp.status = 1
      where ctd.sub_system_page_id = $1 and ctd.status = 1 and (ctdp.id is null  or ctdp.status =1)
      ORDER BY
      ctdp.id, ctdp."order" ASC;
  `;
      const { rows: rawRecord } = await sails
        .getDatastore("postgres")
        .sendNativeQuery(query, [pageId]);
      return rawRecord;
    }

    function buildElementIdWiseDataControlMap(rawRecord) {
      const taggedElementByDataControlParamMap = {};
      for (const row of rawRecord) {
        const { elementId, parameterAbbr, paramType } = row;

        if (!parameterAbbr) continue;
        if (!taggedElementByDataControlParamMap.hasOwnProperty(elementId)) {
          taggedElementByDataControlParamMap[elementId] = {};
        }
        const parameterAbbrKeyName = paramType === "control" ? "controlAbbrs" : "dataAbbrs";
        if (!taggedElementByDataControlParamMap[elementId].hasOwnProperty(parameterAbbrKeyName)) {
          taggedElementByDataControlParamMap[elementId][parameterAbbrKeyName] = [];
        }
        taggedElementByDataControlParamMap[elementId][parameterAbbrKeyName].push(parameterAbbr);
      }
      return taggedElementByDataControlParamMap;
    }

    function buildElementWiseTaggableItemMap(rawRecord) {
      const uiElementMap = {};
      for (const row of rawRecord) {
        const {
          elementId,
          taggedId,
          deviceId,
          offset,
          deviceClass,
          svgLocationId,
          displayFormat,
          label,
          paramStyle,
          paramPosition,
          uiElementType,
          position,
          paramType,
          style,
        } = row;
        if (!uiElementMap.hasOwnProperty(elementId)) {
          uiElementMap[elementId] = {};
        }

        Object.assign(uiElementMap[elementId], {
          offset,
          uiElementType,
          deviceId,
          deviceClass,
          svgLocationId,
          displayFormat,
          label,
          id: taggedId,
          elementId,
          position,
          style,
        });
        if (paramType === "data") {
          uiElementMap[elementId].data = {
            parameters: [],
            style: paramStyle,
            position: paramPosition,
          };
        }
        if (paramType === "control") {
          uiElementMap[elementId].controls = {
            style: paramStyle,
            position: paramPosition,
            controlAbbr: [],
            controlRelationshipData: [],
          };
        }
      }
      return uiElementMap;
    }

    function merger(ElementIdWiseDataControlMap, ElementWiseTaggableItemMap) {
      for (const elementId in ElementIdWiseDataControlMap) {
        if (ElementWiseTaggableItemMap.hasOwnProperty(elementId)) {
          if (ElementIdWiseDataControlMap[elementId].dataAbbrs) {
            ElementWiseTaggableItemMap[elementId].data.parameters.push(
              ...ElementIdWiseDataControlMap[elementId].dataAbbrs,
            );
          } else {
            delete ElementWiseTaggableItemMap[elementId].data;
          }
          if (ElementIdWiseDataControlMap[elementId].controlAbbrs) {
            ElementWiseTaggableItemMap[elementId].controls.controlAbbr.push(
              ...ElementIdWiseDataControlMap[elementId].controlAbbrs,
            );
          } else {
            delete ElementWiseTaggableItemMap[elementId].controls;
          }
        }
      }
      return ElementWiseTaggableItemMap;
    }

    function extractComponentIds(rawRecords) {
      const uniqueComponentIds = new Set();
      for (const { deviceClass, deviceId } of rawRecords) {
        if (deviceClass === "component" && deviceId) {
          uniqueComponentIds.add(deviceId);
        }
      }
      return Array.from(uniqueComponentIds);
    }

    function extractDeviceIds(rawRecords) {
      const uniqueDeviceIds = new Set();
      for (const { deviceClass, deviceId } of rawRecords) {
        if (deviceClass === "device" && deviceId) {
          uniqueDeviceIds.add(deviceId);
        }
      }
      return Array.from(uniqueDeviceIds);
    }

    function mapControlConfig(formattedRecord, ComponentConfigMap, modeDetailsMap) {
      const ConfiguredNodeWithControlRel = [];
      const defaultMode = {
        key: modeServicePublic.DEFAULT_MODE,
        label:
          sails.config.custom.MODE_KEY_MAP[modeServicePublic.DEFAULT_MODE] ||
          modeServicePublic.DEFAULT_MODE,
      };
      for (const elementId in formattedRecord) {
        const record = formattedRecord[elementId];
        const { deviceId, deviceClass } = record;
        if (deviceClass === "component") {
          if (ComponentConfigMap[deviceId]) {
            record.isInMaintenanceMode = ComponentConfigMap[deviceId]?.isInMaintenanceMode ?? "0";
          }
          if (
            record.controls &&
            ComponentConfigMap[deviceId] &&
            ComponentConfigMap[deviceId].controlRelationshipMap &&
            ComponentConfigMap[deviceId].controlRelationshipMap.controls &&
            ComponentConfigMap[deviceId].controlRelationshipMap.controls.length
          ) {
            record.controls.controlRelationshipData = ComponentConfigMap[
              deviceId
            ].controlRelationshipMap.controls.filter((it) => {
              if (record.controls.controlAbbr.indexOf(it.controlAbbr) == -1) return false;
              return it;
            });
          }
        }
        ConfiguredNodeWithControlRel.push(record);
      }
      return ConfiguredNodeWithControlRel;
    }

    async function fetchDeletedDevices(deviceList) {
      const fetchAllActiveDeviceId = (
        await Promise.allSettled(deviceList.map((deviceId) => DeviceService.findOne({ deviceId })))
      )
        .filter((it) => {
          const { status, value } = it;
          return status === "fulfilled" && value;
        })
        .map(({ value }) => value.deviceId);
      //TODO: change the status of deleted ids to zero with DyanmoDB stream
      return deviceList.filter((deviceId) => !fetchAllActiveDeviceId.includes(deviceId));
    }

    async function fetchDeletedComponents(componentList) {
      const fetchActiveComponentList = (
        await Promise.allSettled(
          componentList.map((deviceId) => ComponentService.findOne({ deviceId })),
        )
      )
        .filter((it) => {
          const { status, value } = it;
          return status === "fulfilled" && value;
        })
        .map(({ value }) => value.deviceId);
      //TODO: change the status of deleted ids to zero with DynamoDB stream
      return componentList.filter((deviceId) => !fetchActiveComponentList.includes(deviceId));
    }
  }

  async build() {}

  async save() {}

  async createNewNodeWithTransaction(pageId, newNodeObj, userId, dbTransactionObj) {
    try {
      const newNode = await this.instance.createNewNodeWithTransaction(
        pageId,
        newNodeObj,
        userId,
        dbTransactionObj,
      );
      return newNode;
    } catch (e) {
      if (e.code == "E_UNIQUE") {
        const { deviceId, uiElementType } = newNodeObj;
        return errorHandler.throwExceptionUniqueElementIdViolates(deviceId, uiElementType);
      }
      throw e;
    }
  }

  async updateNodeWithTransaction(pageId, updateNodeObj, userId, dbTransactionObj) {
    return this.instance.updateNodeWithTransaction(pageId, updateNodeObj, userId, dbTransactionObj);
  }

  async deleteNodeWithTransaction(subSystemPageId, taggedDeviceId, userId, dbTransactionObj) {
    await ConfiguratorTaggedDevices.update({
      id: taggedDeviceId,
      subSystemPageId,
      status: 1,
    })
      .set({
        status: 0,
        updatedBy: userId,
      })
      .usingConnection(dbTransactionObj);

    await ConfiguratorTaggedDevicesParam.update({
      configuratorTaggedDevicesId: taggedDeviceId,
      status: 1,
    })
      .set({
        status: 0,
        updatedBy: userId,
      })
      .usingConnection(dbTransactionObj);
  }
}

module.exports = ConfiguratorNode;
