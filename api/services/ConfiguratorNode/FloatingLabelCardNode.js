const ErrorHandler = require("../../utils/configuratorTaggedDevices/ErrorHandler");
const {
  attributes: updatedConfiguratorTaggedDevicesObj,
} = require("../../models/ConfiguratorTables");

/*
 * FloatingComponentDataCardNode is data taggable class
 * to generate a block of component's data parameter
 * */
class FloatingDataParameterCardNode {
  constructor(obj) {
    this._rawObj = obj;
  }

  static ObjectBuilder(raw) {
    const object = {
      id: raw.id,
      elementId: raw.elementId,
      uiElementType: "floatingLabel",
      position: raw.position || {},
      offset: raw.offset,
      label: raw.label,
      style: raw.style || {},
    };
    return object;
  }

  async build() {}

  async createNewNodeWithTransaction(subSystemPageId, node, userId, dbTransactionObj) {
    // eslint-disable-next-line max-len
    const configuratorTaggedNode = JSON.parse(JSON.stringify(node)); //making new copy of node object
    delete configuratorTaggedNode.id;
    // nodeObject.
    configuratorTaggedNode.subSystemPageId = subSystemPageId;
    configuratorTaggedNode.createdBy = userId;
    await ConfiguratorTaggedDevices.create(configuratorTaggedNode)
      .fetch()
      .usingConnection(dbTransactionObj);
  }

  async updateNodeWithTransaction(subSystemPageId, _updateNodeObj, userId, dbTransactionObj) {
    const updateNodeObj = JSON.parse(JSON.stringify(_updateNodeObj));
    const { id: taggedDeviceId } = updateNodeObj;
    const taggingExist = await ConfiguratorTaggedDevices.findOne({
      id: taggedDeviceId,
      subSystemPageId,
      status: 1,
    }).usingConnection(dbTransactionObj);
    if (_.isEmpty(taggingExist)) {
      ErrorHandler.throwExceptionInvalidConfiguratorTaggedDevices(taggedDeviceId);
    }
    const UpdateConfiguratorTaggedDevicesObj = {};
    const allowedUpdateKeys = ["displayFormat", "offset", "style", "label", "position"];
    for (const [key, value] of Object.entries(updateNodeObj)) {
      if (allowedUpdateKeys.includes(key)) {
        UpdateConfiguratorTaggedDevicesObj[key] = value;
      }
    }
    updatedConfiguratorTaggedDevicesObj.updatedBy = userId;
    // noinspection JSUnresolvedReference
    await ConfiguratorTaggedDevices.update({
      id: taggedDeviceId,
      status: 1,
    })
      .set(UpdateConfiguratorTaggedDevicesObj)
      .fetch()
      .usingConnection(dbTransactionObj);
  }
}

module.exports = FloatingDataParameterCardNode;
