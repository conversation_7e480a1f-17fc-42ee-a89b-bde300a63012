/* eslint-disable max-len */
const ErrorHandler = require("../../utils/configuratorTaggedDevices/ErrorHandler");
const {
  attributes: updateConfiguratorTaggedDevicesObj,
} = require("../../models/ConfiguratorTables");

/*
 * FloatingComponentDataCardNode is data taggable class to generate a block of component's data parameter
 * */
class FloatingDataParameterCardNode {
  constructor(obj) {
    this._rawObj = obj;
  }

  static ObjectBuilder(raw) {
    const object = {
      id: raw.id,
      elementId: raw.elementId,
      deviceId: raw.deviceId,
      deviceClass: raw.deviceClass,
      isInMaintenanceMode: raw.isInMaintenanceMode,
      uiElementType: "floatingParameter",
      offset: raw.offset,
      displayFormat: raw.displayFormat,
      data: {
        parameters: (raw.data && raw.data.parameters) || [],
        position: (raw.data && raw.data.position) || {},
        style: (raw.data && raw.data.style) || {},
      },
      style: raw.style,
      position: raw.position,
    };
    return object;
  }

  async build() {}

  async createNewNodeWithTransaction(subSystemPageId, node, userId, dbTransactionObj) {
    const configuratorTaggedNode = JSON.parse(JSON.stringify(node)); //making new copy of node object
    delete configuratorTaggedNode.id;
    // nodeObject.
    configuratorTaggedNode.subSystemPageId = subSystemPageId;
    configuratorTaggedNode.createdBy = userId;
    const { data: dataAbbrCollection } = configuratorTaggedNode;
    // noinspection JSUnresolvedReference
    const newConfiguratorTaggedDevice = await ConfiguratorTaggedDevices.create(
      configuratorTaggedNode,
    )
      .fetch()
      .usingConnection(dbTransactionObj);
    const { id: configuratorTaggedDevicesId } = newConfiguratorTaggedDevice;

    const taggedParams = [];
    if (!dataAbbrCollection) return;
    taggedParams.push(
      ...this._returnDataParamBatchInsertQueries(
        configuratorTaggedDevicesId,
        dataAbbrCollection,
        userId,
        "createdBy",
      ),
    );
    if (!taggedParams.length) return;

    // noinspection JSUnresolvedReference
    await ConfiguratorTaggedDevicesParam.createEach(taggedParams).usingConnection(dbTransactionObj);
  }

  async updateNodeWithTransaction(subSystemPageId, _updateNodeObj, userId, dbTransactionObj) {
    const updateNodeObj = JSON.parse(JSON.stringify(_updateNodeObj));
    const { id: taggedDeviceId } = updateNodeObj;
    // noinspection JSUnresolvedReference
    const taggingExist = await ConfiguratorTaggedDevices.findOne({
      id: taggedDeviceId,
      subSystemPageId,
      status: 1,
    }).usingConnection(dbTransactionObj);
    if (_.isEmpty(taggingExist)) {
      ErrorHandler.throwExceptionInvalidConfiguratorTaggedDevices(taggedDeviceId);
      return;
    }
    const UpdateConfiguratorTaggedDevicesObj = {};
    const allowedUpdateKeys = [
      "position",
      "displayFormat",
      "offset",
      "data",
      "style",
      "deviceClass",
      "deviceId",
    ];
    for (const [key, value] of Object.entries(updateNodeObj)) {
      if (allowedUpdateKeys.includes(key)) {
        UpdateConfiguratorTaggedDevicesObj[key] = value;
      }
    }

    updateConfiguratorTaggedDevicesObj.updatedBy = userId;
    // noinspection JSUnresolvedReference
    await ConfiguratorTaggedDevices.update({
      id: taggedDeviceId,
      status: 1,
    })
      .set(UpdateConfiguratorTaggedDevicesObj)
      .fetch()
      .usingConnection(dbTransactionObj);
    //Disable data params and create new record
    const { data: dataAbbrCollection } = updateNodeObj;
    const taggedParams = [];
    if (dataAbbrCollection && Array.isArray(dataAbbrCollection.parameters)) {
      // noinspection JSUnresolvedReference
      await ConfiguratorTaggedDevicesParam.update({
        configuratorTaggedDevicesId: taggedDeviceId,
        paramType: "data",
        status: 1,
      })
        .set({
          status: 0,
          updatedBy: userId,
        })
        .usingConnection(dbTransactionObj);
      taggedParams.push(
        ...this._returnDataParamBatchInsertQueries(
          taggedDeviceId,
          dataAbbrCollection,
          userId,
          "updatedBy",
        ),
      );
    }
    if (taggedParams.length) {
      await ConfiguratorTaggedDevicesParam.createEach(taggedParams).usingConnection(
        dbTransactionObj,
      );
    }
  }

  _returnDataParamBatchInsertQueries(
    configuratorTaggedDevicesId,
    { parameters, style, position },
    userId,
    operation,
  ) {
    const paramSet = new Set();
    const _taggedParams = [];
    let index = 0;
    for (const paramAbbr of parameters) {
      if (paramSet.has(paramAbbr)) continue;
      _taggedParams.push({
        configuratorTaggedDevicesId,
        paramType: "data",
        paramAbbr,
        position,
        style,
        status: 1,
        order: index,
        [operation]: userId,
      });
      paramSet.add(paramAbbr);
      index++;
    }
    return _taggedParams;
  }
}

module.exports = FloatingDataParameterCardNode;
