/* eslint-disable no-undef */
module.exports = {

  /**
   * Template module private functions
   */
  create: async (params) => {
    return Templates.create(params);
  },
  find: async (searchParams) => {
    // Template.find
    return Templates.find(searchParams);
  },
  findOne: async (searchParams) => {
    // Template.findone
    let templates = await Templates.find(searchParams).limit(1);
    return templates[0];
  },
  update: async (searchParams, updateValue) => {
    return Templates.update(searchParams, updateValue);
  },
  delete: async (searchParams) => {
    return Templates.destroy(searchParams);
  },
};
