const globalHelpers = require('../../utils/globalhelper');
const moment = require('moment');
const Joi = require('joi');
// noinspection JSUnresolvedVariable
module.exports = {
  create: async (params) => {
    const schema = Joi.object().keys({
      siteId:Joi.string().required(),
      productId: Joi.string().required(),
      shiftId: Joi.string().required(),
      production_date: Joi.date().required(),
      production_value: Joi.number().required(),
      production_unit: Joi.string().required(),
      created_by: Joi.string().required(),

    }).unknown()
    const { error } = schema.validate(params);
    if(error) throw new Error(error.message);

    // noinspection JSUnresolvedVariable
    let _productionDataDate = `${moment(params.production_date).format('YYYYMMDD')}#${params.shiftId}#${params.productId}`
    const checkExistingRecord = await ShiftProductionData.findOne({
      pk:params.siteId,
      sk:_productionDataDate
    })
    // if(checkExistingRecord){
    //   return null;
    // }
    const payload = {
      pk:params.siteId,
      sk:_productionDataDate,
      product_id: params.productId,
      shift_id:params.shiftId,
      createdAt: moment().format('YYYY-DD-MMTHH:mm:ssZ'),
      production_value:params.production_value,
      production_unit:params.production_unit,
      createdBy:params.created_by
    }
    // noinspection JSUnresolvedVariable
    const result = await ShiftProductionData.create(payload);
    return {
      ...params,
      production_id:_productionDataDate
    };
  },
  find: async (searchParams) => {
    return await ShiftProductionData.find(searchParams)
  },
  findOne: async (searchParams) => {
    //TODO: implementation pendind
  },
  update: async (searchParams, updateValue) =>{
    //TODO: implementation pendind
  },
  delete: async (params) => {
    await ShiftProductionData.destroy({
      pk:params.siteId,
      sk:params.productionId
    })
  },
};
