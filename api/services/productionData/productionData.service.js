const ProductionService = require('./productionData.private')
const moment = require('moment')
const utils = require('../../utils/productionData/utils');
const ShiftService = require('../shift/shift.public');
const ProductService = require('../product/product.public');
const InfluxService = require('../../services/influx/influx.public')
const flaverr = require('flaverr');

module.exports = {
  create:ProductionService.create,
  find:ProductionService.find,
  delete: ProductionService.delete,
  daysListOfAvailableProductionData:async function(siteId, start_time, end_time ){
    let data =  await this.getProductionData(siteId,start_time,end_time)
    return data.map(it=>{
      let _production_date = it.sk.split("#");
      return moment(_production_date[0]).format('YYYY-MM-DD');
    })
  },
  /**
   *
   * @param siteId
   * @param start_time
   * @param end_time
   * @returns {Promise<*>}
   */
  getProductionData:async function(siteId, start_time, end_time ){
    let data =  await this.find({
      pk:siteId,
      sk:{in:[start_time, end_time]}
    })
    return data.map(it=>{
      let _production_date = it.sk.split("#");
      it.production_date= moment(_production_date[0]).format('YYYY-MM-DD');
      return it;
    })
  },
  addBulkProductionData: async function(siteId, records,createdBy){
    const _shifts =  ShiftService.getShiftBySite(siteId);
    const _products =  ProductService.getProductBySite(siteId)
    const [ Shift, Product ] = await Promise.all([ _shifts, _products ]);
    const shiftSet = Shift.reduce((acm,curr)=>{
      acm.add(curr.sk)
      return acm;
    },new Set())

    const productSet = Product.reduce((acm,curr)=>{
      acm.add(curr.sk)
      return acm;
    },new Set())

    const { error } = utils.validateRecords(records,shiftSet,productSet)
    if(error){
      let err = new Error('');
      err = flaverr({
        name: 'recordValidationFail',
        message: `${JSON.stringify(error)}`,
        code: 'E_RECORD_VALIDATION_FAIL',
        raw: records
      },err)
      throw err;
    }
    const timezoneOffset = Number(await sails.helpers.getSiteTimezone.with({siteId,  timezoneFormat:'utcOffsetInNumber'}))
    let dynamoIngestionHolder = [];
    let influxIngestionHolder = [];
    for (const record of records) {
      dynamoIngestionHolder.push(this.create({
        siteId: siteId,
        productId: record.product_id,
        shiftId: record.shift_id,
        production_date: record.production_date,
        production_value: record.production_value,
        production_unit: record.unit,
        created_by: createdBy,
      }));
      //Ingesting data in influx also for further time series based analysis
      influxIngestionHolder.push(
        InfluxService.write({
          data:{
            tags:[{ key:'product_id', value:record.product_id }, { key: 'shift_id',value: record.shift_id }],
            fields:[{
              type:'float',
              value:record.production_value,
              key:'production_value'
            }],
            timestamp: moment(record.production_date).startOf('days').utcOffset(timezoneOffset).endOf().unix()
          },
          bucket: siteId,
          measurement:'production_data'
        }))
    }
    let returnObject = await Promise.all(dynamoIngestionHolder);
    await Promise.all(influxIngestionHolder);
    return returnObject;
  },


}
