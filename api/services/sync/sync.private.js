/* eslint-disable no-undef */
module.exports = {
  find: async (searchParams) => {
    return Sync.find(searchParams);
  },
  findOne: async (searchParams) => {
    return Sync.findOne(searchParams);
  },
  update: async (searchParams, updateValue) => {
    return Sync.update(searchParams, updateValue);
  },
  create: async (searchParams) => {
    return Sync.create(searchParams);
  },
  destroy: async (searchParams) => {
    return Sync.destroy(searchParams);
  },
};
