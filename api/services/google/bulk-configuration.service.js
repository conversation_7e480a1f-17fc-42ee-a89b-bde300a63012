
const { GoogleSpreadsheet } = require('google-spreadsheet');
const credentials = require("./google-sheet-credentials.json");
const { GoogleSheetsManager } = require("./sheet.service.js");

const headerNameMap = {
    "assetId": "Asset ID",
    "parameterName": "Parameter Name",
    "displayName": "Display Name",
    "address": "Address",
    "componentId": "Component ID",
    "componentName": "Component Name",
    "layerName": "Layer Name",
    "ignoreColumns": "IGNORE COLUMNS AFTER THIS",
    "paramType": "paramType",
};

const layerSheetHeaderNameMap = {
    "layerName": "Layer Name",
    "layerId": "Layer ID",
};

class googleSheet {
    constructor(sheetId){
        this.createNewSheet = false;
        if (!sheetId) {
            // sheetId = "1nJT92Hv1ee6JGDgGSEDMHEGX2OW3D7sigxA37PdHi20";
            this.createNewSheet = true;
        } else this.sheetId = sheetId;
        this.defaultHeaderNameMap = headerNameMap;
        this.layerSheetHeaderNameMap = layerSheetHeaderNameMap;
    }
    async init(deviceId){
        const manager = new GoogleSheetsManager();
        this.googleSheetsManager = manager;
        if(this.createNewSheet){
            // Create a new Google sheet and adding an extra sheet for layer information
            const title = `Parameter Configuration for DeviceId: ${deviceId}`;
            const createdSheet = await manager.createAndShareSheet(title);
            this.sheetId = createdSheet.sheetId;
            const { sheetId: layerSheetId } = await manager.addNewSheet(this.sheetId, "HiddenSheet > Layer name to ID Map", "layerNameToId");
            this.layerSheetId = layerSheetId;
        } else {
            const layerSheetId = await manager.getSheetIdByMetadataTag(this.sheetId, "layerNameToId");
            this.layerSheetId = layerSheetId;
        }
        this.doc = new GoogleSpreadsheet(this.sheetId);
        await this.doc.useServiceAccountAuth({
            client_email: credentials.client_email,
            private_key: credentials.private_key,
        });
    }
    async getAllRows(){
        await this.doc.loadInfo(); // loads document properties and worksheets
        const sheet = this.doc.sheetsByIndex[0];
        const rows = await sheet.getRows();
        const reverseHeaderNameMap = this._returnReverseHeaderNameMap(this.defaultHeaderNameMap);
        const formattedRows =  rows.map(row => {
            let newRowObject = {};
            Object.keys(reverseHeaderNameMap).forEach(headerName =>{
                let newObjectKey = reverseHeaderNameMap[headerName];
                newRowObject[newObjectKey] = row[headerName];
            })
            return newRowObject;
        });
        return formattedRows;
    }
    async addRows(rows){
        await this.doc.loadInfo(); // loads document properties and worksheets
        const sheet = this.doc.sheetsByIndex[0];
        const convertedRowObjects = this._convertRowObjectKeysToHeaderValues(rows, this.defaultHeaderNameMap);
        await sheet.addRows(convertedRowObjects); // Add rows with column based key-value pairs
    }
    async deleteAllRows(){
        await this.doc.loadInfo();
        const sheet = this.doc.sheetsByIndex[0];
        const rows = await sheet.getRows();
        let $deleteQueries = [];
         for( let i in rows){
            const row = rows[i];
            $deleteQueries.push(row.delete());
        }
        await Promise.all($deleteQueries);
    }
    async clearSheet(){
        await this.doc.loadInfo();
        const sheet = this.doc.sheetsByIndex[0];
        await sheet.clear();
    }
    async setHeaderValues(headerValues){
        await this.doc.loadInfo(); // loads document properties and worksheets
        const sheet = this.doc.sheetsByIndex[0];
        await sheet.setHeaderRow(headerValues, 1); // Set Header values
    }
    isNewSheet(){
        return this.createNewSheet;
    }
    generateURL(){
        return `https://docs.google.com/spreadsheets/d/${this.sheetId}`;
    }
    returnParameterSheetHeaders(){
        let headerValues = [];
        for(let key in headerNameMap){
          let headerValue = headerNameMap[key];
          if(headerValue) headerValues.push(headerValue);
        }
        return headerValues;
    }
    _convertRowObjectKeysToHeaderValues(rowObjects, headerMap){
        let convertedRowObjects = rowObjects.map(rowObject => {
            let newRowObject = {};
            for(let key in rowObject){
            let headerValue = headerMap[key];
            if(headerValue) newRowObject[headerValue] = rowObject[key];
            }
            return newRowObject;
        });
        return convertedRowObjects;
    }
    _returnReverseHeaderNameMap(headerMap){
        let reverseHeaderNameMap = {};
        Object.keys(headerMap).forEach(key => {
            let value = headerMap[key];
            reverseHeaderNameMap[value] = key;
        })
        return reverseHeaderNameMap;
    }
    /**\
     * Populates drop down list of Layer Names in the column. 
     * layerInfo.layerNameList : Contains an array of possible elements
     * sortedRows: Number of rows added previously. Used to figure out how many rows to apply the conditional formatting for.
     */
    async populateDropDownListForLayerNames(layerInfo, sortedRows){
        const manager = this.googleSheetsManager;
        const sheetId = this.sheetId;
        const { layerNameList } = layerInfo;

        // Finding range on which the drop down list needs to be applied.
        const numberOfRows = sortedRows.length + 1; // Adding 1 since the length of an array starts from 0 index.

        const columnName = "layerName";
        // Column number: Iterating over header name map. Column number is 1 + the index of the key in the map.
        const columnNumber = Object.keys(this.defaultHeaderNameMap).reduce((columnNumber, columnNameInMap, currentIndex) => {
            if(columnName === columnNameInMap) columnNumber = currentIndex + 1;
            return columnNumber;
        }, null);

        if(columnNumber === null) throw new Error("populateDropDownListForLayerNames >> Can not find 'layerName' in headerNameMap");

        const range = {
            startRow: 2, // Fixed as the data is being populated from 2nd row. 1st row are headers.
            endRow: numberOfRows,
            startCol: columnNumber,
            endCol: columnNumber,
        };

        await manager.populateDropdown(sheetId, "default", layerNameList, range);
    }
    /**
     * Popluates layer sheet with Layer name along with their corresponding Ids.
     * Used by Add Component API later to append component to the relevant layer.
     */
    async populateLayerInfoSheet(layerInfo){
        await this.doc.loadInfo();
        const sheet = this.doc.sheetsById[String(this.layerSheetId)];
        await sheet.clear();
        const headerValues = Object.keys(layerSheetHeaderNameMap).map(key => layerSheetHeaderNameMap[key]);
        await sheet.setHeaderRow(headerValues, 1);
        const { layerNameToIdRows } = layerInfo;
        const convertedRows = this._convertRowObjectKeysToHeaderValues(layerNameToIdRows, this.layerSheetHeaderNameMap);
        await sheet.addRows(convertedRows);
    }
    async fetchLayerInfoFromSheet(){
        await this.doc.loadInfo();
        const sheet = this.doc.sheetsById[String(this.layerSheetId)];
        const rows = await sheet.getRows();
        const reverseHeaderNameMap = this._returnReverseHeaderNameMap(this.layerSheetHeaderNameMap);
        const formattedRows = rows.map(row => {
            let newRowObject = {};
            Object.keys(reverseHeaderNameMap).forEach(headerName =>{
                let newObjectKey = reverseHeaderNameMap[headerName];
                newRowObject[newObjectKey] = row[headerName];
            })
            return newRowObject;
        });
        const layerNameToIdMap = formattedRows.reduce((layerNameToIdMap, row) => {
            const { layerName, layerId } = row;
            layerNameToIdMap[layerName] = layerId;
            return layerNameToIdMap;
        }, {});
        return layerNameToIdMap;
    }
}

module.exports = googleSheet;