const { google } = require('googleapis');
const credentials = require("./google-sheet-credentials.json");

class GoogleSheetsManager {
    constructor() {
        this.credentials = credentials;
        this.SCOPES = ['https://www.googleapis.com/auth/drive', 'https://www.googleapis.com/auth/spreadsheets'];
    }

    async createAuthClient() {
        const auth = new google.auth.JWT(
            this.credentials.client_email,
            null,
            this.credentials.private_key,
            this.SCOPES,
            null
        );
        return auth;
    }

    async createAndShareSheet(title = "New Organization Sheet") {
        const auth = await this.createAuthClient();
        const sheets = google.sheets({version: 'v4', auth});
        const drive = google.drive({version: 'v3', auth});
    
        // Create a new Google Sheet
        const response = await sheets.spreadsheets.create({
            resource: {
                properties: {
                    title
                }
            }
        });
    
        const fileId = response.data.spreadsheetId;
        const defaultSheetId = response.data.sheets[0].properties.sheetId;
    
        // Tag the first sheet as "default" using metadata
        await sheets.spreadsheets.batchUpdate({
            spreadsheetId: fileId,
            requestBody: {
                requests: [{
                    createDeveloperMetadata: {
                        developerMetadata: {
                            metadataKey: 'tag',
                            metadataValue: 'default',
                            location: {
                                sheetId: defaultSheetId
                            },
                            visibility: 'DOCUMENT'
                        }
                    }
                }]
            }
        });
    
        // Share the Google Sheet with all users in the organization
        await drive.permissions.create({
            fileId: fileId,
            requestBody: {
                type: 'domain',
                role: 'writer',
                domain: 'smartjoules.in'
            }
        });
    
        return {
            message: 'Sheet created and shared successfully.',
            url: response.data.spreadsheetUrl,
            sheetId: fileId
        };
    }

    async addNewSheet(spreadsheetId, name, metadataTag) {
        const auth = await this.createAuthClient();
        const sheets = google.sheets({version: 'v4', auth});

        // Add a new sheet
        const response = await sheets.spreadsheets.batchUpdate({
            spreadsheetId: spreadsheetId,
            requestBody: {
                requests: [{
                    addSheet: {
                        properties: {
                            title: name
                        }
                    }
                }]
            }
        });

        const newSheetId = response.data.replies[0].addSheet.properties.sheetId;

        // Tag the new sheet with the provided metadata
        await sheets.spreadsheets.batchUpdate({
            spreadsheetId: spreadsheetId,
            requestBody: {
                requests: [{
                    createDeveloperMetadata: {
                        developerMetadata: {
                            metadataKey: 'tag',
                            metadataValue: metadataTag,
                            location: {
                                sheetId: newSheetId
                            },
                            visibility: 'DOCUMENT'
                        }
                    }
                }]
            }
        });

        return {
            message: 'New sheet added successfully.',
            sheetId: newSheetId
        };
    }

    async getSheetIdByMetadataTag(spreadsheetId, metadataTag) {
        const auth = await this.createAuthClient();
        const sheets = google.sheets({version: 'v4', auth});
    
        // Search for the metadata by its key and value
        const metadataResponse = await sheets.spreadsheets.developerMetadata.search({
            spreadsheetId: spreadsheetId,
            requestBody: {
                dataFilters: [{
                    developerMetadataLookup: {
                        metadataKey: 'tag',
                        metadataValue: metadataTag
                    }
                }]
            }
        });
    
        if (!metadataResponse.data.matchedDeveloperMetadata || metadataResponse.data.matchedDeveloperMetadata.length === 0) {
            throw new Error(`No metadata found with tag: ${metadataTag}`);
        }
    
        return metadataResponse.data.matchedDeveloperMetadata[0].developerMetadata.location.sheetId;
    }

    async populateDropdown(spreadsheetId, metadataTag, elements, range) {
        const sheetId = await this.getSheetIdByMetadataTag(spreadsheetId, metadataTag);
        const auth = await this.createAuthClient();
        const sheets = google.sheets({version: 'v4', auth});

        // Clear existing data validation in the entire column
        const clearValidationRequest = {
            updateCells: {
                range: {
                    sheetId: sheetId,
                    startColumnIndex: range.startCol - 1,
                    endColumnIndex: range.endCol
                },
                fields: 'dataValidation'
            }
        };
    
    
        // Set dropdown data validation for the specified range
        const setValidationRequest = {
            setDataValidation: {
                range: {
                    sheetId: sheetId,
                    startRowIndex: range.startRow - 1,
                    endRowIndex: range.endRow,
                    startColumnIndex: range.startCol - 1,
                    endColumnIndex: range.endCol
                },
                rule: {
                    condition: {
                        type: "ONE_OF_LIST",
                        values: elements.map(value => ({userEnteredValue: value}))
                    },
                    inputMessage: "Choose from the dropdown list.",
                    showCustomUi: true
                }
            }
        };

        // Update the sheet with both requests
        await sheets.spreadsheets.batchUpdate({
            spreadsheetId: spreadsheetId,
            requestBody: {
                requests: [clearValidationRequest, setValidationRequest]
            }
        });
    
        return {
            message: 'Dropdown populated successfully.'
        };
    }
    
}

module.exports = {
    GoogleSheetsManager,
}
