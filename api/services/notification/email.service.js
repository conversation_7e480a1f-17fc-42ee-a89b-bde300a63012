const aws = require('aws-sdk');
const nodemailer = require('nodemailer');
const hbs = require('nodemailer-express-handlebars');
const utils = require('../../utils/notification/utils');

const globalHelpers = require('../../utils/globalhelper');

const TRANSPORTER_NODEMAILER = {};

for (const TEMPLATE_PATH of Object.values(utils.TEMPLATES)) {
  const mailerTransport = nodemailer.createTransport({
    SES: new aws.SES({ apiVersion: '2010-12-01', region: sails.config.REGION }),
  });

  mailerTransport.use(
    'compile',
    hbs({
      viewEngine: {
        extName: '.hbs',
        defaultLayout: TEMPLATE_PATH,
        layoutsDir: 'views/emails/',
        partialsDir: 'views/partials/',
      },
      viewPath: 'views/emails',
      extName: '.hbs',
    }),
  );

  TRANSPORTER_NODEMAILER[TEMPLATE_PATH] = mailerTransport;
}

module.exports = {
  async sendEmail({
    notify, title, description, extra, template,
    siteId, timestamp,
  }) {
    const attachments = [
      {
        filename: 'logo_sm.png',
        path: 'assets/logo_sm.png',
        cid: 'logo_sm',
      },
      {
        filename: 'jr.png',
        path: 'assets/jr.png',
        cid: 'jr',
      },
    ];
    const mailOptions = {
      subject: `Alert: ${title}`,
      from: '"SmartJoules" <<EMAIL>>',
      to: notify,
      context: {
        timestamp: globalHelpers.formatDateTime(timestamp),
        description,
        extra,
        siteId,
        title,
      },
      template,
      attachments,
    };

    try {
      await TRANSPORTER_NODEMAILER[template].sendMail(mailOptions);
      sails.log('Sending email : ', title, ' to ', notify);
      return true;
    } catch (e) {
      sails.log.error('emailservice::sendEmail ', e);
      throw (e);
    }
  },

};
