const notifyService = require('./notify.service');
module.exports = {
  /**
   * @description Notify slack when perform delete operation
   * @param {Object} actionTakerUser - action taking user information
   * @params {Array} userList - list of users to whom action is performed
   * @return {Boolean}
   */
  notifySlackOnDelete: async function (operatedUserList, requesterInfo) {
    const {name,  email} = requesterInfo;
    if(!operatedUserList.length) return;
    const template = notifyService.getTemplateForNotify('deleteUser');
    let _body = templateBuilder(operatedUserList,template)
    let _bodySubject = `${name}<${email}> has performed deleteUser on below users \n`.concat(_body);
    sails.log(_bodySubject)
    await notifyService.notifySlack(_bodySubject)
    return true;
  },
  _notifySlackUpdateRole: async function(operatedUserList,requesterInfo) {
    const {name,  email} = requesterInfo;
    if(!operatedUserList.length) return;
    const template = notifyService.getTemplateForNotify('userRoleUpdated');
    let _body = templateBuilder(operatedUserList,template)
    let _bodySubject = `${name}<${email}> has performed updateUserRole on below users \n`.concat(_body);
    sails.log(_bodySubject)
    await notifyService.notifySlack(_bodySubject)
    return true;
  },
  _notifySlackRemoveSiteAccess: async function(operatedUserList,requesterInfo) {
    const {name,  email} = requesterInfo;
    if(!operatedUserList.length) return;
    const template = notifyService.getTemplateForNotify('siteAccessRemoved');
    let _body = templateBuilder(operatedUserList,template)
    let _bodySubject = `${name}<${email}> has performed updateSiteAccess on below users \n`.concat(_body);
    sails.log(_bodySubject)
    await notifyService.notifySlack(_bodySubject)
    return true;
  },
  _notifySlackAssignSiteAccess: async function(operatedUserList,requesterInfo) {
    const {name,  email} = requesterInfo;
    if(!operatedUserList.length) return;
    const template = notifyService.getTemplateForNotify('newSiteAssigned');
    let _body = templateBuilder(operatedUserList,template)
    let _bodySubject = `${name}<${email}> has performed createSiteAccess on below users \n`.concat(_body);
    sails.log(_bodySubject)
    await notifyService.notifySlack(_bodySubject)
    return true;
  },
  _notifySlackCreateUser: async function(createdUserDetails,requesterInfo) {
    const {name, email} = requesterInfo;
    const template = notifyService.getTemplateForNotify('createUser');
    let _body = templateBuilder([createdUserDetails],template);
    let _bodySubject = `${name}<${email}> has created below users: \n`.concat(_body);
    sails.log(_bodySubject);
    await notifyService.notifySlack(_bodySubject);
    return true;
  }
};

/**
 * @description Get the formatted string
 * @param {String} format
 * @param {Object} replacements
 * @returns {String} formatted string
 */
const templateBuilder = function (placeHolderList,template) {
  return placeHolderList.map((obj,index)=>{
    let _template = template;
    Object.keys(obj).forEach(key=>{
      let regEx = `{{${key}}}`;
      let re = new RegExp(regEx, 'g');
      _template = _template.replace(re, obj[key])
    })
    return `${index+1}.${_template}`
  }).join('\n');
}
