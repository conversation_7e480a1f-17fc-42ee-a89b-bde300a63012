const axios = require('axios');
const SLACK_WEB_HOOK_URL =  process.env.SLACK_WEB_HOOK_URL || '*******************************************************************************';
module.exports = {
  /**
   * @description To notify the slack channel when operation performed on user [delete, update] 
   * @param {Object} bodyObject 
   * @returns {Boolean}
   */
  notifySlack: function (bodyObject) {
     const requestObject = {
      url: SLACK_WEB_HOOK_URL,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json', 
      },
      data: JSON.stringify({
        text:bodyObject
      })
     }
     return axios.post(requestObject.url, requestObject.data, {
      headers: requestObject.headers
     })
  }
}