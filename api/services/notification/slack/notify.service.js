const notifyPrivate = require('./notify.private');
module.exports = {
  notifySlack: notifyPrivate.notifySlack,
  /**
   * @description Get the template for the notification
   * @param {String} operation
   * @param {String} template
   */
  getTemplateForNotify: function (operation) {
    let template = '';
    // const { userId, siteId, newRole, oldRole }
    switch(operation) {
      case 'createUser':{
        template = `{{name}}<{{email}}><{{defaultSite}}><{{defaultRole}}>`
        break;
      }
      case 'deleteUser': {
        template = `{{name}}<{{email}}>`;
        break;
      }
      case 'userRoleUpdated': {
        template = `{{userId}}<{{oldRole}}> to {{newRole}} for {{siteId}}`
        break;
      }
      case 'siteAccessRemoved': {
        template = `{{userId}}<{{email}}> been removed from {{siteId}}`;
        break
      }
      // Create site access
      case "newSiteAssigned": {
        template = `{{userId}}<{{email}}> has been assigned new site access for {{siteId}} role <{{newRole}}>`;
        break;
      }
    }
    return template
  },
}
