const axios = require('axios');
const qs = require('qs');
const globalHelpers = require('../../utils/globalhelper');
const utils = require('../../utils/notification/utils');

module.exports = {

  async sendSMS({
    sms, title, description, extra, template,
    siteId, timestamp, type,
  }) {
    const to = sms
      .filter((mobilenumber) => mobilenumber.startsWith('+91') === true)
      .join(',');
    const templateId = utils.SMS_TEMPLATES_IDS[type];

    const body = utils.makeSMSBody(
      siteId,
      title,
      description,
      type,
      template,
      globalHelpers.formatDateTime(timestamp),
      extra,
    );

    const data = qs.stringify({
      to,
      body,
      type: sails.config.SMS_KALEYRA_TYPE,
      sender: sails.config.KALEYRA_SMS_SENDER,
      template_id: templateId,
    });

    const url = `${sails.config.SMS_KALEYRA_URL}/v1/${sails.config.SMS_KALEYRA_SID}/messages`;

    const requestObject = {
      method: 'POST',
      url,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'api-key': sails.config.SMS_KALEYRA_KEY,
      },
      data,
    };

    try {
      await axios(requestObject);
      return true;
    } catch (e) {
      sails.log.error('sendsms:smsservice Unable to send sms', e);
      throw (e);
    }
  },
};
