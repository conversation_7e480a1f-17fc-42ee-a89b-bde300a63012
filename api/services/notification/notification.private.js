const globalhelper = require('../../utils/globalhelper');

/* eslint-disable no-undef */
module.exports = {
  /**
   * Notification module private functions
   */
  create: async (params) => {
    return Notifications.create(params);
  },
  find: async (searchParams) => {
    // Notification.find
    return Notifications.find(searchParams);
  },
  findOne: async (searchParams) => {
    // Notification.findone
    let notifications = await Notifications.find(searchParams).limit(1);
    return notifications[0];
  },
  update: async (searchParams, updateValue) => {
    return Notifications.update(searchParams, updateValue);
  },
  delete: async (searchParams) => {
    return Notifications.destroy(searchParams);
  },
  bouncedEmails: {
    find: async (searchParams) => {
      // Notification.find
      return BouncedEmails.find(searchParams);
    },
  }
};
