const AWS = require("aws-sdk");
const moment = require("moment");

const cacheService = require("../cache/cache.public");
const emailService = require("./email.service");
const socketService = require("../socket/socket.public");
const globalHelpers = require("../../utils/globalhelper");
const notificationService = require("./notification.private");
const smsService = require("./sms.service");
const userService = require("../user/user.public");
const rabbitMQService = require("../rabbitmq/rabbitmq.public");

const utils = require("../../utils/notification/utils");
const { READ_STATUS } = require("../../utils/notification/utils");

const documentClient = new AWS.DynamoDB.DocumentClient({
  region: sails.config.REGION,
});
const RECIPE_ALERT_QUEUE = new rabbitMQService.consumerWithAckQueue(
  "RECIPE_NOTIF"
);
const NUDGE_ALERT_QUEUE = new rabbitMQService.consumerWithAckQueue(
  "NUDGE_NOTIF"
);
const SMS_QUEUE = new rabbitMQService.consumerWithAckQueue("SMS");
const EMAIL_QUEUE = new rabbitMQService.consumerWithAckQueue("EMAIL");
const NOTIFICATION_QUEUE = new rabbitMQService.consumerWithAckQueue(
  "NOTIFICATION"
);

const NOTIFICATION = new rabbitMQService.consumerWithAckQueue(
  "NOTIFICATION_QUEUE"
);

exports = {
  create: notificationService.create,
  find: notificationService.find,
  findOne: notificationService.findOne,
  update: notificationService.update,
  delete: notificationService.delete,

  RECIPE_ALERT_QUEUE,
  NUDGE_ALERT_QUEUE,
  SMS_QUEUE,
  EMAIL_QUEUE,
  NOTIFICATION_QUEUE,
  NOTIFICATION,

  /**
   *
   * @param {object} message Nudge alert
   * throwing error from this function will push message back to queue for retry
   */
  async handleNudgeAlert(message) {
    try {
      const payload = globalHelpers.toJson(message.content.toString());
      const notification = utils.isValidNudgePayloadFromQueue(payload);

      if (notification === false) {
        sails.log.error(
          "handleNudgeAlert::notification.service found an invalid nudge alert :",
          message.content.toString()
        );
        return { problems: ["Invalid nudge object"] };
      }

      // try {
      // await notificationService.create({
      //   ...notification,
      //   timestamp: `${notification.timestamp}_${notification.alertId}`,
      //   ts: notification.timestamp,
      // });
      // } catch (e) {
      //   throw (e);
      // }
      EMAIL_QUEUE.sendMessage(notification);
      SMS_QUEUE.sendMessage(notification);
      NOTIFICATION_QUEUE.sendMessage(notification);

      return true;
    } catch (e) {
      sails.log.error("handleNudgeAlert:notificationservice: ", e);
      return false;
    }
  },

  async handleRecipeAlert(message) {
    try {
      const payload = globalHelpers.toJson(message.content.toString());
      const notification = utils.isValidRecipePayloadFromQueue(payload);

      if (notification === false) {
        sails.log.error(
          "handleRecipeAlert::notification.service found an invalid recipe alert :",
          message.content.toString()
        );
        return { problems: ["Invalid recipe object"] };
      }

      const { alertId, priority } = notification;
      const ALERT_ID = `cacheAlert_${alertId}`;
      const recipeExists = await cacheService.get(ALERT_ID);

      if (recipeExists === undefined) {
        // save in database await notification.create(payload)
        try {
          await notificationService.create({
            ...notification,
            timestamp: `${notification.timestamp}_${notification.alertId}`,
            ts: notification.timestamp,
          });
        } catch (e) {
          sails.log.error("notification::handleRecipeAlert ", e);
          throw e;
        }
        EMAIL_QUEUE.sendMessage(notification);
        SMS_QUEUE.sendMessage(notification);
        NOTIFICATION_QUEUE.sendMessage(notification);

        const expiry = utils.getExpiryFromPriority(priority);
        await cacheService.set(ALERT_ID, notification.timestamp);
        await cacheService.expire(ALERT_ID, expiry);

        sails.log("Added an alert to database & queue...");
      } else {
        sails.log("Cache stopped the alert");
      }

      return;
    } catch (e) {
      sails.log.error("handleRecipeAlert:notificationservice: ", e);
    }
  },

  /**
   * schema of message = message.content = <
   *    notify=[],
   *    title = "",
   *    description = "",
   *    siteId = "" ,
   *    timestamp = unix,
   *    template = enum(util.TEMPLATES) // make new template and easy integrate here
   *    extra[optional] = {}
   * >
   * @param {*} message
   */
  async handleEmail(message) {
    const payload = globalHelpers.toJson(message.content.toString());
    let usersEmailPreference;

    if (utils.isValidEmailFromQueue(payload) === false) {
      sails.log.error(
        "handleEmail:notification.service found an invalid email :",
        payload
      );
      return true; // Non tryable. So just send back ack to delete from queue
    }
    let { notify, siteId, type } = payload; // type can be NUDGE/RECIPE
    const userpreferncekey = utils.ALERTTYPE_USERPREFERENCE[type];

    try {
      usersEmailPreference =
        (await userService.getUsersEmailPreference(notify, siteId)) || {};
    } catch (e) {
      sails.log.error("eventservice::handleEmail ");
      sails.log.error(e);
      throw e;
    }
    notify = Object.keys(usersEmailPreference).filter(
      (userId) => usersEmailPreference[userId][userpreferncekey] === true
    );

    let $validUserIds = notify.map(async userId => {
      let isBounced = await isBouncedEmail(userId);
      if(isBounced) return null;
      else return userId;
    });
    let validUserIds;
    try {
      validUserIds = await Promise.all($validUserIds);
    } catch (error) {
      sails.log.error("eventservice::handleEmail Error finding bounced emails.", error);
    }
    notify = validUserIds.filter(Boolean);

    if (notify.length === 0) {
      sails.log(
        "[+] No user found to alert for: ",
        payload.siteId,
        " - ",
        payload.title
      );
      return true;
    }
    payload.notify = notify;

    try {
      await emailService.sendEmail(payload);
    } catch (e) {
      sails.log.error("notification::handleEmail ", e);
      throw e;
    }

    return true;
  },

  async handleSMS(message) {
    const payload = globalHelpers.toJson(message.content.toString());
    let userIdMobileMap = {};

    if (utils.isValidSMSFromQueue(payload) === false) {
      sails.log.error(
        "handleSMS:notification.service found an invalid email :",
        payload
      );
      return true; // Non tryable. So just send back ack to delete from queue
    }

    const { sms } = payload; // list of users to sms

    try {
      userIdMobileMap = await userService.getMobileNumbersOfUsers(sms);
    } catch (e) {
      sails.log.error(
        "handleSMS:notification.service.. Error retrieving mobileNumber :",
        e,
        userIdMobileMap
      );
      return;
    }

    // filterWithSMSSettingOFF(userIdMobileMap) : #TODO# userIdMobileMap is {userId: mobile}.. Remove all the
    // userEntries from map who has turned there SMS user setting off

    payload.sms = utils.filterInvalidMobileNumbers(
      Object.values(userIdMobileMap)
    ); // remove bad format mobile number and add +91 where required
    if (payload.sms.length === 0) {
      sails.log(
        "[+] No user found to sms for: ",
        payload.siteId,
        " - ",
        payload.title
      );
      return true;
    }
    try {
      await smsService.sendSMS(payload);
      sails.log("[+] sending message ", payload.sms);
    } catch (e) {
      throw e;
    }
    return true;
  },

  async handleDejouleNotification(message) {
    const payload = globalHelpers.toJson(message.content.toString());

    if (utils.isValidEmailFromQueue(payload) === false) {
      sails.log.error(
        "handleDejouleNotification:notification.service found an invalid notification :",
        payload
      );
      return true; // Non tryable. So just send back ack to delete from queue
    }

    const { notify, siteId, timestamp, title, type, sourceId, alertId } =
      payload;
    let priority = payload.priority || "unknown";

    if (utils.DIGIT_PRIORITY_MAP[priority]) {
      // if priority is in digits(backward compatibility) (0 mean low priorty, 3 means critical).
      // SO if its in digit, convert it to string
      priority = utils.DIGIT_PRIORITY_MAP[priority];
    }
    payload.priority = priority;

    const $createNotif = notify.map((userId) => {
      const id = `${siteId}_${userId}`;

      return notificationService.create({
        id,
        timestamp: `${timestamp}_${alertId}`,
        title,
        sourceId,
        siteId,
        type,
        alertId,
        readstatus: READ_STATUS.UNREAD,
        ts: timestamp,
        priority,
      });
    });

    try {
      await Promise.allSettled($createNotif);
      sails.log("[+] User Notifiction added to table successfully");
    } catch (e) {
      throw e;
    }

    try {
      await socketService.sendSocketMessageToUserIds(
        notify,
        siteId,
        socketService.SOCKET_FRONTEND_TOPICS.NOTIFICATION,
        payload
      );
    } catch (e) {
      sails.log.error(
        "handleDejouleNotification:notification cannot send alert on socket ",
        e
      );
    }
    return true;
  },

  async queryTest(options) {
    documentClient.query(
      {
        TableName: "notifications",
        KeyConditionExpression: "#id = :hkey and #ts = :rkey",
        ExpressionAttributeNames: {
          "#id": "id",
          "#ts": "timestamp",
          "#noti": "notify",
        },
        ExpressionAttributeValues: {
          ":hkey": "xyz",
          ":rkey": "2020-11-10 12:00",
          ":val3": "<EMAIL>",
        },
        FilterExpression: "siteId = :hkey and contains(#noti, :val3)",
      },
      (err, data) => {
        console.log(err);
        console.log(data);
      }
    );
  },

  async addAlertToRecipeQueue(recipeNotification) {
    try {
      RECIPE_ALERT_QUEUE.sendMessage(recipeNotification);
    } catch (e) {
      throw e;
    }
  },

  async addAlertToNudgeQueue(nudgeNotification) {
    try {
      NUDGE_ALERT_QUEUE.sendMessage(nudgeNotification);
    } catch (e) {
      throw e;
    }
  },

  /* on message received from the  RECIPE_QUEUE/NOTIFICATION_QUEUE */
  async handleTopicFromRabbitMQ(data) {
    try {
      let { topic, message } = globalHelpers.toJson(data.content.toString());
      message = globalHelpers.toJson(message);
      if (message === undefined) {
        sails.log.error(
          "Notification.service::Rabbitmq message cannot be undefined: ",
          topic,
          message
        );
        return false;
      }
      await eventHandler(topic, globalHelpers.toJson(message));
      return true;
    } catch (e) {
      console.log(e);
      throw e;
    }
  },
};

async function eventHandler(topic, message) {
  try {
    if (/.*\/alerts\/\d+\/recipe$/.test(topic)) {
      // Alert by Joulerecipe from controller
      const recipeNotification = utils.formatJRecipeAlert(message);
      if (recipeNotification === undefined) return;
      await exports.addAlertToRecipeQueue(recipeNotification);
    } else if (/.*\/alerts\/\d+\/joulerecipe$/.test(topic)) {
      // Alert by Joulerecipe from server
      const recipeNotification = utils.formatJRecipeAlert(message);
      if (recipeNotification === undefined) return;
      await exports.addAlertToRecipeQueue(recipeNotification);
    } else {
      sails.log(`Topic not configured ${topic}`);
    }

    return true;
  } catch (e) {
    sails.log.error("Notification.service::eventHandler ", e);
    return false;
  }
}

/**
   * 
   * @param {string} emailId 
   * @returns {boolean} Returns true if the email bounced 3 times or more in the past 24 hours.
   */
 async function isBouncedEmail(emailId){
  try {
    let currentTime = moment();
    let currentTimestamp = currentTime.format("YYYY-MM-DDTHH:mm:SS");
    let startTimestamp = currentTime.subtract(1,"days").format("YYYY-MM-DDTHH:mm:SS");
    let bouncedEmailList = await notificationService.bouncedEmails.find({
      emailId,
      timestamp:{ 
        in: [startTimestamp, currentTimestamp]
      }
    });
    // console.log("bouncedEmailList length",bouncedEmailList.length);
    if (bouncedEmailList.length > 2) return true;
      else return false;
  } catch (error) {
    sails.log.error("isBouncedEmail::notificationService.service Error confirming if Email Bounced.");
    sails.log.error(error);
  }
}

module.exports = exports;
