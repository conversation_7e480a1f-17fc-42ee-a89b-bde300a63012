const globalhelpers = require('../../utils/globalhelper');

/* eslint-disable no-undef */
module.exports = {

  /**
   * DeviceType Service Private functions
   */
  find: async (searchParams) => { // DeviceType.find
    return DeviceTypes.find(searchParams);
  },
  findOne: async (searchParams) => { // DeviceType.findOne
    let deviceType;
    try {
      deviceType = await DeviceTypes.findOne(searchParams);
    } catch (e) {
      sails.log.error('[-] devicetypeservice.findOne ', e);
      throw new Error(e);
    }
    if (!deviceType) {
      return;
    } else {
      delete deviceType.params;
      if(!deviceType.parameters){
        deviceType.parameters = [];
      }
      deviceType.parameters = deviceType.parameters.map(globalhelpers.toJson).filter(Boolean);
      return deviceType;
    }
  },
  update: async (searchParams, updateValue) => { // DeviceType.update
    return DeviceTypes.update(searchParams, updateValue);
  },
  create: async (searchParams) => {
    return DeviceTypes.create(searchParams);
  }
};
