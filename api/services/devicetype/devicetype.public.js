const devicetypeservice = require("./devicetype.service");

module.exports = {
  find: devicetypeservice.find,
  findOne: devicetypeservice.findOne,
  create: devicetypeservice.create,
  getDeviceTypes: devicetypeservice.getDeviceTypes,
  getDriverDataCommandParams: async (deviceType, driverType) => {
    let driverDetails = await devicetypeservice.findOne({
      deviceType,
      driverType,
      class: "components",
    });

    if (_.isEmpty(driverDetails)) {
      return null;
    }

    if (_.isEmpty(driverDetails.parameters)) {
      return {
        dataParams: [],
        commandParams: [],
      };
    }

    const dataParams = new Set(
      driverDetails.parameters.filter((it) => it.type === "data").map((it) => it.abbr)
    );
    const commandParams = new Set(
      driverDetails.parameters.filter((it) => it.type === "command").map((it) => it.abbr)
    );

    return {
      dataParams: Array.from(dataParams),
      commandParams: Array.from(commandParams),
    };
  },
  getAllDeviceTypes: devicetypeservice.getAllDeviceTypes,
  getComponentDriverConfig: devicetypeservice.getComponentDriverConfig,
  getDeviceDriverConfig: devicetypeservice.getDeviceDriverConfig,
  getDriverParameters: async (deviceType, deviceClass) => {
    let driverParametersData = null;
    if (deviceClass == "device") {
      driverParametersData = await getDeviceDriverParameters(deviceType);
    } else if (deviceClass == "component") {
      driverParametersData = await getComponentDriverParameters(deviceType);
    }
    return driverParametersData;
  },
};

const getComponentDriverParameters = async (deviceType) => {
  const driverConfig = await devicetypeservice.getComponentDriverConfig(deviceType);
  const driverRelationshipData = await DriverControlsRelationshipConfig.find({
    deviceType,
    status: 1,
  });
  const uniqueAbbrDriverData = driverRelationshipData.reduce((acc, data) => {
    const { controlAbbr } = data;
    if (acc.hasOwnProperty(controlAbbr)) {
      return acc;
    }
    acc[controlAbbr] = data;
    return acc;
  }, {});
  const controlWiseRelationship = Object.values(uniqueAbbrDriverData).map((control) => {
    delete control.createdAt;
    delete control.updatedAt;
    delete control.createdBy;
    return control;
  });
  return {
    data: driverConfig.data,
    controlRelationshipData: controlWiseRelationship,
  };
};

const getDeviceDriverParameters = async (deviceType) => {
  const driverConfig = await devicetypeservice.getDeviceDriverConfig(deviceType);
  return {
    data: driverConfig.data,
  };
};
