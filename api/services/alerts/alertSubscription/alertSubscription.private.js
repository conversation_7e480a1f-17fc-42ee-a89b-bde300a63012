/**
 * alertSubscription.private.js
 * @description :: Private service with basic CRUD operations for alert subscriptions
 */

module.exports = {
  /**
   * Find all subscriptions by criteria
   * @param {Object} criteria - Query criteria
   * @returns {Promise<Array>} List of subscriptions
   */
  find: async function(criteria) {
    try {
      return await AlertSubscribers.find(criteria);
    } catch (err) {
      sails.log.error('[alertSubscription.private] Error in find:', err);
      throw err;
    }
  },

  /**
   * Find a subscription by criteria
   * @param {Object} criteria - Query criteria
   * @returns {Promise<Object>} Alert subscription
   */
  findOne: async function(criteria) {
    try {
      return await AlertSubscribers.findOne(criteria);
    } catch (err) {
      sails.log.error('[alertSubscription.private] Error in findOne:', err);
      throw err;
    }
  },

  /**
   * Create a new subscription
   * @param {Object} data - Subscription data
   * @returns {Promise<Object>} Created subscription
   */
  create: async function(data) {
    try {
      return await AlertSubscribers.create(data).fetch();
    } catch (err) {
      sails.log.error('[alertSubscription.private] Error in create:', err);
      throw err;
    }
  },

  /**
   * Update a single subscription
   * @param {Object} criteria - Query criteria
   * @param {Object} data - Update data
   * @returns {Promise<Object>} Updated subscription
   */
  updateOne: async function(criteria, data) {
    try {
      return await AlertSubscribers.updateOne(criteria).set(data);
    } catch (err) {
      sails.log.error('[alertSubscription.private] Error in updateOne:', err);
      throw err;
    }
  }
};
