/**
 * alertSubscription.service.js
 * @description :: Service layer with business logic for alert subscriptions
 */

const moment = require('moment-timezone');
const flaverr = require('flaverr');
const alertSubscriptionService = require('./alertSubscription.private');

module.exports = {
  /**
   * Subscribe a user to an alert
   * @param {Object} params - Subscription parameters
   * @param {string} params.subscriberId - Subscriber ID
   * @param {number} params.alertId - Alert ID
   * @param {string} params.siteId - Site ID
   * @param {Array} params.channels - Notification channels ['email', 'sms', 'whatsapp']
   * @returns {Promise<Object>} Subscription details
   */
  subscribe: async function ({ subscriberId, alertId, siteId, channels }) {
    if (!subscriberId || !alertId || !siteId) {
      throw flaverr('E_BAD_REQUEST', new Error('Missing required parameters'));
    }

    const alert = await AlertInventory.findOne({
      id: alertId,
      siteId
    });

    if (!alert) {
      throw flaverr('E_NOT_FOUND', new Error(`Alert with id ${alertId} not found for site ${siteId}`));
    }

    const alertTemplate = await AlertTemplate.findOne({
      id: alert.alert_template_ref_id
    });

    if (!alertTemplate) {
      throw flaverr('E_NOT_FOUND', new Error(`Alert template not found for alert ${alertId}`));
    }

    const defaultChannels = alertTemplate.alert_category === 'cpa'
      ? ['whatsapp']
      : ['email'];

    const effectiveChannels = channels?.length > 0 ? channels : defaultChannels;

    const existingSubscription = await alertSubscriptionService.findOne({
      alert_id: alertId,
      subscriber_id: subscriberId
    });

    const subscriptionData = {
      notify_on_email: effectiveChannels.includes('email'),
      notify_on_sms: effectiveChannels.includes('sms'),
      notify_on_whatsapp: effectiveChannels.includes('whatsapp'),
      status: 1,
      subscribed_at: moment.tz('UTC').toDate()
    };

    if (existingSubscription) {
      if (existingSubscription.status === 1) {
        const subscriptionChanged =
          existingSubscription.notify_on_email !== subscriptionData.notify_on_email ||
          existingSubscription.notify_on_sms !== subscriptionData.notify_on_sms ||
          existingSubscription.notify_on_whatsapp !== subscriptionData.notify_on_whatsapp;

        if (!subscriptionChanged) {
          return existingSubscription;
        }
      }

      return await alertSubscriptionService.updateOne(
        { id: existingSubscription.id },
        {
          ...subscriptionData,
          unsubscribed_at: null
        }
      );
    }

    return await alertSubscriptionService.create({
      alert_id: alertId,
      subscriber_id: subscriberId,
      ...subscriptionData
    });
  },

  /**
   * Unsubscribe a user from an alert
   * @param {string} subscriberId - Subscriber ID
   * @param {number} alertId - Alert ID
   * @param {string} siteId - Site ID
   * @returns {Promise<Object>} Updated subscription details
   */
  unsubscribe: async function ({ subscriberId, alertId, siteId }) {
    if (!subscriberId || !alertId || !siteId) {
      throw flaverr('E_BAD_REQUEST', new Error('Missing required parameters'));
    }

    const alert = await AlertInventory.findOne({
      id: alertId,
      siteId
    });

    if (!alert) {
      throw flaverr('E_NOT_FOUND', new Error(`Alert with id ${alertId} not found for site ${siteId}`));
    }

    const existingSubscription = await alertSubscriptionService.findOne({
      alert_id: alertId,
      subscriber_id: subscriberId,
      status: 1
    });

    if (!existingSubscription) {
      throw flaverr('E_NOT_FOUND', new Error(`No active subscription found for alert ${alertId} and subscriber ${subscriberId}`));
    }

    return await alertSubscriptionService.updateOne(
      { id: existingSubscription.id },
      {
        status: 0,
        unsubscribed_at: moment.tz('UTC').toDate()
      }
    );
  },

  /**
   * List subscribers for an alert
   * @param {Object} params - Query parameters
   * @param {number} params.alertId - Alert ID
   * @param {string} params.siteId - Site ID
   * @returns {Promise<Array>} List of subscribers
   */
  listSubscribers: async function({ alertId, siteId }) {
    if (!alertId || !siteId) {
      throw flaverr('E_BAD_REQUEST', new Error('Missing required parameters'));
    }

    const alert = await AlertInventory.findOne({
      id: alertId,
      siteId
    });

    if (!alert) {
      throw flaverr('E_NOT_FOUND', new Error(`Alert with id ${alertId} not found for site ${siteId}`));
    }

    const activeSubscriptions = await alertSubscriptionService.find({
      alert_id: alertId,
      status: 1
    });

    if (!activeSubscriptions?.length) {
      return [];
    }

    return activeSubscriptions.map(subscription => ({
      alertId,
      siteId,
      subscriberId: subscription.subscriber_id,
      notificationChannels: {
        email: subscription.notify_on_email,
        sms: subscription.notify_on_sms,
        whatsapp: subscription.notify_on_whatsapp
      },
      subscribedAt: subscription.subscribed_at
    }));
  },

  /**
   * Update pause status for a subscription
   * @param {Object} params - Pause parameters
   * @param {string} params.subscriberId - Subscriber ID
   * @param {number} params.alertId - Alert ID
   * @param {string} params.siteId - Site ID
   * @param {Date|null} params.paused_till - Timestamp until which notifications should be paused
   * @returns {Promise<Object>} Updated subscription details
   */
  updatePause: async function ({ subscriberId, alertId, siteId, paused_till }) {
    if (!subscriberId || !alertId || !siteId) {
      throw flaverr('E_BAD_REQUEST', new Error('Missing required parameters'));
    }

    const existingSubscription = await alertSubscriptionService.findOne({
      alert_id: alertId,
      subscriber_id: subscriberId,
      status: 1
    });

    if (!existingSubscription) {
      throw flaverr('E_NOT_FOUND', new Error(`No active subscription found for alert ${alertId} and subscriber ${subscriberId}`));
    }

    return await alertSubscriptionService.updateOne(
      { id: existingSubscription.id },
      {
        paused_till: paused_till ? moment.tz(paused_till, 'UTC').toDate() : null,
        subscribed_at: moment.tz('UTC').toDate()
      }
    );
  },

  /**
   * Get subscribers by alert ID
   * @param {number} alertInventoryId - Alert inventory ID
   * @returns {Promise<Array>} List of subscribers
   */
  getSubscribersByAlertId: async function(alertInventoryId) {
    const alert = await AlertInventory.findOne({ id: alertInventoryId });

    if (!alert) {
      throw flaverr({
        code: 'E_NOT_FOUND',
        message: `Alert with id ${alertInventoryId} not found`
      });
    }

    const alertTemplate = await AlertTemplate.findOne({
      id: alert.alert_template_ref_id,
      status: 1
    });

    if (!alertTemplate) {
      throw flaverr({
        code: 'E_NOT_FOUND',
        message: `Active alert template not found for alert ${alertInventoryId}`
      });
    }

    const subscribers = await alertSubscriptionService.find({
      alert_id: alertInventoryId,
      status: 1
    });

    return subscribers.map(subscription => ({
      subscriberId: subscription.subscriber_id,
      siteId: alert.siteid,
      notificationChannels: {
        email: subscription.notify_on_email,
        sms: subscription.notify_on_sms,
        whatsapp: subscription.notify_on_whatsapp
      },
      subscribedAt: moment(subscription.subscribed_at).tz('UTC').toISOString(),
      pausedTill: subscription.paused_till ?
        moment(subscription.paused_till).tz('UTC').toISOString() : null
    }));
  }
};
