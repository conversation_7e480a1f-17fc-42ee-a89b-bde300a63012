module.exports = {
  async getAlertCount({ siteId, timeRange, userId }) {
    try {
      let query = `
        SELECT
          COUNT(DISTINCT CASE
                  WHEN sub.subscriber_id = $2
                  THEN aih.alert_inventory_id
                END) AS alert_count_for_subscriber,
          COUNT(DISTINCT aih.alert_inventory_id) AS total_alert_count
        FROM alert_incident_history aih
        JOIN alert_inventory ai ON ai.id = aih.alert_inventory_id
        JOIN alert_template at ON at.id = ai.alert_template_ref_id
        LEFT JOIN alert_subscribers sub ON sub.alert_id = ai.id AND sub.status = 1
        WHERE aih.issue_resolved_at IS NULL
          AND ai.siteid = $1
          AND ai.status = 1
          AND at.status = 1
      `;

      const queryParams = [siteId, userId];

      if (timeRange) {
        query += ` AND COALESCE(aih.recent_occurred_event_ts, aih.issue_occurred_at) AT TIME ZONE 'UTC' >= $3::timestamptz AT TIME ZONE 'UTC'
                   AND COALESCE(aih.recent_occurred_event_ts, aih.issue_occurred_at) AT TIME ZONE 'UTC' <= $4::timestamptz AT TIME ZONE 'UTC'`;
        queryParams.push(timeRange.start, timeRange.end);
      }

      const result = await sails.getDatastore(process.env.SMART_ALERT_DB_NAME)
        .sendNativeQuery(query, queryParams);

      return result?.rows?.[0] || {};

    } catch (error) {
      throw new Error(`Failed to get alert count: ${error.message}`);
    }
  }
};
