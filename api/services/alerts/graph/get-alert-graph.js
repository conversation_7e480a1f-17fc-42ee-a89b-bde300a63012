const { validateTimeWindow, fillTimeFrameWindow } = require("../../../utils/alerts/graph.util");
const influxService = require("../../influx/influx.public")
const moment = require('moment');

module.exports = {
  getAlertSquareWaveGraph: async function({siteId, alertId, startTime, endTime}) {
    const MAX_DAYS = 7;  // Maximum allowed days per query
    const timeZoneOffset = await sails.helpers.getSiteTimezone.with({siteId, timezoneFormat:'utcOffsetInMinute'})
    validateTimeWindow(startTime, endTime, timeZoneOffset);
    const start = moment(startTime).utcOffset(timeZoneOffset);
    const end = moment(endTime).utcOffset(timeZoneOffset);
    const durationDays = end.diff(start, "days");

    if (durationDays <= MAX_DAYS) {
        return await fetchRecords({siteId, alertId, startTime, endTime});
    }

    // If more than 7 days, split into parallel calls
    let promises = [];
    let tempStart = start.clone();

    while (tempStart.isBefore(end)) {
        let tempEnd = tempStart.clone().add(MAX_DAYS, "days");
        if (tempEnd.isAfter(end)) {
            tempEnd = end.clone();
        }

        let partitionStartTime = moment(tempStart).format();
        let partitionEndTime = moment(tempEnd).format();
        promises.push(
          fetchRecords({siteId, alertId, startTime:partitionStartTime, endTime:partitionEndTime})
        );
        tempStart = tempEnd.clone();
    }

    const results = await Promise.all(promises);

    return _.flatten(results);

    async function fetchRecords({siteId, alertId, startTime, endTime}) {
      const [
        lastRecord,
        currentTimeFrameRecord  
      ] = await Promise.all([
        lastThirtyDays({siteId, alertId, startTime}),
        fetchGraphWithReqTimeFrame({siteId, alertId, startTime, endTime})
      ]);
      if (lastRecord ==null && (!currentTimeFrameRecord || !currentTimeFrameRecord.length)) return [];
      if (lastRecord && (!currentTimeFrameRecord || !currentTimeFrameRecord.length)) return fillTimeFrameWindow({startTime, endTime, record:[], value: lastRecord})
      if (lastRecord == null && currentTimeFrameRecord.length) return fillTimeFrameWindow({startTime, endTime, record: currentTimeFrameRecord, value: null})
      return fillTimeFrameWindow({startTime, endTime, record: currentTimeFrameRecord, value: lastRecord})

      async function lastThirtyDays({siteId, alertId, startTime}) {
        try {
          const query = `
          from(bucket: "{{bucket}}")
          |> range(start:{{startTime}}, stop: {{endTime}})
          |> filter(fn: (r) => 
            r["_measurement"] == "{{measurement}}" and
            r["_field"] == "result" and
            r["siteid"] == "{{siteId}}" and
            r["alert_inventory_id"] == "{{alertId}}"
          )
          |> drop(columns: ["alert_incident_id"]) 
          |> last()
          `;
          const rawData = await influxService.runQuery(query, {
            replacements: {
              siteId,
              alertId,
              measurement: 'smartalerteval_new',
              bucket: 'smart_alert_eval',
              startTime: moment(startTime).subtract(30, 'days').utcOffset(timeZoneOffset).format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
              endTime: moment(startTime).utcOffset(timeZoneOffset).format('YYYY-MM-DDTHH:mm:ss.SSSZ')
            },
            debug: true,
          },"iot_influxdb");
          return rawData?.[0] ? rawData[0]._value : null
        } catch(e) {
          return null
        }
      }

      async function fetchGraphWithReqTimeFrame({siteId, alertId, startTime, endTime}) {
        try {
        const query = `
        from(bucket: "{{bucket}}")
        |> range(start: {{startTime}}, stop: {{endTime}})
        |> filter(fn: (r) => 
          r["_measurement"] == "{{measurement}}" and
          r["_field"] == "result" and
          r["siteid"] == "{{siteId}}" and
          r["alert_inventory_id"] == "{{alertId}}"
        )
        |> drop(columns: ["alert_incident_id"]) 
        |> aggregateWindow(every: 1m, fn: last, createEmpty: true)
        |> fill(column: "_value", usePrevious: true)  
        `;
        const rawData = await influxService.runQuery(query, {
          replacements: {
            siteId,
            alertId,
            measurement: 'smartalerteval_new',
            bucket: 'smart_alert_eval',
            startTime: moment(startTime).utcOffset(timeZoneOffset).format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
            endTime: moment(endTime).utcOffset(timeZoneOffset).format('YYYY-MM-DDTHH:mm:ss.SSSZ')
          },
          debug: true,
        },"iot_influxdb");
        return rawData.map((rd)=> [moment(rd._time).unix()*1000, rd._value]);
      } catch(e) {
        return null
      }
  
      }

    }
  }
}