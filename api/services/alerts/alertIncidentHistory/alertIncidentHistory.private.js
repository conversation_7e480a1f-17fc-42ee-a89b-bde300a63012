/**
 * alertIncidentHistory.private.js
 * @description :: Private service with basic CRUD operations for alert incident history
 */

module.exports = {
  /**
   * Find an alert incident by criteria
   * @param {Object} criteria - Query criteria
   * @returns {Promise<Object>} Alert incident
   */
  findOne: async function(criteria) {
    try {
      return await AlertIncidentHistory.findOne(criteria);
    } catch (err) {
      sails.log.error('[alertIncidentHistory.private] Error in findOne:', err);
      throw err;
    }
  },

  /**
   * Update a single alert incident
   * @param {Object} criteria - Query criteria
   * @param {Object} data - Data to update
   * @returns {Promise<Object>} Updated alert incident
   */
  updateOne: async function(criteria, data) {
    try {
      return await AlertIncidentHistory.updateOne(criteria).set(data);
    } catch (err) {
      sails.log.error('[alertIncidentHistory.private] Error in updateOne:', err);
      throw err;
    }
  }
};
