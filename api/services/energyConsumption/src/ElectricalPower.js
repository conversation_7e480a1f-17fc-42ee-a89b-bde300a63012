const utils = require("../../../utils/energyConsumption/electrical-power.util");
const influxService = require("../../influx/enterprise/influx.public");

class ElectricalPower {
  constructor() {
    throw new Error('Electrical-Power constructor cannot be instantiated')
  }

  static generateParameterQuery({ abbr, startTime, endTime, deviceId, siteId, deviceClass }) {
    const table = deviceClass === 'device' ? 'device' : 'components';
    const columnTagName = deviceClass === 'device' ? 'deviceId' : 'componentId';
    const parameterMapping = {
      ebkwh: 'corrected_ebkwh',
      kwh: 'corrected_kwh'
    };
    const correctedAbbr = abbr.map(param => parameterMapping[param] || param);
    const parameter = correctedAbbr.length > 1 ? correctedAbbr : correctedAbbr[0]
    if (Array.isArray(parameter)) {
      const parameterQueries = parameter.map(param => `last("${param}") - first("${param}")`).join(' + ');
      return `
        SELECT ${parameterQueries} AS "value"
        FROM "device_component"."autogen"."${table}"
        WHERE time > '${startTime}'
          AND time < '${endTime}'
          AND "${columnTagName}" = '${deviceId}'
          AND "siteId" = '${siteId}'
      `;
    } else {
      return `
        SELECT last("${parameter}") - first("${parameter}") AS "value"
        FROM "device_component"."autogen"."${table}"
        WHERE time > '${startTime}'
          AND time < '${endTime}'
          AND "${columnTagName}" = '${deviceId}'
          AND "siteId" = '${siteId}'
      `;
    }
  };


  /**
   * @param {string} deviceId
   * @param {Array} abbr
   * @param {String} deviceClass
   * @param {String} startTime
   * @param {String} endTime
   * @returns {Number|null}
   */
  static async getEnergyConsumptionByDeviceId({
    deviceId,
    deviceClass,
    abbr,
    startTime,
    endTime,
    siteId
  }){
    utils.validateConsumptionPayload({
      deviceId,
      deviceClass,
      abbr,
      startTime,
      endTime,
      siteId
    })

    const query = ElectricalPower.generateParameterQuery({siteId, abbr,deviceId, deviceClass, startTime, endTime})
    sails.log(query)
    const result = await influxService.runInfluxQLQuery(query)
    return result.length === 1 ? Number(Number(result[0].value).toFixed(2)) : null;
  }

  /**
   * @param {Array} deviceIds
   * @param {Array} abbr
   * @param {String} deviceClass
   * @param {String} startTime
   * @param {String} endTime
   * @returns {Number|null}
   */
  static async getEnergyConsumptionByDeviceIds({
    deviceIds,
    deviceClass,
    abbr,
    startTime,
    endTime,
    siteId
  }) {
    utils.validateConsumptionByDeviceIds({
      deviceIds,
      deviceClass,
      abbr,
      startTime,
      endTime,
      siteId
    })
    const $getAllDeviceConsumption = deviceIds.map((deviceId) => this.fetchConsumption({
      deviceId,
      deviceClass,
      abbr,
      startTime,
      endTime
    }));
    const allDeviceConsumption = await Promise.all($getAllDeviceConsumption);
    return deviceIds.reduce((acc, deviceId, index) => {
      acc[deviceId] = allDeviceConsumption[index];
      return acc;
    }, {})

  }


 static async getEnergyConsumptionBatch(nodes) {
    utils.validateConsumptionBatch(nodes)
    const $getAllDeviceConsumption = nodes.map((node) => {
      const {
        deviceId,
        deviceClass,
        abbr,
        startTime,
        endTime,
        siteId
      } = node

      return this.getEnergyConsumptionByDeviceId({deviceId, deviceClass, abbr,  startTime, endTime, siteId});
  });
    let allDeviceConsumption = await Promise.all($getAllDeviceConsumption);
    const energyConsumption = {};
    for(let i = 0; i < nodes.length; i++) {
      if(!allDeviceConsumption[i]) continue;
      const node = nodes[i]
      const {deviceId} = node
      energyConsumption[deviceId] = allDeviceConsumption[i];
    }
    return energyConsumption;
  }


  static async fetchConsumption(node) {
    let result;
    if (Array.isArray(node)) {
      result = await ElectricalPower.getEnergyConsumptionBatch(node);
    } else if (typeof node === 'object') {
      if (node.deviceId) {
        result = await ElectricalPower.getEnergyConsumptionByDeviceId(node);
      } else if (node.deviceIds) {
        result = await ElectricalPower.getEnergyConsumptionByDeviceIds(node); // Assuming you meant getEnergyConsumptionByDeviceIds
      }
    }
    return result;
   }


}

module.exports = ElectricalPower
