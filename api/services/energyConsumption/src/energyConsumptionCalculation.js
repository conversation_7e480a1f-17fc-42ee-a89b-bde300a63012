const moment = require('moment-timezone');
const MOMENT_FORMAT = 'YYYY-MM-DDTHH:mm:00Z';
moment.tz.setDefault("Asia/Kolkata");
const Joi = require("joi");
const globalhelper = require('../../../utils/globalhelper');
const influx = require('../../influx/enterprise/influx.service')
const INFLUX_BUCKET = "device_component/autogen";
// const INFLUX_BUCKET = "test_ishan/autogen";
class EnergyConsumptionCalculation {

    constructor(config) {
        try {
            const configSchema = Joi.object().keys({
                energyMeterId: Joi.string().required(),
                startTime: Joi.string().required(),
                endTime: Joi.string().required(),
                consumptionParameter: Joi.string().required(),
            });
            const { error } = configSchema.validate(config);
            if(error) throw new Error("Invalid keys in constructor");

            const { energyMeterId, startTime, endTime, consumptionParameter } = config;

            this.energyMeterId = energyMeterId;
            this.consumptionParameter = consumptionParameter;
            if(moment(startTime).isValid()) this.startTime = moment(startTime).format(MOMENT_FORMAT);
            if(moment(endTime).isValid()) this.endTime = moment(endTime).format(MOMENT_FORMAT);
        } catch (error) {
            sails.log.error("[energyConsumptionCalculation] >> constructor: Error initialising values ");
            throw error;
        }
    }

  async fetchEnergyConsumption() {
    const buildQuery = (consParam, startTime, endTime, energyMeterId) => {
        const query = `
            SELECT
            sum("total_valid_consumption") AS "consumption"
            FROM (SELECT MEAN("filtered_difference") AS "total_valid_consumption" FROM 
            (SELECT DIFFERENCE("${consParam}") AS "filtered_difference" 
            FROM "device_component"."autogen"."device" WHERE 
            time >= '${startTime}'  AND time <= '${endTime}' 
            AND "deviceId"='${energyMeterId}')
            WHERE "filtered_difference" <= 1000 AND "filtered_difference" >= -1000 GROUP BY time(1m))
        `;
        return query
    }


    try {
      let data = await influx.runInfluxQLQuery(
        buildQuery(this.consumptionParameter, this.startTime, this.endTime, this.energyMeterId)
      );
    
      const consumptionData = data[0] || {};
      const consumption = consumptionData.consumption || null;
    //   const firstReading = consumptionData.first_value || null;
    //   const lastReading = consumptionData.last_value || null;

    //   const isInitialReadingInterpolated = !firstReading;
    //   const isLastReadingInterpolated = !lastReading;
    //   const isLastReadingFilled = lastReading !== null;
    //   const isApproximateConsumption = consumption !== null;
    //   const noData = !consumption;

      return {
        // isApproximateConsumption: isApproximateConsumption,
        consumption: consumption,
        // isInitialReadingInterpolated: isInitialReadingInterpolated,
        // initialReading: firstReading,
        // isLastReadingInterpolated: isLastReadingInterpolated,
        // isLastReadingFilled: isLastReadingFilled,
        // lastReading: lastReading,
        // noData: noData,
        energyMeterId: this.energyMeterId,
        consumptionParameter: this.consumptionParameter
      };
    } catch (error) {
      console.error("Error executing InfluxDB query:", error);
      throw error;
    }
  }

    async getConsumption() {
        try {
            const baseCalculationResult = await this._fetchFirstLastConsumptionReading();

            let { isInitialReadingInterpolated,
                initialReading,
                nearestStartTimeWithinRange,
                isLastReadingInterpolated,
                lastReading,
                nearestEndTimeWithinRange,
                noData,
                isApproximateConsumption } = this._initialiseFlagsAndValues(baseCalculationResult);
                let isLastReadingFilled = false;

            if(!noData) {
                let $initialReading
                // TODO: If noData, then query kwh instead of corrected_kwh
                if(isInitialReadingInterpolated)
                    $initialReading = this._estimateInitalConsumptionByInterpolation(nearestStartTimeWithinRange)
                if(isLastReadingInterpolated){
                    lastReading = await this._estimateLastConsumptionByInterpolation(nearestEndTimeWithinRange)
                    // In case unable to interpolate due to lack of data, taking the last known value for consumption.
                    if(!lastReading){
                        lastReading = baseCalculationResult.lastReadingObj.reading;
                        isLastReadingFilled = true;
                    }
                }
                if(isInitialReadingInterpolated) initialReading = await $initialReading;
            }
            const consumption = this._calculateConsumption(initialReading, lastReading);
            return {
                isApproximateConsumption: isApproximateConsumption,
                consumption,
                isInitialReadingInterpolated,
                initialReading,
                isLastReadingInterpolated,
                isLastReadingFilled,
                lastReading,
                noData,
                energyMeterId: this.energyMeterId,
                consumptionParameter: this.consumptionParameter
            }
        } catch (error) {
            sails.log.error("[energyConsumptionCalculation] >> getConsumption: Error! ");
            sails.log.error(error);
            throw {
                message: error,
                deviceId: this.energyMeterId,
                consumptionParameter: this.consumptionParameter
            };
        }
    }

    async _fetchFirstLastConsumptionReading() {
        let result={
            initalReadingObj: {
                timestamp: undefined,
                reading: undefined
            },
            lastReadingObj: {
                timestamp: undefined,
                reading: undefined
            }
        };
        // TODO remove filter functions used to mock missing data.
        // Filter functions for deviceId: 296, time: |> range(start: 2022-03-13T17:00:00+05:30, stop: 2022-03-13T18:00:01+05:30)
        // |> filter(fn: (r) => r._value > 284415000.20) // used to find no values
        // |> filter(fn: (r) => r._value > 2844071.00)  // used to mock firstReading at 5:15PM.
        //   |> filter(fn: (r) => r._value < 2844127.00) // used to mock lastReading at 5:45PM.

        const query = `
            firstReadingTable=from(bucket: "${INFLUX_BUCKET}")
                |> range(start: {{startTime}}, stop: {{endTime}})
                |> filter(fn: (r) => r["_measurement"] == "device")
                |> filter(fn: (r) => r["deviceId"] == "{{deviceId}}")
                |> filter(fn: (r) => r["_field"] == "{{parameter}}")
                |> filter(fn: (r) => r._value != 0)
                //|> filter(fn: (r) => r._value > 2844071.00)
                |> first()

            lastReadingtable=from(bucket: "${INFLUX_BUCKET}")
                |> range(start: {{startTime}}, stop: {{endTime}})
                |> filter(fn: (r) => r["_measurement"] == "device")
                |> filter(fn: (r) => r["deviceId"] == "{{deviceId}}")
                |> filter(fn: (r) => r["_field"] == "{{parameter}}")
                |> filter(fn: (r) => r._value != 0)
                //|> filter(fn: (r) => r._value < 2844127.00)
                |> last()

            union(tables: [lastReadingtable, firstReadingTable])
                |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
                |> rename(columns: {{{parameter}}: "reading"})
                |> sort(columns: ["_time"], desc: false)
                |> yield(name: "emConsumptionBaseCase")
            `;
        const data = await influx.runQuery(query, {
            replacements: {
                startTime: this.startTime,
                endTime: this._endTimeInclusion(this.endTime),
                parameter: this.consumptionParameter,
                deviceId: this.energyMeterId,
            },
            debug: true
        })

        if(data.length > 0){
            result.initalReadingObj = {
                timestamp: data[0]["_time"],
                reading: data[0]["reading"]
            };
        }
        if (data.length == 1){
            // If only a single data point found, putting the same value as both objects.
            result.lastReadingObj = {
                timestamp: data[0]["_time"],
                reading: data[0]["reading"]
            };
        } else if (data.length == 2){
            result.lastReadingObj = {
                timestamp: data[1]["_time"],
                reading: data[1]["reading"]
            };
        }
        return result;
    }
    /**
     *
     * @param {*} baseCalculationResult Nearest consumption reading data found from Influx.
     * @description Sets object flags based on data found in database.
     */
    _initialiseFlagsAndValues(baseCalculationResult){
        const { initalReadingObj, lastReadingObj } = baseCalculationResult;
        let isInitialReadingInterpolated, initialReading, nearestStartTimeWithinRange, isLastReadingInterpolated = false, lastReading,
        nearestEndTimeWithinRange, noData = false, isApproximateConsumption = true;

        if(moment(initalReadingObj.timestamp).isSame(this.startTime)){
            isInitialReadingInterpolated = false;
            initialReading = initalReadingObj.reading;
        } else {
            isInitialReadingInterpolated = true;
            nearestStartTimeWithinRange = moment(initalReadingObj.timestamp).format(MOMENT_FORMAT);
        }
        const lastReadingObjectTimePlusOneMinute = moment(lastReadingObj.timestamp).add(1,"minute");
        if(moment(lastReadingObj.timestamp).isSame(this.endTime) || lastReadingObjectTimePlusOneMinute.isSame(this.endTime)){
            isLastReadingInterpolated = false;
            lastReading = lastReadingObj.reading;
        } else {
            isLastReadingInterpolated = true;
            nearestEndTimeWithinRange = moment(lastReadingObj.timestamp).format(MOMENT_FORMAT);
        }
        if(!isInitialReadingInterpolated && !isLastReadingInterpolated) isApproximateConsumption = false;
        if(!initalReadingObj.timestamp && !lastReadingObj.timestamp)
            noData = true;
        return {
            isInitialReadingInterpolated,
            initialReading,
            nearestStartTimeWithinRange,
            isLastReadingInterpolated,
            lastReading,
            nearestEndTimeWithinRange,
            noData,
            isApproximateConsumption
        }
    }

    /**
     *
     * @returns Calculates consumption if initialReading as well as lastReading is found. Else returns NA
     */
    _calculateConsumption(initialReading, lastReading){
        let consumption;
        if(initialReading && lastReading)
            consumption = lastReading - initialReading;
        else
            consumption = null;
        // TODO: Confirm if main service should be responsible for this.
        // const filteredData = globalhelper.returnFilteredNumber(consumption); // Rounding off decimals.
        return consumption;
    }

    async _estimateInitalConsumptionByInterpolation(nearestStartTimeWithinRange){
        const startTimeMinus2Days = moment(this.startTime).subtract(2,"d").format(MOMENT_FORMAT);

        const query = `
            import "interpolate"

            ST=from(bucket: "${INFLUX_BUCKET}")
                |> range(start: {{startTimeMinus2Days}}, stop: {{startTimeWithEndTimeInclusion}})
                |> filter(fn: (r) => r["_measurement"] == "device")
                |> filter(fn: (r) => r["deviceId"] == "{{deviceId}}")
                |> filter(fn: (r) => r["_field"] == "{{parameter}}")
                |> filter(fn: (r) => r._value != 0)
                |> drop(columns: ["_start", "_stop"])
                |> last()

            ET=from(bucket: "${INFLUX_BUCKET}")
                |> range(start: {{startTime}}, stop: {{nearestStartTimeWithinRange}})
                |> filter(fn: (r) => r["_measurement"] == "device")
                |> filter(fn: (r) => r["deviceId"] == "{{deviceId}}")
                |> filter(fn: (r) => r["_field"] == "{{parameter}}")
                |> filter(fn: (r) => r._value != 0)
                |> drop(columns: ["_start", "_stop"])
                |> first()

            union(tables: [ET, ST])
                |> sort(columns: ["_time"], desc: false)
                |> interpolate.linear(every: 1m)
                |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
                |> rename(columns: {{{parameter}}: "reading"})
                |> filter(fn: (r) => r._time == {{startTime}})
            |> yield(name: "interpolatedValue")`;
        const data = await influx.runQuery(query, {
            replacements: {
                deviceId: this.energyMeterId,
                parameter: this.consumptionParameter,
                startTime: this.startTime,
                startTimeWithEndTimeInclusion: this._endTimeInclusion(this.startTime),
                nearestStartTimeWithinRange: this._endTimeInclusion(nearestStartTimeWithinRange),
                startTimeMinus2Days
            },
            debug: true
        });
        const interpolatedInitalReading = data[0] ? data[0]["reading"] : null;
        return interpolatedInitalReading;
    }

    async _estimateLastConsumptionByInterpolation(nearestEndTimeWithinRange){
        const endTimePlus2Days = moment(this.endTime).add(2,"d").format(MOMENT_FORMAT);

        /**
         * Filter functions to mock missing data
         * |> filter(fn: (r) => r._value < 2844127.50)   // returns value at 5:45PM for ST
         * |> filter(fn: (r) => r._value > 2844179.00)   // return value at 6:15PM for ET
         */
        const query = `
            import "interpolate"

            ST=from(bucket: "${INFLUX_BUCKET}")
                |> range(start: {{nearestEndTimeWithinRange}}, stop: {{endTimePlus1Second}})
                |> filter(fn: (r) => r["_measurement"] == "device")
                |> filter(fn: (r) => r["deviceId"] == "{{deviceId}}")
                |> filter(fn: (r) => r["_field"] == "{{parameter}}")
                |> filter(fn: (r) => r._value != 0)
                //|> filter(fn: (r) => r._value < 2844127.50)
                |> drop(columns: ["_start", "_stop"])
                |> last()

            ET=from(bucket: "${INFLUX_BUCKET}")
                |> range(start: {{endTime}}, stop: {{endTimePlus2Days}})
                |> filter(fn: (r) => r["_measurement"] == "device")
                |> filter(fn: (r) => r["deviceId"] == "{{deviceId}}")
                |> filter(fn: (r) => r["_field"] == "{{parameter}}")
                |> filter(fn: (r) => r._value != 0)
                //|> filter(fn: (r) => r._value > 2844179.00)
                |> drop(columns: ["_start", "_stop"])
                |> first()

            union(tables: [ET, ST])
                |> sort(columns: ["_time"], desc: false)
                |> interpolate.linear(every: 1m)
                |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
                |> rename(columns: {{{parameter}}: "reading"})
                |> filter(fn: (r) => r._time == {{endTime}})
                |> yield(name: "interpolatedValue")
        `;
        const data = await influx.runQuery(query, {
            replacements: {
                deviceId: this.energyMeterId,
                parameter: this.consumptionParameter,
                nearestEndTimeWithinRange: nearestEndTimeWithinRange,
                endTimePlus1Second: this._endTimeInclusion(this.endTime),
                endTimePlus2Days: this._endTimeInclusion(endTimePlus2Days),
                endTime: this.endTime
            },
            debug: true
        });
        // Sending null incase unable to interpolate if no data exists after last known timestamp
        const interpolatedLastReading = data[0] ? data[0]["reading"] : null;
        return interpolatedLastReading;
    }

    _endTimeInclusion(timestamp){
        //add one sec to timestamp and return in same format
        const incrementedTimeStamp = moment(timestamp).add(1,"s").format("YYYY-MM-DDTHH:mm:ssZ");
        return incrementedTimeStamp;
    }

}

module.exports = EnergyConsumptionCalculation;
