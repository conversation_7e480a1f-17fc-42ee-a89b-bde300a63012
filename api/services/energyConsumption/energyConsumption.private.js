const EnergyConsumptionCalculation = require("./src/energyConsumptionCalculation");
const deviceService = require("../device/device.public");
const deviceTypeService = require("../devicetype/devicetype.public");

const priorityListOfConsumptionParameters = [
    "kwh",
    "ebkwh",
    "kvah"
];

module.exports = {
    fetchEnergyConsumptionForEM: async function(deviceId, startTime, endTime){
        try {
            // Fetch device configuration and check if it exists and is an em
            const deviceConfiguration = await deviceService.findOne({ deviceId });
            if (!deviceConfiguration) throw new Error(`Device Configuration not found for ${deviceId}`);
            const { deviceType, driverType } = deviceConfiguration;
            if (deviceType !== "em") throw new Error(`DeviceType(${deviceType}) for deviceId: ${deviceId} is not "em"`);

            // Fetch driver configuration and figure out which parameter to use
            const driverConfiguration = await deviceTypeService.findOne({ deviceType, driverType});
            if(!driverConfiguration) throw new Error(`Driver Configuration not found for deviceId: ${deviceId}`);
            const { parameters } = driverConfiguration;
            const consumptionParameter = findConsumptionParameter(parameters);

            // Calculate consumption
            const consumptionCalculator = new EnergyConsumptionCalculation({
                energyMeterId: deviceId,
                startTime,
                endTime,
                consumptionParameter
            });
            return await consumptionCalculator.fetchEnergyConsumption();

            // Helper functions
            function findConsumptionParameter(parameters){
                let consumptionParameter = null, parameterFound = false;
                for(let i in priorityListOfConsumptionParameters){
                    const attemptedParameter = priorityListOfConsumptionParameters[i];
                    for(let i in parameters){
                        let parameter = parameters[i];
                        const parameterAbbr = parameter["abbr"]
                        if(parameterAbbr == attemptedParameter){
                            consumptionParameter = `corrected_${parameterAbbr}`;
                            parameterFound = true;
                            break;
                        }
                    }
                    if(parameterFound) break;
                }
                if(!parameterFound)
                    throw new Error(`No valid parameter found in driver configuration to calculate consumption for deviceId: ${deviceId}`);
                return consumptionParameter;
            }
        } catch (error) {
            sails.log.error("[energyConsumption] fetchEnergyConsumption >> Error!");
            throw error;
        }
    }
}
