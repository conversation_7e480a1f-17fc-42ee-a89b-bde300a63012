
const moment = require("moment");
const MOMENT_FORMAT = 'YYYY-MM-DDTHH:mm:00';

const globalHelper = require("../../utils/globalhelper");
const energyConsumptionService = require("./energyConsumption.private");

const fetchConsumptionForMultipleMeters =  async function(emList, startTime, endTime, consumptionUnitPreference){
    try {
        const $consumptionPromises = emList.map(deviceId => energyConsumptionService.fetchEnergyConsumptionForEM(deviceId, startTime, endTime));
        const consumptionPromises = await Promise.allSettled($consumptionPromises);
        const totalConsumptionResult = consumptionPromises.reduce((totalConsumptionResult, promiseResult) => {
            if(promiseResult.status == "fulfilled"){
                const consumptionServiceResult = promiseResult.value;
                const { energyMeterId, consumption, consumptionParameter } = consumptionServiceResult;
                const consumptionInUnitPreference = globalHelper.convertConsumptionToPreferredUnit(consumption, consumptionParameter, consumptionUnitPreference);
                totalConsumptionResult.totalConsumption += consumptionInUnitPreference;
                totalConsumptionResult.debug[energyMeterId] = consumptionServiceResult;
            } else totalConsumptionResult.errors.push(promiseResult.value);
            return totalConsumptionResult;
        }, {
            totalConsumption: 0,
            debug: {},
            errors: []
        });
        return totalConsumptionResult;
    } catch (error) {
        sails.log.error("[fetchEMConsumptionForLastHour] >> Error!");
        sails.log.error(error);
        return {
            totalConsumption: 0,
            debug: {},
            errors: [error]
        };
    }
}


module.exports = {
    fetchEnergyConsumptionForEM: energyConsumptionService.fetchEnergyConsumptionForEM,
    fetchConsumptionForMultipleMeters,
    fetchDailyConsumptionForMultipleMeters: async function(emList, startTime, endTime, consumptionUnitPreference){
        try {
            const endTimeObject = moment(endTime), dailyConsumption = [], debugArray = [], $formattedDailyConsumptionArray = [];
            for(let tempTime = moment(startTime); tempTime.diff(endTimeObject, "day") < 0; tempTime.add(1,"day")){
                const startTime = moment(tempTime).format(MOMENT_FORMAT);
                const endTime = moment(tempTime).add(1,"day").format(MOMENT_FORMAT);
                $formattedDailyConsumptionArray.push(formatDailyConsumption(startTime, endTime));
            }
            const formattedDailyConsumptionArray = await Promise.all($formattedDailyConsumptionArray);
            formattedDailyConsumptionArray.forEach(formattedDailyConsumption => {
                dailyConsumption.push(formattedDailyConsumption.dailyConsumption);
                debugArray.push(formattedDailyConsumption.debug);
            });

            async function formatDailyConsumption(startTime, endTime){
                const consumptionServiceResult = await fetchConsumptionForMultipleMeters(emList, startTime, endTime, consumptionUnitPreference);
                const unixTime = moment(startTime).valueOf();
                const { totalConsumption, debug} = consumptionServiceResult;
                const roundedConsumption = globalHelper.returnFilteredNumber(totalConsumption);
                debug["startTime"] = startTime;
                debug["endTime"] = endTime;
                return { dailyConsumption: [unixTime, roundedConsumption], debug };
            }
            return { dailyConsumption, debugArray };
        } catch (error) {
            sails.log.error("[fetchDailyConsumptionForMultipleMeters] >> Error!");
            sails.log.error(error);
            return { dailyConsumption: [], debugArray: [], errors: error };
        }
    }
}
