const siteService = require("../site/site.service");
const utils = require('../../utils/CPAAssetConfigSync/index');
const maintenanceModeService = require("../maintenanceMode/maintenanceMode.service");
const s3Service = require('../s3/s3.service');
const axios = require("axios");
const yaml = require('js-yaml')

const loadCPAAssetDataSource = async (siteId, userId, transactionId) => {
  const siteInfo = await siteService.isValidSite(siteId);
  if (!siteInfo) { utils.throwExceptionInvalidSite(siteId) }

  const assetsConfigRecords = await fetchCPAAssetConfigBySiteFromS3(siteId);
  if (_.isEmpty(assetsConfigRecords)) {
    throw utils.throwExceptionCPAConfDataNotExist(siteId)
  }
  const assetConfigList = utils.CPAAssetConfigSyncList(assetsConfigRecords, siteId, userId)
  await syncAssetConfiguration(siteId, assetConfigList)
  await maintenanceModeService.createMaintenanceModeLog(assetConfigList, transactionId)
  return { siteId, message: `${siteId} has been successfully synced.` }


  async function fetchCPAAssetConfigBySiteFromS3(siteId) {
    let configYamlSignedUrl;
    let configYamlData;
    try {
      configYamlSignedUrl = await s3Service.getSignedUrl(`cpa-conf-bucket`, `${siteId}/cpa-config.yaml`)
      const axiosInstance = axios.create();
      configYamlData = await axiosInstance.get(configYamlSignedUrl, { responseType: 'text' });
      const yamlContent = configYamlData?.data;
      if (_.isEmpty(yamlContent)) {
        return null;
      }
      const jsonData = yaml.load(yamlContent);
      return jsonData;
    } catch (error) {
      sails.log('[Error] > fetchCPAAssetConfigBySiteFromS3', error);
      if (error?.isAxiosError) {
        sails.log.error(`[fetchCPAAssetConfigBySiteFromS3] > axiosError error=${error?.message} configYamlSignedUrl=${configYamlSignedUrl} url=${configYamlSignedUrl} status=${error.response?.status} data=${error.response?.data} headers=${error.response?.headers}`);
      }
      return utils.throwExceptionUnableToFetchAssetCPAConfig(siteId)
    }
  }

}

async function syncAssetConfiguration(siteId, newAssetConfigurations) {
  try {
    return sails.getDatastore('postgres')
      .transaction(async (dbTransactionObj) => {
        const existingAssets = await CPAAssetConfigurationMetadata.find({ siteId, status: 1 }).usingConnection(dbTransactionObj);

        const existingAssetsMap = new Map(existingAssets.map(asset => [asset.assetId, { assetName: asset.assetName, assetType: asset.assetType, id: asset.id }]));
        const newAssetsToCreate = [];
        const assetIdsToUpdate = [];

        for (const newAsset of newAssetConfigurations) {
          const assetId = newAsset.assetId;
          const existingAsset = existingAssetsMap.get(assetId);
          if (!existingAsset) {
            newAssetsToCreate.push(newAsset);
          } else if (
            existingAsset.assetName !== newAsset.assetName ||
            existingAsset.assetType !== newAsset.assetType
          ) {
            assetIdsToUpdate.push(existingAsset.id);
            newAssetsToCreate.push(newAsset);
          }
        }

        if (assetIdsToUpdate.length > 0) {
          await CPAAssetConfigurationMetadata.update({
            siteId,
            id: { in: assetIdsToUpdate },
          }, {
            status: 0,
          }).usingConnection(dbTransactionObj);
        }

        if (newAssetsToCreate.length > 0) {
          await CPAAssetConfigurationMetadata.createEach(newAssetsToCreate).usingConnection(dbTransactionObj);
        }
        return
      });
  } catch (error) {
    sails.log.error('[syncAssetConfiguration] > Error during asset synchronization:', error);
    throw error;
  }
}

module.exports = {
  loadCPAAssetDataSource
}

