const flaverr = require("flaverr");
const util = require("util");
const cacheService = require("../cache/cache.public");
const compare = util.promisify(require("bcryptjs").compare);
const dynamoDbClient = require("../dynamoClient/daxClient");

/* eslint-disable no-undef */
module.exports = {
  /**
   * User module database functions
   */
  create: async (params) => {
    return Users.create(params);
  },
  find: async (searchParams) => {
    return Users.find(searchParams).then((data) => {
      data.forEach((element) => {
        delete element.password;
        // #ToReviewAmit: Any specific reason for deleting createdAt as well as updatedAt?
        // delete element.createdAt;
        // delete element.updatedAt;
      });
      return data;
    });
  },
  findOne: async (searchParams) => {
    try {
      const userInCache = await getUserCache(searchParams?.userId);
      if (!_.isEmpty(userInCache)) return userInCache;
      const user = await Users.findOne(searchParams);
      await setUserCache(searchParams?.userId, user);
      return user;
    } catch (e) {
      throw e;
    }
  },
  update: async (searchParams, updateValue) => {
    await invalidateUserCache(searchParams?.userId);
    return Users.update(searchParams, updateValue);
  },
  delete: async (searchParams) => {
    await invalidateUserCache(searchParams?.userId);
    return Users.destroy(searchParams);
  },
  authenticate: async (userId, password) => {
    let user,
      match = false;
    try {
      user = await Users.findOne({ userId, isDeleted: false });
    } catch (e) {
      throw e;
    }
    if (!user) throw flaverr('E_USER_NOT_FOUND', new Error('User not found'));

    try {
      match = await compare(password, user.password);
    } catch (e) {
      throw e;
    }
    if (match) {
      delete user.password;
      return user;
    } else {
      return false;
    }
  },
  // Exporting functions specific to Table: UserComponentCards
  UserComponentCards: {
    create: async (params) => {
      return UserComponentCards.create(params);
    },
    find: async (params) => {
      return UserComponentCards.find(params);
    },
  },

  comparePassword: async (password, hashedPassword) => {
    try {
      let isValid = await compare(password, hashedPassword);
      return isValid ? true : false;
    } catch (e) {
      sails.log.error("Error in user.private.comparePassword: ", e);
      throw e;
    }
  },

  fetchUsersBySiteId: async (siteId) => {
    if (!siteId) {
      throw flaverr("E_INVALID_ARGUMENT", new Error("Site ID is required"));
    }
    try {
      const params = {
        TableName: "usersitemaps",
        IndexName: "siteId-index",
        KeyConditionExpression: "#siteId = :siteId",
        ProjectionExpression: "userId",
        ExpressionAttributeNames: {
          "#siteId": "siteId",
        },
        ExpressionAttributeValues: {
          ":siteId": siteId,
        },
      };
      const queryResult = await dynamoDbClient.query(params).promise();
      const users = queryResult.Items ?? [];
      if (users.length === 0) {
        sails.log.info(`[fetchUsersBySiteId] No users found for siteId: ${siteId}`);
        return [];
      }
      const userIds = users.map(user => user.userId);
      const userDetails = await Promise.all(
        userIds.map(async (userId) => {
          return await Users.findOne({
            userId,
            isDeleted: false,
          });
        })
      );
      const filteredUsers = userDetails.filter(Boolean);
      const formattedUsers = filteredUsers.map(user => ({
        userId: user.userId.trim(),
        userOrganization: user.userOrganization || '',
        name: user.name?.trim() || '',
        email: user.email?.trim() || '',
        phone: user.phone || '',
        policiesGroup: user.policiesGroup || '{}',
        defaultSite: user.defaultSite || '',
        designation: user.designation || '',
        accountable: user.accountable || '[]',
        notify: user.notify || '[]',
      }));

      formattedUsers.sort((a, b) => a.name.localeCompare(b.name));
      return formattedUsers;
    } catch (error) {
      sails.log.error('[fetchUsersBySiteId] Error fetching user IDs by site!');
      throw error;
    }
  }
};

function _getUserCacheKey(userId) {
  return `user-detail:${userId}`;
}

async function getUserCache(userId) {
  if (!userId) return null;
  const cacheKey = _getUserCacheKey(userId);
  const cacheUser = await cacheService.get(cacheKey);
  if (!cacheUser) return null;
  sails.log.info(`Successfully retrieved user details from cache for ${userId}`);
  return JSON.parse(cacheUser);
}

async function setUserCache(userId, userDetail) {
  if (!userId) return;
  const cacheKey = _getUserCacheKey(userId);
  await cacheService.set(cacheKey, JSON.stringify(userDetail));
  sails.log.info(`Cache for ${userId} is stored successfully!`);
  await cacheService.expire(cacheKey, 24 * 60 * 60);
}

async function invalidateUserCache(userId) {
  if (!userId) return;
  const cacheKey = _getUserCacheKey(userId);
  await cacheService.delete(cacheKey);
}
