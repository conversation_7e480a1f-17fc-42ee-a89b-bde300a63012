const userService = require("./user.private");
const userSiteMapService = require("../userSiteMap/userSiteMap.public");
const roleService = require("../role/role.public");
const Flaverr = require("flaverr");
const userSiteMapPublic = require("../userSiteMap/userSiteMap.public");
const globalHelper = require("../../utils/globalhelper");
const utils = require("../../utils/user/utils");
const notificationService = require("../../services/notification/slack/notify.public");
const { Novu } = require('@novu/node');
const flaverr = require("flaverr");
const { isSubscriberExistInNovu } = require("../novu");
const { result } = require("lodash");
const novu = new Novu(sails.config.NOVU_CONFIG.SECRET_KEY);
const CryptoJS = require("crypto-js");

/**
 * Gets the info from database of all the unique roles present in the policies
 * @param {object} policiesGroup Mapping of siteId with its role name.
 * @returns {object} Mapping of role name and its info
 */
const getRolesInfo = async (policiesGroup) => {
  const uniqueRolesSet = new Set();
  let roleInfo = {};
  try {
    for (const siteId in policiesGroup) {
      const roleName = policiesGroup[siteId];
      uniqueRolesSet.add(roleName);
    }
    const uniqueRolesArray = Array.from(uniqueRolesSet); // Converting Set to Array
    const allRoles = await Promise.all(
      uniqueRolesArray.map((roleName) => roleService.findOne({ roleName }))
    );
    for (const [index, eachRole] of allRoles.entries()) {
      if (!eachRole) {
        roleInfo = { problems: ["Invalid Role."] };
        break;
      } else roleInfo[uniqueRolesArray[index]] = eachRole;
    }
    return roleInfo;
  } catch (e) {
    sails.log.error("Error in userService.getRolesInfo: ", e);
    throw e;
  }
};

/**
 * @description Decrypts an encrypted password using AES encryption
 * @param {string} encryptedPassword - The AES encrypted password
 * @returns {string} Decrypted password
 */
const decryptPassword = (encryptedPassword) => {
  const secretKey = process.env.CRYPTOJS_SECRET_KEY;
  try {
    const bytes = CryptoJS.AES.decrypt(encryptedPassword, secretKey);
    const decryptedPassword = bytes.toString(CryptoJS.enc.Utf8);

    if (decryptedPassword && decryptedPassword !== '') {
      return decryptedPassword;
    } else {
      throw new Error("Failed to decrypt password.");
    }
  } catch (error) {
    // If decryption fails, assume the password is in plain text.
    sails.log.error("[userservice > decryptPassword] Error!");
    return encryptedPassword;
  }
};

module.exports = {
  create: userService.create,
  find: userService.find,
  findOne: userService.findOne,
  update: userService.update,
  delete: userService.delete,
  authenticate: userService.authenticate,
  // Exporting functions specific to Table: UserComponentCards
  UserComponentCards: userService.UserComponentCards,

  comparePassword: userService.comparePassword,
  decryptPassword: decryptPassword,

  /**
   * Give a user Id and site Id, find the preference, i.e unit preference, roles etc of user on that site.
   * @param {string} userId unique id of user
   * @param {string} siteId unique id of site for which we need to return user preference of
   * @return {object} user preference object, or Undefined
   */
  async getUserPreference(userId, siteId) {
    let userPreference;
    try {
      userPreference = await userSiteMapService.findOne({ userId, siteId });
    } catch (e) {
      throw e;
    }
    return userPreference;
  },
  /**
   * Find the preferences(i.e unit prefernce, roles etc) of the user on every site he has access to.
   * @param {string} userId Unique id of user
   * @returns {array} array of found user prefernces
   */
  async getUserPreferenceOnAllSites(userId) {
    let userPreferences;
    try {
      userPreferences = await userSiteMapService.find({ userId });
    } catch (e) {
      throw e;
    }
    return userPreferences;
  },
  getRolesInfo: getRolesInfo,
  getMobileNumbersOfUsers: async (userIds) => {
    try {
      // we can improve this by using caching
      const $users = userIds.map(
        (userId) => userService.findOne({ userId }),
      );

      const users = await Promise.allSettled($users);
      const userMobileNumberList = users.map((userPromise) => {
        if (userPromise.status === 'fulfilled'
          && userPromise.value !== undefined) {
          const user = userPromise.value;
          return user.phone;
        }
      }).filter(Boolean);

      return userMobileNumberList;
    } catch (e) {
      sails.log.error('userservice::getMobileNumbersOfUsers ', e);
      throw (e);
    }
  },
  /**
   * Get the email prefernce of user.
   * output = {userId: {JouleRecipe: true, ...}, ...}
   * @param {array} userIds Id of users
   * @param {string} siteId id of site
   */
  async getUsersEmailPreference(userIds, siteId) {
    try {
      const $users = userIds.map((userId) => userSiteMapService.findOne({ userId, siteId }));

      const users = await Promise.allSettled($users);
      const userEmailPrefernaceMap = users.reduce((acc, userPromise) => {
        if (userPromise.status === 'fulfilled'
          && userPromise.value !== undefined) {
          const user = userPromise.value;
          const userMailPrefernce = globalHelper.toJson(user.mailConfig) || {};
          //  userMailPrefernce = {"JouleRecipe":"0","nudge": "0"}

          const preferenceEachAsset = Object.keys(userMailPrefernce).reduce((acc, asset) => {
            if (userMailPrefernce[asset] === null || userMailPrefernce[asset] === '') {
              sails.log.error(`userservice::getUsersEmailPreference Invalid user config for user ${user.userId}`);
              acc[asset] = false;
            } else if (typeof userMailPrefernce[asset] === 'number') {
              if (userMailPrefernce[asset] === 1) {
                acc[asset] = true;
              } else {
                acc[asset] = false;
              }
            } else {
              acc[asset] = Object.values(userMailPrefernce[asset]).some((elem) => elem === '1');
            }

            return acc;
          }, {});

          acc[user.userId] = preferenceEachAsset;
          // {JouleRecipe: true, MaintenanceAction: false, hvacNotif: false}
        } else {
          sails.log.error('userservice::getUsersEmailPreference: ');
          sails.log.error(userPromise.reason);
        }
        return acc;
      }, {});

      return userEmailPrefernaceMap;
      // {userId : {JouleRecipe: true, MaintenanceAction: false, hvacNotif: false}}
    } catch (e) {
      sails.log.error('userservice::getUsersEmailPreference ');
      sails.log.error(e);
      throw (e);
    }
  },

  /**
   * It fetches the policies from the role table and converts them to the old policy structure.
   * This is done to add the users to their respective socket rooms.
   * @param {string} roleName The name of the role
   * @returns flattened policies as per the previous strucutre
   */
  getOldPoliciesByRoleName: async function(roleName) {
    let role;
    try {
      role = await roleService.findOne({ roleName });
    } catch (e) {
      sails.log.error(e);
      throw e;
    }
    if (!role) {
      throw 'Invalid User Role';
    }
    let policies = globalHelper.toJson(role.policies);
    let flattenedPolicies = {};
    for (let eachPolicy in policies) {
      let subHeadings = policies[eachPolicy]["subHeadings"];
      let pageViewKey = `${eachPolicy}_View`;
      let pageViewValue = (policies[eachPolicy]["pageView"] === true) ? "1" : "0";
      flattenedPolicies[pageViewKey] = pageViewValue;
      for (let eachSubHead in subHeadings) {
        let innerPolicies = subHeadings[eachSubHead]["policies"];
        let camelCaseSubHead = eachSubHead.charAt(0).toUpperCase() + eachSubHead.slice(1);
        for (let eachInnerPolicy in innerPolicies) {
          let innerPolicyCameCase = eachInnerPolicy.charAt(0).toUpperCase() + eachInnerPolicy.slice(1);
          let key = `${camelCaseSubHead}_${innerPolicyCameCase}`;
          let value = (innerPolicies[eachInnerPolicy]["hasAccess"] === true) ? "1" : "0";
          flattenedPolicies[key] = value;
        }
      }
    }
    return flattenedPolicies;
  },
  /**
   * @description Update user site
   * @param {String} userId
   * @param {String} siteId
   * @param {String} newRole
   * @return {Object}
   */
  updateUserSiteRole: async function ({ userId, siteId, role: newRole }) {
    try {
      let oldRole;
      const user = await userService.findOne({
        userId: userId,
        isDeleted: false,
      });
      if (!user) {
        throw new Flaverr(
          {
            message: `user ${userId} does not exist `,
            code: "E_USER_NOT_FOUND",
            data: { userId, siteId, newRole, operation: "userRoleUpdated" },
          },
          new Error(`User ${userId} does not exist `)
        );
      }
      let policiesGroup = Object.assign({},JSON.parse(user.policiesGroup));
      oldRole = policiesGroup[siteId];
      policiesGroup[siteId] = newRole;
      policiesGroup = JSON.stringify(policiesGroup);

      const userSiteMap = await userSiteMapPublic.findOne({
        userId: userId,
        siteId,
      });
      if (!userSiteMap) {
        throw new Flaverr(
          {
            message: `this site(${siteId}) is not assigned to user. `,
            code: "E_SITE_NOT_ASSIGNED_TO_USER",
            data: { userId, siteId, newRole, operation: "userRoleUpdated" },
          },
          new Error(`this site(${siteId}) is not assigned to user. `)
        );
      }
      const roleInfo = await getRolesInfo({ siteId: newRole });
      if (_.isEmpty(roleInfo)) {
        throw new Flaverr(
          {
            message: `Role:${newRole} does not exist}`,
            code: "E_ROLE_NOT_FOUND",
            data: { userId, siteId, newRole, operation: "userRoleUpdated" },
          },
          new Error(`Role:${newRole} does not exist}`)
        );
      }
      await userSiteMapPublic.update(
        {
          userId: userId,
          siteId,
        },
        {
          role: newRole,
        }
      );

      await userService.update(
        {
          userId: user.userId,
          userOrganization: user.userOrganization,
        },
        {
          policiesGroup,
        }
      );
      return {
        operation: "userRoleUpdated",
        message: `${userId} role has been changed from ${oldRole} to ${newRole} for ${siteId} successfully`,
        data: {
          userId,
          siteId,
          oldRole,
          newRole,
          operation: "userRoleUpdated",
        },
      };
    } catch (e) {
      throw new Flaverr(
        {
          message: e.message,
          code: e.code,
          operation: "userRoleUpdated",
          data: { userId, siteId, role: newRole },
        },
        new Error(e.message)
      );
    }
  },
  removeUserSiteAccess: async function ({ userId, siteId }) {
    try {
      const user = await userService.findOne({ userId, isDeleted: false });
      if (_.isEmpty(user)) {
        throw new Flaverr(
          {
            message: `${userId} doesn't exist at jouletrack.`,
            code: "E_USER_NOT_FOUND",
            data: { userId, siteId, operation: "siteAccessRemoved" },
          },
          new Error(`${userId} doesn't exist at jouletrack.`)
        );
      }
      let policiesGroup = Object.assign({},JSON.parse(user.policiesGroup));
      if (Object.keys(policiesGroup).length <= 1)  {
        throw new Flaverr(
          {
            message: `User ${userId} must have atleast one site access either delete the user`,
            code: "E_USER_SITE_REMOVE_LIMIT",
            data: { userId, siteId, operation: "siteAccessRemoved" },
          },
          new Error(`User ${userId} must have atleast one site access or either delete the user`)
        );
      }
      delete policiesGroup[siteId];
      policiesGroup = JSON.stringify(policiesGroup);

      let email = user.email;
      const userSiteMap = await userSiteMapService.findOne({ userId, siteId });
      if (_.isEmpty(userSiteMap)) {
        throw new Flaverr(
          {
            message: `SiteId ${siteId} does not assigned to this user`,
            code: "E_USER_SITE_NOT_FOUND",
            data: { userId, siteId, operation: "siteAccessRemoved" },
          },
          new Error(`SiteId ${siteId} does not assigned to this user`)
        );
      }
      await userSiteMapService.delete({ userId, siteId });
      await userSiteMapService.removeSiteIdInUserSiteCache(userId, siteId);
      await userService.update(
        {
          userId: user.userId,
          userOrganization: user.userOrganization,
        },
        {
          policiesGroup,
        }
      );
      return {
        operation: "siteAccessRemoved",
        message: `${userId} has been removed from ${siteId} successfully`,
        data: { userId, siteId, email, operation: "siteAccessRemoved" },
      };
    } catch (e) {
      throw new Flaverr(
        {
          message: e.message,
          code: e.code,
          operation: "siteAccessRemoved",
          data: { userId, siteId },
        },
        new Error(e.message)
      );
    }
  },
  assignNewSiteUserAccess: async function ({
    userId,
    siteId,
    role: newRole,
    userOrganization,
  }) {
    let email;
    try {
      let defaultPreferences = {};
      utils.addDefaultPreferences(defaultPreferences);
      globalHelper.stringifyEachKeyOfObject(defaultPreferences);

      const userInfo = await userService.findOne({
        userId: userId,
        isDeleted: false,
      });
      if (_.isEmpty(userInfo)) {
        throw new Flaverr(
          {
            message: `${userId} doesn't exist at jouletrack.`,
            code: "E_USER_NOT_FOUND",
            data: {
              userId,
              siteId,
              newRole,
              userOrganization,
              operation: "newSiteAssigned",
            },
          },
          new Error(`${userId} doesn't exist at jouletrack.`)
        );
      }
      email = userInfo.email;
      const userSiteMap = await userSiteMapPublic.findOne({
        userId: userId,
        siteId: siteId,
      });
      if (!_.isEmpty(userSiteMap)) {
        throw new Flaverr(
          {
            message: `this site(${siteId}) already assigned to this user.`,
            code: "E_SITE_ALREADY_ASSIGNED",
            data: {
              userId,
              siteId,
              newRole,
              userOrganization,
              operation: "newSiteAssigned",
            },
          },
          new Error(`this site(${siteId}) already assigned to this user.`)
        );
      }
      await userSiteMapPublic.create({
        userId,
        siteId,
        role: newRole,
        ...defaultPreferences,
      });
      await userSiteMapPublic.addSiteIdInUserSiteCache(userId, siteId);
      let policiesGroup = {
        [siteId]: newRole,
      };
      policiesGroup = Object.assign(
        policiesGroup,
        JSON.parse(userInfo.policiesGroup)
      );
      policiesGroup = JSON.stringify(policiesGroup);
      await userService.update(
        {
          userId,
          userOrganization,
        },
        {
          policiesGroup,
        }
      );
      return {
        operation: "newSiteAssigned",
        message: `${userId} has been assigned to ${siteId} successfully`,
        data: { userId, siteId, email, newRole, operation: "newSiteAssigned" },
      };
    } catch (e) {
      throw new Flaverr(
        {
          message: e.message,
          code: e.code,
          operation: "newSiteAssigned",
          data: { userId, siteId, email, newRole },
        },
        new Error(e.message)
      );
    }
  },
  batchUpdateUserSiteRole: async function (params, requesterInfo) {
    let batchOperationPromiseHolder = [];
    params.forEach((param) =>
      batchOperationPromiseHolder.push(this.updateUserSiteRole(param))
    );
    let batchOperationResponse = await Promise.allSettled(
      batchOperationPromiseHolder
    );

    const userList = batchOperationResponse.reduce((acc, item) => {
      if (item.status === "fulfilled") {
        acc.push(item.value.data);
      }
      return acc
    }, [])
    if (!_.isEmpty(userList)) {
      await notificationService._notifySlackUpdateRole(userList,requesterInfo);
    }
    return batchOperationResponse;
  },
  batchRemoveUserSiteAccess: async function (params, requesterInfo) {
    let batchOperationResponse = [];
    const userList = [];
    for (const param of params) {
      try {
        const assignedSite = await this.removeUserSiteAccess(param)
        userList.push(assignedSite.data)
        batchOperationResponse.push({
          status: 'fulfilled',
          value: assignedSite
        })
      } catch(e) {
        batchOperationResponse.push({
          status: 'rejected',
          reason: e
        })
      }
    };

    if (!_.isEmpty(userList)) {
      await notificationService._notifySlackRemoveSiteAccess(userList,requesterInfo);
    }
    return batchOperationResponse;
  },
  batchAssignNewSiteUserAccess: async function (params, requesterInfo) {
    let batchOperationResponse = [];
    const userList = [];
    for (const param of params) {
      try {
        const assignedSite = await this.assignNewSiteUserAccess(param)
        userList.push(assignedSite.data)
        batchOperationResponse.push({
          status: 'fulfilled',
          value: assignedSite
        })
      } catch(e) {
        batchOperationResponse.push({
          status: 'rejected',
          reason: e
        })
      }
    };

    if (!_.isEmpty(userList)) {
      await notificationService._notifySlackAssignSiteAccess(userList,requesterInfo);
    }
    return batchOperationResponse;
  },
  /**
   * @description getUserInfo
   * @param {String} userId
   * @returns {Object}
   */
  getUserInfo: async function (userId) {
    return Users.findOne({ userId })
  },
  getUserAuthDetailsWithDefaultSite: async function (userId, siteId) {
    let [userPreference, userInfo] = await Promise.all([
      userSiteMapPublic.fetchUserSitePreferences(userId, siteId),
      userService.findOne({ userId }),
    ]);

    if (!userInfo) {
      throw flaverr(
        "E_UNAUTHORIZED_USER",
        new Error("User does not exist. Please contact the administrator team."),
      );
    }
    let defaultSiteId = userPreference?.siteId || "";
    if (_.isEmpty(userPreference)) {
      const userSites = await userSiteMapPublic.getUserSiteIds(userId);
      defaultSiteId =
        userSites.find((siteId) => siteId === userInfo?.defaultSite) || userSites?.[0];
      userPreference = await userSiteMapPublic.findOne({ siteId: defaultSiteId, userId });
    }

    if (_.isEmpty(defaultSiteId)) {
      throw flaverr(
        "E_UNAUTHORIZED_USER",
        new Error("You do not have any site access. Please connect with admin team."),
      );
    }

    const message =
      defaultSiteId !== siteId
        ? `Access denied for ${siteId}, Redirecting to ${defaultSiteId}`
        : "";

    if (_.isEmpty(userPreference)) {
      throw flaverr(
        "E_UNAUTHORIZED_USER",
        new Error("You do not have any default site. Please connect with admin team."),
      );
    }

    return { userInfo, userPreference, defaultSiteId, message };
  },
  getUserSiteIfDefaultSiteNotExist: async function(userId, defaultSite) {
    const userDefaultSite =  await userSiteMapPublic.findOne({userId, siteId:defaultSite});
    if (userDefaultSite) {
      return defaultSite;
    }

    const userSites = await userSiteMapPublic.find({userId});
    if (userSites.length) {
      const {siteId} = userSites[0];
      return siteId;
    }

    return null;
  },
  registerUserOnNovu: async function(userObj) {
    try {
      const {name, userId, phone, whatsappNumber} = userObj;
      const buildSubObj = {
        firstName: name.split(' ')[0],
        lastName: name.split(' ').slice(1).join(' '),
        email: userId,
        phone: '+91'+ phone.trim(),
        data: {

        },
      };
      if (whatsappNumber) buildSubObj.data.whatsappNumber = whatsappNumber.trim();
      const response = await novu.subscribers.identify(userId, buildSubObj);
      return response.status == 201 && response.data.data;
    } catch (e) {
      sails.log('[fn:registerUserOnNovu] Create User Novo Subscription Error: ' + e.message);
    }
  },
  syncUserSubscriber: async function(userId) {
    try {
      const user = await userService.findOne({userId, isDeleted: false});
      if (!user) throw flaverr('E_USER_NOT_FOUND', new Error('User not found'));

      const {userOrganization} = user;
      const isUserExistInNovu = await isSubscriberExistInNovu(userId)
      if (isUserExistInNovu) return true;

      const {_id:novuSubscriberId} = await this.registerUserOnNovu(user);
      await userService.update({userId, userOrganization}, {subscriber_id: novuSubscriberId})
      return true;
    } catch (e) {
      sails.log('Sync User Novu Subscription Error: ' + e.message);
      throw e;
    }
  },
  /**
   *
   * @param {Array<userId>} userList
   * @returns Void
   */
  removeUsersOnNovu: async function(userList) {
    try {
      const response = {
        deleted: [],
        notDeleted: []
      }
      const $subscriberDeletePromise = userList.map((userId) => novu.subscribers.delete(userId));
      const subscriberDeletedResult = await Promise.allSettled($subscriberDeletePromise);
      for(let index =0; index< subscriberDeletedResult; index++) {
        if (result.status == 'rejected' && result.reason.response.status !== 404) {
          response.notDeleted.push(userList[index]);
          sails.log.error('[fn:removeUserOnNovu] Unable to remove User Novo Subscription Error', result.reason.response.data);
        } else if (result.status == 'fulfilled') {
          response.deleted.push(userList[index]);;
        }
      }
      return response;
    } catch(e) {
      sails.log.error('[fn:removeUserOnNovu] Remove User Novo Subscription Error: ' + e.message);
      throw e;
      sails.log('Create User Novu Subscription Error: ' + e.message);
    }
  }
};
