const flaverr = require("flaverr");
const configuratorPageService = require("../configuratorPage/configuratorPage.public");
const configuratorTableGroupService = require("./configuratorTableGroup.private");
const {
  updateTableGroupResponseGenerator,
} = require("../../utils/ConfiguratorTableGroup/responseBuilder");
const tableGroupThrowException = require("../../utils/ConfiguratorTableGroup/InputValidation");
const configuratorPagesPublicService = require("../configuratorPage/configuratorPage.public");

const createNewTableGroup = async (pageRefId, name, userId) => {
  await configuratorPagesPublicService.validateTablePage(pageRefId);
  return configuratorTableGroupService.create({
    name: name,
    pageRefId: pageRefId,
    created_by: userId,
  });
};

const deleteTableGroup = async (pageRefId, tableGroupId, userId) => {
  await configuratorPagesPublicService.validateTablePage(pageRefId);
  const allTableGroups = await configuratorTableGroupService.find({
    pageRefId,
    status: 1,
  });
  if (_.isEmpty(allTableGroups)) {
    tableGroupThrowException.throwExceptionNoTableGroupExist(pageRefId, tableGroupId);
  }

  let isTableGroupExist = false;
  allTableGroups.forEach((tableGrp) => {
    if (tableGrp.id == tableGroupId) isTableGroupExist = true;
  });
  if (!isTableGroupExist) {
    tableGroupThrowException.throwExceptionInvalidTableGroup(pageRefId, tableGroupId);
  }

  if (allTableGroups.length == 1) {
    tableGroupThrowException.throwExceptionOnlyTableGroupExist(pageRefId, tableGroupId);
  }
  return await configuratorTableGroupService.updateOne(
    {
      id: tableGroupId,
      status: 1,
    },
    {
      status: 0,
      updated_by: userId,
    },
  );
};

const isValidTableGroup = async (tableGroupId, pageId) => {
  return (await configuratorTableGroupService.findOne({
    id: tableGroupId,
    pageRefId: pageId,
    status: 1,
  }))
    ? true
    : false;
};

async function updateTableGroup(params) {
  const {
    pageId,
    tableGroupId,
    name,
    _userMeta: { id: userId },
  } = params;
  const _page = await configuratorPageService.findOne({
    id: pageId,
    status: 1,
  });
  if (!_page) {
    throw flaverr("E_PAGE_NOT_FOUND", new Error(`Page not found`));
  }
  if (_page.pageType != 2) {
    throw flaverr("E_INPUT_VALIDATION", new Error(`Invalid Page type`));
  }
  const updatedTableGroup = await configuratorTableGroupService.updateOne(
    {
      id: tableGroupId,
      status: 1,
    },
    { name: name.trim(), updated_by: userId },
  );
  if (!updatedTableGroup) {
    throw flaverr("E_TABLE_GROUP_NOT_FOUND", new Error(`Table group not found`));
  }
  return updateTableGroupResponseGenerator(updatedTableGroup);
}

const fetchPageTableGroups = async (pageId) => {
  return await configuratorTableGroupService.find({
    pageRefId: pageId,
    status: 1,
  });
};

module.exports = {
  find: configuratorTableGroupService.find,
  findOne: configuratorTableGroupService.findOne,
  create: configuratorTableGroupService.create,
  update: configuratorTableGroupService.update,
  delete: configuratorTableGroupService.delete,
  updateOne: configuratorTableGroupService.updateOne,
  createNewTableGroup,
  deleteTableGroup,
  isValidTableGroup: isValidTableGroup,
  updateTableGroup,
  fetchPageTableGroups,
};
