const configuratorTableGroupService = require("./configuratorTableGroup.service")

module.exports = {
  find: configuratorTableGroupService.find,
  findOne: configuratorTableGroupService.findOne,
  create: configuratorTableGroupService.create,
  update: configuratorTableGroupService.update,
  updateOne: configuratorTableGroupService.updateOne,
  delete: configuratorTableGroupService.delete,
  createNewTableGroup: configuratorTableGroupService.createNewTableGroup,
  deleteTableGroup: configuratorTableGroupService.deleteTableGroup,
  isValidTableGroup: configuratorTableGroupService.isValidTableGroup,
  fetchPageTableGroups: configuratorTableGroupService.fetchPageTableGroups
}
