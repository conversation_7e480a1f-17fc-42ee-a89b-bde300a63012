module.exports = {
  find: async (searchParams) => {
    return ConfiguratorTableGroup.find(searchParams);
  },
  findOne: async (searchParams) => {
    return ConfiguratorTableGroup.findOne(searchParams);
  },
  update: async (searchParams, updateValue) => {
    return ConfiguratorTableGroup.update(searchParams, updateValue);
  },
  updateOne: async (searchParams, updateValue) => {
    return ConfiguratorTableGroup.updateOne(searchParams, updateValue);
  },
  create: async (param) => {
    return ConfiguratorTableGroup.create(param).fetch();
  },
  delete: async (searchParams) => {
    return ConfiguratorTableGroup.destroy(searchParams);
  }
}
