const flaverr = require('flaverr');
const RecipeAlertIntegration = require('./lib/SmartAlertRecipeSyncService');

/**
 * This service provides integration with the Smart Alert Engine for recipes.
 * It handles registering, updating, and deleting alert templates for recipes.
 */

/**
 * Validates recipe data for alert operations
 *
 * @param {Object} recipeData - Recipe data to validate
 * @param {Boolean} requireChildren - Whether to require child recipes
 * @throws {Error} If validation fails
 */
const validateRecipeData = function (recipeData, requireChildren = true) {
  if (!recipeData || !recipeData.id || !recipeData.site_id) {
    throw flaverr('E_MISSING_RECIPE_DATA', new Error('Recipe data is missing'));
  }

  if (requireChildren) {
    if (!recipeData.children_recipes || !Array.isArray(recipeData.children_recipes) || recipeData.children_recipes.length === 0) {
      throw flaverr('E_MISSING_RECIPE_DATA', new Error('Recipe must have children_recipes array'));
    }
  }
};

module.exports = {
  /**
   * Registers a new recipe alert in the smart alert engine
   *
   * @param {Object} recipeData - Complete recipe data including all necessary information
   * @param {String} userId - ID of the user creating the recipe
   * @returns {Promise<Object>} - Result of the registration
   */
  registerRecipeAlert: async function (recipeData, userId) {
    /**
     *
     *
     *
     */

    try {
      validateRecipeData(recipeData);

      if (recipeData.recipe_type !== 'alert') {
        return { message: 'Recipe is not an alert type' };
      }

      const alertIntegration = new RecipeAlertIntegration(recipeData, userId);
      return await alertIntegration.createAlertTemplate();
    } catch (error) {
      sails.log.error('[smartAlertIntegration.service -> registerRecipeAlert]', error);
      throw error;
    }
  },

  /**
   * Updates an existing recipe alert in the smart alert engine
   *
   * @param {Object} recipeData - Complete recipe data including all necessary information
   * @param {String} userId - ID of the user making the change
   * @returns {Promise<Object>} - Result of the update
   */
  updateRecipeAlert: async function (recipeData, userId) {
    try {
      validateRecipeData(recipeData);

      if (recipeData.recipe_type !== 'alert') {
        return { message: 'Recipe is not an alert type' };
      }

      const alertIntegration = new RecipeAlertIntegration(recipeData, userId);
      return await alertIntegration.updateAlertTemplate();
    } catch (error) {
      sails.log.error('[smartAlertIntegration.service -> updateRecipeAlert]', error);
      throw error;
    }
  },

  /**
   * Deletes a recipe alert from the smart alert engine
   *
   * @param {Object} recipeData - Recipe data containing at least the ID
   * @param {String} userId - ID of the user making the change
   * @returns {Promise<Object>} - Result of the deletion
   */
  deleteRecipeAlert: async function (recipeData, userId) {
    try {
      validateRecipeData(recipeData, false);

      if (recipeData.recipe_type !== 'alert') {
        return { message: 'Recipe is not an alert type' };
      }

      const alertIntegration = new RecipeAlertIntegration(recipeData, userId);
      return await alertIntegration.deleteAlertTemplate();
    } catch (error) {
      sails.log.error('[smartAlertIntegration.service -> deleteRecipeAlert]', error);
      throw error;
    }
  },
};
