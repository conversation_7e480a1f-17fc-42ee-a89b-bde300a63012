const recipeService = require('./recipe.service');

module.exports = {
  find: recipeService.find,
  findOne: recipeService.findOne,
  create: recipeService.create,
  update: recipeService.update,
  delete: recipeService.delete,
  find_recipe_actions: recipeService.find_recipe_actions,
  create_recipe_actions: recipeService.create_recipe_actions,
  update_recipe_actions: recipeService.update_recipe_actions,
  delete_recipe_actions: recipeService.delete_recipe_actions,
  find_children_recipes: recipeService.find_children_recipes,
  create_children_recipes: recipeService.create_children_recipes,
  update_children_recipes: recipeService.update_children_recipes,
  delete_children_recipes: recipeService.delete_children_recipes,
  getActionRecipeInfoById: recipeService.getActionRecipeInfoById,
  fetchAllSuperRecipesBySiteId: recipeService.fetchAllSuperRecipesBySiteId,
  fetchAllSuperRecipeTemplatesBySiteId: recipeService.fetchAllSuperRecipeTemplatesBySiteId,
  createRecipe: recipeService.createRecipe,
  createRecipePayload: recipeService.createRecipePayload,
  getRecipeTemplateUsedCount: recipeService.getRecipeTemplateUsedCount,
  deleteTemplateLinks: recipeService.deleteTemplateLinks,
  getActionRecipeTemplateInfoById: recipeService.getActionRecipeTemplateInfoById,
  updateDeploymentStatus: recipeService.updateDeploymentStatus,
  syncSmartAlertRecipeById: recipeService.syncSmartAlertRecipeById,
  syncSmartAlertMetaDetailById: recipeService.syncSmartAlertMetaDetailById,
  deleteSmartAlertRecipeById: recipeService.deleteSmartAlertRecipeById,
};
