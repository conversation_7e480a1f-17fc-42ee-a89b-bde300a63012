const flaverr = require('flaverr');
const moment = require('moment');
const CronBuilder = require('../lib/cron.builder');
const { v4: uuidv4 } = require('uuid');

const VALID_DAYS = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];

module.exports = {
  /**
   * Fetch all records
   */
  find: async function () {
    return await RecipeSchedule.find();

  },

  /**
   * Fetch a single record by ID
   */
  findOne: async function (id) {
    if (!id) {
      throw flaverr('E_MISSING_FIELDS', new Error('ID is required.'));
    }
    return await RecipeSchedule.findOne({ id });
  },

  /**
   * Create a new record
   */
  create: async function (data) {
    const {
      recipe_id,
      repeat_type,
      status,
      start_date,
      end_date,
      time_range,
      customDays
    } = data;

    if (!recipe_id || !repeat_type || !status || !start_date || !end_date || _.isEmpty(time_range)) {
      throw flaverr('E_MISSING_FIELDS', new Error('Missing required fields.'));
    }

    return await _handleScheduleOperations(data);
  },

  /**
   * @description Update a record by ID (with full cron rebuild and validation)
   */
  update: async function (id, updates) {
    if (!id) {
      throw flaverr('E_MISSING_FIELDS', new Error('ID is required.'));
    }

    const existingSchedule = await this.findOne(id);
    if (!existingSchedule) {
      throw flaverr('E_NOT_FOUND', new Error('Schedule not exists with id=' + id));
    }

    const {
      repeat_type,
      status,
      start_date,
      end_date,
      time_range,
      customDays
    } = updates;

    if (!repeat_type || !status || !start_date || !end_date || _.isEmpty(time_range)) {
      throw flaverr('E_MISSING_FIELDS', new Error('Missing required fields.'));
    }

    const cronBuilder = new CronBuilder(start_date, end_date);
    let allCrons = [];

    /** Format all time ranges to HH:mm:ss */
    const formattedTimeRanges = time_range.map(time => {
      const formattedStart = moment(time.start_time)
        .format('HH:mm:ss');
      const formattedEnd = moment(time.end_time)
        .format('HH:mm:ss');

      let cronList = [];
      switch (repeat_type) {
        case 'dailySlot':
          cronList = cronBuilder.createNoIntervalCron(formattedStart, formattedEnd);
          break;
        case 'daily':
          cronList = cronBuilder.createDailyCron(formattedStart, formattedEnd);
          break;
        case 'custom':
          isValidDays(customDays);
          cronList = cronBuilder.createCustomCron(formattedStart, formattedEnd, customDays);
          break;
        default:
          throw flaverr('E_INPUT_VALIDATION', new Error('Invalid repeat interval'));
      }

      const cronsTab = cronBuilder.extractCronsFromSchedules(cronList);
      allCrons.push(...cronsTab);

      return {
        start_time: formattedStart,
        end_time: formattedEnd,
      };
    });

    const finalCron = cronBuilder.mergeCronExpressions(allCrons);

    const updatedData = {
      repeat_type,
      status,
      cron: finalCron,
      start_date,
      end_date,
      time_ranges: formattedTimeRanges,
    };

    return await RecipeSchedule.update({ id })
      .set(updatedData)
      .fetch();
  },

  /**
   * Soft delete a record by ID (set status to 0)
   */
  delete: async function (criteria) {
    if (!criteria) {
      throw flaverr('E_MISSING_FIELDS', new Error('Field is required.'));
    }

    return await RecipeSchedule.update(criteria)
      .set({ status: 0 });

  },
};

/**
 * @description Handles cron generation, validation, and creates a single schedule with merged cron expressions.
 * @param {Object} data - Contains schedule-related parameters.
 * @returns {Promise<Object>} - Created schedule record.
 */
async function _handleScheduleOperations(data) {
  const {
    recipe_id,
    repeat_type,
    status,
    start_date,
    end_date,
    time_range,
    customDays
  } = data;

  const cronBuilder = new CronBuilder(start_date, end_date);
  const uniqId = uuidv4();
  let allCrons = [];

  /**Convert all times to HH:mm:ss format*/
  const formattedTimeRanges = time_range.map(time => {
    const formattedStart = moment(time.start_time)
      .format('HH:mm:ss');
    const formattedEnd = moment(time.end_time)
      .format('HH:mm:ss');

    let cronList = [];
    switch (repeat_type) {
      case 'dailySlot':
        cronList = cronBuilder.createNoIntervalCron(formattedStart, formattedEnd);
        break;
      case 'daily':
        cronList = cronBuilder.createDailyCron(formattedStart, formattedEnd);
        break;
      case 'custom':
        isValidDays(customDays);
        cronList = cronBuilder.createCustomCron(formattedStart, formattedEnd, customDays);
        break;
      default:
        throw flaverr('E_INPUT_VALIDATION', new Error('Invalid repeat interval'));
    }

    const cronsTab = cronBuilder.extractCronsFromSchedules(cronList);
    allCrons.push(...cronsTab);

    return {
      start_time: formattedStart,
      end_time: formattedEnd,
    };
  });

  const finalCron = cronBuilder.mergeCronExpressions(allCrons);

  const scheduleData = {
    recipe_id,
    repeat_type,
    status,
    cron: finalCron,
    uniqId,
    start_date,
    end_date,
    time_ranges: formattedTimeRanges,
  };

  return await RecipeSchedule.create(scheduleData)
    .fetch();
}

/**
 * Validates if the given array contains only valid day names.
 * @param {Array} daysArray - Array of days (e.g., ['mon', 'wed', 'fri'])
 * @returns {boolean} Returns true if all days are valid, otherwise throws an error
 * @throws {Error} If input is not an array or contains invalid days
 */
function isValidDays(daysArray) {
  if (!Array.isArray(daysArray)) {
    throw flaverr('E_INPUT_VALIDATION', new Error('Invalid input: Expected an array of days.'));
  }

  if (daysArray.some(day => !VALID_DAYS.includes(day))) {
    throw flaverr('E_INPUT_VALIDATION', new Error(`Invalid custom days: Allowed values are ${VALID_DAYS.join(', ')}.`));
  }

  return true;
}
