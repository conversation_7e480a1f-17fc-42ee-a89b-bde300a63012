class SuperRecipeDeployer {
  constructor(recipeData) {
    this.recipeData = recipeData;
  }

  generateIoTPayload() {
    const {
      children_recipes,
      rid,
      app_type,
      dependentOnOthers,
      controllers,
      recipe_category,
      title,
      description,
      priority,
      switch_off,
      notify,
      smslist,
    } = this.recipeData;

    const transformActions = (actions, parent, category) =>
      actions.map(action => ({
        controls_rel: {},
        command: action.command,
        did: action.did,
        parent: parent,
        value: action.value,
        title: title,
        description: description,
        type: 'action',
        notify: notify,
        smslist: smslist,
        accountable: notify,
        priority: priority,
        uniqId: action.uniqid,
        category: [category],
      }));

    const transformChildrenRecipes = recipes =>
      recipes.reduce((acc, recipe) => {
        const blockKey = `${recipe.block_type}/${recipe.execution_order}`;
        acc[blockKey] = {
          everyMinuteTopics: recipe.everyMinuteTopics || [],
          topics: recipe.everyMinuteTopics || [],
          recipe: recipe.formula,
          syncAsyncAction: 'True',
          startNow: 'True',
          maxDataNeeded: String(recipe.observation_time),
          rid: `${rid}/${blockKey}`,
          notRun: '[]',
          appType: 'recipe',
          actionAlert: transformActions(recipe.actions, recipe.params['#1'], recipe_category),
        };
        return acc;
      }, {});

    return {
      operation: 'recipeInit',
      scheduleInfo: [
        {
          rid: rid,
          id: rid,
          cron: '* * * * *',
          startdate: '2023-08-25 00:00',
          enddate: '2025-08-25 23:59',
        },
      ],
      recipeInfo: {
        runOn: this.recipeData.run_on,
        failsafe: '{}',
        dependentOnOthers: dependentOnOthers.map(String),
        controllers: [...new Set(controllers)].map(String),
        startNow: 'True',
        maxDataNeeded: '1',
        rid: rid,
        notRun: '[]',
        appType: app_type,
        actionAlert: [
          {
            category: [recipe_category],
            parent: 'parentRecipe',
            title: title,
            description: description,
            priority: priority,
            switchOff: String(switch_off),
            type: app_type,
            childrenRecipe: transformChildrenRecipes(children_recipes),
          },
        ],
      },
    };
  }
}

