const globalHelper = require('../../../utils/globalhelper');
const componentService = require('../../component/component.public');
const utils = require('../../../utils/recipe/utils');
const deviceService = require('../../device/device.public');
const { run_v2 } = require('googleapis');

class RecipeObservationParser {
  constructor() {
  }

  async parseFormula(formula, observableParams, operatorsDict, size) {
    try {
      const deviceParamList = await this.getDeviceParamList(observableParams);
      const {
        runOnServer,
        didArray,
        topicArray,
        updatedFormula
      } = this.createFormula(deviceParamList, observableParams, formula, size, operatorsDict);

      return {
        runOnServer,
        didArray,
        everyMinuteTopics: topicArray,
        formula: updatedFormula,
      };
    } catch (e) {
      throw new Error(`Error in RecipeObservationParser.parseFormula: ${e.message}`);
    }
  }

  async getDeviceParamList(observableParams) {
    const devicePromises = Object.values(observableParams)
      .map(deviceIdDotParam =>
        this.findDeviceForParam(...deviceIdDotParam.split('.'))
      );

    const deviceParamList = await Promise.all(devicePromises);
    return deviceParamList.filter(Boolean);
  }

  async findDeviceForParam(deviceId, param) {
    let runOnServer = false;
    try {
      const deviceIdType = this.getDeviceIdType(deviceId);
      return deviceIdType === 'compId' ? await this.processComponentId(deviceId, param) : [deviceId, param, runOnServer];
    } catch (error) {
      sails.log.error('Error in RecipeObservationParser.findDeviceForParam during Create/Update Recipe', error);
      throw new Error(`Failed to process Create/Update Recipe for deviceId: ${deviceId} and param: ${param}. Details: ${error?.message || 'unable to extract deviceId from param'}`);
    }
  }

  getDeviceIdType(deviceId) {
    return isNaN(parseInt(deviceId)) ? 'compId' : 'deviceId';
  }

  createFormula(deviceParamList, observableParams, formula, size, operatorsDict) {
    const topicArray = [];
    const didArray = [];
    formula += ' ';
    let runOnServer = false;

    deviceParamList.forEach(([did, param, willRunOnServer], i) => {
      const paramSymbol = Object.keys(observableParams)[i];
      runOnServer = willRunOnServer;

      const regexObservableParam = new RegExp(`\\${paramSymbol} `, 'gi');
      const countPatternInFormula = (formula.match(regexObservableParam) || []).length;
      const topic = `db/${did}/${param}/${size}`;

      topicArray.push(...Array(countPatternInFormula)
        .fill(topic));
      didArray.push(did);
      formula = formula.replace(regexObservableParam, `${did}.${param}.${size}`);
    });

    formula = this.replaceOperators(formula, operatorsDict);
    formula = this.replaceLogicalOperators(formula);
    formula = this.makeSpotQueries(formula, topicArray, size);

    return {
      runOnServer,
      didArray,
      topicArray,
      updatedFormula: formula
    };
  }

  replaceOperators(formula, operatorsDict) {
    for (const operatorSymbol in operatorsDict) {
      formula = utils.regexReplace(operatorSymbol, operatorsDict[operatorSymbol], formula);
    }
    return formula;
  }

  replaceLogicalOperators(formula) {
    return formula.replace(/ and /gi, ' & ')
      .replace(/ or /gi, ' | ');
  }

  async processComponentId(deviceId, param) {
    let runOnServer = false;
    let dataList,
      data,
      expression;
    const isProcess = globalHelper.isComponentOfTypeProcess(deviceId);

    const entity = await componentService.findOne({ deviceId });

    dataList = entity ? globalHelper.toArray(isProcess ? entity.rawParams : entity.data) : [];
    data = this.findDataFromList(dataList, param, isProcess);

    if (!data) return [deviceId, param, runOnServer];

    expression = data?.expression?.split('||');
    runOnServer = utils.haveCalculatedParameter(expression);

    if (runOnServer || ['wbt'].includes(param)) {
      return [deviceId, param, runOnServer];
    } else {
      const {
        newDeviceId,
        newParam
      } = this._extractDeviceParamFromExpression(data);
      return [newDeviceId || deviceId, newParam || param, runOnServer];
    }
  }

  findDataFromList(dataList, param, isProcess) {
    const key = isProcess ? 'abbr' : 'key';
    return dataList.find(item => globalHelper.toJson(item)[key] === param);
  }

  makeSpotQueries(formula, deviceToSubscribeArray, size) {
    const regex = /valueAt\s*\(\s*([\d\w_-]+)\.([a-zA-Z_]+)\.(\d+)\s*,\s*(\d+)\s*\)/gm;
    let match;

    while ((match = regex.exec(formula)) !== null) {
      if (match.index === regex.lastIndex) regex.lastIndex += 1;

      const [fullMatch, deviceId, param, , timestamp] = match;
      const spotTopic = `spot/${deviceId}/${param}/${timestamp}`;
      const dbTopic = `db/${deviceId}/${param}/${size}`;

      const index = deviceToSubscribeArray.indexOf(dbTopic);
      if (index !== -1) deviceToSubscribeArray[index] = spotTopic;

      formula = formula.replace(new RegExp(`valueAt\\s*\\(\\s*${deviceId}\\.${param}\\.${size}\\s*,\\s*${timestamp}\\s*\\)`), `valueAt (${deviceId}.${param}, ${timestamp})`);
    }

    return formula;
  }

  _extractDeviceParamFromExpression(data) {
    const deviceParamList = data?.expression
      ? Components.getDeviceParamListFromDataExpression(data.expression)
      : [];

    if (deviceParamList.length > 0) {
      const {
        deviceId: newDeviceId,
        param: newParam
      } = deviceParamList[0]; // Recipe does not support multiple devices in expression
      return {
        newDeviceId,
        newParam
      };
    }

    return {};
  }

  async getRunOnControllerId(deviceIds, params) {
    try {
      if (!params) return 'NA';

      const idToParameterMap = this.createIdToParameterMap(params);

      let deviceList = await this.populateDeviceList(deviceIds, idToParameterMap);

      const devices = await this.fetchDevices(deviceList.flat());
      const controllerIds = this.getControllerIds(devices);

      let runon = controllerIds.length > 0 ? utils.getMaxCountElement(controllerIds) : 'server';

      const controllers = await Promise.all(
        controllerIds.map(async (controller) => {
          try {
            const result = await this.verifyThirdPartyController(controller);
            const parsed = parseInt(result, 10);
            if (isNaN(parsed)) {
              sails.log.error(`Invalid controller ID: ${result}`);
              return null;
            }
            return parsed;
          } catch (error) {
            sails.log.error(`Error verifying third-party controller ${controller}: ${error.message}`);
            return null;
          }
        })
      );

      const validControllers = controllers.filter(controller => controller !== null);
      const runOn = await this.verifyThirdPartyController(runon);
      return {
        runOn,
        controllers: validControllers
      };
    } catch (e) {
      throw new Error(`Error in RecipeObservationParser.getRunOn: ${e.message || e}`);
    }
  }

  createIdToParameterMap(params) {
    return Object.values(params)
      .reduce((map, parameterInfo) => {
        const [componentId, parameter] = parameterInfo.split('.');
        if (!map[componentId]) {
          map[componentId] = [parameter];
        } else {
          map[componentId].push(parameter);
        }
        return map;
      }, {});
  }

  async populateDeviceList(deviceIds, idToParameterMap) {
    let deviceList = [];
    for (const deviceId of deviceIds) {
      if (this.isDeviceId(deviceId)) {
        deviceList.push(deviceId);
      } else {
        const componentParameters = idToParameterMap[deviceId];
        const deviceListForComponent = await componentService.getDeviceListFromDataParameter(deviceId, componentParameters);
        deviceList = [...deviceList, ...deviceListForComponent];
      }
    }
    return deviceList;
  }

  isDeviceId(deviceId) {
    return !isNaN(parseInt(deviceId));
  }

  async fetchDevices(deviceList) {
    const devicePromises = deviceList.map((deviceId) => deviceService.findOne(deviceId));
    return await Promise.all(devicePromises);
  }

  async verifyThirdPartyController(runon) {
    return await deviceService.getParentControllerIdIfThirdPartyController(runon);
  }

  getControllerIds(devices) {
    const controllerIds = [];
    let
      device;
    for (let i = 0; i < devices.length; i++) {
      device = devices[i];
      if (!device || device.deviceType === 'joulesense') {
        continue;
      } else {
        device.controllerId
          ? controllerIds.push(device.controllerId) : controllerIds.push(device.deviceId);
      }
    }
    return controllerIds;
  }
}

module.exports = RecipeObservationParser;
