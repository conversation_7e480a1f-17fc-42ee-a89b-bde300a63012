const moment = require('moment');

class CronBuilder {
  constructor(startDate, endDate) {
    this.startDate = moment(startDate)
      .format('YYYY-MM-DD');
    this.endDate = moment(endDate)
      .format('YYYY-MM-DD');
  }

  /**
   * Extracts time from an ISO date string
   * @param {string} date ISO formatted date
   * @returns {string} Time in HH:mm format
   */
  static extractTime(date) {
    return moment(date)
      .format('HH:mm');
  }

  /**
   * @description Extracts days of the week from a cron expression.
   * @param {string} cronExpression - The cron string.
   * @returns {string[]} - Array of days (e.g., ["mon", "wed", "fri"])
   */
  static extractDaysOfWeekFromCron(cronExpression) {
    const parts = cronExpression.trim().split(/\s+/);

    /**cron should have 5 parts: minute, hour, day of month, month, day of week*/
    if (parts.length !== 5) {
      return [];
    }

    const dayOfWeekPart = parts[4];

    return dayOfWeekPart.split(',').map(day => day.trim());
  }

  /**
   * @description Extracts cron expressions from an array of cron schedules.
   * @param {Array<[string, string, string]>} schedules - Each item is [startTime, endTime, cronExpression].
   * @returns {string[]} - Array of cron expressions.
   */
  extractCronsFromSchedules(schedules) {
    return schedules.map(item => item[2]);
  }

  /**
   * @description Generates cron expressions for daily execution.
   * @param {string} startTime HH:mm formatted start time
   * @param {string} endTime HH:mm formatted end time
   * @returns {Array} Array of cron schedules
   */
  createDailyCron(startTime, endTime) {
    let crons = this.parseTime(this.startDate, this.endDate, startTime, endTime);
    return crons.map(cron => [
      `${this.startDate} ${startTime}`,
      `${this.endDate} ${endTime}`,
      cron,
    ]);
  }

  /**
   * @description Generates cron expressions with no intervals.
   * @param {string} startTime HH:mm formatted start time
   * @param {string} endTime HH:mm formatted end time
   * @returns {Array} Array of cron schedules
   */
  createNoIntervalCron(startTime, endTime) {
    let crons = this.parseTime(this.startDate, this.endDate, startTime, endTime);
    return crons.map(cron => [
      `${this.startDate} ${startTime}`,
      `${this.endDate} ${endTime}`,
      cron,
    ]);
  }

  /**
   * @description Generates custom cron schedules for specific days.
   * @param {string} startTime HH:mm formatted start time
   * @param {string} endTime HH:mm formatted end time
   * @param {Array} customDays Array of days e.g. ['mon', 'fri']
   * @returns {Array} Array of cron schedules
   */
  createCustomCron(startTime, endTime, customDays) {
    let crons = this.parseTime(this.startDate, this.endDate, startTime, endTime);
    let daysStr = customDays.join(',');

    return crons.map(cron => {
      let tempCron = cron.split(' ');
      tempCron[4] = daysStr; // Update days field

      return [
        `${this.startDate} ${startTime}`,
        `${this.endDate} ${endTime}`,
        tempCron.join(' '),
      ];
    });
  }

  /**
   * @description Parses the given time range to create a cron schedule.
   * @param {string} startDate Start date (YYYY-MM-DD)
   * @param {string} endDate End date (YYYY-MM-DD)
   * @param {string} startTime Start time (HH:mm)
   * @param {string} endTime End time (HH:mm)
   * @returns {Array} Array of cron expressions
   */
  parseTime(startDate, endDate, startTime, endTime) {
    let start = moment(startDate, 'YYYY-MM-DD');
    let end = moment(endDate, 'YYYY-MM-DD');
    return this._generateCronExpressions(start, end, startTime, endTime);
  }

  /**
   * @description Merges compatible cron expressions into one line if possible.
   * @param {string[]} crons - An array of cron expressions.
   * @returns {string} Merged cron string or joined original crons.
   */
  mergeCronExpressions(crons) {
    if (!crons || crons.length === 0) return '';
    if (crons.length === 1) return crons[0];

    const parsedCrons = crons.map(expr => expr.trim()
      .split(' '));
    const allFiveFields = parsedCrons.every(parts => parts.length === 5);
    if (!allFiveFields) return crons.join('\n');

    const [_, __, baseDay, baseMonth, baseDow] = parsedCrons[0];
    const hourSet = new Set();

    for (const [min, hour, day, month, dow] of parsedCrons) {
      if (day !== baseDay || month !== baseMonth || dow !== baseDow) {
        return crons.join('\n');
      }

      const hours = hour.split(',')
        .flatMap(part => {
          if (part.includes('-')) {
            const [start, end] = part.split('-')
              .map(Number);
            return Array.from({ length: end - start + 1 }, (_, i) => start + i);
          }
          return [Number(part)];
        });

      hours.forEach(h => {
        if (!Number.isNaN(h)) hourSet.add(h);
      });
    }

    const mergedHours = Array.from(hourSet)
      .sort((a, b) => a - b);
    const compactHourRange = this._mergeNumberListToRange(mergedHours);

    return `* ${compactHourRange} ${baseDay} ${baseMonth} ${baseDow}`;
  }

  /**
   * @description Converts a sorted number list into cron-style ranges.
   * @example [0,1,2,4,5,7] => "0-2,4-5,7"
   */
  _mergeNumberListToRange(numbers) {
    if (!Array.isArray(numbers) || numbers.length === 0) return '';

    const result = [];
    let start = numbers[0];
    let end = numbers[0];

    for (let i = 1; i <= numbers.length; i++) {
      const current = numbers[i];
      if (current === end + 1) {
        end = current;
      } else {
        result.push(start === end ? `${start}` : `${start}-${end}`);
        start = current;
        end = current;
      }
    }

    return result.join(',');
  }

  /**
   * @description Generates cron expressions based on given time range.
   * @param {Object} start Moment.js start date object
   * @param {Object} end Moment.js end date object
   * @param {string} stime Start time (HH:mm)
   * @param {string} etime End time (HH:mm)
   * @returns {Array} Array of cron expressions
   */
  _generateCronExpressions(start, end, stime, etime) {
    let month = '*';
    let days = '*';
    let hour = '*';
    let min = '*';
    let minHour;

    try {
      const totalMonths = (end.month() + 1) - (start.month() + 1);
      const totalYear = end.year() - start.year();

      if (start > end) {
        return null;
      }

      if (stime === etime) {
        hour = parseInt(stime.split(':')[0]);
        min = parseInt(stime.split(':')[1]);
        minHour = [[min, hour]];
      } else {
        let [stHour, stMin] = stime.split(':')
          .map(Number);
        let [enHour, enMin] = etime.split(':')
          .map(Number);
        minHour = [];

        if (enMin === stMin && stMin === 0) {
          if (stHour === enHour) {
            minHour.push([enMin, `${stHour}`]);
          } else {
            minHour.push(['*', `${stHour}-${enHour - 1}`]);
          }
        } else if (stMin === 0 && enMin !== 0) {
          if (stHour !== enHour) minHour.push(['*', `${stHour}-${enHour - 1}`]);
          minHour.push([`0-${enMin}`, `${enHour}`]);
        } else if (stMin !== 0 && enMin === 0) {
          minHour.push([`${stMin}-59`, `${stHour}`]);
          if (stHour !== enHour && stHour + 1 !== enHour) minHour.push(['*', `${stHour + 1}-${enHour - 1}`]);
        } else if (stHour === enHour) {
          minHour.push([`${stMin}-${enMin}`, `${stHour}`]);
        } else {
          minHour.push([`${stMin}-59`, `${stHour}`]);
          if (stHour + 1 !== enHour) minHour.push(['*', `${stHour + 1}-${enHour - 1}`]);
          minHour.push([`0-${enMin}`, `${enHour}`]);
        }
      }

      if (totalYear === 0) {
        if (totalMonths === 0) {
          month = end.month() + 1;
          const totalDates = end.date() - start.date();
          if (totalDates === 0) {
            days = String(end.date());
          } else if (totalDates > 31) {
            return null;
          } else {
            days = Array.from({ length: totalDates + 1 }, (_, i) => i + start.date())
              .join(',');
          }
        } else if (totalMonths < 12) {
          month = Array.from({ length: totalMonths + 1 }, (_, i) => i + 1 + start.month())
            .join(',');
        } else if (totalMonths === 12) {
          month = '*';
        } else {
          return null;
        }
      }

      return minHour.map(([m, h]) => `${m} ${h} ${days} ${month} *`);
    } catch (e) {
      console.error(e);
      return null;
    }
  }
}

module.exports = CronBuilder;
