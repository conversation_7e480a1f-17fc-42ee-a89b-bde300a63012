// eslint-disable-next-line max-classes-per-file

const moment = require('moment-timezone');

class _SmartAlertSync {
  /**
   * @param {Object} recipe - ID of the recipe to sync
   */
  constructor(recipe) {
    this.recipe = recipe;
    this.alertCategory = 'recipe';
    this.templateCategory = 1;
    this.db = sails.getDatastore(process.env.SMART_ALERT_DB_NAME || 'vigilante');
    this.channels = ['email', 'sms', 'whatsapp'];
    this.smartAlertData = {};
    Object.freeze(this.recipe);
  }

  async sync() {
    const { rid } = this.recipe;
    return this.db.transaction(async (conn) => {
      const alertTemplateData = await this._syncAlertTemplate(conn, rid);
      Object.assign(this.smartAlertData, alertTemplateData);
      return this.smartAlertData;
    });
  }

  async _syncAlertTemplate(conn, rid) {
    const result = {};
    // noinspection JSUnresolvedVariable
    const alertTemplate = await AlertTemplate.findOne({
      observer_execution_ref_id: rid,
      status: 1,
    })
      .usingConnection(conn);

    if (!alertTemplate) {
      // eslint-disable-next-line no-return-await
      // noinspection JSUnresolvedVariable
      const alertTemplate = await AlertTemplate.create(this._buildTemplateFields())
        .fetch()
        .usingConnection(conn);

      const alertInventory = await AlertInventory.create(
        this._buildInventoryFieldsFromAlertTemplate(alertTemplate)
      )
        .fetch()
        .usingConnection(conn);
      result.alertTemplate = alertTemplate;
      result.alertInventory = alertInventory;
    } else {
      const updateFields = this._buildUpdateTemplateFields();
      // noinspection JSUnresolvedVariable
      const updatedAlertTemplate = AlertTemplate.updateOne({ id: alertTemplate.id })
        .set(updateFields)
        .usingConnection(conn);
      const updatedAlertInventory = await AlertInventory.updateOne({
        alert_template_ref_id: alertTemplate.id,
      })
        .set({
          name: alertTemplate.title,
          description: alertTemplate.description,
          severity: this._mapSeverity(this.recipe.priority),
        })
        .usingConnection(conn);
      result.alertTemplate = updatedAlertTemplate;
      result.alertInventory = updatedAlertInventory;
    }
    return result;
  }

  async delete() {
    return this.db.transaction(async (conn) => {
      // noinspection JSUnresolvedVariable
      const template = await AlertTemplate.findOne({
        observer_execution_ref_id: this.recipe.rid,
        status: 1,
      })
        .usingConnection(conn);

      if (!template) {
        return { message: 'No alert template found to delete' };
      }

      await NotificationMessageTemplate.update({
        alert_template_ref_id: template.id,
        status: 1
      })
        .set({ status: 0 })
        .usingConnection(conn);
      // noinspection JSUnresolvedVariable
      // eslint-disable-next-line no-undef
      await AlertSubscribers.update({
        alert_template_ref_id: template.id,
        status: 1
      })
        .set({ status: 0 })
        .usingConnection(conn);
      await AlertInventory.updateOne({ alert_template_ref_id: template.id })
        .set({ status: 0 })
        .usingConnection(conn);
      await AlertTemplate.updateOne({ id: template.id })
        .set({ status: 0 })
        .usingConnection(conn);

      return {
        message: 'Alert template and related data deleted',
        success: true
      };
    });
  }

  // ===== Internal Helpers =====
  _buildTemplateFields() {
    //Add the observation formula
    return {
      name: this.recipe.title,
      description: this.recipe.description,
      observer_source: this.recipe.app_type,
      observer_execution_ref_id: this.recipe.rid,
      template_category: this.templateCategory,
      alert_category: this.alertCategory,
      site_id: this.recipe.site_id,
      severity: this._mapSeverity(this.recipe.priority),
      status: 1,
      misc: {
        recipeType: this.recipe.recipe_type,
        runOn: this.recipe.run_on,
        appType: this.recipe.app_type,
        recipeCategory: this.recipe.recipe_category,
      },
      created_by: this.recipe.created_by,
    };
  }

  _buildUpdateTemplateFields() {
    return {
      name: this.recipe.title,
      description: this.recipe.description,
      severity: this._mapSeverity(this.recipe.priority),
      status: 1,
      misc: {
        recipeType: this.recipe.recipe_type,
        runOn: this.recipe.run_on,
        appType: this.recipe.app_type,
        recipeCategory: this.recipe.recipe_category,
      },
      updated_by: this.recipe.last_updated_by,
    };
  }

  _buildInventoryFields(childrenRecipe) {
    return {
      alert_template_ref_id: this.smartAlertData.id,
      name: this.recipe.title,
      description: childrenRecipe.description,
      siteId: this.recipe.site_id,
      severity: this._mapSeverity(this.recipe.priority),
      observer_execution_ref_id: childrenRecipe.uniqid,
      created_by: this.recipe.created_by,
    };
  }

  _buildInventoryFieldsFromAlertTemplate(alertTemplate) {
    return {
      alert_template_ref_id: alertTemplate.id,
      name: alertTemplate.name,
      description: alertTemplate.description,
      siteId: alertTemplate.site_id,
      severity: alertTemplate.severity,
      asset_type: this.recipe?.components_type?.[0],
      asset_id: extractParentFromParams(this.recipe?.children_recipes?.[0]?.params),
      observer_execution_ref_id: alertTemplate.observer_execution_ref_id,
      created_by: this.recipe.created_by,
    };
  }

  _buildNotificationFields(alertInventory) {
    return {
      alert_template_ref_id: alertInventory.alert_template_ref_id,
      alert_inventory_ref_id: alertInventory.id,
      channel: 'email',
      event_type: 'OCCURRED',
      title: this.recipe.title,
      description: alertInventory.description,
      status: 1,
      created_by: this.recipe.created_by,
    };
  }

  _mapSeverity(priority) {
    return (
      {
        1: 'low',
        2: 'medium',
        3: 'high',
        4: 'critical',
      }[priority] || 'medium'
    );
  }

  //Alert Inventory and Notification Template Sync
  async _syncChildrenRecipeWithAlertInventory(conn, alertTemplateId) {
    const orphanedInventoryIds = [];
    const staleChildrenRecipes = [];

    // noinspection JSUnresolvedVariable
    const smartAlertInventories = await AlertInventory.find({
      status: 1,
      alert_template_ref_id: alertTemplateId,
    })
      .usingConnection(conn);

    const childrenRecipeMap = this.recipe.children_recipes.reduce((acm, curr) => {
      const { uniqid } = curr;
      // eslint-disable-next-line no-param-reassign
      acm[uniqid] = curr;
      return acm;
    }, {});

    const activeAlertInventoryIds = new Set();
    for (const {
      observer_execution_ref_id: uniqid,
      id
    } of smartAlertInventories) {
      if (!childrenRecipeMap.hasOwnProperty(uniqid)) {
        orphanedInventoryIds.push(id);
      } else {
        staleChildrenRecipes.push({
          ...childrenRecipeMap[uniqid],
          alertInventoryId: id
        });
        activeAlertInventoryIds.add(uniqid);
      }
    }

    const childrenRecipesWithoutAlertInventory = this.recipe.children_recipes.filter(
      (it) => !activeAlertInventoryIds.has(it.uniqid)
    );
    await this._disableOrphanedAlertInventories(conn, orphanedInventoryIds);
    await this._syncStaleChildrenRecipes(conn, staleChildrenRecipes);
    await this._createAlertInventoriesForChildren(conn, childrenRecipesWithoutAlertInventory);
  }

  async _disableOrphanedAlertInventories(conn, orphanedInventoryIds) {
    // Soft delete alert inventories and corresponding templates
    if (orphanedInventoryIds.length) {
      // noinspection JSUnresolvedVariable
      await Promise.all(
        orphanedInventoryIds.map((id) =>
          AlertInventory.updateOne({ id })
            .set({ status: 0 })
            .usingConnection(conn)
        )
      );

      // noinspection JSUnresolvedVariable
      await Promise.all(
        orphanedInventoryIds.map((id) =>
          NotificationMessageTemplate.update({
            alert_inventory_ref_id: id,
            status: 1,
          })
            .set({ status: 0 })
            .usingConnection(conn)
        )
      );
    }
  }

  async _syncStaleChildrenRecipes(conn, staleChildrenRecipes) {
    if (staleChildrenRecipes.length) {
      await Promise.all(
        // noinspection JSUnresolvedVariable
        staleChildrenRecipes.map((childrenRecipe) =>
          AlertInventory.updateOne({ id: childrenRecipe.alertInventoryId })
            .set({
              name: this.recipe.title,
              description: childrenRecipe.description,
              severity: this._mapSeverity(this.recipe.priority),
            })
            .usingConnection(conn)
        )
      );

      await Promise.all(
        // noinspection JSUnresolvedVariable
        staleChildrenRecipes.map((childrenRecipe) =>
          NotificationMessageTemplate.updateOne({
            alert_inventory_ref_id: childrenRecipe.alertInventoryId,
            channel: 'email',
            event_type: 'OCCURRED',
          })
            .set({
              name: this.recipe.title,
              description: childrenRecipe.description,
            })
            .usingConnection(conn)
        )
      );
    }
  }

  async _createAlertInventoriesForChildren(conn, newChildrenRecipes) {
    if (!newChildrenRecipes.length) return [];
    const batchEntry = newChildrenRecipes.map(this._buildInventoryFields.bind(this));

    // noinspection JSUnresolvedVariable
    const createdAlertInventory = await AlertInventory.createEach(batchEntry)
      .fetch()
      .usingConnection(conn);

    const notificationRecord = createdAlertInventory.map(this._buildNotificationFields.bind(this));

    // noinspection JSUnresolvedVariable
    const notificationRecordEntry = await NotificationMessageTemplate.createEach(notificationRecord)
      .fetch()
      .usingConnection(conn);
    return notificationRecordEntry;
  }
}

class SmartAlertRecipeSyncService {
  static async build(siteId, recipeId) {
    const recipe = await RecipeInfo.findOne({
      status: 1,
      site_id: siteId,
      id: recipeId,
      is_recipe_template: 0,
      recipe_type: 'alert',
    })
      .populate('children_recipes', {
        where: { status: 1 },
      });
    if (_.isEmpty(recipe)) throw new Error('Recipe not found or invalid for syncing');
    return new _SmartAlertSync(recipe);
  }

  static async syncSmartAlertMetaDetails(siteId, recipeId, subscribers, escalatedTo, escalationInMin) {
    const recipe = await RecipeInfo.findOne({
      status: 1,
      site_id: siteId,
      id: recipeId,
      is_recipe_template: 0,
      recipe_type: 'alert',
    })
      .populate('children_recipes', {
        where: { status: 1 },
      });
    if (_.isEmpty(recipe)) throw new Error('Recipe not found or invalid for syncing');
    return new _SyncSubscriber(recipe, subscribers, siteId, escalatedTo, escalationInMin);
  }
}

class _SyncSubscriber {
  /**
   * @param {Object} recipe - The full recipe object including `rid`, `subscribers` etc.
   * @param subscribers - The alert subscribers
   * @param siteId
   * @param escalatedTo
   * @param escalationInMin
   */
  constructor(recipe, subscribers, siteId, escalatedTo, escalationInMin) {
    this.recipe = recipe;
    this.escalatedTo = escalatedTo;
    this.escalationInMin = escalationInMin;
    this.subscribers = subscribers;
    this.siteId = siteId;
    this.db = sails.getDatastore(process.env.SMART_ALERT_DB_NAME || 'vigilante');
  }

  async sync() {
    const { rid } = this.recipe;

    if (!rid || !Array.isArray(this.subscribers)) {
      throw new Error('Invalid recipe data for subscriber sync');
    }

    return this.db.transaction(async (conn) => {
      /**Find the active alertTemplate*/
      const alertTemplate = await AlertTemplate.findOne({
        observer_execution_ref_id: rid,
        status: 1,
      })
        .usingConnection(conn);

      if (!alertTemplate) {
        throw new Error(`No active AlertTemplate found for recipe rid: ${rid}`);
      }

      const alertTemplateId = alertTemplate.id;
      await AlertTemplate.update({
        id: Number(alertTemplateId),
        status: 1,
      })
        .set({
          escalation_time_in_min: this.escalationInMin,
          escalated_to: [this.escalatedTo]
        })
        .usingConnection(conn);

      const alert = await AlertInventory.findOne({
        alert_template_ref_id: alertTemplateId,
        siteId: this.siteId,
      });

      const alertId = alert.id;

      await AlertInventory.update({
        id: Number(alertId),
        status: 1,
      })
        .set({
          escalation_time_in_min: this.escalationInMin,
          escalated_to: [this.escalatedTo]
        })
        .usingConnection(conn);

      /**Soft delete existing subscribers*/
      await AlertSubscribers.update({
        alert_id: alertId,
        alert_template_ref_id: alertTemplateId,
        status: 1,
      })
        .set({
          status: 0,
          unsubscribed_at: moment.tz('UTC')
            .toDate(),
        })
        .usingConnection(conn);

      /**Prepare new subscriber entries**/
      const formattedSubscribers = this.subscribers.map((subscriber) => ({
        user_id: subscriber.userId,
        subscriber_id: subscriber.userId,
        subscribed_at: moment.tz('UTC')
          .toDate(),
        site_id: this.siteId,
        priority: this.recipe.priority,
        notify_on_email: subscriber.channels?.email,
        notify_on_sms: subscriber.channels?.sms,
        notify_on_whatsapp: subscriber.channels?.whatsapp,
        alert_id: alertId,
        alert_template_ref_id: alertTemplateId,
        status: 1,
      }));

      /**Insert new subscribers if present*/
      if (formattedSubscribers.length) {
        await AlertSubscribers.createEach(formattedSubscribers)
          .usingConnection(conn);
      }
      return {
        message: `Synced ${formattedSubscribers.length} subscribers for AlertTemplate ID: ${alertTemplateId}`,
        success: true,
      };
    });
  }
}

/**
 * @description Extracts the parent value (prefix before dot) from the first param entry.
 * @param params An object with key-value pairs like { "#1": "ash-tri_32.status" }
 * @returns The parent string (e.g., "ash-tri_32") or undefined if not found.
 */
function extractParentFromParams(params) {
  const firstParamValue = Object.values(params)[0];
  return firstParamValue?.split('.')?.[0];
}

module.exports = SmartAlertRecipeSyncService;
