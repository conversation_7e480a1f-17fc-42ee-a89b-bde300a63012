// api/services/PrometheusService.js

const uuid = require('uuid');
const { performance } = require('perf_hooks');
const client = require('prom-client');

const { collectDefaultMetrics } = client;

// Collect default metrics like CPU and memory usage, etc.
// collectDefaultMetrics();

module.exports = {
  uptimeGauge: new client.Gauge({
    name: 'nodejs_service_uptime_seconds',
    help: 'Uptime of the Node.js service in seconds',
  }),

  httpRequestDurationMicroseconds: new client.Histogram({
    name: 'http_request_duration_microseconds',
    help: 'Duration of HTTP requests in microseconds',
    labelNames: ['method', 'route', 'code'],
    buckets: [0, 10, 20, 50, 100, 200, 500, 1000, 2000, 5000, 10000, 20000, 50000, 100000]
  }),

  httpRequestCounter: new client.Counter({
    name: 'http_requests_total',
    help: 'Total number of HTTP requests',
    labelNames: ['method', 'route', 'code'],
  }),

  errorCounter: new client.Counter({
    name: 'http_errors_total',
    help: 'Total number of HTTP errors',
    labelNames: ['method', 'route', 'code'],
  }),

  apdexScore: new client.Gauge({
    name: 'apdex_score',
    help: 'Application Performance Index score',
    labelNames: ['route'],
  }),

  requestSizeHistogram: new client.Histogram({
    name: 'http_request_size_bytes',
    help: 'Size of HTTP requests in bytes',
    labelNames: ['method', 'route', 'code'],
    buckets: [100, 500, 1000, 5000, 10000, 50000, 100000] // Customize as needed
  }),

  responseSizeHistogram: new client.Histogram({
    name: 'http_response_size_bytes',
    help: 'Size of HTTP responses in bytes',
    labelNames: ['method', 'route', 'code'],
    buckets: [100, 500, 1000, 5000, 10000, 50000, 100000] // Customize as needed
  }),

  // Method to set uptime gauge
  setUptime(seconds) {
    this.uptimeGauge.set(seconds);
  },

  // Method to observe HTTP request duration
  observeRequestDuration(method, route, code, duration) {
    this.httpRequestDurationMicroseconds.labels(method, route, code).observe(duration);
    this.httpRequestCounter.labels(method, route, code).inc();
    if (code >= 500) {
      console.error(`API Error Log method=${method} route=${route} code=${code}`,)
      this.errorCounter.labels(method, route, code).inc();
    }
  },

  // Method to observe request size
  observeRequestSize(method, route, code, size) {
    this.requestSizeHistogram.labels(method, route, code).observe(size);
  },

  // Method to observe response size
  observeResponseSize(method, route, code, size) {
    this.responseSizeHistogram.labels(method, route, code).observe(size);
  },

  // Method to calculate Apdex score
  calculateApdexScore(route, satisfiedCount, toleratingCount, totalCount) {
    const apdexScore = (satisfiedCount + toleratingCount / 2) / totalCount;
    this.apdexScore.labels(route).set(apdexScore);
  },

  // Middleware to measure request duration
  requestDurationMiddleware: function (req, res, next) {
    req.headers['x-transaction-id'] = req.headers['x-transaction-id'] || uuid.v4();
    const start = performance.now();
    res.on('finish', () => {
      const durationInMicroseconds = performance.now() - start;
      PrometheusService.observeRequestDuration(req.method, req.route ? req.route.path : req.url, res.statusCode, durationInMicroseconds);
      const requestSize = parseInt(req.headers['content-length'] || '0', 10);
      const responseSize = parseInt(res.getHeader('Content-Length') || '0', 10);
      const reqLoggingObject = {
        method: req.method,
        body: req.body || {},
        queryParams: req.query || {},
        params: req.params || {},
        transaction_id: req.headers['x-transaction-id'],
        statusCode: req.res.statusCode,
        statusMessage: req.res.statusMessage,
        fromIP: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
        originalUrl: req.originalUrl,
        referer: req.headers.referer || '',
        ua: req.headers['user-agent'],
        responseTime: durationInMicroseconds,
        requestSize,
        responseSize
      }
      if (req.headers['x-amzn-trace-id']) {
        reqLoggingObject.x_amzn_trace_id = req.headers['x-amzn-trace-id'];
      }
      if (req.headers['x-amzn-request-id']) {
        reqLoggingObject.x_amzn_request_id = req.headers['x-amzn-request-id'];
      }
      delete reqLoggingObject.body.password;
      delete reqLoggingObject.params.password;
      delete reqLoggingObject.queryParams.password;
      sails.log.info(`ts=${new Date().toISOString()} requestId=${reqLoggingObject.transaction_id} x_amzn_trace_id=${reqLoggingObject?.x_amzn_trace_id} x_amzn_request_id=${reqLoggingObject?.x_amzn_request_id} method=${reqLoggingObject.method} route=${reqLoggingObject.originalUrl} statusCode=${reqLoggingObject.statusCode} responseTime=${reqLoggingObject.responseTime} requestSize=${reqLoggingObject.requestSize} responseSize=${reqLoggingObject.responseSize} body=${JSON.stringify(reqLoggingObject.body)} queryParams=${JSON.stringify(reqLoggingObject.queryParams)} params=${JSON.stringify(reqLoggingObject.params)} fromIP=${reqLoggingObject.fromIP} referer="${reqLoggingObject.referer}" ua="${reqLoggingObject.ua}"`);

      PrometheusService.observeRequestSize(req.method, req.route ? req.route.path : req.url, res.statusCode, requestSize);
      PrometheusService.observeResponseSize(req.method, req.route ? req.route.path : req.url, res.statusCode, responseSize);
    });
    next();
  }
};
