const recipeService = require("../../recipe/recipe.public");
const { priorityLevels } = require("./constants");

class RecipeAlertBuilder {
  constructor(rid, alertDetails) {
    if (!rid || _.isEmpty(alertDetails)) {
      throw new RecipeAlertBuilderException(
        "RID and alert details are required.",
        "VALIDATION_ERROR",
      );
    }
    this.rid = rid;
    this.alertCategory = "recipe";
    this.templateCategory = 2;
    this.alertDetails = alertDetails;
    this.priorityLevels = priorityLevels;
  }

  static async init(rid) {
    const alertRecipeConfig = await recipeService.findOne({ rid });

    if (!alertRecipeConfig) {
      throw new RecipeAlertBuilderException(
        `Recipe with ID ${rid} not found`,
        "ALERT_RECIPE_ID_NOT_FOUND",
      );
    }

    if (alertRecipeConfig?.actionable) {
      try {
        alertRecipeConfig.actionable = JSON.parse(alertRecipeConfig.actionable)?.[0];
      } catch (e) {
        throw new RecipeAlertBuilderException(
          `Invalid JSON format for actionable in recipe with ID ${rid}.`,
          "VALIDATION_ERROR",
        );
      }
    }

    if (alertRecipeConfig?.actionable?.type !== "alert") {
      throw new RecipeAlertBuilderException(
        `Recipe with ID ${rid} is not a recipe alert.`,
        "VALIDATION_ERROR",
      );
    }

    const schedules = alertRecipeConfig.scheduled ? JSON.parse(alertRecipeConfig.scheduled) : [];
    alertRecipeConfig.schedules = (
      await Promise.all(schedules.map(async (sid) => Schedules.findOne({ sid })))
    ).filter((schedule) => schedule !== undefined);


    return new RecipeAlertBuilder(rid, alertRecipeConfig);
  }

  async register() {
    return await this._saveOrUpdate("create");
  }

  async sync() {
    const existingAlert = await AlertTemplate.findOne({
      observer_execution_ref_id: this.rid,
      status: 1,
    });
    return await this._saveOrUpdate(existingAlert ? "update" : "create", existingAlert);
  }

  async _saveOrUpdate(actionType, existingAlert = null) {
    return await sails.getDatastore(process.env.SMART_ALERT_DB_NAME).transaction(async (dbTxn) => {
      if (actionType === "create") {
        return await this._create(dbTxn);
      } else if (actionType === "update") {
        return await this._update(existingAlert, dbTxn);
      }
    });
  }

  async _create(dbTxn) {
    const params = this.alertDetails;

    const alertTemplate = await this._saveAlertTemplate(params, dbTxn);
    const alertInventory = await this._saveAlertInventory(alertTemplate.id, params, dbTxn);
    const notificationTemplate = await this._saveNotificationMessageTemplate(
      alertTemplate.id,
      params,
      dbTxn,
    );
    const subscribers = this._saveSubscribers(alertInventory.id, params, dbTxn);

    await Promise.all([subscribers, notificationTemplate]);

    return {
      success: true,
      alertTemplate,
      alertInventory,
    };
  }

  async _update(existingAlert, dbTxn) {
    const params = this.alertDetails;

    const alertTemplate = await this._saveAlertTemplate(params, dbTxn, existingAlert.id);
    const alertInventory = await this._saveAlertInventory(
      alertTemplate.id,
      params,
      dbTxn,
      existingAlert.id,
    );
    const notificationTemplate = await this._saveNotificationMessageTemplate(
      alertTemplate.id,
      params,
      dbTxn,
    );
    const subscribers = this._saveSubscribers(alertInventory.id, params, dbTxn);

    await Promise.all([subscribers, notificationTemplate]);

    return {
      success: true,
      alertTemplate,
      alertInventory,
    };
  }

  /**
   * @description Create or update alert template based on optional ID.
   */
  async _saveAlertTemplate(params, dbTxn, templateId = null) {
    const templateFields = {
      ...this._getAlertTemplateFields(params),
      misc: this._buildRecipeAlertMetaData(),
    };

    if (templateId) {
      return await AlertTemplate.updateOne({ id: templateId })
        .set(templateFields)
        .usingConnection(dbTxn);
    } else {
      return await AlertTemplate.create(templateFields).usingConnection(dbTxn).fetch();
    }
  }

  /**
   * @description Create or update alert inventory based on optional ID.
   */
  async _saveAlertInventory(templateId, params, dbTxn, alertInventoryId = null) {
    const inventoryFields = this._getAlertInventoryFields(templateId, params);

    if (alertInventoryId) {
      return await AlertInventory.updateOne({ alert_template_ref_id: templateId })
        .set(inventoryFields)
        .usingConnection(dbTxn);
    } else {
      return await AlertInventory.create(inventoryFields).usingConnection(dbTxn).fetch();
    }
  }

  /**
   * @description Create or update the notification message template.
   */
  async _saveNotificationMessageTemplate(templateId, params, dbTxn) {
    const emailTemplateFields = this._getNotificationMessageTemplateFields(
      templateId,
      params,
      "email",
    );
    const smsTemplateFields = this._getNotificationMessageTemplateFields(templateId, params, "sms");

    if (!_.isEmpty(params.smslist)) {
      await this._saveNotificationChannel("sms", smsTemplateFields, dbTxn);
    }
    return await this._saveNotificationChannel("email", emailTemplateFields, dbTxn);
  }

  /**
   * @description Helper function to save a notification template for a specific channel.
   */
  async _saveNotificationChannel(channel, templateFields, dbTxn) {
    const existingTemplates = await NotificationMessageTemplate.find({
      alert_template_ref_id: templateFields.alert_template_ref_id,
      channel: channel,
      status: 1,
    });

    if (!_.isEmpty(existingTemplates)) {
      const updatePromises = existingTemplates.map((template) =>
        NotificationMessageTemplate.updateOne({ id: template?.id })
          .set(templateFields)
          .usingConnection(dbTxn),
      );
      return await Promise.all(updatePromises);
    } else {
      return await NotificationMessageTemplate.create(templateFields)
        .usingConnection(dbTxn)
        .fetch();
    }
  }

  /**
   * @description Register or update alert subscribers, linked to the AlertInventory.
   */
  async _saveSubscribers(alertId, params, dbTxn) {
    const subscribers = params?.actionable?.notify || [];

    const subscriberPromises = subscribers.map(async (subscriber) => {
      const existingSubscriber = await AlertSubscribers.findOne({
        alert_id: alertId,
        subscriber_id: subscriber,
        status: 1,
      });

      const subscriberFields = {
        alert_id: alertId,
        site_id: params.siteId,
        device_id: params?.actionable?.parent,
        subscriber_id: subscriber,
        notify_on_email: true,
        notify_on_whatsapp: params?.actionable?.notifyOnWhatsapp ?? false,
        notify_on_sms: params?.actionable?.smslist?.includes(subscriber),
        user_id: subscriber,
      };

      if (existingSubscriber) {
        return await AlertSubscribers.updateOne({
          alert_id: alertId,
          subscriber_id: subscriber,
        })
          .set(subscriberFields)
          .usingConnection(dbTxn);
      } else {
        return await AlertSubscribers.create(subscriberFields).usingConnection(dbTxn);
      }
    });

    return Promise.all(subscriberPromises);
  }

  _buildRecipeAlertMetaData() {
    const misc = {
      formula: this.alertDetails?.formula,
      componentsType: this.alertDetails?.componentsType
        ? JSON.parse(this.alertDetails?.componentsType)
        : [],
      recipelabel: this.alertDetails?.recipelabel
        ? JSON.parse(this.alertDetails?.recipelabel)
        : null,
      runInterval: this.alertDetails?.runInterval,
      runOn: this.alertDetails?.runOn,
      involvedUsers: this.alertDetails?.actionable?.notify,
      schedules: [],
    };

    for (const schedule of this.alertDetails?.schedules) {
      misc.schedules.push({
        sid: schedule.sid,
        repeat_type: schedule?.repeat_type,
        timestamp: schedule?.ts ? JSON.parse(schedule?.ts) : [],
        isDeployed: schedule?.isDeployed,
      });
    }
    return misc;
  }

  _getAlertTemplateFields(params) {
    return {
      name: params?.label || "Recipe Alert Template",
      description: params?.actionable?.description ?? null,
      observer_source: this.alertCategory,
      observer_execution_ref_id: this.rid,
      template_category: this.templateCategory,
      alert_category: this.alertCategory,
      severity: this.priorityLevels[params?.actionable?.priority] ?? 'low',
      expression: params?.formula,
      created_by: params?.user,
    };
  }

  _getAlertInventoryFields(templateId, params) {
    return {
      alert_template_ref_id: templateId,
      name: params?.label || "Recipe Alert Template",
      description: params?.actionable?.description ?? null,
      siteId: params.siteId,
      severity: this.priorityLevels[params?.actionable?.priority],
      asset_id: params?.actionable?.parent,
      asset_type: params?.neo,
      escalation_time_in_min: params?.escalationTimeInMin ?? 30,
      escalated_to: [params?.user],
      created_by: params?.user,
    };
  }

  _getNotificationMessageTemplateFields(templateId, params, channel) {
    return {
      alert_template_ref_id: templateId,
      channel: channel,
      title: params?.actionable?.title || "Recipe Alert",
      description: params?.actionable?.description ?? null,
      template_id: params?.templateId ?? "",
      created_by: params?.user,
      event_type: 'OCCURRED'
    };
  }

  async delete() {
    return await sails.getDatastore(process.env.SMART_ALERT_DB_NAME)
      .transaction(async (dbTxn) => {
        const alertTemplate = await AlertTemplate.findOne({
          observer_execution_ref_id: this.rid,
          status: 1,
        })
          .usingConnection(dbTxn);

        if (!alertTemplate) {
          return {
            success: true,
            message: `No active alert template found for rid: ${this.rid}. Possibly already deleted.`,
            deleted: null,
          };
        }

        const alertInventories = await AlertInventory.find({
          alert_template_ref_id: alertTemplate.id,
          status: 1,
        })
          .usingConnection(dbTxn);

        const inventoryIds = alertInventories.map((inv) => inv.id);

        await AlertTemplate.updateOne({ id: alertTemplate.id })
          .set({ status: 0 })
          .usingConnection(dbTxn);

        if (!_.isEmpty(inventoryIds)) {
          await AlertInventory.update({ id: inventoryIds })
            .set({ status: 0 })
            .usingConnection(dbTxn);

          await AlertSubscribers.update({
            alert_id: inventoryIds,
            status: 1
          })
            .set({ status: 0 })
            .usingConnection(dbTxn);
        }

        await NotificationMessageTemplate.update({
          alert_template_ref_id: alertTemplate.id,
          status: 1,
        })
          .set({ status: 0 })
          .usingConnection(dbTxn);

        return {
          success: true,
          message: `Soft-deleted Smart Alert entities for rid: ${this.rid}`,
          deleted: {
            alertTemplateId: alertTemplate.id,
            alertInventoryIds: inventoryIds,
          },
        };
      });
  }
}

class RecipeAlertBuilderException extends Error {
  constructor(message, code = "RECIPE_ALERT_ERROR") {
    super(message);
    this.name = this.constructor.name;
    this.code = code;
    Error.captureStackTrace(this, this.constructor);
  }

  static validationError(message) {
    return new RecipeAlertBuilderException(message, "VALIDATION_ERROR");
  }

  static dbError(message) {
    return new RecipeAlertBuilderException(message, "DATABASE_ERROR");
  }

  static syncError(message) {
    return new RecipeAlertBuilderException(message, "SYNC_ERROR");
  }
}

module.exports = RecipeAlertBuilder;
