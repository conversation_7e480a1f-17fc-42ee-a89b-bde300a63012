/* eslint-disable no-undef */
module.exports = {

    /**
     * graph module private functions
     */
    create: async (graph) => {
        return Graphs.create(graph);
    },
    find: async (searchGraph) => {
        // graph.find
        return Graphs.find(searchGraph);
    },
    findOne: async (searchParams) => {
        // graph.findOne
        return Graphs.findOne(searchParams);
    },
    update: async (searchGraph, updateValue) => {
        return Graphs.update(searchGraph, updateValue);
    },
    delete: async (searchGraph) => {
        return Graphs.destroy(searchGraph);
    },
};
