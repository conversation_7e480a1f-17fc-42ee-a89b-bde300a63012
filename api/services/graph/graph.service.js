/* eslint-disable no-undef */
const { getMeasurementByAssetIdForInflux } = require('../../utils/globalhelper');
const { validateTimeWindow } = require('../../utils/graph/graph.util');
const graph = require('./graph.private');
const influxEnterpriseService = require('../../services/influx/enterprise/influx.public')
const moment = require('moment');
module.exports = {
    create: graph.create,
    find: graph.find,
    findOne: graph.findOne,
    update: graph.update,
    delete: graph.delete,
    fetchDeviceParamLineGraphs
};

async function fetchDeviceParamLineGraphs({
  siteId,
  startTime,
  endTime,
  deviceId,
  abbr
}) {
  const MAX_DAYS = 7;  // Maximum allowed days per query
  const timeZoneOffset = await sails.helpers.getSiteTimezone.with({siteId, timezoneFormat:'utcOffsetInMinute'})
  validateTimeWindow(startTime, endTime, timeZoneOffset);
  const start = moment(startTime).utcOffset(timeZoneOffset);
  const end = moment(endTime).utcOffset(timeZoneOffset);
  const durationDays = end.diff(start, "days");

  if (durationDays <= MAX_DAYS) {
      return await fetchRecords({deviceId, siteId, abbr, startTime, endTime});
  }

  // If more than 7 days, split into parallel calls
  let promises = [];
  let tempStart = start.clone();

  while (tempStart.isBefore(end)) {
      let tempEnd = tempStart.clone().add(MAX_DAYS, "days");
      if (tempEnd.isAfter(end)) {
          tempEnd = end.clone();
      }

      let partitionStartTime = moment(tempStart).format();
      let partitionEndTime = moment(tempEnd).format();
      promises.push(
        fetchRecords({deviceId, siteId, abbr, startTime:partitionStartTime, endTime: partitionEndTime})
      );
      tempStart = tempEnd.clone();
  }

  const results = await Promise.all(promises);

  return _.flatten(results);


  async function fetchRecords({ siteId, deviceId, startTime: startTimestamp, endTime: endTimestamp, abbr}) {
    const measurement = getMeasurementByAssetIdForInflux(deviceId);
    const identifierKey = measurement == 'device' ? "deviceId" : "componentId";
    const query = `
      SELECT  LAST("${abbr}") AS "${abbr}"
      FROM "device_component"."autogen"."${measurement}"
      WHERE "${identifierKey}" = '${deviceId}' AND "siteId" = '${siteId}' AND time > '${startTimestamp}' AND time <= '${endTimestamp}'
      GROUP BY time(1m), "${abbr}"
      FILL(null)
      ORDER BY time ASC
    `;
    const results = await influxEnterpriseService.runInfluxQLQuery(query);
    if (_.isEmpty(results)) return [];
    const formattedResults = results.reduce((acc, data) => {
      const timeInUnix = (parseInt(data.time)/1000000)
      acc.push([
          timeInUnix,
          parseFloat(data[abbr])
      ]);
      return acc;
    }, []);
    return formattedResults;
  }
}