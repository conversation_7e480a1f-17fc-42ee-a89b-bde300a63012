/* eslint-disable no-undef */
module.exports = {

    /**
     * Processes module private functions
     */
   
    
    create: async (params) => {
        return Processes.create(params);
    },
    find: async (searchParams) => {
        // Processes.find
        return Processes.find(searchParams);
    },
    findOne: async (searchParams) => {
        // Processes.findone
        return Processes.findOne(searchParams);
    },
    update: async (searchParams, updateValue) => {
        return Processes.update(searchParams, updateValue);
    },
    delete: async (searchParams) => {
        return Processes.destroy(searchParams);
    },
};