const parameterservice = require('./parameter.service');


module.exports = {
  findOne : parameterservice.findOne,
  find : parameterservice.find,
  create: parameterservice.create,
  getFilteredParams: parameterservice.getFilteredParams,
  getParamsDauAndParamgroup: parameterservice.getParamsDauAndParamgroup,
  getParametersBySiteId: parameterservice.getParametersBySiteId,
  getDeviceParameter: async function (siteId, deviceId) {
   const parameters = await parameterservice.findWithoutAbbr(siteId, deviceId)
   const dataParam =  parameters.map((parameter) => {
    return {
      abbr: parameter.abbr,
      name: parameter.displayName,
    }
   }).filter(Boolean)
    .sort((a, b) => a.name.localeCompare(b.name))
   return {
      dataParam,
      controlPara:[]
    }
  },
  destroyWithoutAbbr: parameterservice.destroyWithoutAbbr,
}

