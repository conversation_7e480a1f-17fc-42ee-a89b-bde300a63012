const parameterservice = require('./parameter.private');
const utils = require('../../utils/parameter/utils');
const componetService = require('../component/component.public');
const globalhelper = require('../../utils/globalhelper');
const deviceTypeService = require("../devicetype/devicetype.public");
const dynamokeystorePublic = require('../dynamokeystore/dynamokeystore.public');
const Flaverr = require('flaverr');

module.exports = {
  find: parameterservice.find,
  findOne: parameterservice.findOne,
  create: parameterservice.create,
  update: parameterservice.update,
  delete: parameterservice.delete,
  findWithoutAbbr: parameterservice.findWithoutAbbr,
  destroyWithoutAbbr: parameterservice.destroyWithoutAbbr,
  getParametersBySiteId : parameterservice.getParametersBySiteId,
  /**
   * For each configured parameter in the site siteID, create mapping of
   * deviceId to PortConfig. PortConfig is either for 'data' related port
   * or for 'command'. So {deviceId : {PORT-CONFIG} }
   * @param {string} siteId site id
   * @param {object} devicePortConfigMap 'device_abbr' to 'port config' map with controlDevice
   * to its config map
   */
  getFilteredParams: async function (siteId, devicePortConfigMap) {

    let parameters = await parameterservice.find({ siteId });
    // let filteredParameters = parameters.map(parameter => utils.filterParams(parameter, devicePortConfigMap));
    let filteredParameters = parameters.reduce((acc, parameter, index) => {
      let { deviceId, abbr } = parameter;
      let filteredparam = utils.filterParams(parameter, devicePortConfigMap);
      if (!acc[deviceId]) acc[deviceId] = {};
      acc[deviceId][abbr] = filteredparam;
      return acc;
    }, {});
    return filteredParameters;
  },

  /**
   * Get Dau and paramGroup of asked 'param' configured in 'deviceId' device at 'siteId' site
   * @param {string} siteId site id the device belong to
   * @param {string} deviceId unique device/componentId of the device param belong
   * @param {string} param parameter to get dau and paramGroup of
   */
  getParamsDauAndParamgroup: async function (siteId, deviceId, param) {

    let parameterInfo;

    if (globalhelper.isComponent(deviceId)) {
      let component = await componetService.findOne({ siteId, deviceId });
      let parameters = component.data ? component.data : [];
      parameterInfo = parameters.find(parameter =>
        parameter['key'] === param
      );
    } else {
      let deviceIdAbbr = `${deviceId}_${param}`;
      parameterInfo = await this.findOne({ siteId, 'deviceId_abbr': deviceIdAbbr });
    }

    if (parameterInfo === undefined) {
      return { paramGroup: 'NA', dau: 'unitless' };
    } else {
      let { paramGroup, dau } = parameterInfo;
      return { paramGroup, dau };
    }
  },
  /**
	 * @function addDeviceParameters
	 * @param {string} deviceType
	 * @param {string} driverType
	 * @param {string} deviceId
   * @param {string} siteId
   * @param {object} driverObject If driver object is passed, it doesn't query the deviceTypes table again.
	 * @description Method adds parameters of a new device based on its devicetype and driver type. It will link the parameters of that device type with the deviceId that is created
	 * @returns {Promise} Boolean: true if successful else false
	 */
  addDeviceParameters: async function(deviceType, driverType, deviceId, siteId, driverObject){
    try {
      let driver;
      if(!driverObject){
        driver = await deviceTypeService.findOne({ deviceType, driverType });
        if (!driver) {
          sails.log.error("[ parameter.service >> addDeviceParameters ] Device type does not exist in db");
          throw Flaverr('E_NOT_FOUND', new Error("Driver not found"));
        }
      } else driver = driverObject;
			let { parameters } = driver;

			//append deviceId and flatten filter object
			let paramList = parameters.map(parameter => {
				let paramObj = JSON.parse(JSON.stringify(parameter));
        Object.keys(paramObj).forEach((key) =>  {
          if (paramObj[key]  === null || paramObj[key] === 'null') {
            delete paramObj[key];
          }
        })
				let { filter, abbr, type, properties } = paramObj;
				paramObj["deviceId"] = deviceId;
				if (typeof filter != "undefined") {
					Object.keys(filter).forEach((key) => {
						let val = filter[key];
						paramObj[`filter_${key}`] = val;
					});
				}
				if ( properties){
					Object.keys(properties).forEach((key)=>{
            if (properties[key] !== 'null' && properties[key] !== null)  {
              paramObj[key] = properties[key] ;
            }
					});
				}
				paramObj.siteId=siteId;
				paramObj.mode = "jt";
				paramObj.utilityType = type;
				paramObj.deviceId_abbr=`${deviceId}_${abbr}`;
				paramObj.inheritedFrom = `${deviceType}@${driverType}`;
				delete paramObj.filter;
				delete paramObj.properties;
				return paramObj;
			});
			// console.log(JSON.stringify(paramList));
      await parameterservice.create(paramList);
      await dynamokeystorePublic.updateConfigTs(siteId);
			return true;
		} catch (e) {
			sails.log.error(e);
			throw e;
		}
  },
  addDeviceParametersWithCustomValues: async function(deviceType, driverType, deviceId, siteId, driverObject, customParameterList){
    try {
      let driver;
      if(!driverObject){
        driver = await deviceTypeService.findOne({ deviceType, driverType });
        if (!driver) {
          sails.log.error("[ parameter.service >> addDeviceParameters ] Device type does not exist in db");
          throw Flaverr('E_NOT_FOUND', new Error("Driver not found"));
        }
      } else driver = driverObject;
      let { parameters } = driver;

			let paramList = parameters.map(utils.flattenDeviceParameterIterator(deviceType, driverType, deviceId, siteId));;
      let abbrParamMap = paramList.reduce((accumulator, currentValue) => {
        let { abbr } = currentValue;
        accumulator[abbr] = currentValue;
        return accumulator;
      }, {});

      let modifiedParamList = customParameterList.map(parameterValues => {
        const { displayName, address, assetId, paramType, componentId } = parameterValues;
        let paramTemplate = JSON.parse(JSON.stringify(abbrParamMap[paramType]));
        let newAbbr = `${paramTemplate.abbr}_${assetId}`
        paramTemplate.displayName = displayName;
        paramTemplate.address = address;
        paramTemplate.abbr = newAbbr;
        paramTemplate.deviceId_abbr = `${deviceId}_${newAbbr}`;
        paramTemplate["assetId"] = assetId;
        paramTemplate["paramType"] = paramType;
        if(componentId) paramTemplate["componentId"] = componentId;
        return paramTemplate;
      });
      await parameterservice.create(modifiedParamList);
      await dynamokeystorePublic.updateConfigTs(siteId);
			return modifiedParamList;

    } catch (error) {
      sails.log.error("[addDeviceParametersWithCustomValues] Error while creating device parameters with custom values!");
      sails.log.error(error);
      throw error;
    }
  }
};
