/* eslint-disable no-undef */
const AWS = require("aws-sdk");
const dynamoDBClient = require("../dynamoClient/daxClient");


const dynamoDb = new AWS.DynamoDB({
  region: process.env.REGION,
});


const getParametersBySiteId = async (siteId) => _getParametersBySiteId(siteId, null, []);

/**
 * @description Takes siteId and only the deviceId. Each entry is actually in the format: deviceId_abbr.
 * This skips the requirements of knowing individual abbr.
 * @param {string} siteId siteId
 * @param {string} deviceId deviceId
 * @returns {Promise} Array: List of parameters starting with deviceId
 */
const findWithoutAbbr = async function (siteId, deviceId) {
  const dynamoDbDocClient = new AWS.DynamoDB.DocumentClient({
    region: process.env.REGION,
  });
  let params = {
    TableName: 'parameters',
    KeyConditionExpression: '#siteId = :siteId and begins_with(#deviceId_abbr, :deviceId)',
    ExpressionAttributeNames: {
      "#siteId": "siteId",
      '#deviceId_abbr': 'deviceId_abbr',
    },
    ExpressionAttributeValues: {
      ":siteId": siteId,
      ":deviceId": String(deviceId)
    },
  };
  let parameters=[];
  try {
    const queryResult = await dynamoDbDocClient.query(params).promise();
    parameters = queryResult.Items;
  } catch (error) {
    sails.log.error("Error quering parameters from Dynamo using aws-sdk!");
    sails.log.error(error);
    // TODOI: Figure out if throwing an error is required.
    // throw new Error("Error fetching drivers from Dynamo!");
  }
  return parameters;
}

const getDeviceParameter = async function (siteId, deviceId) {
  const parameters = await findWithoutAbbr(siteId, deviceId)
  const dataParam =  parameters.map((parameter) => {
    return {
      abbr: parameter.abbr,
      name: parameter.displayName,
    }
  }).filter(Boolean)
    .sort((a, b) => a.name.localeCompare(b.name))
  return {
    dataParam,
    controlPara:[]
  }
}

module.exports = {

  /**
   * Parameter module private functions
   */
  find: async (searchParams) => {
    // Parameters.find
    return Parameters.find(searchParams).meta({ skipRecordVerification: true });
  },
  findOne: async (searchParams) => {
    // Parameters.findone
    return Parameters.findOne(searchParams);
  },
  update: async (searchParams, updateValue) => {
    return Parameters.update(searchParams, updateValue);
  },
  create: async (searchParams) => {
    if (Array.isArray(searchParams)) {
      const createList = searchParams.map(parameter=>Parameters.create(parameter));
      return Promise.all(createList);
    } else {
      return Parameters.create(searchParams);
    }
  },
  delete: async (searchParams) => {
    if (Array.isArray(searchParams)) {
      const destroyList = searchParams.map(parameter => Parameters.destroy(parameter))
      return Promise.all(destroyList);
    } else {
      return Parameters.destroy(searchParams);
    }
  },
  findWithoutAbbr: findWithoutAbbr,
  /**
   * @description Takes siteId and only the deviceId. Each entry is actually in the format: deviceId_abbr.
   * This skips the requirements of knowing individual abbrs. Deletes all items that match the criteria.
   * @param {string} siteId siteId
   * @param {string} deviceId deviceId
   * @returns {Promise}
   */
  destroyWithoutAbbr: async(siteId, deviceId) => {
    let params = {
      RequestItems: {
        "parameters": []
      }
    };

    let parameters = await findWithoutAbbr(siteId, deviceId);
    if(parameters.length == 0){
      sails.log.silly("[parameterService >> destroyWithoutAbbr] No parameters found to be deleted. Skipping execution.");
      return true;
    }
    // Iterating over parameters to create batch delete param object.
    let deleteQueries = [];
    let counter = 0;
    for(let i=0; i<parameters.length; i++){
      let paramObject = parameters[i];
      let deleteObject = {
        DeleteRequest: {
          Key:{
            "siteId": {
              "S": siteId
            },
            "deviceId_abbr": {
              "S": paramObject["deviceId_abbr"]
            }
          }
        }
      };
      params.RequestItems.parameters.push(deleteObject);
      counter++;
      if(counter == 25) { // Single batch request can take a max of 25 entries.
        const $queryResult = dynamoDb.batchWriteItem(params).promise();
        deleteQueries.push($queryResult)
        counter = 1;
        params.RequestItems.parameters = [];
      }
    }
    const $queryResult = dynamoDb.batchWriteItem(params).promise();
    deleteQueries.push($queryResult)
    try {
      const queryResult = await Promise.all(deleteQueries);
    } catch (error) {
      sails.log.error("Error deletings parameters from Dynamo using aws-sdk!");
      sails.log.error(error);
      throw new Error("Error deleting some paramerters from Dynamo!");
    }
    return true;
  },
  getDeviceParameter: getDeviceParameter,
  getParametersBySiteId,
};

/**Recursive Function to query dynamoDB Dax to retrieve all parameters by siteId with pagination*/
async function _getParametersBySiteId(siteId, lastEvaluatedKey = null, items = []) {
  const params = {
    TableName: "parameters",
    KeyConditionExpression: "#siteId = :siteId",
    ExpressionAttributeNames: {
      "#siteId": "siteId",
    },
    ExpressionAttributeValues: {
      ":siteId": siteId,
    },
  };

  if (lastEvaluatedKey) {
    params.ExclusiveStartKey = lastEvaluatedKey;
  }

  try {
    const queryResult = await dynamoDBClient.query(params).promise();
    const allItems = items.concat(queryResult?.Items || []);

    if (queryResult.LastEvaluatedKey) {
      return _getParametersBySiteId(siteId, queryResult.LastEvaluatedKey, allItems);
    }

    return allItems;
  } catch (error) {
    sails.log.error(
      `Error querying components from DynamoDB using aws-sdk: getParametersBySiteId ${siteId}`,
      error,
    );
    throw error;
  }
}
