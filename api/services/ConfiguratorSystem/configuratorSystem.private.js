module.exports = {
  create: async (params) => {
    return ConfiguratorSystem.create(params);
  },
  find: async (searchParams) => {
    return ConfiguratorSystem.find(searchParams);
  },
  update: async (searchParams, updateValue) => {
    return ConfiguratorSystem.update(searchParams, updateValue);
  },
  delete: async (searchParams) => {
    return ConfiguratorSystem.destroy(searchParams);
  },
  findOne: async (searchParams) => {
    return ConfiguratorSystem.findOne(searchParams);
  }
};
