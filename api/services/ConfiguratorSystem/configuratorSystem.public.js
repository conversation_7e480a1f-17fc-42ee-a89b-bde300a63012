/* eslint-disable max-len */
const systemCategoryService = require('./configuratorSystem.service');
module.exports = {
  create: systemCategoryService.create,
  find: systemCategoryService.find,
  update: systemCategoryService.update,
  delete: systemCategoryService.delete,
  isSystemIsInEditableMode: systemCategoryService.isSystemInEditableMode.bind(systemCategoryService),
  isSystemExist: systemCategoryService.isSystemExist.bind(systemCategoryService),
};
