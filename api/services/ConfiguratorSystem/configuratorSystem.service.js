const moment = require("moment-timezone");
const flaverr = require("flaverr");
const systemService = require("./configuratorSystem.private");
const systemCategoryService = require("../configuratorSystemCategory/configuratorSystemCategory.service");
const s3Service = require("../s3/s3.public");
const siteService = require("../site/site.public");
const utils = require("../../utils/configuratorTaggedDevices");
const ALLOWED_SVG_EXTENSIONS = ["svg", "ico"];
const ConfiguratorNode = require("../ConfiguratorNode/ConfiguratorNode");
const { getComponentConfig } = require("../component/component.service");
const deviceService = require("../device/device.service");
const { throwExceptionInvalidPageType } = require("../../utils/configuratorSystem/ErrorHandler");
const { PAGE_TYPE } = require("../configuratorPage/configuratorPage.service");
const {
  throwExceptionInvalidConfiguratorSubsystemPage,
  throwExceptionInvalidSystemMappedSite,
  throwExceptionInvalidConfiguratorSystem,
} = require("../../utils/configuratorTaggedDevices/ErrorHandler");
const { isValidSvgContent, isValidIcoContent } = require("../../utils/globalhelper");
const fs = require("fs");

moment.tz.add("Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6");
moment.tz.setDefault("Asia/Kolkata");

const fetchComponentConfigByComponentIds = async (componentList) => {
  const componentConfigMap = new Map();
  let $componentConfigPromise = componentList.map(getComponentConfig);
  const componentConfigs = await Promise.allSettled($componentConfigPromise);
  for (const { status, reason, value } of componentConfigs) {
    if (status === "rejected") {
      //TODO: Throw error
      sails.log.error(`${reason.code} - ${reason.message}`);
      return;
    }
    const { deviceId } = value;
    componentConfigMap.set(deviceId, value);
  }
  return componentConfigMap;
};

const isSystemHasPages = async (systemId) => {
  const pages = await ConfiguratorPages.find({
    subsystemId: systemId,
    status: 1,
  });
  return !_.isEmpty(pages) ? true : false;
};
module.exports = {
  create: systemService.create,
  find: systemService.find,
  update: systemService.update,
  delete: systemService.delete,
  registerSystem: async (param, userId) => {
    const { systemName, siteId, description, systemCategoryId, iconFile } = param;

    const isSiteValid = await siteService.isValidSite(siteId);
    if (!isSiteValid) {
      throw flaverr("E_SITE_NOT_FOUND", new Error("Site not found"));
    }

    const isValidSystemCategory = await systemCategoryService.isValidSystemCategory(
      systemCategoryId
    );
    if (!isValidSystemCategory) {
      throw flaverr("E_SYSTEM_CATEGORY_NOT_FOUND", new Error("System category does not exist"));
    }
    let iconFileName = null;
    if (iconFile) {
      let { filename: fileName, fd: fileLocation, type: fileType } = iconFile;
      const fileExtension = fileName.split(".").pop().toLowerCase();
      if (!ALLOWED_SVG_EXTENSIONS.includes(fileExtension)) {
        throw flaverr(
          "E_INVALID_FILE",
          new Error(`File should be only in these ${ALLOWED_SVG_EXTENSIONS.join(",")} format`)
        );
      }

      const fileContent = await fs.promises.readFile(
        fileLocation,
        fileExtension === "ico" ? null : "utf-8"
      );
      if (
        (fileExtension === "svg" && !isValidSvgContent(fileContent)) ||
        (fileExtension === "ico" && !isValidIcoContent(fileContent))
      ) {
        throw flaverr(
          "E_INVALID_FILE_CONTENT",
          new Error("The content of the file is invalid or not an allowed format")
        );
      }

      const fileUID = `${fileName.split(".")[0]}_${fileLocation.split("/").pop()}`;
      const { Key } = await s3Service.upload(
        fileLocation,
        fileUID,
        fileType,
        process.env.CONFIGURATOR_BUCKET
      );
      if (!Key) {
        throw flaverr("E_FILE_NOT_UPLOADED", new Error("File not uploaded please try again later"));
      }
      iconFileName = Key;
    }

    // noinspection JSUnresolvedVariable
    const createSystemObj = {
      name: systemName,
      site_id: siteId,
      description,
      system_id: systemCategoryId,
      created_by: userId,
    };
    if (iconFileName) {
      createSystemObj.icon = iconFileName;
    }
    const _System = await ConfiguratorSystem.create(createSystemObj).fetch();
    return {
      systemId: _System.id,
      icon:
        (iconFileName &&
          (await s3Service.getSignedUrl(process.env.CONFIGURATOR_BUCKET, iconFileName))) ||
        null,
      description: _System.description,
      systemName: _System.name,
      systemCategoryId: _System.system_id,
    };
  },
  fetchAllSystems: async function (siteId) {
    const isSiteValid = await siteService.isValidSite(siteId);
    if (!isSiteValid) {
      throw flaverr("E_SITE_NOT_FOUND", new Error("Site not found"));
    }

    let query = ` SELECT csc.id AS "systemCategoryId", csc.name AS "systemCategoryName", cs.id AS "systemId", cs.icon, cs.name AS "systemName", cs.description, cs.status, cs.svg_path as "svgPath", cs."updatedAt" FROM configurator_system_category AS csc FULL JOIN configurator_systems AS cs ON cs.system_id = csc.id WHERE cs.site_id = $1 and cs.status != 0 and cs.status !=0 order by cs.id, csc.id ASC `;
    let systemDetailRaw = await sails.getDatastore("postgres").sendNativeQuery(query, [siteId]);

    const { rows } = systemDetailRaw;

    systemDetailRaw = await Promise.all(
      rows.map(async (row) => {
        const { icon, svgPath } = row;
        if (icon) {
          row.fileName =
            icon && icon.slice(0, icon.lastIndexOf("_")).concat(".", icon.split(".").pop());
          row.icon = await s3Service.getSignedUrl(process.env.CONFIGURATOR_BUCKET, icon);
        } else {
          row.fileName = "";
          //default icon s3 key
        }
        if (svgPath) {
          row.svgPath = await s3Service.getSignedUrl(process.env.CONFIGURATOR_BUCKET, svgPath);
        }
        row.updatedAt = moment(row.updatedAt || row.createdAt).unix();
        return row;
      })
    );

    const _SystemCatMap = {};
    for (let row of systemDetailRaw) {
      const {
        systemCategoryId,
        systemCategoryName,
        systemId,
        systemName,
        description,
        status,
        icon,
        fileName,
        svgPath,
        updatedAt,
      } = row;

      if (_SystemCatMap.hasOwnProperty(systemCategoryId)) {
        _SystemCatMap[systemCategoryId].systems.push({
          systemId: systemId,
          systemName: systemName,
          icon: icon,
          fileName,
          description,
          status,
          svgURL: svgPath,
          lastUpdatedAt: updatedAt,
        });
      } else {
        _SystemCatMap[systemCategoryId] = {
          systemCategoryId,
          name: systemCategoryName,
          systems: [
            {
              systemId: systemId,
              systemName: systemName,
              icon: icon,
              fileName,
              description,
              status,
              svgURL: svgPath,
              lastUpdatedAt: updatedAt,
            },
          ],
        };
      }
    }
    return Object.values(_SystemCatMap);
  },
  fetchDynamicNavigation: async function (siteId) {
    const query = `SELECT DISTINCT
    csc.id AS "systemCategoryId",
    csc.name AS "systemCategoryName",
    cs.id AS "systemId",
    cs."order"  as "systemOrder",
    cs.name AS "systemName",
    cs.icon
    FROM
    configurator_system_category AS csc
    JOIN
    configurator_systems AS cs
    ON
    csc.id = cs.system_id
    JOIN
    sub_system_pages AS ssp
    ON
    cs.id = ssp.sub_system_id AND ssp.is_published = 1 AND ssp.status = 1
    WHERE
    csc.status = 1
    AND
    cs.status = 1
    AND
    cs.site_id = $1`;
    let systemDetailRaw = await sails.getDatastore("postgres").sendNativeQuery(query, [siteId]);
    const { rows } = systemDetailRaw;
    const enrichedSystemDetails = await this.enrichSystemIcons(rows);
    return _mapToNavigation(enrichedSystemDetails);
  },
  async enrichSystemIcons(rows) {
    return Promise.all(
      rows.map(async (row) => {
        if (row.icon) {
          row.icon = await s3Service.getSignedUrl(process.env.CONFIGURATOR_BUCKET, row.icon);
        }
        return row;
      })
    );
  },
  saveSystemSVG: async function (param) {
    const { siteId, systemId, resetDataTaggerDetail, svgFilePath, userId } = param;
    let { filename: fileName, fd: fileLocation, type: fileType } = svgFilePath;
    const isSiteValid = await siteService.isValidSite(siteId);
    if (!isSiteValid) {
      throw flaverr("E_SITE_NOT_FOUND", new Error("Site not found"));
    }
    if (!(await this.isSystemInEditableMode(systemId, siteId))) {
      throw flaverr("E_SYSTEM_NOT_IN_EDITABLE_MODE", new Error("system is not in editable mode"));
    }

    const fileExtension = fileName.split(".").pop().toLowerCase();

    if (!ALLOWED_SVG_EXTENSIONS.includes(fileExtension)) {
      throw flaverr(
        "E_INVALID_FILE_FORMAT",
        new Error(`File should be only in these ${ALLOWED_SVG_EXTENSIONS.join(",")} format`)
      );
    }

    const fileContent = await fs.promises.readFile(
      fileLocation,
      fileExtension === "ico" ? null : "utf-8"
    );
    if (
      (fileExtension === "svg" && !isValidSvgContent(fileContent)) ||
      (fileExtension === "ico" && !isValidIcoContent(fileContent))
    ) {
      throw flaverr(
        "E_INVALID_FILE_CONTENT",
        new Error("The content of the file is invalid or not an allowed format")
      );
    }

    const fileUID = `${fileName.split(".")[0]}_${fileLocation.split("/").pop()}`;
    const { Key: svgFileKey } = await s3Service.upload(
      fileLocation,
      fileUID,
      fileType,
      process.env.CONFIGURATOR_BUCKET
    );
    if (!svgFileKey) {
      throw flaverr("E_FILE_NOT_UPLOADED", new Error("File not uploaded please try again later"));
    }

    const updateObj = {
      svg_path: svgFileKey,
      updated_by: userId,
    };

    if (resetDataTaggerDetail) {
      updateObj.data_tagger = null;
    }

    await ConfiguratorSystem.update({ id: systemId }).set(updateObj);

    return {
      systemId,
      svgURL: await s3Service.getSignedUrl(process.env.CONFIGURATOR_BUCKET, svgFileKey),
    };
  },
  unpublishSystem: async function (siteId, systemId) {
    const isSiteValid = await siteService.isValidSite(siteId);
    if (!isSiteValid) {
      throw flaverr("E_SITE_NOT_FOUND", new Error("Site not found"));
    }

    const isValidSystem = await this.isSystemExist(siteId, systemId);
    if (!isValidSystem) {
      throw flaverr("E_SYSTEM_NOT_FOUND", new Error("System not found"));
    }

    //We can also remove this check as it is idempotent in behaviour
    const isSystemPublished = await this.isSystemPublished(systemId, siteId);
    if (!isSystemPublished) {
      throw flaverr("E_SYSTEM_NOT_PUBLISHED", new Error("System already unpublished"));
    }

    const isSystemUnpublished = await this.setSystemToUnpublish(systemId, siteId);
    if (!isSystemUnpublished) {
      throw flaverr(
        "E_SYSTEM_NOT_UNPUBLISHED",
        new Error("System is not unpublished yet please try again later")
      );
    }
    return true;
  },
  isSystemPublished: async function (systemId, siteId) {
    const system = await systemService.findOne({
      id: systemId,
      status: 1, // Should be in published state
      site_id: siteId,
    });
    if (!system) {
      return false;
    }
    return true;
  },
  isSystemUnPublished: async function (systemId, siteId) {
    const system = await systemService.findOne({
      id: systemId,
      status: 2,
      site_id: siteId,
    });
    if (!system) {
      return false;
    }
    return true;
  },
  setSystemToUnpublish: async function (systemId, siteId) {
    try {
      await this.update(
        {
          id: systemId,
          site_id: siteId,
          status: 1,
        },
        {
          status: 2,
        }
      );
      return true;
    } catch (e) {
      return false;
    }
  },
  publishSystem: async function (siteId, systemId) {
    const isSiteValid = await siteService.isValidSite(siteId);
    if (!isSiteValid) {
      throw flaverr("E_SITE_NOT_FOUND", new Error("Site not found"));
    }

    const isValidSystem = await this.isSystemExist(siteId, systemId);
    if (!isValidSystem) {
      throw flaverr("E_SYSTEM_NOT_FOUND", new Error("System not found"));
    }

    //We can also remove this check as it is idempotent in behaviour
    const isSystemAlreadyPublished = await this.isSystemUnPublished(systemId, siteId);
    if (!isSystemAlreadyPublished) {
      throw flaverr("E_SYSTEM_NOT_UNPUBLISHED", new Error("System already published"));
    }

    const isSystemPublished = await this.setSystemToPublishState(systemId, siteId);
    if (!isSystemPublished) {
      throw flaverr("E_SYSTEM_NOT_PUBLISHED", new Error("System is not published yet"));
    }
    return true;
  },
  setSystemToPublishState: async function (systemId, siteId) {
    try {
      await this.update(
        {
          id: systemId,
          site_id: siteId,
          status: 2,
        },
        {
          status: 1,
        }
      );
      return true;
    } catch (e) {
      return false;
    }
  },
  deleteSystem: async function (siteId, systemId) {
    const isSiteValid = await siteService.isValidSite(siteId);
    if (!isSiteValid) {
      throw flaverr("E_SITE_NOT_FOUND", new Error("Site not found"));
    }

    if (await isSystemHasPages(systemId)) {
      throw flaverr(
        "E_SYSTEM_NOT_IN_EDITABLE_MODE",
        new Error(
          "Pages exist within the system. To delete the system, kindly remove the pages beforehand."
        )
      );
    }

    const isSystemDeleted = await this.setSystemToDeleteState(siteId, systemId);
    if (!isSystemDeleted) {
      throw flaverr("E_SYSTEM_NOT_DELETED", new Error("System not deleted yet"));
    }
    return true;
  },
  isSystemExist: async function (siteId, systemId) {
    const system = await systemService.findOne({
      id: systemId,
      status: { "!=": 0 },
      site_id: siteId,
    });
    if (!system) {
      return false;
    }
    return true;
  },
  setSystemToDeleteState: async function (siteId, systemId) {
    try {
      await this.update(
        {
          id: systemId,
          site_id: siteId,
        },
        {
          status: 0,
        }
      );
      return true;
    } catch (e) {
      return false;
    }
  },
  fetchSystemDetails: async function (systemId, siteId) {
    return ConfiguratorSystem.findOne({
      id: systemId,
      site_id: siteId,
      status: { "!=": 0 },
    });
  },
  isSystemInEditableMode: async function (systemId, siteId) {
    let system = await this.fetchSystemDetails(systemId, siteId);
    if (_.isEmpty(system)) {
      throw flaverr("E_SYSTEM_NOT_FOUND", new Error("system does not exist"));
    }
    const { status } = system;
    return status === 2;
  },
  fetchSystem: async function (siteId, systemId, pageId) {
    let query = `
    SELECT
    ssp.title,
    ssp.id,
    ssp.svg_path AS "svgPath",
    ssp.status,
    ssp.page_type AS "pageType"
    FROM configurator_system_category AS csc
    LEFT OUTER JOIN configurator_systems AS cs ON cs.system_id = csc.id and cs.id = $1
    INNER JOIN sub_system_pages as ssp ON ssp.id = $2 and ssp.status != 0 and ssp.sub_system_id = cs.id
    WHERE cs.site_id = $3 and cs.status != 0;
  `;
    let systemDetailRaw = await sails
      .getDatastore("postgres")
      .sendNativeQuery(query, [systemId, pageId, siteId]);
    const { rows } = systemDetailRaw;
    if (!rows.length) {
      throw flaverr({
        code: "E_SYSTEM_NOT_FOUND",
        message: `Sub-system page not found`,
      });
    }

    const CONFIGURATOR_PAGE_TYPE = PAGE_TYPE;
    const INVERTED_CONFIGURATOR_PAGE_TYPE = _.invert(PAGE_TYPE);
    const pageDetail = rows[0];
    let response = [];
    if (!INVERTED_CONFIGURATOR_PAGE_TYPE.hasOwnProperty(pageDetail.pageType)) {
      throwExceptionInvalidPageType(systemId, pageId);
    }
    if (pageDetail.pageType == CONFIGURATOR_PAGE_TYPE.SVG) {
      response = await getSVGPageDetails(pageDetail);
    } else if (pageDetail.pageType == CONFIGURATOR_PAGE_TYPE.TABLE) {
      response = await getTablePageDetails(pageDetail);
    }

    return response;

    async function getTablePageDetails(pageDetail) {
      return [];
    }

    async function getSVGPageDetails(pageDetails) {
      const { title: pageTitle, id: pageId, status, svgPath } = pageDetails;
      let presignedSVGURI =
        svgPath && !_.isEmpty(svgPath)
          ? await s3Service.getSignedUrl(process.env.CONFIGURATOR_BUCKET, svgPath)
          : null;
      let svgContent = null;
      const data = await ConfiguratorNode.fetchAllConfiguredNodes(siteId, systemId);
      if (presignedSVGURI) {
        let svgObjectContent = await s3Service.get(svgPath, process.env.CONFIGURATOR_BUCKET);
        const { Body: svgBuffer } = svgObjectContent;
        svgContent = svgBuffer.toString();
      }
      return {
        pageId,
        pageTitle,
        systemDataTagger: data,
        status,
        svgURL: presignedSVGURI,
        svgXml: svgContent,
      };
    }
  },
  updateSystem: async function (params) {
    const { siteId, systemId, systemCategoryId, systemName, description, iconFile, userId } =
      params;

    const isSiteValid = await siteService.isValidSite(siteId);
    if (!isSiteValid) {
      throw flaverr("E_SITE_NOT_FOUND", new Error("Site not found"));
    }

    const isValidSystem = await this.isSystemExist(siteId, systemId);
    if (!isValidSystem) {
      throw flaverr("E_SYSTEM_NOT_FOUND", new Error("System not found"));
    }

    if (systemCategoryId) {
      const isValidSystemCategory = await systemCategoryService.isValidSystemCategory(
        systemCategoryId
      );
      if (!isValidSystemCategory) {
        throw flaverr("E_SYSTEM_CATEGORY_NOT_FOUND", new Error("System category does not exist"));
      }
    }

    let iconFileName = null;
    if (iconFile) {
      let { filename: fileName, fd: fileLocation, type: fileType } = iconFile;
      const fileExtension = fileName.split(".").pop().toLowerCase();
      if (!ALLOWED_SVG_EXTENSIONS.includes(fileExtension)) {
        throw flaverr(
          "E_INVALID_FILE",
          new Error(`File should be only in these ${ALLOWED_SVG_EXTENSIONS.join(",")} format`)
        );
      }

      const fileContent = await fs.promises.readFile(
        fileLocation,
        fileExtension === "ico" ? null : "utf-8"
      );
      if (
        (fileExtension === "svg" && !isValidSvgContent(fileContent)) ||
        (fileExtension === "ico" && !isValidIcoContent(fileContent))
      ) {
        throw flaverr(
          "E_INVALID_FILE_CONTENT",
          new Error("The content of the file is invalid or not an allowed format")
        );
      }

      const fileUID = `${fileName.split(".")[0]}_${fileLocation.split("/").pop()}`;
      const { Key } = await s3Service.upload(
        fileLocation,
        fileUID,
        fileType,
        process.env.CONFIGURATOR_BUCKET
      );
      if (!Key) {
        throw flaverr("E_FILE_NOT_UPLOADED", new Error("File not uploaded please try again later"));
      }
      iconFileName = Key;
    }

    const updateObj = {};
    if (iconFile) {
      updateObj.icon = iconFileName;
    }
    if (description !== undefined) {
      updateObj.description = description;
    }
    if (systemName) {
      updateObj.name = systemName;
    }
    if (systemCategoryId) {
      updateObj.system_id = systemCategoryId;
    }
    if (userId) {
      updateObj.updated_by = userId;
    }
    try {
      await this.update(
        {
          id: systemId,
        },
        updateObj
      );
      return true;
    } catch (e) {
      sails.log.info("[configurator > updateSystem] server error", e);
      return false;
    }
  },
  fetchComponentConfigByComponentIds: fetchComponentConfigByComponentIds,
  saveSystemDataTagging: async function (systemConfig, userId) {
    const { siteId, subSystemId, pageId, dataTaggerDetail } = systemConfig;
    const { devices, components } = utils.extractComponentDeviceList(dataTaggerDetail);
    const isPageExist = await ConfiguratorPages.findOne({
      id: pageId,
      subsystemId: subSystemId,
      status: 1,
      pageType: 1, // Svg page type
    });
    if (!isPageExist) throwExceptionInvalidConfiguratorSubsystemPage(pageId);
    const configuratorSystem = await ConfiguratorSystem.findOne({
      id: subSystemId,
    });
    if (!configuratorSystem) throwExceptionInvalidConfiguratorSystem(subSystemId);
    const { site_id: systemMappedSiteId } = configuratorSystem;
    if (!systemMappedSiteId || systemMappedSiteId != siteId) {
      throwExceptionInvalidSystemMappedSite(siteId);
    }

    if (!_.isEmpty(devices)) {
      const deviceDataParamMap = {};
      const deviceParameterList = await deviceService.fetchParameterListByDeviceIds(
        siteId,
        devices
      );
      deviceParameterList.forEach((it) => {
        const {
          deviceId,
          parameters: { dataParam },
        } = it;
        if (!_.isEmpty(dataParam)) {
          deviceDataParamMap[deviceId] = dataParam;
        }
      });
      utils.validateDevicesExists(devices, Object.keys(deviceDataParamMap));
      const _inputAbbrs = utils.extractAllTypeParamFromPayload(
        dataTaggerDetail.filter((it) => it.assetData.deviceClass === "device")
      );
      utils.validateTaggedDeviceDataAbbrs(_inputAbbrs, deviceDataParamMap);
    }
    if (!_.isEmpty(components)) {
      const _inputComponentDataComponentMap = utils.extractAllTypeParamFromPayload(
        dataTaggerDetail.filter((it) => it.assetData.deviceClass === "component")
      );
      const ComponentDetailMap = !_.isEmpty(components)
        ? await this.fetchComponentConfigByComponentIds(components)
        : new Map();
      if (!_.isEmpty(components)) {
        utils.validateComponentIds(components, [...ComponentDetailMap.keys()]);
      }
      utils.validateTaggedComponentDataControlAbbrs(
        _inputComponentDataComponentMap,
        ComponentDetailMap
      );
    }
    await sails.getDatastore("postgres").transaction(async (dbTransactionObj) => {
      const batchProcessingPromiseHolder = [];
      for (const taggedNode of dataTaggerDetail) {
        const { assetData, operation } = taggedNode;
        const { uiElementType } = assetData;
        const instance = new ConfiguratorNode(uiElementType);

        if (operation === "InsertNewNode") {
          batchProcessingPromiseHolder.push(
            instance.createNewNodeWithTransaction(pageId, assetData, userId, dbTransactionObj)
          );
        } else if (operation === "UpdateNode") {
          batchProcessingPromiseHolder.push(
            instance.updateNodeWithTransaction(pageId, assetData, userId, dbTransactionObj)
          );
        } else if (operation === "DeleteNode") {
          const { id: taggedDeviceRefId } = assetData;
          const instance = new ConfiguratorNode(uiElementType);
          batchProcessingPromiseHolder.push(
            instance.deleteNodeWithTransaction(pageId, taggedDeviceRefId, userId, dbTransactionObj)
          );
        }
      }
      await Promise.all(batchProcessingPromiseHolder);
    });
    return;
  },
  fetchDashboardBySiteId: async function (siteId) {
    const dashboardRawData = await this._fetchRawDashboardData(siteId);
    if (dashboardRawData.length === 0) {
      return [];
    }
    return dashboardRawData;
  },
  async _fetchRawDashboardData(siteId) {
    const query = `SELECT
    csc.id AS "systemId",
    csc.name AS "systemName",
    cs.id AS "subsystemId",
    cs.name AS "subsystemName",
    cs.icon AS "subsystemIcon",
    cs.description AS "subsystemDescription",
    cs.order as "subsystemOrder",
    ssp.id AS "pageId",
    ssp.title AS "pageName",
    ssp.page_type AS "pageType",
    ssp.is_published AS "isPagePublished",
    ssp.order AS "pageOrder"
    FROM
      configurator_system_category AS csc
    JOIN
      configurator_systems AS cs ON csc.id = cs.system_id
    LEFT JOIN
      sub_system_pages AS ssp ON cs.id = ssp.sub_system_id AND ssp.status = 1
    WHERE
      csc.status = 1
      AND cs.status = 1
      AND cs.site_id = $1
    `;
    const { rows } = await sails.getDatastore("postgres").sendNativeQuery(query, [siteId]);
    return rows;
  },
  setSystemOrder: async function (systemIds) {
    if (!Array.isArray(systemIds)) {
      throw flaverr('E_INPUT_VALIDATION',new Error("Invalid input: systemIds must be an array."));
    }

    const query = `UPDATE configurator_systems SET "order" = $1 WHERE id = $2`;

    await sails.getDatastore("postgres").transaction(async (dbConnection) => {
      const updatePromises = systemIds.map((systemId, index) => {
        if (typeof systemId !== 'number') {
          throw flaverr('E_INPUT_VALIDATION', new Error(`Invalid systemId: ${systemId} must be a number.`));
        }
        return dbConnection.query(query, [index, systemId]);
      });

      await Promise.all(updatePromises);
    });
  },
};

function _mapToNavigation(rows) {
  const dynamicNavigationMap = rows.reduce((map, row) => {
    const { systemCategoryId, systemCategoryName, systemId, systemName, icon, systemOrder } = row;

    if (!map[systemCategoryId]) {
      map[systemCategoryId] = {
        systemCategoryId,
        name: systemCategoryName,
        systems: [],
      };
    }

    map[systemCategoryId].systems.push({
      id: systemId,
      name: systemName,
      icon: icon,
      order: systemOrder,
    });

    map[systemCategoryId].systems.sort((a, b) => a.order - b.order);

    return map;
  }, {});

  return Object.values(dynamicNavigationMap).map((category) => ({
    ...category,
    systems: category.systems.map(({ order, ...system }) => system),
  }));
}

