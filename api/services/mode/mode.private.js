/* eslint-disable no-undef */
module.exports = {
  /**
   * Mode module private functions
   */
  create: async (params) => {
    return Modes.create(params);
  },
  find: async (searchParams) => {
    // Mode.find
    return Modes.find(searchParams);
  },
  findOne: async (searchParams) => {
    // Mode.findone
    let modes = await Modes.find(searchParams).limit(1);
    return modes[0];
  },
  update: async (searchParams, updateValue) => {
    return Modes.update(searchParams, updateValue);
  },
  delete: async (searchParams) => {
    return Modes.destroy(searchParams);
  },
};
