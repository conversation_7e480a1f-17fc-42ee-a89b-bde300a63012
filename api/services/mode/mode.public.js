/* eslint-disable no-undef */

const mode = require("./mode.service");

module.exports = {
  findOne: mode.findOne,
  find: mode.find,
  create: mode.create,
  fetchLastKnowModeByDeviceCommandAbbr: mode.fetchLastKnowModeByDeviceCommandAbbr,
  MODE_FEEDBACK_QUEUE: mode.MODE_FEEDBACK_QUEUE(),
  modeFeedbackMessageHandler: mode.modeFeedback<PERSON>essageHand<PERSON>,
  changeControlModeByCommand:mode.changeControlModeByCommand,
  DEFAULT_MODE: mode.DEFAULT_MODE,

};
