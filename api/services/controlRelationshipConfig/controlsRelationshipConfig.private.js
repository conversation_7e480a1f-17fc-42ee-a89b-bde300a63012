module.exports = {
  DriverControlConfigMapping:{
    create: async (params)=>{
      return DriverControlsRelationshipConfig.create(params);
    },
    findOne: async (params)=>{
      return DriverControlsRelationshipConfig.findOne(params);
    },
    find: async (params)=>{
      return DriverControlsRelationshipConfig.find(params);
    },
    update: async (searchParams, updateValue)=>{
      return DriverControlsRelationshipConfig.update(searchParams, updateValue).fetch();
    },
    delete: async (searchParams) => {
      return DriverControlsRelationshipConfig.destroy(searchParams);
    },
    createEach: async (params)=>{
      return DriverControlsRelationshipConfig.createEach(params)
    },
    count: async (params) => {
      return DriverControlsRelationshipConfig.count(params);
    }
  },
  DeviceControlRelationshipConfig:{
    create: async (params)=>{
      return DeviceControlRelationshipConfig.create(params);
    },
    findOne: async (params)=>{
      return DeviceControlRelationshipConfig.findOne(params);
    },
    find: async (params)=>{
      return DeviceControlRelationshipConfig.find(params);
    },
    update: async (searchParams, updateValue)=>{
      return DeviceControlRelationshipConfig.update(searchParams, updateValue);
    },
    delete: async (searchParams) => {
      return DeviceControlRelationshipConfig.destroy(searchParams);
    },
    createEach: async (params)=>{
      return DeviceControlRelationshipConfig.createEach(params)
    },
  }
}
