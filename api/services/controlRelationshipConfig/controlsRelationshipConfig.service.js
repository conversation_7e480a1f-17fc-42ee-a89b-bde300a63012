const flaverr = require("flaverr");
const Excel = require("exceljs");
const controlsRelationshipConfigService = require("./controlsRelationshipConfig.private");
const deviceTypeService = require("../devicetype/devicetype.public");
const utils = require("../../utils/controlsConfig");
const { ERROR_CODE } = require("../../utils/controlsConfig/constants");

module.exports = {
  DriverControlConfigMapping: {
    ...controlsRelationshipConfigService.DriverControlConfigMapping,
    clearControlRelationship: async function ({ driverType, deviceType }) {
      let currentControlRelationships =
        await controlsRelationshipConfigService.DriverControlConfigMapping.find({
          driverType,
          deviceType,
          status: 1,
        });
      await controlsRelationshipConfigService.DriverControlConfigMapping.update(
        {
          driverType,
          deviceType,
          status: 1,
        },
        { status: 0 },
      );
      const currentControlRelationshipPromise = [];
      for (const it of currentControlRelationships) {
        const {
          id,
          controlName,
          controlAbbr,
          controlType,
          controlClass,
          description,
          dataParamAbbr,
          controlProperty,
        } = it;
        currentControlRelationshipPromise.push({
          id,
          controlName,
          controlAbbr,
          controlType,
          controlClass,
          description,
          dataParamAbbr,
          controlProperty,
        });
        await controlsRelationshipConfigService.DeviceControlRelationshipConfig.update(
          {
            driverRefId: id,
          },
          { status: 0 },
        );
      }
      return Promise.all(currentControlRelationshipPromise);
    },
    fetchControlRelationshipDriver: async function ({ deviceType, driverType }) {
      const rawControlRelationships =
        await controlsRelationshipConfigService.DriverControlConfigMapping.find({
          driverType,
          deviceType,
          status: 1,
        });
      const controlRelationship = [];
      for (const rawControlRelationship of rawControlRelationships) {
        const {
          controlProperty,
          controlName,
          controlType,
          controlClass,
          description,
          dataParamAbbr,
          controlAbbr,
        } = rawControlRelationship;
        controlRelationship.push({
          controlProperty,
          controlName,
          controlType,
          controlClass,
          description,
          dataParamAbbr,
          controlAbbr,
        });
      }
      return {
        deviceType,
        driverType,
        controlRelationship,
      };
    },
    getDriverControlsRelationship: async function () {
      const controlConfigList =
        await controlsRelationshipConfigService.DriverControlConfigMapping.find({
          status: 1,
        });

      if (_.isEmpty(controlConfigList)) {
        throw flaverr("E_DRIVER_NOT_EXIST", new Error("Driver does not exist"));
      }

      const ControlRelationshipDriverMap = {};
      for (const controlConfig of controlConfigList) {
        const {
          driverType,
          deviceType,
          controlProperty,
          controlName,
          controlType,
          controlClass,
          description,
          dataParamAbbr,
          controlAbbr,
        } = controlConfig;
        const key = `${deviceType}_${driverType}`;
        const controlRow = {
          controlProperty,
          controlName,
          controlType,
          controlClass,
          description,
          dataParamAbbr,
          controlAbbr,
          driverType,
          deviceType,
        };
        if (ControlRelationshipDriverMap.hasOwnProperty(key)) {
          ControlRelationshipDriverMap[key].push(controlRow);
        } else {
          ControlRelationshipDriverMap[key] = [controlRow];
        }
      }
      return ControlRelationshipDriverMap;
    },
    generateExcel: async function (data) {
      const workBook = new Excel.Workbook();
      for (const [key, value] of Object.entries(data)) {
        const sheet = workBook.addWorksheet(key);
        sheet.columns = [
          {
            header: "controlName",
            key: "controlName",
            width: 35,
          },
          {
            header: "controlAbbr",
            key: "controlAbbr",
            width: 16,
          },
          {
            header: "description",
            key: "description",
            width: 24,
          },
          {
            header: "controlType",
            key: "controlType",
            width: 14,
          },
          {
            header: "controlProperty",
            key: "controlProperty",
            width: 16,
          },
          {
            header: "controlClass",
            key: "controlClass",
            width: 15,
          },
          {
            header: "dataParamAbbr",
            key: "dataParamAbbr",
            width: 16,
          },
          {
            header: "driverType",
            key: "driverType",
            width: 16,
          },
          {
            header: "deviceType",
            key: "deviceType",
            width: 16,
          },
        ];
        value.forEach((item) => sheet.addRow(item));
      }
      return workBook.xlsx.writeBuffer();
    },
    isControlRelationshipDriverExists: async function ({ deviceType, driverType }) {
      const driverControlRelationshipRecords = await this.count({
        driverType,
        deviceType,
        status: 1,
      });
      return driverControlRelationshipRecords > 0;
    },
    syncControlRelationship: async function (componentIds, deviceType, driverType) {
      try {
        return await sails.getDatastore("postgres").transaction(async (db) => {
          const driverControlRecords =
            await controlsRelationshipConfigService.DriverControlConfigMapping.find({
              select: [
                "id",
                "controlAbbr",
                "driverType",
                "deviceType",
                "controlProperty",
                "description",
                "controlName",
                "controlType",
                "controlClass",
                "dataParamAbbr",
                "status",
              ],
              where: {
                deviceType,
                driverType,
                status: 1,
              },
            });
          const driverControlMap = driverControlRecords.reduce((acm, curr) => {
            acm[curr.id] = curr;
            return acm;
          }, {});

          const inOperatorCompIds = componentIds.map((it) => `'${it}'`).join(",");
          const fetchAllComponentControlsQuery = `
                SELECT
                dcrc.id AS "driverControlId",
                dcrc.status AS "driverControlStatus",
                dccr.id AS "deviceControlId",
                dccr.status AS "deviceControlStatus",
                dccr.device_id AS "componentId",
                dccr.driver_control_ref_if AS "driverControlRefId"
                FROM
                driver_controls_relationship_config dcrc
                JOIN
                device_control_config_relationship dccr
                ON
                dccr.driver_control_ref_if = dcrc.id
                WHERE
                dcrc.drivertype = '${driverType}'
                AND dcrc.devicetype = '${deviceType}'
                and dccr.device_id in (${inOperatorCompIds})`;

          const allComponentControls = await sails
            .getDatastore("postgres")
            .sendNativeQuery(fetchAllComponentControlsQuery, []);
          const batchProcessor = [];
          const componentControlMap = {};
          for (const componentControl of allComponentControls.rows) {
            const {
              driverControlStatus,
              deviceControlStatus,
              driverControlRefId,
              componentId,
              deviceControlId,
            } = componentControl;
            if (!componentControlMap[componentId]) {
              componentControlMap[componentId] = [];
            }

            if (
              (driverControlStatus == 0 && deviceControlStatus == 0) ||
              (driverControlStatus == 1 && deviceControlStatus == 0)
            )
              continue;
            if (driverControlStatus != deviceControlStatus) {
              batchProcessor.push(
                DeviceControlRelationshipConfig.update(
                  { id: deviceControlId },
                  { status: driverControlStatus },
                ).usingConnection(db),
              );
            }
            if (driverControlStatus == 1) {
              componentControlMap[componentId].push(driverControlRefId);
            }
          }

          for (const componentId of componentIds) {
            if (!componentControlMap[componentId]) {
              componentControlMap[componentId] = [];
            }
          }

          const activeDriverControlIds = Object.keys(driverControlMap);
          for (const [componentId, existingControlList] of Object.entries(componentControlMap)) {
            const siteId = componentId.split("_")[0];
            const pendingDriverControlForSyncId = _.difference(
              activeDriverControlIds,
              existingControlList,
            );

            for (const driverControlId of pendingDriverControlForSyncId) {
              const { id, deviceType, driverType, ...restOfDriverControlMap } =
                driverControlMap[driverControlId];
              batchProcessor.push(
                DeviceControlRelationshipConfig.create({
                  ...restOfDriverControlMap,
                  deviceId: componentId,
                  driverRefId: driverControlId,
                  siteId,
                }).usingConnection(db),
              );
            }
          }
          return await Promise.all(batchProcessor);
        });
      } catch (error) {
        throw flaverr("E_DB", new Error(`Database error, ${error.message}`));
      }
    },
    appendNewItem: async function ({ driverType, deviceType, newNode }, userId) {
      utils.validateControlConfigUpsertItem({
        driverType,
        deviceType,
        data: newNode,
      });
      const driverControlConfig = await this.fetchControlRelationshipDriver({
        deviceType,
        driverType,
      });
      const { controlRelationship } = driverControlConfig;
      const driverDataCommandParams = await deviceTypeService.getDriverDataCommandParams(
        deviceType,
        driverType,
      );
      if (_.isEmpty(driverDataCommandParams)) {
        const errObj = {
          code: "INVALID_DRIVER_ID",
          data: {
            ...ERROR_CODE.INVALID_DRIVER_ID,
            data: {
              driverType,
              deviceType,
            },
          },
          statusCode: 400,
        };
        throw flaverr(errObj);
      }
      const { dataParams, commandParams } = driverDataCommandParams;
      if (_.isEmpty(dataParams) || _.isEmpty(commandParams)) {
        const errObj = {
          code: "CONTROl_PARAM_NOT_FOUND",
          data: {
            ...ERROR_CODE.CONTROl_PARAM_NOT_FOUND,
            data: {
              driverDataParam: dataParams,
              driverControlParam: commandParams,
            },
          },
          statusCode: 400,
        };
        throw flaverr(errObj);
      }
      controlRelationship.push(newNode);

      let error = utils.validateControlConfigList(
        controlRelationship,
        [...dataParams],
        [...commandParams],
      );
      if (error) {
        utils.throwExceptionInvalidControlConfigList(error);
      }

      const appendDriverObj = {
        ...newNode,
        deviceType,
        driverType,
        createdBy: userId,
      };
      await controlsRelationshipConfigService.DriverControlConfigMapping.create(appendDriverObj);
      return this.fetchControlRelationshipDriver({
        deviceType,
        driverType,
      });
    },
    updateItem: async function ({ driverType, deviceType, updatedNode }, userId) {
      utils.validateControlConfigUpsertItem({
        driverType,
        deviceType,
        data: updatedNode,
      });
      const { controlAbbr } = updatedNode;
      const driverControlConfig = await this.fetchControlRelationshipDriver({
        deviceType,
        driverType,
      });
      const { controlRelationship } = driverControlConfig;
      if (_.isEmpty(controlRelationship)) {
        utils.throwExceptionControlsConfigNotExist(controlRelationship);
      }

      const isControlAbbrExistIndex = controlRelationship.findIndex(
        (control) => control.controlAbbr === controlAbbr,
      );
      if (isControlAbbrExistIndex === -1) {
        utils.throwExceptionControlAbbrNotExist(controlAbbr);
      }
      const mergeControlRelationship = controlRelationship.filter(
        (control) => control.controlAbbr !== updatedNode.controlAbbr,
      );
      mergeControlRelationship.push(updatedNode);
      const driverDataCommandParams = await deviceTypeService.getDriverDataCommandParams(
        deviceType,
        driverType,
      );
      if (_.isEmpty(driverDataCommandParams)) {
        const errObj = {
          code: "INVALID_DRIVER_ID",
          data: {
            ...ERROR_CODE.INVALID_DRIVER_ID,
            data: {
              driverType,
              deviceType,
            },
          },
          statusCode: 400,
        };
        throw flaverr(errObj);
      }
      const { dataParams, commandParams } = driverDataCommandParams;
      if (_.isEmpty(dataParams) || _.isEmpty(commandParams)) {
        const errObj = {
          code: "CONTROl_PARAM_NOT_FOUND",
          data: {
            ...ERROR_CODE.CONTROl_PARAM_NOT_FOUND,
            data: {
              driverDataParam: dataParams,
              driverControlParam: commandParams,
            },
          },
          statusCode: 400,
        };
        throw flaverr(errObj);
      }
      let error = utils.validateControlConfigList(
        mergeControlRelationship,
        [...dataParams],
        [...commandParams],
      );
      if (error) {
        utils.throwExceptionInvalidControlConfigList(error);
      }
      const appendDriverObj = {
        ...updatedNode,
        deviceType,
        driverType,
        createdBy: userId,
      };

      await sails.getDatastore("postgres").transaction(async (dbTransactionObj) => {
        await DriverControlsRelationshipConfig.update(
          {
            deviceType,
            driverType,
            controlAbbr,
            status: 1,
          },
          {
            status: 0,
          },
        ).usingConnection(dbTransactionObj);

        await DriverControlsRelationshipConfig.create(appendDriverObj).usingConnection(
          dbTransactionObj,
        );
      });
      return this.fetchControlRelationshipDriver({
        deviceType,
        driverType,
      });
    },
    deleteItem: async function ({ driverType, deviceType, deleteControlsList }, userId) {
      utils.validateControlConfigDeleteItem({
        driverType,
        deviceType,
        data: deleteControlsList,
      });
      const driverControlConfig = await this.fetchControlRelationshipDriver({
        deviceType,
        driverType,
      });
      const { controlRelationship } = driverControlConfig;
      if (_.isEmpty(controlRelationship)) {
        utils.throwExceptionControlsConfigNotExist(controlRelationship);
      }

      const controlAbbrSet = controlRelationship.reduce((acc, node) => {
        const { controlAbbr } = node;
        acc.add(controlAbbr);
        return acc;
      }, new Set());

      const controlAbbrNotFound = [];
      for (const controlAbbr of deleteControlsList) {
        if (!controlAbbrSet.has(controlAbbr)) {
          controlAbbrNotFound.push(controlAbbr);
        }
      }

      if (controlAbbrNotFound.length > 0) {
        utils.throwExceptionControlAbbrNotExist(controlAbbrNotFound);
      }

      await DriverControlsRelationshipConfig.update(
        {
          deviceType,
          driverType,
          controlAbbr: {
            in: deleteControlsList,
          },
          status: 1,
          createdBy: userId,
        },
        {
          status: 0,
        },
      );

      return this.fetchControlRelationshipDriver({
        deviceType,
        driverType,
      });
    },
  },
  DeviceControlRelationshipConfig: {
    ...controlsRelationshipConfigService.DeviceControlRelationshipConfig,
    getComponentControlRelationship: async function ({ siteId, componentId }) {
      let componentControlsRecords =
        await controlsRelationshipConfigService.DeviceControlRelationshipConfig.find({
          siteId,
          deviceId: componentId,
          status: 1,
        });
      const componentDriverRefIds = [];
      const componentControls = [];
      for (let control of componentControlsRecords) {
        let {
          id,
          controlName,
          controlAbbr,
          controlType,
          controlClass,
          description,
          dataParamAbbr,
          controlProperty,
          driverRefId,
        } = control;
        componentControls.push({
          id,
          controlName,
          controlAbbr,
          controlType,
          controlClass,
          description,
          dataParamAbbr,
          controlProperty,
        });
        componentDriverRefIds.push(parseInt(driverRefId, 10));
      }
      if (_.isEmpty(componentControls)) {
        return {
          controls: [],
          isSyncWithDriver: false,
        };
      }

      let driverControlsDetails = (
        await controlsRelationshipConfigService.DriverControlConfigMapping.find({
          where: {
            id: {
              in: componentDriverRefIds,
            },
            status: 1,
          },
        })
      ).map(({ id, componentPageViewOrder, controlAbbr }) => ({
        id,
        componentPageViewOrder,
        controlAbbr,
      }));
      const driverControlAbbrMap = driverControlsDetails.reduce((acc, control) => {
        acc[control.controlAbbr] = control.componentPageViewOrder;
        return acc;
      }, {});

      componentControls.forEach((control) => {
        const { controlAbbr } = control;
        if (driverControlAbbrMap.hasOwnProperty(controlAbbr)) {
          control.componentPageViewOrder = driverControlAbbrMap[controlAbbr];
        }
      });
      componentControls
        .sort((a, b) => a.componentPageViewOrder - b.componentPageViewOrder)
        .forEach((control) => {
          delete control.componentPageViewOrder;
        });
      return {
        controls: componentControls || [],
        isSyncWithDriver: _.isEqual(
          componentDriverRefIds.sort(),
          driverControlsDetails.map((it) => it.id).sort(),
        ),
      };
    },
    getSiteControlsRelationShipConfig: async function (siteId) {
      let componentControlsRecords =
        await controlsRelationshipConfigService.DeviceControlRelationshipConfig.find({
          siteId,
          status: 1,
        });

      const componentDriverRefIds = [],
        componentControls = [];
      for (let control of componentControlsRecords) {
        let {
          id,
          controlName,
          controlAbbr,
          controlType,
          controlClass,
          description,
          dataParamAbbr,
          controlProperty,
          driverRefId,
        } = control;
        componentControls.push({
          id,
          controlName,
          controlAbbr,
          controlType,
          controlClass,
          description,
          dataParamAbbr,
          controlProperty,
        });
        componentDriverRefIds.push(parseInt(driverRefId));
      }
      if (_.isEmpty(componentControls)) {
        return {
          controls: [],
          isSyncWithDriver: false,
        };
      }
      let driverControlsIds = (
        await controlsRelationshipConfigService.DriverControlConfigMapping.find({
          where: {
            id: {
              in: componentDriverRefIds,
            },
            status: 1,
          },
        })
      ).map((it) => it.id);
      return {
        controls: componentControls || [],
        isSyncWithDriver: _.isEqual(componentDriverRefIds, driverControlsIds),
      };
    },

    getDriverControlsOrder: async function ({ siteId, componentId }) {
      let componentControlsRecords =
        await controlsRelationshipConfigService.DeviceControlRelationshipConfig.find({
          siteId,
          deviceId: componentId,
          status: 1,
        });
      const componentDriverRefIds = [];
      for (const control of componentControlsRecords) {
        componentDriverRefIds.push(parseInt(control.driverRefId, 10));
      }
      const driverControlsOrder = (
        await controlsRelationshipConfigService.DriverControlConfigMapping.find({
          where: {
            id: {
              in: componentDriverRefIds,
            },
            status: 1,
          },
        })
      ).map(({ id, componentPageViewOrder, controlAbbr }) => ({
        id,
        controlAbbr,
        order: componentPageViewOrder,
      }));
      return driverControlsOrder;
    },

    updateDriverControlsOrder: async function (driverControlsOrder) {
      for (const control of driverControlsOrder) {
        await controlsRelationshipConfigService.DriverControlConfigMapping.update(
          {
            id: control.id,
            status: 1,
          },
          {
            componentPageViewOrder: control.order,
          },
        );
      }
    },
  },
};
