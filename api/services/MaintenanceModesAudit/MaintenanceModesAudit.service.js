const flaverr = require('flaverr');
const moment = require('moment');
const maintenanceModesAuditService = require('./MaintenanceModesAudit.private');
const iotCoreService = require("../../services/iotCore/iotCore.service");

module.exports = {
  create: maintenanceModesAuditService.create,
  find: maintenanceModesAuditService.find,
  update: maintenanceModesAuditService.update,
  delete: maintenanceModesAuditService.delete,
  findOne: maintenanceModesAuditService.findOne,

  setMaintenanceModeAuditLog: async function (
    assetId,
    maintenanceModeValue,
    deviceClass,
    siteId,
    userId
  ) {
    try {
      const validMaintenanceModes = ["0", "1"];

      if (!validMaintenanceModes.includes(String(maintenanceModeValue))) {
        throw flaverr(
          `INVALID_VALUE`,
          new Error(
            `Invalid maintenance mode value provided. Mode should be '1' (ON) or '0' (OFF).`
          )
        );
      }

      const timezone = await sails.helpers.getSiteTimezone.with({ siteId, timezoneFormat: "tz" });

      const ongoingMaintenance = await this.getLatestMaintenanceModeAuditLog(assetId);
      if (maintenanceModeValue === "1" && ongoingMaintenance && !ongoingMaintenance.endTime)
        return ongoingMaintenance;
      if (maintenanceModeValue === "0" && ongoingMaintenance && ongoingMaintenance.endTime) {
        throw flaverr(
          `AUDIT_LOG_NOT_FOUND`,
          new Error(`No active maintenance record found for ${assetId}`)
        );
      }
      let res;
      const now = moment().tz(timezone).format("YYYY-MM-DDTHH:mm:ssZ")
      if (maintenanceModeValue === "1") {
        res = await MaintenanceModesAudit.create({
          deviceId: assetId,
          startTime: now,
          deviceClass: deviceClass,
          siteId: siteId,
          startedBy: userId,
        }).fetch();
      } else {
        res = await MaintenanceModesAudit.updateOne({ id: ongoingMaintenance.id }).set({
          endTime: now,
          stoppedBy: userId,
        });
      }
      const maintenancePayload = {
        mid: res.id,
        componentId: assetId,
        isInMaintenanceMode: maintenanceModeValue == 1 ? true : false,
        timestamp: now
      };
      await iotCoreService.publish(
        `${siteId}/maintenancemode/${assetId}/status`,
        maintenancePayload
      );
      return res;
    } catch (error) {
      sails.log.error(`Error setting the maintenance mode audit log for: ${assetId}`);
      sails.log.error(error);
      throw flaverr(
        "ERROR_FOUND",
        new Error(`Error setting the maintenance mode audit log for ${assetId}`)
      );
    }
  },

  getLatestMaintenanceModeAuditLog: async function (assetId) {
    try {
      const latestMaintenanceLog = await MaintenanceModesAudit.find({
        deviceId: assetId,
      })
        .sort("startTime DESC")
        .limit(1);

      if (!latestMaintenanceLog.length) {
        return null;
      } else {
        return latestMaintenanceLog[0];
      }
    } catch (error) {
      sails.log.error(`Error fetching the maintenance mode audit log for: ${assetId}`);
      sails.log.error(error);
      throw flaverr(
        "AUDIT_LOG_NOT_FOUND",
        new Error(`Error finding active maintenance record for ${assetId}`)
      );
    }
  },
  getMaintenanceModeAuditHistory: async function (assetId, siteId, startTime, endTime) {
    const query = `
  SELECT id, started_by, stopped_by, start_time, end_time
  FROM maintenance_modes_audit mma
  WHERE device_id = $1
    AND site_id = $2
    AND (
      (start_time BETWEEN $3 AND $4)
      OR (end_time BETWEEN $3 AND $4)
      OR (start_time < $3 AND end_time > $4)
      OR (end_time IS NULL)
    );
`;

    const data = await sails
      .getDatastore("postgres")
      .sendNativeQuery(query, [assetId, siteId, startTime, endTime]);

    const { rows } = data;

    return rows;
  },
};
