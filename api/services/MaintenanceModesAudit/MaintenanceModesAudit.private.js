module.exports = {
    create: async (params) => {
      return MaintenanceModesAudit.create(params);
      ;
    },
    find: async (searchParams) => {
      return MaintenanceModesAudit.find(searchParams);
    },
    update: async (searchParams, updateValue) => {
      return MaintenanceModesAudit.update(searchParams, updateValue);
    },
    findOne: async (searchParams) => {
      return MaintenanceModesAudit.findOne(searchParams);
    },
    delete: async (searchParams) => {
      return MaintenanceModesAudit.destroy(searchParams);
    },
  };
  