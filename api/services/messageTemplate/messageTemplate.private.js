// noinspection JSUnresolvedVariable
const flaverr = require("flaverr");
const Joi = require("joi");

/**
 * Extracts parameters from a message string that are contained within double curly braces.
 *
 * @param {string} messageStr - The message string to extract parameters from.
 * @returns {Array} An array of extracted parameters as strings.
 *
 * @example
 * const messageStr = "I am sample parameterized message that have {{username}} and  {{email}} ";
 * const parameters = fetchParameterFromMessageTemplate(messageStr);
 * console.log(parameters);
 * // Output: [ 'username', 'email' ]
 */

function fetchParameterFromMessageTemplate(messageStr) {
  const parameterRegex = /\{\{([^}]+)\}\}/g;
  const parameters = new Set();
  let match;
  while ((match = parameterRegex.exec(messageStr))) {
    parameters.add(match[1]);
  }
  return [...parameters];
}

function validateMessageTemplate(param) {
  const templateSchema = Joi.object().keys({
    channel: Joi.string()
      .valid("email", "whatsapp", "message", "slack")
      .required(),
    event_type: Joi.string().valid("OCCURRED", "RESOLVED").required(),
    vendor_template_id: Joi.string().optional(),
    title: Joi.string().required(),
    body: Joi.string().required(),
    template_name: Joi.string().required(),
  });
  const { value, error } = templateSchema.validate(param);
  if (error) {
    throw flaverr("INPUT_VALIDATION_ERROR", new Error(error.message));
  } else {
    return value;
  }
}

function MessageTemplateFormatter(input) {
  console.log(input);
  const MessageTemplateObj = {
    template_id: null,
    template_name: null,
    channels: [],
  };
  const _buildChannelWiseMessageObj = function (message) {
    const {
      message_id,
      channel_name,
      title,
      body,
      event_type,
      third_party_template_id,
    } = message;
    return {
      name: channel_name,
      id: message_id,
      message: {
        title,
        body,
      },
      event_type,
      third_party_template_id,
    };
  };
  const { template_id, template_name } = input[0];
  MessageTemplateObj.template_id = template_id;
  MessageTemplateObj.template_name = template_name;
  input.forEach((message) => {
    MessageTemplateObj.channels.push(_buildChannelWiseMessageObj(message));
  });
  return MessageTemplateObj;
}

module.exports = {
  register: async function (param) {
    /* Guard Clauses*/
    validateMessageTemplate(param);
    const {
      template_name: name,
      event_type,
      channel,
      body,
      title,
      vendor_template_id,
    } = param;
    // noinspection JSUnresolvedVariable
    return await sails
      .getDatastore(process.env.SMART_ALERT_DB_NAME)
      .transaction(async (db) => {
        let [_MessageTemplate] = await MessageTemplate.find({
          name,
          status: 1,
        }).usingConnection(db);

        if (!_MessageTemplate) {
          _MessageTemplate = await MessageTemplate.create({ name })
            .fetch()
            .usingConnection(db);
        }
        const _messageTemplateId = _MessageTemplate.id;

        let [_Message] = await Message.find({
          channel_name: channel,
          template_id: _messageTemplateId,
          event_type,
          status: 1,
        }).usingConnection(db);

        if (!_.isEmpty(_Message)) {
          throw flaverr(
            "DUPLICATE_MESSAGE_TEMPLATE",
            new Error(
              `This message template ${name} is already exist for channel ${channel} and event type ${event_type}.`
            )
          );
        }

        _Message = await Message.create({
          channel_name: channel,
          template_id: _messageTemplateId,
          event_type,
          title,
          body,
          vendor_template_id,
        })
          .fetch()
          .usingConnection(db);

        const { id: _messageRefId } = _Message;

        const parameters = fetchParameterFromMessageTemplate(
          `${title}---${body}`
        );

        const messagePlaceholders = [];
        messagePlaceholders.push(
          ...parameters.map((placeholder_name) => ({
            message_id: _messageRefId,
            placeholder_name,
          }))
        );

        // noinspection JSUnresolvedVariable
        await MessagePlaceholder.createEach(messagePlaceholders)
          .fetch()
          .usingConnection(db);

        return {
          template_name: name,
          event_type,
          channel,
          body,
          title,
          vendor_template_id,
          message_template_id: _messageTemplateId,
        };
      });
  },
  updateMessageById: async function (messageId, param) {
    const { event_type, channel, body, title, vendor_template_id } = param;
    // noinspection JSUnresolvedVariable
    return await sails
      .getDatastore(process.env.SMART_ALERT_DB_NAME)
      .transaction(async (db) => {
        let [_MessageTemplate] = await Message.find({
          id: messageTemplateId,
          status: 1,
        }).usingConnection(db);

        if (_MessageTemplate) {
          //throw new Error
        }
        const updateObj = {};
        if (vendor_template_id) {
          updateObj.vendor_template_id = vendor_template_id;
        }
        if (title) {
          updateObj.title = title;
        }
        if (body) {
          updateObj.body = body;
        }
        await Message.update({ id: messageId }, updateObj)
          .fetch()
          .usingConnection(db);
        const { id: _messageRefId } = _Message;

        const parameters = fetchParameterFromMessageTemplate(
          `${title}---${body}`
        );

        const messagePlaceholders = [];
        messagePlaceholders.push(
          ...parameters.map((placeholder_name) => ({
            message_id: messageId,
            placeholder_name,
          }))
        );

        // noinspection JSUnresolvedVariable
        await MessagePlaceholder.createEach(messagePlaceholders)
          .fetch()
          .usingConnection(db);

        return {
          channel,
          body,
          title,
          vendor_template_id,
          message_template_id: _messageTemplateId,
        };
      });
  },
  delete: async function (messageTemplateId) {
    const softDeleteObject = {
      status: 0,
    };
    // noinspection JSUnresolvedVariable
    await MessageTemplate.update({ id: messageTemplateId }).set(
      softDeleteObject
    );
  },
  fetchMessageTemplateById: async function (messageTemplateId) {
    const query = `select mt.id as template_id,mt.name as template_name,m.id as message_id, m.channel_name , m.title ,m.body ,m.event_type ,m.vendor_template_id  from message_template mt
join message m on m.template_id = mt.id
where mt.id=$1 and mt.status=1`;
    let { rows } = await sails
      .getDatastore(process.env.SMART_ALERT_DB_NAME)
      .sendNativeQuery(query, [messageTemplateId]);
    if (rows && rows.length > 0) {
      return MessageTemplateFormatter(rows);
    } else {
      return null;
    }
  },
  isMessageTemplateExist: async function (messageTemplateId) {
    let [_MessageTemplate] = await MessageTemplate.find({
      name,
      status: 1,
    });
    return _MessageTemplate ? true : false;
  },
};
