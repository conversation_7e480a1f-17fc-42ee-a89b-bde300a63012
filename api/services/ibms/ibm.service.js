const DataDeviceService = require("../../services/datadevice/datadevice.public");
const influxService = require("../influx/enterprise/influx.public");
const moment = require("moment-timezone");
moment.tz.add("Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6");
moment.tz.setDefault("Asia/Kolkata");
module.exports = {
  getFASFloorList: async function (siteId, FAS_system_id, building_id) {
    let floorListQuery = `select id,name from nodes n where site_id = $1 and system_id = $2 and parent_id = $3 order by id asc`;
    let floorList = await Nodes.getDatastore().sendNativeQuery(floorListQuery, [
      siteId,
      FAS_system_id,
      building_id,
    ]);
    return floorList.rows;
  },
  getFloorWiseDeviceCount: async function (floors) {
    let _inCondition = floors.join(",");
    let query = `select parent_id as floor_id, count(id) as total_device from nodes where level_type ='component' and parent_id  in (${_inCondition}) group by parent_id `;
    let data = await Nodes.getDatastore().sendNativeQuery(query, []);
    let _result = data.rows.reduce((acm, curr) => {
      const { floor_id, total_device } = curr;
      acm[floor_id] = parseInt(total_device);
      return acm;
    }, {});
    return _result;
  },
  getFireDevicesStatusCount: async function (floors, siteId) {
    let _inCondition = floors.join(",");
    let query = `select device_id,parent_id as floor_id from nodes where level_type ='component' and parent_id  in (${_inCondition}) `;
    let floorComponenRecord = await Nodes.getDatastore().sendNativeQuery(query, []);

    const componentList = [],
      floorDeviceStateCountMap = {},
      componentFloorMapping = {};
    floorComponenRecord.rows.forEach((it) => {
      const { floor_id, device_id } = it;
      componentFloorMapping[device_id] = floor_id;
      floorDeviceStateCountMap[floor_id] = {
        FIRE_DETECTED_MODE: 0,
        MAINTENANCE_MODE: 0,
        NORMAl_MODE: 0,
      };
      componentList.push(device_id);
    });

    let data = await Promise.all(
      componentList.map((component) =>
        DataDeviceService.getComponentLastDataPoint(siteId, component),
      ),
    );
    data = data.filter(Boolean).map((it) => [it]);
    data = data.filter((it) => it.length).flat();
    data.forEach((it) => {
      const { data, deviceId } = it;
      try {
        let packet = JSON.parse(data);
        floorDeviceStateCountMap[componentFloorMapping[deviceId]][
          this.getFireDeviceState(packet)
        ]++;
      } catch (e) {}
    });
    return floorDeviceStateCountMap;
  },
  getFireDeviceState: function (packet) {
    if (packet.hasOwnProperty("fireDetected") && packet.fireDetected == 1)
      return this.FIRE_DEVICE_STATUS["1"];
    if (packet.hasOwnProperty("maintenance") && packet.maintenance == 1)
      return this.FIRE_DEVICE_STATUS["2"];
    return this.FIRE_DEVICE_STATUS["0"];
  },
  FIRE_DEVICE_STATUS: {
    1: "FIRE_DETECTED_MODE",
    2: "MAINTENANCE_MODE",
    0: "NORMAl_MODE",
  },
  getJCoolLineGraph: async function (deviceList, filterObject) {
    const { startTime, endTime } = filterObject;
    let query = `
    import "timezone"
    option location = timezone.fixed(offset: {{timezoneOffset}})
    agdcc_kol_consumption_data = from(bucket: "device_component/autogen")
          |> range(start: {{startTime}},stop:{{endTime}})
          |> filter(fn: (r) => r["_measurement"] == "device")
          |> filter(fn: (r) => r["siteId"] == "{{siteId}}")
          |> filter(fn: (r) => {{devices}})
          |> filter(fn: (r) => r["_field"] == "corrected_kwh")
          |> aggregateWindow(every: 1d, fn: spread,createEmpty: false,timeSrc:"_start")
          |> group(columns: ["_field", "_time"], mode:"by")
          |> sum(column: "_value")
          |> group(columns: ["_field"], mode:"by")
          |> map(fn: (r) => ({r with siteId: "{{siteId}}"}))
          |> rename(columns: {_value: "kWh"})


    agdcc_kol_TRh = from(bucket: "device_component/autogen")
          |> range(start: {{startTime}},stop:{{endTime}})
          |> filter(fn: (r) => r["_measurement"] == "components")
          |> filter(fn: (r) => r["siteId"] == "{{siteId}}")
         |> filter(fn: (r) => r["componentId"] == "{{componentId}}" )
         |> filter(fn: (r) => r["_field"] == "tptr")
          |> drop(columns: ["_field", "_measurement","componentId"])
          |> aggregateWindow(every: 1m, fn: mean, createEmpty: false,timeSrc:"_start")
          |> map(fn: (r) => ({ r with _value: r._value /60.0 }))
          |> aggregateWindow(every: 1d, fn: sum,createEmpty: false,timeSrc:"_start")
          |> rename(columns: {_value: "TRh"})

    agdcc_kol_kwh_trh = join(tables: {agdcc_kol_consumption_data: agdcc_kol_consumption_data, agdcc_kol_TRh: agdcc_kol_TRh}, on: ["siteId","_time"], method: "inner")
      |> map(fn: (r) => ({ r with kWh_TRh: r.kWh /r.TRh }))
      |> drop(columns: ["kWh", "TRh","_field"])
      |> yield(name: "custom-name")
    `;
    const devices = deviceList.map((device) => `r["deviceId"]=="${device}"`).join(" or ");
    const timezoneOffset = await sails.helpers.getSiteTimezone.with({
      siteId: "aeh-mad",
      timezoneFormat: "utcOffsetInMinute",
    });
    const replacements = {
      siteId: "aeh-mad",
      componentId: "aeh-mad_1",
      startTime,
      endTime,
      devices: devices,
      timezoneOffset,
    };
    const queryData = await influxService.runQuery(query, {
      replacements,
      debug: true,
    });
    return queryData;
  },
  getAllElevators: async function (siteId, elevator_system_id) {
    let getElevatorQuery = `select device_id as id,site_id, name from nodes where system_id  = $1 and level_type = 'component' and is_deleted = false and site_id=$2  order by id asc ;`;
    let elevators = await Nodes.getDatastore().sendNativeQuery(getElevatorQuery, [
      elevator_system_id,
      siteId,
    ]);
    return elevators.rows;
  },
};
