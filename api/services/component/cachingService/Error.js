/**
 * @description ComponentCachingError class extend the standard Error class of nodejs and provide some additional functionality
 * over it
 * @usage instead of throwing error by 'throw new Error(<error message>)', please call throw new ComponentCachingError(<error message>)
 */
 class ComponentCachingError extends Error {
    constructor(message) {
        super(message);
        this.message = message;
        Error.captureStackTrace(this, this.constructor);
        this.name = this.constructor.name;
    }
}