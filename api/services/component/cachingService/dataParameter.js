const cacheService = require('../../cache/cache.service')
const Error = require('./Error');
const Component = require('../../component/component.service')

module.exports = (function () {
  const COLUMN = "data_parameter";
  const EXPIRE = 24*60*60; //1 day only

  /**
   * @description create a unified key pattern for component related stuff in redis
   * @param {string} siteId
   * @param {string} deviceId
   * @param {string} column
   * @returns string
   */
  function storageKey(siteId, deviceId, column) {
    if (siteId && deviceId && column)
      return `components:${siteId}:${deviceId}:${column}`;
    throw new ComponentCachingError("PARAM_MISSING_AT_keyBuilder");
  }

  /**
   * @praveen-sjpl
   * @description Store data keys inside the redis so
   * @param {string} key | to create a set in redis
   * @param {*} parameters | parameters are the member of set
   * @returns { promise } | it will send the count of member
   */
  async function store(key, parameters) {
    await cacheService.delete(key);
    let _response = await cacheService.sadd(key, parameters);
    await cacheService.expire(key, EXPIRE);
    return parameters;
  }

  /**
   * @praveen-sjpl
   *@description fetching the data column from dynamo's component
   * @param { string } componentId | componentId is referring to deviceId column of component table of dynamo db
   * @returns { void promise }
   */
  async function fetch(componentId) {
    const configurationData = await Component.findOne({
      deviceId: componentId,
    });
    if(!configurationData) return [];
    if(!configurationData.hasOwnProperty('data')) return [];
    if (!Array.isArray(configurationData.data)) return [];
    let { data } = configurationData;
    return data.length > 0 ? data : [];
  }

  /**
   *@praveen-sjpl
   * @param { string } siteId | siteId is required to look into redis
   * @param { string } componentId | componentId is referring to deviceId column of component table of dynamo db
   * @returns { void promise }
   */
  async function set(siteId, componentId) {
    if (!siteId) throw new Error("SITEID_MISSING");
    if (!componentId) throw new Error("COMPONENT_ID_MISSING");

    const data = await fetch(componentId);

    //extracting only key's form data
    let configuredDataKeys = data.reduce((acm, curr) => {
      if (curr.key) acm.push(curr.key);
      return acm;
    }, []);

    return configuredDataKeys.length
      ? await store(
          storageKey(siteId, componentId, "data_parameter"),
          configuredDataKeys
        )
      : null;
  }

  /**
   * @description deleting record from redis
   * @param {string} siteId
   * @param {string} componentId
   * @returns { void promise }
   */
  async function remove(siteId, componentId) {
    await cacheService.delete(key(siteId, componentId));
  }

  /**
   * @description fetch data from redis
   * @param {*} siteId
   * @param {*} componentId
   * @returns { promise }
   */
  async function get(siteId, componentId) {
    return await cacheService.smembers(key(siteId, componentId));
  }

  /**
   * @description generating key for data_parameter
   * @param {string} siteId
   * @param {string} componentId
   * @returns {string}
   */
  function key(siteId, componentId) {
    return storageKey(siteId, componentId, COLUMN);
  }

  return {
    set: set,
    remove: remove,
    get: get,
    key: key,
    update: set,
  };
})()
