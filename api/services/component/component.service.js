const flaverr = require('flaverr');
const AWS = require("aws-sdk");
const moment = require("moment");
const componentservice = require("./component.private");
const deviceTypeService = require("../devicetype/devicetype.public");
const { getDeviceUpdatedCompData, getDeviceControllerMap } = require("../device/device.public");
const globalhelper = require("../../utils/globalhelper");
const componentUtils = require("../../utils/component/utils");
const ComponentControlsRelationshipService = require("../controlRelationshipConfig/controlsRelationshipConfig.service").DeviceControlRelationshipConfig;
const ModeService = require('../mode/mode.public');
const { DeviceControlRelationshipConfig } = require('../controlRelationshipConfig/controlsRelationshipConfig.public');
const cacheService = require("../cache/cache.public");
const errorUtils  = require('../../utils/component/error');
const uuid = require('uuid');
const socketService = require('../socket/socket.service');
const iotCoreService = require('../iotCore/iotCore.service');
const deviceService = require("../device/device.public");
const siteService = require('../../services/site/site.private');
const influxService = require('../influx/enterprise/influx.public');


const getComponentConfig = async (componentId) => {
  const component = await componentservice.findOne({ deviceId: componentId });
  if (_.isEmpty(component)) {
    throw flaverr(`COMPONENT_ID_NOT_EXIST`, new Error(`${componentId} does not exists`));
  }
  const {
    driverType,
    deviceType,
    siteId,
    data: configuredDataParameters,
    controls: configuredCommandParameters
  } = component;
  delete component.controls;
  delete component.data;
  component.configuredDataParameters = configuredDataParameters;
  component.configuredCommandParameters = configuredCommandParameters;

  const [driverInfo, globalDriverInfo] = await Promise.all([
    deviceTypeService.findOne({ driverType, deviceType }),
    deviceTypeService.findOne({ driverType: '0', deviceType: 'global' })
  ]);


  if (_.isEmpty(driverInfo)) throw flaverr(`DRIVER_DETAIL_NOT_EXIST`, new Error(`driver info doesn't exists`));
  const { parameters: driverParameterList } = driverInfo;

  /*
  * Component's table stores only configured data and command parameter. But
  * we have to merge configured data and command parameters that is available at driver level also
  * */
  component.controls = (function mergeDriverCommandParameterWithConfiguredCommand({
    driverCommandParams,
    configuredCommandParameters
  }) {
    const mergedCommandParams = [];
    const componentCommandKeyMap = {};
    for (const param of configuredCommandParameters) {
      componentCommandKeyMap[param.key] = param;
    }
    for (const driverParam of driverCommandParams) {
      const {
        abbr: key,
        type,
      } = driverParam;
      if (type !== 'command') continue;
      const mergedParam = {
        key,
        timeout: ''
      };

      Object.assign(mergedParam, driverParam);
      if (componentCommandKeyMap.hasOwnProperty(key)) {
        Object.assign(mergedParam, componentCommandKeyMap[key]);
      }
      mergedParam.device_abbr = mergedParam.device_abbr || mergedParam.key;
      mergedCommandParams.push(controlParamFactoryBuilder(mergedParam));
    }
    function controlParamFactoryBuilder(packet) {
      const obj = {
        deviceId: '',
        displayName: '',
        expression: '',
        key: '',
        max: '',
        min: '',
        paramGroup: '',
        timeout: '',
        device_abbr: ''
      };
      for (const key in packet) {
        if (obj.hasOwnProperty(key)) {
          obj[key] = packet[key];
        }
      }
      return obj;
    }
    return componentUtils.sortParameter(mergedCommandParams);
  }({
    driverCommandParams: driverParameterList,
    configuredCommandParameters
  }));

  component.data = (function mergeDriverDataParameterWithConfiguredCommand({
    driverDataParams,
    configuredDataParameters
  }) {
    const componentDataKeyMap = {};
    for (const param of configuredDataParameters) {
      componentDataKeyMap[param.key] = param;
    }
    const mergedDriverDataParams = mergeDriverParams(driverDataParams, componentDataKeyMap);
    const driverKeysSet = new Set(mergedDriverDataParams.map(param => param.key));
    const globalParamsToMerge = (globalDriverInfo?.parameters || []).filter(
      param => !driverKeysSet.has(param.key)
    );
    const mergedGlobalParams = mergeDriverParams(
      globalParamsToMerge,
      componentDataKeyMap,
      globalDriverInfo?.deviceType,
      true
    );
    const mergedDataParams = [
      ...mergedDriverDataParams,
      ...mergedGlobalParams
    ].filter((param, index, self) =>
      index === self.findIndex(p => p.key === param.key)
    );
    function mergeDriverParams(params, componentDataKeyMap, deviceType = null, requiresDeviceTypeCheck = false) {
      const mergedDataParams = [];

      for (const driverParam of params) {
        const { abbr: key, type } = driverParam;
        if (type !== 'data') continue;

        const mergedParam = { key, ...driverParam };

        if (componentDataKeyMap.hasOwnProperty(key)) {
          const componentData = componentDataKeyMap[key];

          if (requiresDeviceTypeCheck && componentData?.belongsTo?.deviceType !== deviceType) {
            continue;
          }

          Object.assign(mergedParam, componentData);
        }

        const data = dataParamFactoryBuilder(mergedParam);
        mergedDataParams.push(data);
      }

      return mergedDataParams;
    }
    function dataParamFactoryBuilder(packet) {
      const obj = {
        dau: '',
        deviceId: '',
        displayName: '',
        expression: '',
        belongsTo: undefined,
        group: '',
        key: '',
        max: '',
        min: '',
        origin: '',
        paramGroup: '',
        rogueMax: '',
        rogueMin: '',
      };
      for (const key in packet) {
        if (obj.hasOwnProperty(key)) {
          obj[key] = packet[key];
        }
      }
      return obj;
    }
    return componentUtils.sortParameter(mergedDataParams);
  }({
    driverDataParams: driverParameterList,
    configuredDataParameters
  }));

  component.controlRelationshipMap = await ComponentControlsRelationshipService.getComponentControlRelationship({
    siteId,
    componentId,
  });
  //Map raw control param to control relationship

  if (component.controlRelationshipMap && component.controlRelationshipMap.controls.length) {
    const commandKeyMap = component.controls.reduce((acm, curr) => {
      const { key } = curr;
      acm[key] = curr;
      return acm;
    }, {});
    for (const control of component.controlRelationshipMap.controls) {
      const {
        controlType,
        controlProperty
      } = control;
      if (controlType === 'BIT') {
        Object.assign(controlProperty.left, commandKeyMap[controlProperty.left.commandAbbr]);
        Object.assign(controlProperty.right, commandKeyMap[controlProperty.right.commandAbbr]);
      } else if (controlType === 'VIT') {
        Object.assign(controlProperty, commandKeyMap[controlProperty.commandAbbr]);
      }
    }
  }

  //utility function
  component.isValidDataAbbr = function (dataAbbr) {
    const { data } = this;
    return data && Array.isArray(data) && data.map((it) => it.key)
      .includes(dataAbbr);
  };
  component.isValidControlAbbr = function (controlAbbr) {
    if (!this.controlRelationshipMap) return false;
    const {
      controlRelationshipMap: { controls },
    } = this;
    return (
      controls
      && Array.isArray(controls)
      && controls.map((it) => it.controlAbbr)
        .includes(controlAbbr)
    );
  };
  component.isInMaintenanceMode = component.isInMaintenanceMode ?? '0';
  Object.freeze(component);
  return component;
}

const getDriverComponentIds = async (siteId, deviceType) => {
  const componentRecords = await componentservice.find({ siteId, deviceType });
  if (_.isEmpty(componentRecords)) {
    return []
  }
  return componentRecords.reduce((acc, record) => {
    acc.push(record.deviceId);
    return acc;
  }, [])
}

const orderControlsByComponent = async ({ componentId, controls }) => {
  const component = await componentservice.findOne({ deviceId: componentId });
  if (_.isEmpty(component)) {
    throw new flaverr(`COMPONENT_ID_NOT_EXIST`, new Error(`${componentId} does not exists`));
  }
  const driverControlsOrder = await DeviceControlRelationshipConfig.getDriverControlsOrder({ siteId: component.siteId, componentId });
  for (const control of controls) {
    let isControlAbbrFound = false;
    for (let i = 0; i < driverControlsOrder.length; i++) {
      if (driverControlsOrder[i].controlAbbr === control.controlAbbr) {
        if (control.order >= 0 && control.order < driverControlsOrder.length) {
          driverControlsOrder[i].order = control.order;
          isControlAbbrFound = true;
        } else {
          throw flaverr('E_INPUT_VALIDATION', new Error("Control order is out of valid range"))
        }
      } else if (isControlAbbrFound) {
        driverControlsOrder[i].order += 1;
      }
    }
  }
  await DeviceControlRelationshipConfig.updateDriverControlsOrder(driverControlsOrder);
}

const getComponentPageDetail = async (siteId, componentId) => {
  if(!componentId.includes(siteId)) {
    throw flaverr(`COMPONENT_ID_NOT_EXIST`, new Error(`${componentId} does not exists`));
  }
  const componentPageCacheKey = `site:${siteId}:componentPage:${componentId}`;
  const componentPageCacheData = await cacheService.get(componentPageCacheKey);
  if(!_.isEmpty(componentPageCacheData)) return JSON.parse(componentPageCacheData);
  const [
    component,
    controlsRecord
  ] = await Promise.all([
    componentservice.fetchComponentById({ deviceId: componentId, siteId }),
    DeviceControlRelationshipConfig.find({
      siteId,
      deviceId: componentId,
      status: 1,
    })
  ]);

  if (_.isEmpty(component)) {
    throw flaverr(`COMPONENT_ID_NOT_EXIST`, new Error(`${componentId} does not exists`));
  }

  if (_.isEmpty(controlsRecord) || _.isEmpty(component?.controls)) {
    component.controlRelationshipMap = { controls : [] };
    await cacheService.set(componentPageCacheKey, JSON.stringify(component));
    await cacheService.expire(componentPageCacheKey,60);
    return component;
  }

  const {
    controls: configuredCommandParameters
  } = component;

  const commandKeyMap = configuredCommandParameters.reduce((acm, curr) => {
    const { key } = curr;
    acm[key] = curr;
    return acm;
  }, {});
  const processedControlConfig = [];
  for (const control of controlsRecord) {
    const {
      controlType,
      controlProperty
    } = control;
    if (controlType === 'BIT') {
      Object.assign(controlProperty.left, componentUtils.controlParamFactoryBuilder(commandKeyMap[controlProperty.left.commandAbbr]));
      Object.assign(controlProperty.right, componentUtils.controlParamFactoryBuilder(commandKeyMap[controlProperty.right.commandAbbr]));
    } else if (controlType === 'VIT') {
      Object.assign(controlProperty, componentUtils.controlParamFactoryBuilder(commandKeyMap[controlProperty.commandAbbr]));
    }
    processedControlConfig.push(control);
  }
  component.controlRelationshipMap = { controls : processedControlConfig };

  await cacheService.set(componentPageCacheKey, JSON.stringify(component));
  await cacheService.expire(componentPageCacheKey,60);
  return component;
}

const fetchControlRelationshipByComponent = async (siteId, componentId) => {
  const [
    component,
    controlsRecord
  ] = await Promise.all([
    componentservice.findOne({ deviceId: componentId }),
    DeviceControlRelationshipConfig.find({
      siteId,
      deviceId: componentId,
      status: 1,
    })
  ]);

  if (_.isEmpty(component)) {
    throw flaverr(`COMPONENT_ID_NOT_EXIST`, new Error(`${componentId} does not exists`));
  }

  if (_.isEmpty(controlsRecord) || _.isEmpty(component?.controls)) return [];
  const {
    controls: configuredCommandParameters
  } = component;

  const commandKeyMap = configuredCommandParameters.reduce((acm, curr) => {
    const { key } = curr;
    acm[key] = curr;
    return acm;
  }, {});
  const processedControlConfig = [];
  // TODO: Mutation of rawControlConfig will do later as discussed
  for (const control of controlsRecord) {
    const {
      controlType,
      controlProperty
    } = control;
    if (controlType === 'BIT') {
      Object.assign(controlProperty.left, componentUtils.controlParamFactoryBuilder(commandKeyMap[controlProperty.left.commandAbbr]));
      Object.assign(controlProperty.right, componentUtils.controlParamFactoryBuilder(commandKeyMap[controlProperty.right.commandAbbr]));
    } else if (controlType === 'VIT') {
      Object.assign(controlProperty, componentUtils.controlParamFactoryBuilder(commandKeyMap[controlProperty.commandAbbr]));
    }
    processedControlConfig.push(control);
  }

  return processedControlConfig;

}
const sendUpdateUserConfigDataParamRequestToIOT = async function({
  siteId,
  componentId,
  abbr,
  expressionDataValue,
  userId
}) {
  try {
    const component = await componentservice.fetchComponentById({
      siteId,
      deviceId: componentId
    });
    if (!component) errorUtils.throwExceptionInvalidComponent(componentId)
    if (!component.hasOwnProperty('data')) errorUtils.throwExceptionInvalidComponentDataParam(componentId)
    const abbrParam = component.data.find(data =>  data.key == abbr && data?.belongsTo?.deviceType == 'global')
    if (!abbrParam) errorUtils.throwExceptionInvalidAbbrToConfigure(componentId, abbr)
    const iotSendDataPacket = {
      siteId,
      componentId,
      abbr,
      value: expressionDataValue,
      userId
    }
    await publishUserConfigParamToIoT(iotSendDataPacket)
  } catch(e) {
    if (e.code == 'E_DEVICE_NOT_FOUND') {
      sails.log('Error > Device Not Found [sendUpdateUserConfigDataParamRequestToIOTs]', e);
      return errorUtils.throwExceptionInvalidDeviceId(e)
    } else {
      sails.log('Error > sendUpdateUserConfigDataParamRequestToIOTs', e);
      throw e
    }
  }
}

const publishUserConfigParamToIoT = async function(iotDataPacket) {
  const {
    siteId,
    componentId,
    abbr,
    value,
    userId
  } = iotDataPacket;
  const topic = `${siteId}/config/${componentId}/componentsync`;
  const requestId =  uuid.v4();

  const syncComp = {
    "operation": "user_param_sync",
    "requestId": requestId,
      "config": {
        "siteId": siteId,
        "componentId": componentId,
        "abbr": abbr,
        "value": value
      }
  }
  sails.log(`[sendUpdateUserConfigDataParamRequestToIOT > Sending data packet to IOT] requestId:${requestId} at time:${moment().unix()*1000}`,syncComp)
  const isPublished = await iotCoreService.publish(topic, syncComp);
  if (!isPublished) {
    errorUtils.throwExceptionUnableToPublishIOT(topic, syncComp);
  }
  await ComponentUddCommandHistory.create({
    status: 2,
    requestId,
    siteId,
    componentId,
    abbr,
    value,
    created_by: userId
  })
}

const updateComponentUserConfigParam = async function({siteId, componentId, requestId, abbr, expressionValue}) {
  const component = await componentservice.findOne({siteId, deviceId: componentId});
  if (!component || !component.hasOwnProperty('data') || component.data.length === 0) {
    sails.log(`Error> updateComponentUserConfigParam do not have data parameter ${componentId}`)
    return};
  const componentDataParam = typeof(component.data) == 'string' && globalHelpers.toJson(component.data) || component.data;
  const paramToUpdate = componentDataParam.find((data) => data.key == abbr && data?.belongsTo?.deviceType == 'global');
  if (!paramToUpdate) {
    sails.log(`Error > updateComponentUserConfigParam component do not have configured param user ${abbr}`);
    return;
  }
  paramToUpdate.expression = expressionValue.toString();

  await updateComponentDataParamInTransaction(componentId, siteId, componentDataParam)
  await ComponentUddCommandHistory.update({
    requestId,
    status: 2
  },{
    status: 1
  })
}

const updateComponentDataParamInTransaction = async function(deviceId, siteId, dataParam) {
  try {
    const params = {
      TransactItems: [
        {
          Update: {
            TableName: "components",
            Key: {
              deviceId: deviceId,
              siteId: siteId,
            },
            UpdateExpression: "SET #data = :dataParam",
            ExpressionAttributeNames: {
              "#data": "data",
            },
            ExpressionAttributeValues: {
              ":dataParam": JSON.stringify(dataParam),
            },
          },
        },
      ],
    }
    const dynamoDb = new AWS.DynamoDB.DocumentClient();
    const result = await dynamoDb.transactWrite(params).promise();
    return result;
  } catch(e) {
    sails.log("Transaction failure: updateComponentDataParamInTransaction", e);
  }

}


module.exports = {
  create: componentservice.create,
  find: componentservice.find,
  findOne: componentservice.findOne,
  update: componentservice.update,
  fetchComponentById: componentservice.fetchComponentById,
  invalidateComponentConfigCache: componentservice.invalidateComponentConfigCache,
  getDriverComponentIds: getDriverComponentIds,
  fetchControlRelationshipByComponent,
  getComponentPageDetail,
  getComponentsBySiteId: componentservice.getComponentsBySiteId,
  getComponentCatBySiteId: componentservice.getComponentCatBySiteId,
  getLinkedConfiguratorPageByComponentId: componentservice.getLinkedConfiguratorPageByComponentId,
  getDevicesOnComponent: componentservice.getDevicesOnComponent,
  updateDevices: componentservice.updateDevices,
  deleteComponent: componentservice.deleteComponent,
  getDeviceTypeByComponentId: async function (deviceId, siteId) {
    const componentInfo = await this.fetchComponentById({
      deviceId,
      siteId,
    });
    if (!componentInfo || !componentInfo?.deviceType) {
      throw flaverr(
        "E_COMPONENT_NOT_FOUND",
        new Error(`No component Exists with component Id ${deviceId}`)
      );
    }
    return componentInfo?.deviceType;
  },
  /**
   *
   * @param {string} siteId
   * @returns Generated the next possible componentId for a site.
   */
  generateComponentId: async function (siteId) {
    try {
      let componentList = await Components.find({ siteId: siteId });
      let currentLargestSequence = 0;
      for (let i = 0; i < componentList.length; i++) {
        const { deviceId } = componentList[i];
        if (!deviceId) continue;
        let deviceIdSequence = deviceId.split("_")[1];
        if (isNaN(deviceIdSequence)) continue;
        let sequenceId = Number(deviceIdSequence);
        if (sequenceId > currentLargestSequence) {
          currentLargestSequence = sequenceId;
        }
      }
      return `${siteId}_${currentLargestSequence + 1}`;
    } catch (err) {
      sails.log.error(err);
      return null;
    }
  },
  getComponentListByDriverAndDeviceType: async function (siteId, driverType, deviceType) {
    const componentList = await Components.find({
      siteId,
      driverType,
      deviceType,
    });
    return componentList
      .map((component) => {
        if (component.deviceId && component.name) {
          return {
            deviceId: component.deviceId,
            name: component.name,
            deviceType: component.deviceType,
            driverType: component.driverType,
          };
        }
      })
      .filter(Boolean)
      .sort((a, b) => a.name.localeCompare(b.name));
  },
  async getComponents(siteId) {
    if (!siteId) return [];
    return await componentservice.getComponentsBySiteId(siteId);
  },
  getComponentListBySiteId: async function (siteId) {
    const componentList = await Components.find({
      siteId,
    });
    return componentList
      .map((component) => {
        if (component.deviceId && component.name) {
          return {
            deviceId: component.deviceId,
            name: component.name,
            deviceType: component.deviceType,
            driverType: component.driverType,
          };
        }
      })
      .filter(Boolean)
      .sort((a, b) => a.name.localeCompare(b.name));
  },
  getComponentAssetData: async function (deviceId, siteId, paramType) {
    const Component = await this.getComponentConfig(deviceId);
    const { data: _dataParameters, controlRelationshipMap: { controls } } = Component;
    const result = {
      dataParam: [],
      controlParam: controls,
    }

    result.dataParam = (function getDataParams(_dataParameters, paramType) {
      let dataParameters = []
      if (paramType == "all") {
        dataParameters = _dataParameters;
      } else {
        dataParameters = _dataParameters.filter(componentUtils.isDataParameterConfigured)
      }
      return dataParameters.map((it) => {
        const { key: abbr, displayName: name } = it
        return {
          abbr, name
        }
      })
    }(_dataParameters, paramType))

    result.controlParam = (function getControlParams(controls, paramType) {
      if (paramType == "all") {
        return controls;
      }

      return controls.filter(componentUtils.isControlConfigured)
    }(controls || [], paramType))
    return result;
  },
  async getComponentListByType(siteId, componentType) {
    // eslint-disable-next-line no-undef
    if (_.isEmpty(siteId)) {
      throw new flaverr("E_SITE_ID_MISSING", new Error("site id is missing"));
    }
    if (!Array.isArray(componentType)) {
      throw new flaverr(
        "E_TYPE_FORMAT_ISSUE",
        new Error("component type parameter should be an array")
      );
    }
    if (componentType.length === 0) {
      throw new flaverr(
        "E_COMPONENT_TYPE_LENGTH_ISSUE",
        new Error("please pass atleast one componentType")
      );
    }

    const componentTypeSet = new Set(componentType);
    const _components = await this.getComponents(siteId);
    return _components.filter((it) => {
      if (it.hasOwnProperty("deviceType") && componentTypeSet.has(it.deviceType)) {
        return true;
      }
      return false;
    });
  },
  /**   -
   * @function getSiteDeviceComponent
   * @param {Object} componentData :- old component data
   * @param {Object} updatedComponentData:- the component data the was updated as per the user request
   * @description Returns component response schema along with data and controls of given componentId
   * @returns {Object} responseObject
   */
  async getSiteDeviceComponent(componentData, updatedComponentData) {
    let responseObject = { ...componentData };
    responseObject.updatedAt = updatedComponentData.updatedAt;
    const {
      driverType,
      deviceType,
      isVirtualComponent
    } = componentData;
    let deviceTypeInfo = await deviceTypeService.findOne({
      driverType,
      deviceType,
    });
    if (!deviceTypeInfo) deviceTypeInfo = [];
    let dataKeys = {};
    let controlKeys = {};
    responseObject.data = [];
    responseObject.controls = [];

    if (isVirtualComponent == "1") {
      responseObject.isVirtualComponent = "1";
    } else {
      responseObject.isVirtualComponent = "0";
    }
    if (updatedComponentData.svgId) {
      responseObject.svgId = updatedComponentData.svgId;
    }
    for (const d of updatedComponentData.data) {
      const { result, parameterDataKeys } = componentUtils.generateCompDeviceParameterSchema(
        "data",
        d
      );
      dataKeys = Object.assign(dataKeys, parameterDataKeys);
      responseObject.data.push(result);
    }
    for (const c of updatedComponentData.controls) {
      const { result, parameterControlKeys } = componentUtils.generateCompDeviceParameterSchema(
        "controls",
        c
      );
      controlKeys = Object.assign(controlKeys, parameterControlKeys);
      responseObject.controls.push(result);
    }

    responseObject.data.forEach((d) => {
      if (!dataKeys.hasOwnProperty(d.key)) {
        dataKeys[d.key] = d;
      }
    });
    responseObject.controls.forEach((c) => {
      if (!dataKeys.hasOwnProperty(c.key)) {
        controlKeys[c.key] = c;
      }
    });
    const parameters = deviceTypeInfo.parameters || [];
    parameters.forEach((e) => {
      if (e.type == "data") {
        if (!dataKeys.hasOwnProperty(e.abbr)) {
          const { result } = componentUtils.generateCompDeviceParameterSchema("data", e);
          responseObject.data.push(result);
        }
      } else if (!controlKeys.hasOwnProperty(e.abbr)) {
        const { result } = componentUtils.generateCompDeviceParameterSchema("controls", e);
        responseObject.controls.push(result);
      }
    });
    return responseObject;
  },
  removeCachedComponentCategories: function (siteId) {
    try {
      const cacheKey = componentUtils.getComponentCategoryCacheKey(siteId);
      return cacheService.delete(cacheKey)
    } catch (e) {
      sails.log.error('Error while deleting component category [componentCategories]', e);
      return null;
    }
  },
  /**
   * @description Update old component data by replacing with new component
   * @param {Object} requestedComponent :- New component parameter requested object
   * @param {Object} componentData :- Old componentData
   * @returns {Object} updatedComponent
   */
  async updateComponent(requestedComponent, componentData) {
    const {
      name,
      data,
      controls,
      regionId,
      controllerId,
      isVirtualComponent,
      svgId,
      isInMaintenanceMode="0"
    } = requestedComponent;
    const {
      deviceId,
      siteId
    } = componentData;
    const siteInfo = await siteService.findOne({siteId, status: 1});
    const isSiteIBMS = siteInfo && siteInfo.hasOwnProperty('industryType') && siteInfo.industryType == 'ibms';
    let newCompObj = {};
    await componentservice.invalidateComponentConfigCache(deviceId);
    await cacheService.delete(`siteId:${siteId}:available_device_component_categories`);
    if(isInMaintenanceMode != componentData?.isInMaintenanceMode){
      newCompObj.isInMaintenanceMode = isInMaintenanceMode;
    }
    if (svgId && svgId != componentData.svgId) {
      newCompObj.svgId = svgId;
    }
    if (name && name != componentData.name) {
      newCompObj.name = name;
    }
    if (regionId && regionId != componentData.regionId) {
      newCompObj.regionId = regionId;
    }
    if (controllerId && controllerId != componentData.controllerId) {
     if(!isSiteIBMS) await isValidController({
        newControllerId:controllerId,
        siteId,
        regionId
      });
      newCompObj.controllerId = controllerId;
    }

    if (isVirtualComponent == "1") {
      newCompObj.isVirtualComponent = "1";
    } else {
      newCompObj.isVirtualComponent = "0";
    }

    if (data) {
      newCompObj.data = JSON.stringify(data);
    }
    if (controls) {
      newCompObj.controls = JSON.stringify(controls);
    }
    newCompObj.updatedAt = moment()
      .utc()
      .format('YYYY-MM-DDTHH:mm:ss.SSS\\Z');
    try {
      await Components.update({
        deviceId,
        siteId,
      })
        .set({ ...newCompObj });
    } catch (e) {
      throw flaverr('E_COMPONENT_NOT_UPDATED', new Error('Component device not updated'));
    }
    newCompObj.data = JSON.parse(newCompObj.data);
    newCompObj.controls = JSON.parse(newCompObj.controls);
    return newCompObj;
  },
  /**
   * @description Update device and set componentId to computed componentId
   * @param {Object}  component :- Object  {
   addedDevices, {Array}
   deletedDevices, {Array}
   deviceId: updatedComponentId,
   siteId,
   }
   * @returns {Object} updatedDevice
   */
  async updateComponentInfoInDevices({
    addedDevices,
    deletedDevices,
    deviceId: updatedComponentId,
    siteId,
  }) {
    let promiseArr = [];
    addedDevices.forEach((deviceId) => {
      let $update = getDeviceUpdatedCompData({
        deviceId,
        siteId,
        updatedComponentId,
        operation: 'add',
      });
      promiseArr.push($update);
    });
    deletedDevices.forEach((deviceId) => {
      let $update = getDeviceUpdatedCompData({
        deviceId,
        siteId,
        updatedComponentId,
        operation: 'remove',
      });
      promiseArr.push($update);
    });
    let devicesExist = [];
    const validateDevices = await Promise.all(promiseArr);
    const devicesNotUpdated = [];
    validateDevices.forEach((device) => {
      const {
        status,
        deviceId,
        operation
      } = device;
      if (status !== 'fullfilled') {
        if (operation == 'add') {
          devicesNotUpdated.push(deviceId);
        }
      } else {
        devicesExist.push(device);
      }
    });

    if (!_.isEmpty(devicesNotUpdated)) {
      throw flaverr(
        'E_DEVICES_NOT_FOUND',
        new Error(`${devicesNotUpdated.join(', ')} devices not found`)
      );
    }
    /**
     * Updating site_configTS and componentId in devices
     * Removing devices from componentId string in case of remove componentId from device
     * And update the new componentId in devices in case of add componentId to device
     * deviceExist contains all the devices that already exist
     * All queries being done in transaction if any of the query failed it will not be reflected to other
     */
    const transactionItems = [
      {
        Update: {
          Key: {
            key: { S: `${siteId}_configTS` },
          },
          ExpressionAttributeNames: {
            '#U': 'updatedAt',
            '#value': 'value',
          },
          ExpressionAttributeValues: {
            ':u': {
              S: moment()
                .utc()
                .format(),
            },
            ':v': {
              S: globalhelper.getCurrentUnixTs()
                .toString(),
            },
          },
          UpdateExpression: 'SET #U = :u, #value = :v',
          TableName: 'dyanmokeystores',
        },
      },
    ];

    devicesExist.forEach((device) => {
      transactionItems.push({
        Update: {
          ConditionExpression:
            'attribute_exists(#deviceId) AND #deviceId = :expectedDeviceId AND attribute_exists(#siteId) AND #siteId = :expectedSiteId',
          ExpressionAttributeNames: {
            '#componentId': 'componentId',
            '#siteId': 'siteId',
            '#deviceId': 'deviceId',
          },
          ExpressionAttributeValues: {
            ':expectedDeviceId': { S: device.deviceId },
            ':expectedSiteId': { S: siteId },
            ':value': { S: device.compData },
          },
          TableName: 'devices',
          Key: {
            deviceId: { S: device.deviceId },
            siteId: { S: siteId },
          },
          UpdateExpression: 'SET #componentId = :value',
        },
      });
    });
    try {
      const documentClient = new AWS.DynamoDB();
      const result = await documentClient
        .transactWriteItems({
          TransactItems: transactionItems,
        })
        .promise();
      return result;
    } catch (e) {
      sails.log.error('Transaction failed > updateComponentAPI', e);
      throw flaverr(
        'E_TRANSACTION_FAILED',
        new Error('Something went wrong device can not be updated please try again later')
      );
    }
  },
  /**
   * @description Generate componentId
   * @param {Object} oldComponents :- updating oldComponentId
   * @param {String} componentId :- updating newComponentId
   * @param {String} operation :- [add, remove]
   * @returns {String} deviceId :- comma separated deviceIds
   */
  generateComponentIds(oldComponents, componentId, operation) {
    if (!oldComponents) return 'NA';
    if (operation === 'remove') {
      let cIdSet = new Set(oldComponents.split(','));
      cIdSet.delete(componentId);
      componentId = [...cIdSet].join(',');
      if (componentId === '') componentId = 'NA';
    } else if (operation === 'add') {
      //edge case for device is attached to component for the first time
      if (typeof oldComponents === 'undefined') oldComponents = '';
      if (oldComponents != 'NA' && oldComponents !== 'null') {
        let cIdSet = new Set(oldComponents.split(','));
        cIdSet.add(componentId);
        componentId = [...cIdSet].join(',');
      }
    }
    return componentId;
  },
  /**
   * Retrieves the configuration for a specific component based on its ID.
   * @async
   * @param {string} componentId - The ID of the component to retrieve the configuration for.
   * @throws {Error} If the component ID does not exist or if driver details are not found.
   * @returns {Promise<object>} The configuration object for the component.
   */
  getComponentConfig: getComponentConfig,
  orderControlsByComponent: orderControlsByComponent,
  async getLastKnownModeByComponentId(componentId) {
    const componentConfig = await getComponentConfig(componentId);
    if (!componentConfig) {
      throw flaverr(
        'E_COMPONENT_NOT_FOUND',
        new Error(`Configuration of componentId(${componentId}) does not exists`)
      );
    }
    const {
      controls,
      modeLabel
    } = componentConfig;

    if (!componentConfig.controlRelationshipMap || !componentConfig.controlRelationshipMap.controls.length) {
      throw flaverr({
        message: `Either control relationship doesn't exist or controls do not exist for this component`,
        code: `E_CONTROL_NOT_FOUND`,
        operation: 'getLastKnownModeByComponentId',
        data: { componentId },
      });
    }

    const componentModeDetailsPromise = [];
    for (const {
      device_abbr: commandAbbr,
      deviceId
    } of controls) {
      if (deviceId) {
        componentModeDetailsPromise.push(ModeService.fetchLastKnowModeByDeviceCommandAbbr(deviceId, commandAbbr));
      }
    }
    const modeDetail = (await Promise.all(componentModeDetailsPromise))
    let componentModeResponse = componentUtils.componentModeResponseBuilder(
      modeDetail,
      componentConfig.controlRelationshipMap.controls,
    );
    return {
      componentId,
      assetModeLabel: {
        key: modeLabel || ModeService.DEFAULT_MODE,
        label: sails.config.custom.MODE_TRACK_LABEL[modeLabel] || ModeService.DEFAULT_MODE,
      },
      controlWiseMode: componentModeResponse,
    };
  },
  isComponentExistByDriverType: async (component, siteId, deviceType) => {
    const params = {
      TableName: 'components',
      IndexName: 'deviceId-siteId-index',
      KeyConditionExpression: '#siteId = :siteId AND #deviceId = :deviceId',
      ExpressionAttributeNames: {
        '#siteId': 'siteId',
        '#deviceId': 'deviceId',
      },
      ExpressionAttributeValues: {
        ':siteId': siteId,
        ':deviceId': component,
      },
      ProjectionExpression: 'driverType, deviceType',
    };
    try {
      const dynamoDbDocClient = new AWS.DynamoDB.DocumentClient({
        region: process.env.REGION,
      });
      const queryResult = await dynamoDbDocClient.query(params).promise();
      if (queryResult.Count == 0 || queryResult.Count > 1) {
        return false
      }
      if (
        queryResult.Items[0].deviceType != deviceType
      ) {
        return false
      }
      return true
    } catch (error) {
      sails.log.error('Error querying components from DynamoDB using aws-sdk: fetchConfiguratorTable', error);
      throw error;
    }
  },
  async changeAssetControlMode({
    componentId,
    modeChangeDetail,
    assetModeLabel,
    transactionId
  }) {
    const ComponentDetail = await this.getComponentConfig(componentId);
    const { controlRelationshipMap: { controls }, siteId, deviceType } = ComponentDetail;
    componentUtils.throwExceptionInvalidControl(modeChangeDetail.map((it) => it.controlAbbr), controls.map((it) => it.controlAbbr));
    const changeControlModeMap = modeChangeDetail.reduce((acm, curr) => {
      acm[curr.controlAbbr] = curr.mode;
      return acm;
    }, {})
    const componentControls = controls.filter((it) => changeControlModeMap.hasOwnProperty(it.controlAbbr))
    componentUtils.throwExceptionControlNotConfigured(componentControls);
    const controlCommandDeviceList = []
    for (const {
      controlProperty,
      controlType,
      controlAbbr
    } of componentControls) {
      if (controlType == "BIT") {
        const {
          left: {
            deviceId: leftCommandDeviceId,
            device_abbr: leftDeviceCommandAbbr,
          },
          right: {
            deviceId: rightCommandDeviceId,
            device_abbr: rightDeviceCommandAbbr
          }
        } = controlProperty;
        controlCommandDeviceList.push({
          deviceId: leftCommandDeviceId,
          commandAbbr: leftDeviceCommandAbbr,
          mode: changeControlModeMap[controlAbbr],
          siteId
        }, {
          deviceId: rightCommandDeviceId,
          commandAbbr: rightDeviceCommandAbbr,
          mode: changeControlModeMap[controlAbbr],
          siteId
        });
      } else if (controlType == "VIT") {
        const { deviceId, device_abbr: commandAbbr } = controlProperty
        controlCommandDeviceList.push({ deviceId, commandAbbr, mode: changeControlModeMap[controlAbbr], siteId })
      } else { }
    }
    const response = await ModeService.changeControlModeByCommand(controlCommandDeviceList,transactionId);
    await this.update({
      siteId,
      deviceId: componentId
    }, { modeLabel: assetModeLabel });
    return response;
  },
  async fetchControlsByComponentId(componentId) {
    const componentConfig = await this.getComponentConfig(componentId);
    return (
      componentConfig
      && componentConfig.controlRelationshipMap
      && componentConfig.controlRelationshipMap.controls
      || []
    );
  },
  async fetchConfiguredControlsByComponentId(componentId) {
    const componentConfig = await this.getComponentConfig(componentId);
    if (
      !(componentConfig
        && componentConfig.controlRelationshipMap
        && componentConfig.controlRelationshipMap.controls)) {
      return [];
    }
    return componentUtils.configuredControlsConfig(componentConfig.configuredCommandParameters, componentConfig.controlRelationshipMap.controls);
  },
  async fetchConfiguredControlsWithSyncStatus(componentId) {
    const componentConfig = await this.getComponentConfig(componentId);
    if (
      !(componentConfig
        && componentConfig.controlRelationshipMap
        && componentConfig.controlRelationshipMap.controls)) {
      return { controls: [] };
    }
    const controls = componentConfig.controlRelationshipMap.controls;
    await this.addControllerIdToControlRelationship(controls);
    return {
      controls: componentUtils.configuredControlsConfig(componentConfig?.configuredCommandParameters, controls),
      isSyncWithDriver: componentConfig?.controlRelationshipMap?.isSyncWithDriver
    }
  },
  async addControllerIdToControlRelationship(controls) {
    const deviceIds = new Set();
    for (const control of controls) {
      if (control.controlType === "VIT") {
        deviceIds.add(control?.controlProperty?.deviceId);
      } else if (control.controlType === "BIT") {
        deviceIds.add(control?.controlProperty?.left?.deviceId);
        deviceIds.add(control?.controlProperty?.right?.deviceId);
      }
    }
    const controllerIdsMap = await getDeviceControllerMap([...deviceIds]);
    componentUtils.assignControllerIdToRespectiveDeviceId(controls, controllerIdsMap);
  },
  async getComponentListByAbbr(deviceDriverDetails, siteId) {
    const components = [];
    for (const {
      driverType,
      deviceType,
      abbr
    } of deviceDriverDetails) {
      let componentList = await Components.find({
        siteId,
        driverType,
        deviceType,
      });
      componentList = componentList.filter(component =>
        component.data && JSON.parse(component.data).some(dataItem => dataItem.key === abbr && dataItem.expression && dataItem.expression.trim().length > 0)
      ).map(component => {
        return {
          deviceId: component.deviceId,
          deviceType: component.deviceType,
          driverType: component.driverType,
          name: component.name,
          class: 'component',
          abbr
        };
      });
      components.push(...componentList);
    }
    return components;
  },

  /**
   * @description returns an Array of deviceIds being used for the calculation of the specified component DATA parameter.
   * This list is non unique, ie, same deviceIds can be present.
   * @param {string} deviceId deviceId of the component
   * @param {string} parameter parmaeter `abbr` or `key` for which the deviceList is required to be fetched.
   * @returns {Array} deviceList
   */
  getDeviceListFromDataParameter: async function(deviceId, parameters){
    try {
      const componentConfiguration = await this.findOne({deviceId});
      const { data: dataParameters } = componentConfiguration;
      const relevantParameters = dataParameters.reduce((relevantParameters, param) =>{
        if (parameters.includes(param["key"]))
          relevantParameters.push(param);
        return relevantParameters;
      }, []);
      if (relevantParameters.length == 0) return []; // Returning empty list incase no relevant parameters found.
      let deviceList = [];
      relevantParameters.forEach(relevantParameter => {
        const deviceListFromExpression = Components.getDeviceParamListFromDataExpression(relevantParameter.expression)
          .map(deviceInfo => deviceInfo.deviceId)
          .filter(Boolean);
        deviceList = [...deviceList, ...deviceListFromExpression];
      });
      return deviceList;
    } catch (error) {
      sails.log.error("Component Service >>> getDeviceListFromDataParameter >> Unknown error! Returning empty list");
      return [];
    }
  },
  fetchControlRelationShipByComponentList: async function (siteId, componentList) {
    const $componentsDetail = this.fetchComponentDetailByList(siteId, componentList);
    const $controlRelationships = DeviceControlRelationshipConfig.find({
      where: {
        siteId,
        deviceId: {
          in: componentList,
        },
        status: 1,
      },
    });
    const [componentsDetail, controlRelationships] = await Promise.all([
      $componentsDetail,
      $controlRelationships,
    ]);
    const componentRawControlMap = componentsDetail.reduce((acm, curr) => {
      const { controls: configuredCommandParameters, deviceId: componentId } = curr;
      if (_.isEmpty(configuredCommandParameters)) {
        acm[componentId] = {};
        return acm;
      }
      const commandKeyMap = configuredCommandParameters.reduce((acm, curr) => {
        const { key } = curr;
        acm[key] = curr;
        return acm;
      }, {});
      acm[componentId] = commandKeyMap;
      return acm;
    }, {});

    const componentsControlRelMap = controlRelationships.reduce((acm, curr) => {
      const {deviceId : componentId} = curr;
      if (!componentId || !componentRawControlMap.hasOwnProperty(componentId)) return acm
      if (!acm.hasOwnProperty(componentId)) acm[componentId] = [];
      acm[componentId].push(curr);
      return acm;
    }, {});


    for (const componentId in componentsControlRelMap) {
      for (let control of componentsControlRelMap[componentId]) {
        const { controlType, controlProperty } = control;
        if (controlType === "BIT") {
          Object.assign(
            controlProperty.left,
            componentUtils.controlParamFactoryBuilder(
              componentRawControlMap[componentId][controlProperty.left.commandAbbr]
            )
          );
          Object.assign(
            controlProperty.right,
            componentUtils.controlParamFactoryBuilder(
              componentRawControlMap[componentId][controlProperty.right.commandAbbr]
            )
          );
        } else if (controlType === "VIT") {
          Object.assign(
            controlProperty,
            componentUtils.controlParamFactoryBuilder(
              componentRawControlMap[componentId][controlProperty.commandAbbr]
            )
          );
        }
      }
    }
    return componentsControlRelMap;
  },
  fetchComponentDetailByList: async (siteId, componentList) => {
    const componentListDetails = await Promise.all(
      componentList.map((deviceId) => componentservice.fetchComponentById({ siteId, deviceId }))
    );
    return componentListDetails.filter(Boolean);
  },
  getLastKnownModeByComponentIdV2: async function (siteId, componentList) {
    const componentControls = await this.fetchControlRelationShipByComponentList(siteId,componentList);
    const componentModeDetailsPromise = [];
    for (const componentId in componentControls) {
      for(const control of componentControls[componentId]){
        const { controlType, controlProperty } = control;
        if (controlType === "BIT") {
          const {
            device_abbr:left_device_abbr,deviceId:leftDeviceId } = controlProperty.left;
          const {
            device_abbr:right_device_abbr,deviceId:rightDeviceId } = controlProperty.right;
          if(_.isEmpty(left_device_abbr) || _.isEmpty(right_device_abbr) || _.isEmpty(leftDeviceId) || _.isEmpty(rightDeviceId)) continue;
          componentModeDetailsPromise.push(
            ModeService.fetchLastKnowModeByDeviceCommandAbbr(rightDeviceId, right_device_abbr)
          );
          componentModeDetailsPromise.push(
            ModeService.fetchLastKnowModeByDeviceCommandAbbr(leftDeviceId, left_device_abbr)
          );
        } else if(controlType === "VIT") {
          const { device_abbr,deviceId } = controlProperty;
          if(_.isEmpty(deviceId) || _.isEmpty(device_abbr)) continue;
          componentModeDetailsPromise.push(
            ModeService.fetchLastKnowModeByDeviceCommandAbbr(deviceId, device_abbr)
          );
        }
      }
    }
    const modeDetail = await Promise.all(componentModeDetailsPromise);

    return componentUtils.componentModeResponseBuilderV2(
      modeDetail,
      componentControls
    );
  },
  sendUpdateUserConfigDataParamRequestToIOT,
  updateCommandUCDParam,

  async validateDriverDetails(driverType, deviceType) {
    const driverDetail = await deviceTypeService.findOne({ driverType, deviceType })
    return driverDetail;
  },

  async getControllerDeviceRecord(controllerId, siteId) {
    const controllerRecord = await deviceService.findOne({ deviceId: controllerId, siteId });
    return controllerRecord;
  },

  async linkComponentToController(controllerRecord, componentId, siteId) {
    const existingComponents = controllerRecord.componentId
      ? controllerRecord.componentId.split(',')
      : [];
    existingComponents.push(componentId);

    await deviceService.update({ deviceId: controllerRecord.deviceId, siteId }, {
      componentId: existingComponents.join(',')
    });
  }
};

async function updateCommandUCDParam(dataParamIoTFeedbackPacket) {
    try {
        const {siteId, componentId, requestId, status, error} = dataParamIoTFeedbackPacket;
        const configuredParam = await ComponentUddCommandHistory.findOne({requestId, status: 2});
        if (!configuredParam) {
          sails.log(`[Error >updateCommandUCDParam ] requestId: ${requestId} is Invalid.`)
          return
        }
        if (siteId != configuredParam.siteId || componentId != configuredParam.componentId) errorUtils.throwExceptionInvalidRequestId(requestId);
        const {abbr, value: expressionValue} = configuredParam;
        if (status == 1) {
          await updateComponentUserConfigParam({siteId, componentId,requestId, abbr, expressionValue});
        } else {
          sails.log(`[Error: updateCommandUCDParam] Failed to update user configuration parameter. Error: ${error}. Request ID: ${requestId}`);
          await ComponentUddCommandHistory.update({
            requestId,
            status: 2
          },{
            status: 0
          })
        }
        const socketResponseRecord = {
          componentId: componentId,
          key: abbr,
          expression: expressionValue,
          status,
        };
        if (error && error != 'undefined') socketResponseRecord.error = error;
        socketService.notifyServiceOnJouleTrack(siteId, 'public', 'component',
          {
            data: socketResponseRecord,
            'event': 'updateUserConfigParam'
        });
        await componentservice.invalidateComponentConfigCache(componentId);
      return;
    } catch (e) {
      if (e.HTTP_STATUS_CODE == 400) {
        sails.log.error(`[error > bad-request] componentservice.eventHandler feedback/component_config_sync: ${e}`);
        throw e
      } else {
        sails.log.error(`componentservice.eventHandler feedback/component_config_sync: ${e}`);
        throw e
      }
    }
}

async function isValidController({
  newControllerId,
  siteId,
  regionId
}) {
  const device = await deviceService.getControllerById(siteId, newControllerId);
  if (!device) {
    throw flaverr('E_DEVICE_NOT_FOUND', new Error(`Requested controller controllerId:${newControllerId} not found.`));
  } else if (device.regionId != regionId) {
    throw flaverr('E_DEVICE_NOT_FOUND', new Error(`Requested controller controllerId:${newControllerId} region does not match.`));
  }
  const isSlaveController = device && device.hasOwnProperty('isSlaveController') && device.isSlaveController === 1;
  if (isSlaveController) {
    throw flaverr('E_DEVICE_NOT_FOUND', new Error(`Slave Controller can't be added as a Primary Controller.`));
  }
  return;
}
