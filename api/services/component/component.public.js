const componentservice = require("./component.service");
const ComponentCache = require("./cachingService");

module.exports = {
  findOne: componentservice.findOne,
  fetchComponentById: componentservice.fetchComponentById,
  find: componentservice.find,
  update: componentservice.update,
  cachedDataParameter: ComponentCache.dataParameter,
  getComponentListByType: componentservice.getComponentListByType,
  getComponents: componentservice.getComponents,
  getComponentConfig: componentservice.getComponentConfig,
  getComponentPageDetail : componentservice.getComponentPageDetail,
  fetchControlsByComponentId: componentservice.fetchControlsByComponentId,
  fetchConfiguredControlsByComponentId: componentservice.fetchConfiguredControlsByComponentId,
  getComponentCatBySiteId: componentservice.getComponentCatBySiteId,
  isComponentExistByDriverType: componentservice.isComponentExistByDriverType,
  getComponentListByDriverAndDeviceType: componentservice.getComponentListByDriverAndDeviceType,
  getComponentListBySiteId: componentservice.getComponentListBySiteId,
  getDeviceListFromDataParameter: componentservice.getDeviceListFromDataParameter,
  invalidateComponentConfigCache: componentservice.invalidateComponentConfigCache,
  fetchControlRelationshipByComponent: componentservice.fetchControlRelationshipByComponent,
  sendUpdateUserConfigDataParamRequestToIOT:  componentservice.sendUpdateUserConfigDataParamRequestToIOT,
  updateCommandUCDParam: componentservice.updateCommandUCDParam,
  getDeviceTypeByComponentId: componentservice.getDeviceTypeByComponentId,
  fetchTonnageDelivery : componentservice.fetchTRHLoadPattern
};
