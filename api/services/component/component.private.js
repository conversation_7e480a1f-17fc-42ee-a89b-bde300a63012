const globalHelpers = require("../../utils/globalhelper");
const cacheService = require("../cache/cache.public");
const dynamoDBClient = require("../dynamoClient/daxClient");
const AWS = require("aws-sdk");
/* eslint-disable no-undef */
module.exports = {
  /**
   * Components table.
   */

  find: async (searchParams) => {
    // Components.find
    let components = await Components.find(searchParams);
    let componentArr = components.map((component) => {
      component.data = globalHelpers.convertStringArrayToJSONArray(component.data) || [];
      component.controls = globalHelpers.convertStringArrayToJSONArray(component.controls) || [];

      delete component.info;
      return component;
    });
    return componentArr;
  },
  getComponentsBySiteId: async (siteId) => _getComponentsBySiteId(siteId, null, []),
  getComponentCatBySiteId: async (siteId) => _getComponentCatBySiteId(siteId),
  /*** Avoid using siteId in search parameters. If you need to use siteId, please call fetchComponentById instead. */
  findOne: async (searchParams) => {
    // Components.findOne
    const { deviceId } = searchParams;
    let cachedData = null;
    if (deviceId) {
      cachedData = await _getComponentConfigFromCache(deviceId);
    }
    if (cachedData) return cachedData;

    let components = await Components.find(searchParams).limit(1);
    let componentArr = components.map((component) => {
      component.data = globalHelpers.convertStringArrayToJSONArray(component.data) || [];
      component.controls = globalHelpers.convertStringArrayToJSONArray(component.controls) || [];

      delete component.info;
      return component;
    });
    await _setComponentConfigInCache(componentArr[0]?.deviceId, componentArr[0]);
    return componentArr[0];
  },
  /**
   * @description Fetch a component by its deviceId and optionally by siteId.
   * @optional siteId. If siteId is provided, the query will use the deviceId-siteId-index.
   * @param {Object} params - The parameters for the query.
   * @param {string} params.deviceId - The ID of the device to fetch. (Required)
   * @param {string} [params.siteId] - The ID of the site to fetch. (Optional)
   * @returns {Object|null} The fetched component or null if not found.
   * @throws {Error} If deviceId is not provided or if there is an error querying DynamoDB.
   */
  fetchComponentById: async ({ siteId, deviceId }) => {
    if (!deviceId) {
      throw new Error("No deviceId provided");
    }
    const cacheData = await _getComponentConfigFromCache(deviceId);
    if (cacheData) return cacheData;
    const params = {
      TableName: "components",
      IndexName: "deviceId-siteId-index",
      KeyConditionExpression: siteId
        ? "#siteId = :siteId AND #deviceId = :deviceId"
        : "#deviceId = :deviceId",
      ExpressionAttributeNames: siteId
        ? {
            "#siteId": "siteId",
            "#deviceId": "deviceId",
          }
        : {
            "#deviceId": "deviceId",
          },
      ExpressionAttributeValues: siteId
        ? {
            ":siteId": siteId,
            ":deviceId": deviceId,
          }
        : {
            ":deviceId": deviceId,
          },
    };
    try {
      const dynamoDbDocClient = await sails.helpers.getDynamoDocClient();
      const queryResult = await dynamoDbDocClient.query(params).promise();
      const result = queryResult.Items[0] ?? null;

      if (result === null) return result;

      result.data = globalHelpers.convertStringArrayToJSONArray(result.data) || [];
      result.controls = globalHelpers.convertStringArrayToJSONArray(result.controls) || [];
      delete result.info;
      await _setComponentConfigInCache(deviceId, result);
      return result;
    } catch (error) {
      sails.log.error(
        "Error querying components from DynamoDB using aws-sdk: fetchComponentById",
        error
      );
      throw error;
    }
  },
  update: async (searchParams, updateValue) => {
    const { deviceId } = searchParams;
    if (deviceId) {
      await invalidateComponentConfigCache(deviceId);
    }
    return Components.update(searchParams, updateValue);
  },
  create: async (searchParams) => {
    let { data, controls } = searchParams;
    // Stringify data and control objects because sails 0.12, ie, Jouletrack-API does not support MAP objects in Dynamo.
    // Can be removed after ALL components table interaction has been deprecated from Jouletrack-API.
    if (typeof data == "object") searchParams.data = JSON.stringify(data);
    if (typeof controls == "object") searchParams.controls = JSON.stringify(controls);
    return Components.create(searchParams);
  },
  invalidateComponentConfigCache,

getLinkedConfiguratorPageByComponentId: async (componentId) => {
  const getLinkedConfiguratorPageQuery = `
    WITH combined_pages AS (
        SELECT 
            STRING_AGG(ssp.title, ',') AS title,
            ctr.device_id
        FROM 
            configurator_table_row ctr
        JOIN 
            configurator_table ct ON ct.id = ctr.table_id
        JOIN 
            configurator_table_group ctg ON ctg.id = ct.table_group_id
        JOIN 
            sub_system_pages ssp ON ssp.id = ctg.pages_ref_id
        WHERE 
            ctr.device_id = $1
            AND ctr.status = 1 
            AND ct.status = 1 
            AND ctg.status = 1
            AND ssp.status IN (1, 2)
            AND ctr.device_id IN (
                SELECT device_id
                FROM sankey_graph_nodes
                WHERE device_id = $1
            )
        GROUP BY 
            ctr.device_id
        UNION ALL
        SELECT 
            STRING_AGG(ssp.title, ',') AS title,
            ctd.device_id
        FROM 
            sub_system_pages ssp
        JOIN 
            configurator_tagged_devices ctd ON ctd.sub_system_page_id = ssp.id
        WHERE 
            ctd.device_id = $1
            AND ctd.status = 1 
            AND ctd."device_Type" = 'component'
            AND ssp.status IN (1, 2)
            AND ctd.device_id IN (
                SELECT device_id
                FROM sankey_graph_nodes
                WHERE device_id = $1
            )
        GROUP BY 
            ctd.device_id
    )
    SELECT 
        title, device_id 
    FROM 
        combined_pages;
  `;

  const linkedConfiguratorPages = await sails
      .getDatastore("postgres")
      .sendNativeQuery(getLinkedConfiguratorPageQuery, [componentId]);

  return linkedConfiguratorPages.rows;
},

  getDevicesOnComponent: async (siteId, componentId) => {
    let devices = await Devices.find({ siteId });
    return devices.filter(dev => dev.componentId === componentId);
  },

  updateDevices: async (devices) => {
    let promiseArr = [];
    for (let device of devices) {
      let { siteId, deviceId } = device;
      let componentId = "null";
      promiseArr.push(Devices.update({ siteId, deviceId }, { componentId }));
    }
    return Promise.all(promiseArr);
  },

  deleteComponent: async (deviceId, siteId) => {
    await Components.destroy({ deviceId, siteId });
  },
};

async function _getComponentConfigFromCache(componentId) {
  try {
    const cacheKey = _getComponentConfigCacheKey(componentId);
    const cachedData = await cacheService.get(cacheKey);
    return cachedData ? JSON.parse(cachedData) : null;
  } catch (error) {
    sails.log.error(`Error retrieving cached data for Component Config: ${componentId}`);
    sails.log.error(error);
    return null;
  }
}

async function _setComponentConfigInCache(componentId, componentConfig) {
  try {
    const cacheKey = _getComponentConfigCacheKey(componentId);
    const cacheTTL = sails.config.custom.componentConfigDataTTL;
    await cacheService.set(cacheKey, JSON.stringify(componentConfig));
    await cacheService.expire(cacheKey, cacheTTL);
  } catch (error) {
    sails.log.error(`Unable to set cache for Component Config Data: ${componentId}`);
    sails.log.error(error);
  }
}

async function invalidateComponentConfigCache(componentId) {
  try {
    const cacheKey = _getComponentConfigCacheKey(componentId);
    await cacheService.delete(cacheKey);
  } catch (error) {
    sails.log.error(`Unable to delete cached data for Component Config: ${componentId}`);
    sails.log.error(error);
  }
}

function _getComponentConfigCacheKey(componentId) {
  return `componentConfig:componentId:${componentId}`;
}

/**Use this recursive function to retrieve list of all components*/
async function _getComponentsBySiteId(siteId, lastEvaluatedKey = null, components = []) {
  const params = {
    TableName: "components",
    IndexName: "siteId_deviceType_global_index",
    KeyConditionExpression: "#siteId = :siteId",
    ExpressionAttributeNames: {
      "#siteId": "siteId",
    },
    ExpressionAttributeValues: {
      ":siteId": siteId,
    },
  };

  if (lastEvaluatedKey) {
    params.ExclusiveStartKey = lastEvaluatedKey;
  }

  try {
    const queryResult = await dynamoDBClient.query(params).promise();

    const processedItems = (queryResult?.Items ?? []).map(({ info, data, controls, ...rest }) => ({
      ...rest,
      data: globalHelpers.convertStringArrayToJSONArray(data) ?? [],
      controls: globalHelpers.convertStringArrayToJSONArray(controls) ?? [],
    }));

    const allComponents = components.concat(processedItems);

    if (queryResult.LastEvaluatedKey) {
      return _getComponentsBySiteId(siteId, queryResult.LastEvaluatedKey, allComponents);
    }

    return allComponents;
  } catch (error) {
    sails.log.error(
      `Error querying components from DynamoDB using aws-sdk: getComponentsBySiteId for siteId ${siteId}`,
      error
    );
    throw error;
  }
}

/**
 * @description Fetches and deduplicates component categories by siteId from DynamoDB, handling pagination for large datasets.
 */

async function _getComponentCatBySiteId(siteId) {
  const params = {
    TableName: "components",
    IndexName: "siteId_deviceType_global_index",
    KeyConditionExpression: "#siteId = :siteId",
    ExpressionAttributeNames: {
      "#siteId": "siteId",
    },
    ExpressionAttributeValues: {
      ":siteId": siteId,
    },
    ProjectionExpression: "deviceType, driverType",
  };

  try {
    let queryResult;
    let items = [];
    do {
      queryResult = await dynamoDBClient.query(params).promise();
      items = items.concat(queryResult.Items);
      params.ExclusiveStartKey = queryResult.LastEvaluatedKey;
    } while (queryResult.LastEvaluatedKey);
    const uniqueCat = items.reduce((acc, curr) => {
      const { driverType, deviceType } = curr;
      if (!driverType || !deviceType) return acc;
      const key = `${driverType}_${deviceType}`;
      acc[key] = curr;
      return acc;
    }, {});

    return Object.values(uniqueCat);
  } catch (error) {
    sails.log.error(
      "Error querying components from DynamoDB using aws-sdk: fetchConfiguratorTableDeviceComponentDeviceType",
      error
    );
    throw error;
  }
}


