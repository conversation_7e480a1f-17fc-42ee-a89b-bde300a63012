/* eslint-disable eqeqeq */
/* eslint-disable max-len */
const inputValidation = require("../../utils/ConfiguratorTable/inputValidation.utils");
const deviceTypeService = require("../devicetype/devicetype.public");
const componentService = require("../component/component.public");
const errorHandler = require("../../utils/ConfiguratorTable/errorHandler");

class ConfiguratorComponentTable {
  constructor(params) {
    this.siteId = params.siteId;
  }

  async _validateTableProperties({ deviceType, rows, columns }) {
    inputValidation.validateRequestParamsComponentClassTable({
      deviceType,
      rows,
      columns,
    });
    const deviceDriverConfig = await deviceTypeService.getComponentDriverConfig(
      deviceType
    );
    if (!deviceDriverConfig) errorHandler.throwExceptionInvalidDriverConfig();

    const invalidDevices = await this._getInvalidTableRows(rows, deviceType);
    if (!_.isEmpty(invalidDevices)) errorHandler.throwExceptionInvalidTableRows(invalidDevices);

    const invalidColumns = this._getInvalidTableColumns(deviceDriverConfig, columns);
    if (!_.isEmpty(invalidColumns)) errorHandler.throwExceptionInvalidTableColumns(invalidColumns);
  }

  async _getInvalidTableRows(rows, deviceType) {
    const invalidDevices = [];
    const $isComponentExist = rows.map((componentId) => componentService.isComponentExistByDriverType(
      componentId, 
      this.siteId,
      deviceType,
    ));

    const isValidComponents = await Promise.all($isComponentExist);

    isValidComponents.forEach((isComponentExist, index) => {
      if (!isComponentExist) {
        invalidDevices.push(rows[index]);
      }
    });
    return invalidDevices;
  }

  _getInvalidTableColumns(deviceDriverConfig, columns) {
    const invalidColumns = [];
    columns.forEach((col) => {
      const { abbr, type } = col;
      if (type == "data" && !deviceDriverConfig.isValidDataAbbr(abbr)) invalidColumns.push(abbr);
      else if (type == "controls" && !deviceDriverConfig.isValidControlAbbr(abbr)) { invalidColumns.push(abbr); }
    });
    return invalidColumns;
  }
}

module.exports = ConfiguratorComponentTable;
