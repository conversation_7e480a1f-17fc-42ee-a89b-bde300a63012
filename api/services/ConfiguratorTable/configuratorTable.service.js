const flaverr = require("flaverr");
const configuratorTableService = require("./configuratorTable.private");
const configuratorPageService = require("../configuratorPage/configuratorPage.public");
const configuratorTableGroupService = require("../ConfiguratorTableGroup/configuratorTableGroup.public");
const configuratorTaggedDevicesService = require("../ConfiguratorTaggedDevices/configuratorTaggedDevices.public");
const componentService = require("../component/component.public");
const deviceService = require("../device/device.public");
const {
  configuratorTaggedDevicesParam,
} = require("../ConfiguratorTaggedDevices/configuratorTaggedDevices.public");

module.exports = {
  async deleteTable(params) {
    const {
      pageId,
      tgId,
      tableId,
      _userMeta: { id: userId },
    } = params;
    const _page = await configuratorPageService.findOne({
      id: pageId,
      status: 1,
    });
    if (!_page) {
      throw flaverr("E_PAGE_NOT_FOUND", new Error(`Page not found for pageId: ${pageId}`));
    }
    if (_page.pageType != 2) {
      throw flaverr("E_INVALID_PAGE_TYPE", new Error(`Invalid Page type for id: ${pageId}`));
    }
    const _tableGroup = await configuratorTableGroupService.findOne({
      id: tgId,
      pageRefId: pageId,
      status: 1,
    });
    if (!_tableGroup) {
      throw flaverr("E_TABLE_GROUP_NOT_FOUND", new Error(`Table Group not found for id: ${tgId}`));
    }
    const deletedTable = await configuratorTableService.updateOne(
      {
        id: tableId,
        tableGroupId: tgId,
      },
      { status: 0, updatedBy: userId },
    );
    if (!deletedTable) {
      throw flaverr("E_TABLE_NOT_FOUND", new Error(`Table not found for id: ${tableId}`));
    }
  },

  async isTableInEditableMode(tgId, tableId) {
    const _table = await configuratorTableService.findOne({
      id: tableId,
      tableGroupId: tgId,
      status: 1,
    });
    if (!_table) return true;
    return _table.isPublished === 0;
  },
  async autoCreateTable(params) {
    const { pageId } = params;
    const rows = await configuratorTaggedDevicesService.find({
      subSystemPageId: pageId,
      status: 1,
    });
    if (rows.length !== 0) {
      const groupedData = new Map();
      for (const row of rows) {
        const { id, deviceId, deviceClass } = row;
        let component, device, deviceType;
        if (deviceClass === "component") {
          component = await componentService.findOne({ deviceId });
          deviceType = component && component.deviceType;
        } else if (deviceClass === "device") {
          device = await deviceService.findOne({ deviceId });
          deviceType = device && device.deviceType;
        }
        if (!component && !device) continue;

        const key = deviceType;
        if (!groupedData.has(key)) {
          groupedData.set(key, {
            deviceType,
            deviceClass,
            table: {
              rows: [],
              columns: new Set(),
            },
          });
        }
        const group = groupedData.get(key);
        group.table.rows.push(deviceId);
        const params = await configuratorTaggedDevicesParam.find({
          configuratorTaggedDevicesId: id,
        });
        params.forEach((param) => {
          const { paramType, paramAbbr } = param;
          const column = {
            abbr: paramAbbr,
            type: paramType,
          };
          group.table.columns.add(JSON.stringify(column));
        });
      }
      groupedData.forEach((group) => {
        group.table.columns = Array.from(group.table.columns).map((column) => JSON.parse(column));
      });
      return Array.from(groupedData.values());
    }
    return [];
  },
};
