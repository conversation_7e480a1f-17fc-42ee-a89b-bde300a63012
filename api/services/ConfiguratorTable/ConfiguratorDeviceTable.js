const deviceService = require("../device/device.public");
const deviceTypeService = require("../devicetype/devicetype.public");
const inputValidation = require('../../utils/ConfiguratorTable/inputValidation.utils')
const errorHandler = require('../../utils/ConfiguratorTable/errorHandler');
class ConfiguratorDeviceTable {
  constructor(params) {
    this.siteId = params.siteId
  }


 async _validateTableProperties({
    deviceType,
    rows,
    columns
  }) {
    inputValidation.validateRequestParamsDeviceClassTable({
      deviceType,
      rows,
      columns
    });

    const deviceDriverConfig = await deviceTypeService.getDeviceDriverConfig(deviceType);
    if (!deviceDriverConfig) errorHandler.throwExceptionInvalidDriverConfig();

    const invalidDevices = await this._getInvalidTableRows(rows, deviceType);
    if (!_.isEmpty(invalidDevices)) errorHandler.throwExceptionInvalidTableRows(invalidDevices)

    const invalidColumns = this._getInvalidTableColumns(deviceDriverConfig, columns.map(column => column.abbr));
    if (!_.isEmpty(invalidColumns)) errorHandler.throwExceptionInvalidTableColumns(invalidColumns)

    return
  }



async _getInvalidTableRows (rows, deviceType) {
  const invalidDevices = [];
  const $isDeviceExist = rows.map((deviceId) => deviceService.isDeviceExist(
    deviceId,
    this.siteId,
    deviceType
  ));

  const getInvalidDevices = await Promise.all($isDeviceExist);

  getInvalidDevices.forEach((isDeviceExist, index) => {
    if (!isDeviceExist) {
      invalidDevices.push(rows[index]);
    }
  });
  return invalidDevices;
}


_getInvalidTableColumns(deviceDriverConfig, columns) {
  const invalidColumns = []
  columns.forEach((dataAbbr) => {
    if (!deviceDriverConfig.isValidDataAbbr(dataAbbr)) invalidColumns.push(dataAbbr)
  })
  return invalidColumns
}

}

module.exports = ConfiguratorDeviceTable;
