module.exports = {
  find: async (searchParams) => {
    return ConfiguratorTables.find(searchParams);
  },
  findOne: async (searchParams) => {
    return ConfiguratorTables.findOne(searchParams);
  },
  update: async (searchParams, updateValue) => {
    return ConfiguratorTables.update(searchParams, updateValue);
  },
  updateOne: async (searchParams, updateValue) => {
    return ConfiguratorTables.updateOne(searchParams, updateValue);
  },
  create: async (param) => {
    return ConfiguratorTables.create(param).fetch();
  },
  delete: async (searchParams) => {
    return ConfiguratorTables.destroy(searchParams);
  },
}
