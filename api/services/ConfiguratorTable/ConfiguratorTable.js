const errorHandler = require("../../utils/ConfiguratorTable/errorHandler");
const ConfiguratorDeviceTable = require("./ConfiguratorDeviceTable");
const ConfiguratorComponentTable = require("./ConfiguratorComponentTable");
const configuratorTableGroup = require("../ConfiguratorTableGroup/configuratorTableGroup.public");
const configuratorPageService = require("../configuratorPage/configuratorPage.public");
const inputValidation = require("../../utils/ConfiguratorTable/inputValidation.utils");
const configuratorTableUtils = require("../../utils/ConfiguratorTable/configuratorTable.utils");
const configuratorTableGroupService = require("../ConfiguratorTableGroup/configuratorTableGroup.service");

class ConfiguratorTable {
  constructor() {
    this.instance = null;
    this.siteId = null;
  }

  async setSiteId(pageId) {
    const siteId = await configuratorPageService.getSiteIdFromPage(pageId);
    if (!siteId) {
      errorHandler.throwExceptionInvalidPageSiteId(pageId);
    }
    this.siteId = siteId;
    return siteId;
  }

  async _validateTable(pageId, tableGroupId) {
    inputValidation.validateTableWithPageParams({
      pageId,
      tableGroupId,
    });
    await configuratorPageService.validateTablePage(pageId);
    const isValidTableGroup = await configuratorTableGroup.isValidTableGroup(tableGroupId, pageId);

    if (!isValidTableGroup) {
      errorHandler.throwExceptionInvalidTableGroupID(tableGroupId);
    }
  }

  async buildTable(tableProperties) {
    const { userId, ...filteredTableProperties } = tableProperties;
    inputValidation.validateTableInputParams(filteredTableProperties);
    const {
      tableProperty: {
        deviceType,
        class: deviceClass,
        table: { rows, columns },
        name,
        canTranspose,
      },
      tableGroupId,
      pageId,
    } = tableProperties;
    await this._validateTable(pageId, tableGroupId);
    this.instance =
      deviceClass == "device"
        ? new ConfiguratorDeviceTable({ siteId: this.siteId })
        : new ConfiguratorComponentTable({ siteId: this.siteId });
    await this.instance._validateTableProperties({
      deviceType,
      rows,
      columns,
    });
    return sails.getDatastore("postgres").transaction(async (dbTransactionObj) => {
      // eslint-disable-next-line no-undef
      const table = await ConfiguratorTables.create({
        name,
        deviceType,
        deviceClass,
        tableGroupId,
        canTranspose,
        createdBy: userId,
      })
        .fetch()
        .usingConnection(dbTransactionObj);
      const response = this._saveTableProperty(
        table,
        {
          rows,
          columns,
        },
        dbTransactionObj,
        userId,
      );
      await this._updateOrdering(
        table.id,
        {
          rows,
          columns,
        },
        dbTransactionObj,
        userId,
      );
      return response;
    });
  }

  async updateTable(tableId, tableProperties, updatedBy) {
    inputValidation.validateTableInputParams(tableProperties);
    const {
      tableProperty: {
        deviceType,
        class: deviceClass,
        table: { rows, columns },
        name,
        canTranspose,
      },
      tableGroupId,
      pageId,
    } = tableProperties;
    await this._validateTable(pageId, tableGroupId);
    this.instance =
      deviceClass == "device"
        ? new ConfiguratorDeviceTable({ siteId: this.siteId })
        : new ConfiguratorComponentTable({ siteId: this.siteId });
    const table = await this._getTable(tableId, tableGroupId);
    if (!table) errorHandler.throwExceptionInvalidTable(tableId, tableGroupId);

    const oldTableProperty = await this._getTableProperty(tableId);
    const { deletedRows, deletedColumns, createdRows, createdColumns } =
      configuratorTableUtils.logChangeTableProperties(oldTableProperty, {
        rows,
        columns,
      });

    return sails.getDatastore("postgres").transaction(async (dbTransactionObj) => {
      await this.instance._validateTableProperties({
        deviceType,
        rows,
        columns,
      });
      const deletedProperty = await this._deleteTableProperty(
        tableId,
        deletedRows,
        deletedColumns,
        name,
        canTranspose,
        dbTransactionObj,
        updatedBy,
      );
      const response = await this._saveTableProperty(
        table,
        {
          rows: createdRows,
          columns: createdColumns,
        },
        dbTransactionObj,
        updatedBy,
      );
      response.deletedProperty = deletedProperty;
      await this._updateOrdering(
        table.id,
        {
          rows,
          columns,
        },
        dbTransactionObj,
        updatedBy,
      );
      response.name = name;
      response.canTranspose = canTranspose;
      return response;
    });
  }

  async _deleteTableProperty(
    tableId,
    rows,
    columns,
    name,
    canTranspose,
    dbTransactionObj,
    updatedBy,
  ) {
    const updateValue = {
      status: 0,
      updatedBy,
    };
    const searchRowObject = {
      id: {
        in: rows.map((row) => row.id),
      },
    };

    const searchColumnObject = {
      id: {
        in: columns.map((column) => column.id),
      },
    };

    const $deleteTablePropertyPromise = [];
    if (rows.length > 0) {
      $deleteTablePropertyPromise.push(
        // eslint-disable-next-line no-undef
        ConfiguratorTableRow.update(searchRowObject, updateValue).usingConnection(dbTransactionObj),
      );
    }
    if (columns.length > 0) {
      $deleteTablePropertyPromise.push(
        ConfiguratorTableColumn.update(searchColumnObject, updateValue).usingConnection(
          dbTransactionObj,
        ),
      );
    }
    if (name) {
      $deleteTablePropertyPromise.push(
        // eslint-disable-next-line no-undef
        ConfiguratorTables.update({ id: tableId }, { name }).usingConnection(dbTransactionObj),
      );
    }
    if (canTranspose == 0 || canTranspose == 1) {
      $deleteTablePropertyPromise.push(
        // eslint-disable-next-line no-undef
        ConfiguratorTables.update({ id: tableId }, { canTranspose }).usingConnection(
          dbTransactionObj,
        ),
      );
    }
    await Promise.all($deleteTablePropertyPromise);
    return {
      rows: rows.map((row) => ({
        id: row.id,
        asset: row.deviceId,
      })),
      columns: columns.map((col) => ({
        id: col.id,
        abbr: col.abbrName,
        type: col.type,
      })),
    };
  }

  async _getTable(tableId, tableGroupId) {
    // eslint-disable-next-line no-undef
    return ConfiguratorTables.findOne({
      id: tableId,
      tableGroupId,
      status: 1,
    });
  }

  async _getTableProperty(tableId) {
    const [rows, column] = await Promise.all([
      // eslint-disable-next-line no-undef
      ConfiguratorTableRow.find({
        tableId,
        status: 1,
      }).sort({ order: 1 }),
      // eslint-disable-next-line no-undef
      ConfiguratorTableColumn.find({
        tableId,
        status: 1,
      }).sort({ order: 1 }),
    ]);
    return {
      rows,
      column,
    };
  }

  async _saveTableProperty(table, { rows, columns }, dbTransactionObj, userId) {
    const { id: tableId } = table;
    let tableRows = [];
    let tableColumns = [];
    if (rows.length > 0) {
      const buildTableRows = rows.map((deviceId) => ({
        deviceId,
        tableId,
        createdBy: userId,
      }));
      // eslint-disable-next-line no-undef
      tableRows = await ConfiguratorTableRow.createEach(buildTableRows)
        .fetch()
        .usingConnection(dbTransactionObj);
    }

    if (columns.length > 0) {
      const buildTableColumn = columns.map((column) => ({
        abbrName: column.abbr,
        type: column.type,
        tableId,
        createdBy: userId,
      }));
      // eslint-disable-next-line no-undef
      tableColumns = await ConfiguratorTableColumn.createEach(buildTableColumn)
        .fetch()
        .usingConnection(dbTransactionObj);
    }

    return configuratorTableUtils.buildTableResponseSchema(table, tableRows, tableColumns);
  }

  async _updateOrdering(tableId, { rows, columns }, dbTransactionObj, userId) {
    const $rows = rows.map((deviceId, index) => {
      return ConfiguratorTableRow.updateOne(
        {
          tableId,
          status: 1,
          deviceId,
          updatedBy: userId,
        },
        { order: index + 1 },
      ).usingConnection(dbTransactionObj);
    });
    const $columns = columns.map(({ abbr }, index) => {
      return ConfiguratorTableColumn.updateOne(
        {
          tableId,
          status: 1,
          abbrName: abbr,
        },
        { order: index + 1 },
      ).usingConnection(dbTransactionObj);
    });

    await Promise.all($rows.concat($columns));
    return;
  }

  async getAllTableOfTableGroup(tableGroupId) {
    // eslint-disable-next-line no-undef
    return ConfiguratorTables.find({
      where: {
        tableGroupId,
        status: 1,
      },
      sort: "id ASC",
    });
  }

  async fetchAllTableByTableGroupId(tableGroupId) {
    const tableGroups = await this.getAllTableOfTableGroup(tableGroupId);
    const tableProperties = await Promise.all(
      tableGroups.map((tableRow) => this._getTableProperty(tableRow.id)),
    );

    const response = [];
    for (let index = 0; index < tableGroups.length; index++) {
      const table = tableGroups[index];
      const { rows, column } = tableProperties[index];
      response.push(configuratorTableUtils.buildTableResponseSchema(table, rows, column));
    }
    return response;
  }

  async fetchPageTableGroups(pageId) {
    if (!pageId || isNaN(pageId)) errorHandler.throwExceptionInvalidTableGroupId(pageId);
    await configuratorPageService.validateTablePage(pageId);
    const tableGroups = await configuratorTableGroupService.fetchPageTableGroups(pageId);
    return tableGroups.map((tableGrp) => {
      return {
        id: tableGrp.id,
        name: tableGrp.name,
      };
    });
  }

  async getTableDetailsByPageId(pageId) {
    await configuratorPageService.validateTablePage(pageId);
    const pageDetails = await configuratorPageService.findOne({
      id: pageId,
      status: 1,
    });

    /**Check if page is published and data is in cache*/
    let cachedData = null;
    if (+pageDetails.isPublished === 1) {
      cachedData = await configuratorPageService.getCachedDataByPageId(pageId);
    }
    if (cachedData) {
      return cachedData;
    }

    const tableGroups = await this.fetchPageTableGroups(pageId);

    /**Fetch all table details for each table group*/
    const tableDetailsByPageId = await Promise.all(
      tableGroups.map(async (tableGroup) => {
        const tableDetails = await this.fetchAllTableByTableGroupId(tableGroup.id);
        return {
          ...tableGroup,
          tableDetails,
        };
      }),
    );

    if(+pageDetails.isPublished === 1) {
      /**Cache the data with TTL*/
      await configuratorPageService.setPageCache(pageId, tableDetailsByPageId);
    }
    return tableDetailsByPageId;
  }
}

module.exports = ConfiguratorTable;
