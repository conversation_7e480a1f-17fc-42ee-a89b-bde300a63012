const componentService = require("../component/component.public");
const deviceService = require("../device/device.public");
const cacheService = require("../cache/cache.public");
const maintenanceModesAuditService = require("../MaintenanceModesAudit/MaintenanceModesAudit.service");
const flaverr = require("flaverr");

const maintenanceModeService = {
  getMaintenanceModeByComponent: async function (assetId) {
    let isInMaintenanceMode = await this._getMaintenanceModeFlagFromCache(assetId);
    if (isInMaintenanceMode !== null) {
      return { deviceId: assetId, isInMaintenanceMode };
    }

    const componentDetail = await componentService.findOne({ deviceId: assetId });
    if (_.isEmpty(componentDetail)) {
      throw flaverr(`ASSET_NOT_EXIST`, new Error(`${assetId} does not exists`));
    }
    isInMaintenanceMode = componentDetail?.isInMaintenanceMode ?? 0;
    await this._setMaintenanceModeFlagInCache(assetId, isInMaintenanceMode);
    return {
      deviceId: assetId,
      isInMaintenanceMode,
    };
  },
  getMaintenanceModeByDevice: async function (assetId) {
    let isInMaintenanceMode = await this._getMaintenanceModeFlagFromCache(assetId);
    if (isInMaintenanceMode !== null) {
      return { deviceId: assetId, isInMaintenanceMode };
    }

    const deviceDetails = await deviceService.findOne({ deviceId: assetId });
    if (_.isEmpty(deviceDetails)) {
      throw flaverr(`ASSET_NOT_EXIST`, new Error(`${assetId} does not exists`));
    }
    isInMaintenanceMode = deviceDetails?.isInMaintenanceMode ?? 0;
    await this._setMaintenanceModeFlagInCache(assetId, isInMaintenanceMode);
    return {
      deviceId: assetId,
      isInMaintenanceMode,
    };
  },
  getMaintenanceModeByAssets: async function (assetList) {
    const maintenanceModePromises = assetList.map(async (asset) => {
      try {
        if (asset?.deviceClass === "component") {
          return await this.getMaintenanceModeByComponent(asset.deviceId);
        }
        if (asset?.deviceClass === "device") {
          return await this.getMaintenanceModeByDevice(asset.deviceId);
        }
      } catch (error) {
        sails.log.error("maintenanceMode> ASSET_NOT_EXIST");
        sails.log.error(error);
        return null;
      }
    });
    let maintenanceModeDetailsByAssetList = await Promise.all(maintenanceModePromises);
    maintenanceModeDetailsByAssetList = maintenanceModeDetailsByAssetList.filter(Boolean);
    return maintenanceModeDetailsByAssetList;
  },
  _getMaintenanceModeFlagFromCache: async function (assetId) {
    try {
      const cacheKey = _getMaintenanceModeCacheKey(assetId);
      const cachedData = await cacheService.get(cacheKey);
      return cachedData ?? null;
    } catch (error) {
      sails.log.error(`Error retrieving cached data for maintenance mode: ${assetId}`);
      sails.log.error(error);
      return null;
    }
  },
  _setMaintenanceModeFlagInCache: async function (assetId, maintenanceFlag) {
    try {
      const cacheKey = _getMaintenanceModeCacheKey(assetId);
      const cacheTTL = sails.config.custom.maintenanceModeDataTTL;
      await cacheService.set(cacheKey, maintenanceFlag);
      await cacheService.expire(cacheKey, cacheTTL);
    } catch (error) {
      sails.log.error(`Error setting up the cached data for maintenance mode: ${assetId}`);
      sails.log.error(error);
    }
  },
  _invalidateMaintenanceModeCache: async function (assetId) {
    try {
      const cacheKey = _getMaintenanceModeCacheKey(assetId);
      await cacheService.delete(cacheKey);
    } catch (error) {
      sails.log.error(`Error deleting cached data for maintenance mode: ${assetId}`);
      sails.log.error(error);
    }
  },
  setComponentMaintenanceMode: async function (assetId, siteId, maintenanceModeValue, deviceClass, userId) {
    const component = await componentService.fetchComponentById({ deviceId: assetId, siteId });
    if (_.isEmpty(component) || component?.siteId !== siteId) {
      throw flaverr(`ASSET_NOT_EXIST`, new Error(`${assetId} does not exist on ${siteId}.`));
    }

    const oldMaintenanceMode = component?.isInMaintenanceMode ?? "0";
    await componentService.update(
      {
        siteId,
        deviceId: assetId,
      },
      { isInMaintenanceMode: String(maintenanceModeValue) }
    );

    await this._setMaintenanceModeFlagInCache(assetId, maintenanceModeValue);
    await componentService.invalidateComponentConfigCache(assetId);
    try {
      const response = await maintenanceModesAuditService.setMaintenanceModeAuditLog(
        assetId,
        String(maintenanceModeValue),
        deviceClass,
        siteId,
        userId
      );
      return response;
    } catch (postgreError) {
      // If Postgre DB action fails, revert Dynamo update in the components table.
      await componentService.update(
        {
          siteId,
          deviceId: assetId,
        },
        { isInMaintenanceMode: String(oldMaintenanceMode) }
      );
      sails.log.error(`Reverted DynamoDB update due to PostgreSQL failure for: ${assetId}`);
      throw flaverr('ERROR_FOUND', new Error(postgreError?.message));
    }
  },
  setDeviceMaintenanceMode: async function (assetId, siteId, maintenanceModeValue, deviceClass, userId) {
    const device = await deviceService.findOne({ deviceId: assetId });
    if (_.isEmpty(device) || device?.siteId !== siteId) {
      throw flaverr(`ASSET_NOT_EXIST`, new Error(`${assetId} does not exist on ${siteId}.`));
    }

    const oldMaintenanceMode = device.isInMaintenanceMode ?? "0";
    await deviceService.update(
      {
        deviceId: assetId,
        siteId,
      },
      { isInMaintenanceMode: String(maintenanceModeValue) }
    );
    await this._setMaintenanceModeFlagInCache(assetId, maintenanceModeValue);
    try {
      const response = await maintenanceModesAuditService.setMaintenanceModeAuditLog(
        assetId,
        String(maintenanceModeValue),
        deviceClass,
        siteId,
        userId
      );
      return response;
    } catch (postgreError) {
      // If Postgre DB action fails, revert Dynamo update in the components table.
      await deviceService.update(
        {
          deviceId: assetId,
          siteId,
        },
        { isInMaintenanceMode: String(oldMaintenanceMode) }
      );
      sails.log.error(`Reverted DynamoDB update due to PostgreSQL failure for: ${assetId}`);
      throw flaverr('AUDIT_LOG_NOT_FOUND', new Error(postgreError?.message));
    }
  },
  checkUserAccessForDeviceList: async function (assetList, siteId) {
    try {
      for (const asset of assetList) {
        if (asset.deviceClass === "component") {
          const extractedSiteId = asset.deviceId.split("_")[0];
          if (extractedSiteId !== siteId) {
            return false;
          }
        }
        if (asset.deviceClass === "device") {
          const device = await deviceService.findOne({ deviceId: asset.deviceId });
          if (_.isEmpty(device)) {
            throw flaverr(`ASSET_NOT_EXIST`, new Error(`${asset.deviceId} does not exist on ${siteId}.`));
          }
          if (device.siteId !== siteId) {
            return false;
          }
        }
      }
      return true;
    } catch (error) {
      sails.log.error(`Error in fetching user access for devices on: ${siteId}`);
      sails.log.error(error);
      return false;
    }
  }
};

function _getMaintenanceModeCacheKey(assetId) {
  return `maintenanceMode:assetId:${assetId}`;
}

maintenanceModeService.getMaintenanceModeByComponent =
  maintenanceModeService.getMaintenanceModeByComponent.bind(maintenanceModeService);
maintenanceModeService.getMaintenanceModeByDevice =
  maintenanceModeService.getMaintenanceModeByDevice.bind(maintenanceModeService);
maintenanceModeService.getMaintenanceModeByAssets =
  maintenanceModeService.getMaintenanceModeByAssets.bind(maintenanceModeService);
maintenanceModeService._getMaintenanceModeFlagFromCache =
  maintenanceModeService._getMaintenanceModeFlagFromCache.bind(maintenanceModeService);
maintenanceModeService._setMaintenanceModeFlagInCache =
  maintenanceModeService._setMaintenanceModeFlagInCache.bind(maintenanceModeService);
maintenanceModeService._invalidateMaintenanceModeCache =
  maintenanceModeService._invalidateMaintenanceModeCache.bind(maintenanceModeService);
maintenanceModeService.setComponentMaintenanceMode =
  maintenanceModeService.setComponentMaintenanceMode.bind(maintenanceModeService);
maintenanceModeService.setDeviceMaintenanceMode =
  maintenanceModeService.setDeviceMaintenanceMode.bind(maintenanceModeService);

module.exports = maintenanceModeService;
