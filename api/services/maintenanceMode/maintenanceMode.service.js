const maintenanceModeService = require("./maintenanceMode.private");
const influxService = require("../influx/influx.service");

async function ingestMaintenanceModeLogToInflux({siteId, assetId, userId, maintenanceModeValue, transactionId}) {
  try {
  const maintenanceLogFiler = await CPAAssetConfigurationMetadata.findOne({siteId, assetId, status: 1});
  if (!maintenanceLogFiler) {
    sails.log(`[Not Found > ingestMaintenanceModeLogToInflux] COA maintenance mode log not found for site ${siteId} and assetId:${assetId} with transactionId:${transactionId}`);
    return;
  } 

  const {assetType, assetName} = maintenanceLogFiler;
  await storeMaintenanceModeLogToInflux({
    siteId,
    assetId,
    assetName,
    assetType,
    userId,
    maintenanceModeValue,
    transactionId
  })
} catch(err) {
  sails.log('[Error > ingestMaintenanceModeLogToInflux] Error while storing cpa maintenance mode log to Influx', err)
}
}

async function createMaintenanceModeLog(extractMaintenanceLogs, transactionId) {
   const $maintenanceLogs = extractMaintenanceLogs.map((logPacket) => insertMaintenanceLog(logPacket, transactionId));
   await Promise.all($maintenanceLogs);

    async function insertMaintenanceLog({siteId, assetId, assetType, assetName,userId}, transactionId) {
      try {
        const defaultMaintenanceModeValue = 0;
        if (!siteId && !assetId) {
          sails.log(`[Error > insertMaintenanceLog] siteId:- ${siteId} and assetId:- ${assetId} does not exist`,logPacket)
          return
        }
        const isLogExist = await isAssetMaintenanceModeLogged(siteId, assetId, assetName)
        if (isLogExist) return true; 
        await storeMaintenanceModeLogToInflux({
          siteId,
          assetId,
          assetName,
          assetType,
          userId,
          maintenanceModeValue: defaultMaintenanceModeValue,
          transactionId
        })
        return;

        async function isAssetMaintenanceModeLogged(siteId, assetId, assetName) {
          const replacements = {
            siteId,
            assetId,
            assetName,
            bucket: 'gamma-cpa-metrics',
            measurement: 'maintenance_mode_log',
            rows: 1,
            offset: 0
          }
          const maintenanceModeDRecord = await influxService.runQuery(getLastMaintenanceModeRecord(),{
            replacements,
            debug: true
          },"iot_influxdb");

          return _.isEmpty(maintenanceModeDRecord) ? false: true;

          function getLastMaintenanceModeRecord() {
            return `
            from(bucket: "{{bucket}}")
            |> range(start: -90d)
            |> filter(fn: (r) => r["_measurement"] == "{{measurement}}")
            |> filter(fn: (r) => r["site_id"] == "{{siteId}}")
            |> filter(fn: (r) => r["asset_id"] == "{{assetId}}")
             |> filter(fn: (r) => r["asset_name"] == "{{assetName}}")
            |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
            |> sort(columns: ["_time"], desc: true)
            |> limit(n:{{rows}}, offset: {{offset}})
            `
          }

        }
    
    } catch(e) {
      sails.log('[Error > createMaintenanceModeLog] Unable to assert maintenance log', e, {siteId, assetId, assetType, assetName, userId})
    }


  }



}

function storeMaintenanceModeLogToInflux({
  siteId,
  assetId,
  assetName,
  assetType,
  userId,
  maintenanceModeValue,
  transactionId
}) {
  return influxService.write({
    data:{
      tags:[{ key:'site_id', value:siteId }, { key: 'asset_id',value: assetId}, { key: 'asset_name',value: assetName}],
      fields:[{
        type:'string',
        key:'asset_type',
        value:assetType
      },{
        type:'string',
        key:'user_id',
        value:userId
      },{
        type:'string',
        key:'maintenance_mode_value',
        value:maintenanceModeValue
      },{
        type:'string',
        key:'txn_id',
        value:transactionId
      }
    ],
    },
    bucket: 'gamma-cpa-metrics',
    measurement:'maintenance_mode_log'
  },'iot_influxdb')
}

module.exports = {
  getMaintenanceModeByAssets: maintenanceModeService.getMaintenanceModeByAssets,
  getMaintenanceModeByDevice: maintenanceModeService.getMaintenanceModeByDevice,
  getMaintenanceModeByComponent: maintenanceModeService.getMaintenanceModeByComponent,
  setComponentMaintenanceMode: maintenanceModeService.setComponentMaintenanceMode,
  setDeviceMaintenanceMode: maintenanceModeService.setDeviceMaintenanceMode,
  checkUserAccessForDeviceList: maintenanceModeService.checkUserAccessForDeviceList,
  ingestMaintenanceModeLogToInflux,
  createMaintenanceModeLog
};

