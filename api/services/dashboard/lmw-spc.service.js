const Influx = require('../influx/enterprise/influx.service');

module.exports = {
  getDataFromInflux: async (startTimestamp, endTimestamp) => {
    const timezoneOffset = await sails.helpers.getSiteTimezone.with({siteId: 'lmw-coi', timezoneFormat:'utcOffsetInMinute'})
    const fluxQuery = `
    import "timezone"
    option location = timezone.fixed(offset: {{timezoneOffset}})
    from(bucket: "device_component/autogen")
    |> range(start: {{startTime}}, stop: {{endTime}})
		|> filter(fn: (r) => r["siteId"] == "lmw-coi")
		|> filter(fn: (r) =>  r["_measurement"] == "device" )
		|> filter(fn: (r) => r["deviceId"] == "8642" or r["deviceId"] == "8475" or r["deviceId"] == "8476" or r["deviceId"] == "8477" or r["deviceId"] == "8478" or r["deviceId"] == "8479")
		|> filter(fn: (r) => r["_field"] == "kw" or r["_field"] == "airflowrate")
    |> sort(columns: ["_time"], desc: true)
    |> yield(name: "result")
  `;
    // TODO: dynamic deviceIds and replacement of variable in fluxQuery
    return Influx.runQuery(fluxQuery, {
      replacements: {
        startTime: startTimestamp,
        endTime: endTimestamp,
        timezoneOffset
      },
      debug: true,
    });
  },
};
