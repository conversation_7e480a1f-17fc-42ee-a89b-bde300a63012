const Influx = require("../influx/enterprise/influx.service");
const componentService = require("../component/component.public");
const componentPublic = require("../component/component.public");
const moment = require('moment');
const influxService = require("../influx/enterprise/influx.service");
const datadeviceService = require("../datadevice/datadevice.service");
const influxPublic = require("../influx/enterprise/influx.public");
const energyConsumptionService = require("../energyConsumption/energyConsumption.service");
const dynamoKeyStorePublic = require("../dynamokeystore/dynamokeystore.public");
const { parsedToThreeDecimals } = require("../../utils/globalhelper");
const baselineService = require("../baseline/baseline.service");
const MIN_TRH_USAGE = (146000*12)/365;
const UNIT_COST_ELECTRICITY = 1122;
const COOLING_SERVICE_SERVICE_INDEX = 1.2581;
const CONTRACTED_SUPPLY_TRH = 226300;
const siteService = require('../site/site.service');
const baselineUtils = require("../../utils/baseline/utils");
const dailyConsumptionService = require("../dailyconsumption/dailyconsumption.service");
module.exports = {
  tonnageDeliveryCardData: async function (siteId) {
    const result = {
      tptr: null,
      chleff: null,
    };
    const componentType = ["chiller", "airCooledChiller"];
    const _componentList = await componentService.getComponentListByType(
      siteId,
      componentType
    );
    if (_componentList.length === 0) return result;

    const fluxQuery = `
      from(bucket: "device_component/autogen")
        |> range(start: -15m)
        |> filter(fn: (r) => r["_measurement"] == "components")
        |> filter(fn: (r) => r["siteId"] == "{{siteId}}")
        |> filter(fn: (r)=>{{componentList}})
        |> filter(fn: (r) => r["_field"] == "tptr" or r["_field"] == "chleff")
        |> last()
        |> drop(columns: ["componentId"])
        |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
        |> yield(name: "tonnageDelivery")
    `;

    const componentList = _componentList
      .map((_component) => `r["componentId"] == "${_component.deviceId}"`)
      .join(" or ");
    const data = await Influx.runQuery(fluxQuery, {
      replacements: {
        siteId,
        componentList,
      },
      debug: true,
    });
    if (data.length === 0) return result;
    const { tptr, chleff } = data[0];
    result.tptr = tptr;
    result.chleff = chleff;
    return result;
  },
  getTRHLoadPattern: async function(siteId) {
    const NO_OF_DAYS = 7
    //  Using only one asset named chiller 3 requested by APP. Eng Team.
    const componentIds = ['smyras-yog_2'];
    const timezone = await sails.helpers.getSiteTimezone.with({ siteId, timezoneFormat: 'tz' });
    const now = moment().tz(timezone);
    const startTimestamp =  moment(now).tz(timezone).subtract(NO_OF_DAYS, 'days').startOf('days').format('YYYY-MM-DDT00:00:00').concat('Z')
    const endTimestamp =  moment(now).tz(timezone).startOf('day').format('YYYY-MM-DDT00:00:00').concat('Z');
    return fetchTonnageDelivery(siteId, componentIds, {startTime: startTimestamp, endTime:endTimestamp, groupBy:'1d', TZ: timezone});
    async function fetchTonnageDelivery(siteId, deviceIds, {startTime , endTime, groupBy, TZ}) {
      const componentIds = deviceIds.map(id => `"componentId" = '${id}'`).join(' OR ');
      const query = `
      SELECT SUM("daily_tptr") 
      FROM (
          SELECT SUM("tptr")/60.0 AS "daily_tptr" 
          FROM "device_component"."autogen"."components" 
          WHERE time > '${startTime}'  
            AND time < '${endTime}'  
          AND (${componentIds})
            AND "siteId" = '${siteId}'  
          GROUP BY time(${groupBy})  
          FILL(0)
          TZ('${TZ}')  
      )  GROUP BY time(${groupBy})  
         TZ('${TZ}')  
      `;
      const data = await influxService.runInfluxQLQuery(query);
      // Influx giving me two timestamp values one is from start and end time
      data.shift()
      return data.length? data.map(item => [Number(item.time)/1000000, Number(Number(item.sum).toFixed(3))]): [];
    }
  },
  fetchEnergyConsWithTRCost: async function(siteId) {
    const timezone = await sails.helpers.getSiteTimezone.with({ siteId, timezoneFormat: 'tz' });
    const now = moment().tz(timezone)
    const startTimestamp =  moment(now).tz(timezone).startOf('month').format('YYYY-MM-DDT00:00:00Z');
    const endTimestamp =  moment(now).tz(timezone).format('YYYY-MM-DDTHH:00:00Z');
    const mainMeterList = await dynamoKeyStorePublic.findSitesMainMeterSet(siteId);
    const emList = Array.from(mainMeterList);
    const noOfDaysTillDate = now.date();;

    const minUsageTRHMTD = (MIN_TRH_USAGE * noOfDaysTillDate)
    const response =  {
      chillerPlantMTD: null,
      chillerPlantEnergyCostMTD:null,
      TRHMTD: null,
      kwhByTRHMTD:null,
      minUsageTRHMTD:minUsageTRHMTD,
      unitCostOfElectricity:UNIT_COST_ELECTRICITY,
      coolingServiceIndex:COOLING_SERVICE_SERVICE_INDEX,
      contractedSupplyTRh:CONTRACTED_SUPPLY_TRH
    }
    if(!emList || emList.length == 0) {
        return response;
    };
    const components = await componentPublic.find({siteId, deviceType:'chiller'});
    if (_.isEmpty(components)) return response;
    const chiller3 = 'smyras-yog_2'
    const [
      energyConsumptionSoFar,
      TRHMTD
    ]=  await Promise.all([
      energyConsumptionService.fetchConsumptionForMultipleMeters(emList, startTimestamp, endTimestamp, 'kwh'),
      fetchChillerPlantTR(siteId, [chiller3], {startTime: startTimestamp, endTime:endTimestamp})
    ]);
    const chillerPlantMTD = energyConsumptionSoFar?.totalConsumption || 0
    response.chillerPlantMTD = parsedToThreeDecimals(chillerPlantMTD);
    response.chillerPlantEnergyCostMTD = parsedToThreeDecimals(chillerPlantMTD * UNIT_COST_ELECTRICITY);
    response.TRHMTD = parsedToThreeDecimals(TRHMTD);
    response.kwhByTRHMTD = parsedToThreeDecimals(chillerPlantMTD/TRHMTD);
    return response;
  
    async function fetchChillerPlantTR(siteId, compIds, {startTime , endTime}) {
      try {
        const componentIds = compIds.map(id => `"componentId" = '${id}'`).join(' OR ');
        const query = `SELECT SUM("tptr") AS "tptr" FROM (SELECT ("tptr")/60.0 FROM "device_component"."autogen"."components" WHERE time > '${startTime}' AND time < '${endTime}' AND (${componentIds}) AND "siteId"='${siteId}' FILL(0))`
        const data = await influxService.runInfluxQLQuery(query);
        return data[0]?.tptr || 0;
      } catch(e) {
        sails.log('[error > fetchChillerPlantTR]', e);
        return null
      }
    }
  },
  fetchEnergyConsumptionSavings: async function(siteId) {
    const energyCostMTD = await this.fetchEnergyConsWithTRCost(siteId);
    const consumptionSavingsResponse = {
      consumption: null,
      reducedCarbonEmission: null,
     }
    if (!energyCostMTD.kwhByTRHMTD || !energyCostMTD.TRHMTD) return consumptionSavingsResponse;
    const consumption = Math.max(0,((1.2581 - energyCostMTD.kwhByTRHMTD) * energyCostMTD.TRHMTD));
    consumptionSavingsResponse.consumption = parseInt(consumption);
    consumptionSavingsResponse.reducedCarbonEmission = parseInt((consumption * 0.84))

    return consumptionSavingsResponse;
  },
  fetchSheratonCooling: async function(siteId) {
    const response = {
      "chargesByMTD":{
          "minCoolingService":null,
          "totalCoolingServiceCharge":null,
          "coolingServiceCharge":null,
          },
      "additionalCharges": {
            "excessCoolingCharge":0,
            "overCapacityCharge":0,
            "SESServiceFee":null
        }
    };
    
    const [
      energyConsWithCost,
      trhMTD
    ]= await Promise.all([
      this.fetchEnergyConsWithTRCost(siteId),
      this.fetchPlantTRWithMTD(siteId)
    ]) 
    response.chargesByMTD.minCoolingService = parseInt(energyConsWithCost.minUsageTRHMTD * COOLING_SERVICE_SERVICE_INDEX * UNIT_COST_ELECTRICITY);
    response.chargesByMTD.totalCoolingServiceCharge = parseInt(energyConsWithCost.TRHMTD * COOLING_SERVICE_SERVICE_INDEX * UNIT_COST_ELECTRICITY);
    if (response.chargesByMTD.totalCoolingServiceCharge >  response.chargesByMTD.minCoolingService) {
      response.chargesByMTD.coolingServiceCharge = parseInt(response.chargesByMTD.totalCoolingServiceCharge - energyConsWithCost.chillerPlantEnergyCostMTD) 
    } else {
      response.chargesByMTD.coolingServiceCharge = parseInt(response.chargesByMTD.minCoolingService - energyConsWithCost.chillerPlantEnergyCostMTD);
    }

    if (energyConsWithCost.TRHMTD > 226300) {
      response.additionalCharges.excessCoolingCharge = Math.max(0,parseInt((energyConsWithCost.TRHMTD - 226300)*1.2581*UNIT_COST_ELECTRICITY*1.5))
    }

    const chiller2 = trhMTD.find(item => item.componentId === `${siteId}_2`);
    if (chiller2 && chiller2?.runHour > 0) {
      response.additionalCharges.overCapacityCharge = Math.max(0, parseInt(((chiller2.runHour * chiller2.avgTR) - (chiller2.runHour * 310)) * 1.2581 * UNIT_COST_ELECTRICITY *  1.5))
    }



    response.additionalCharges.SESServiceFee = Math.max(0,parseInt(
      response.additionalCharges.excessCoolingCharge +
      response.additionalCharges.overCapacityCharge +
      response.chargesByMTD.coolingServiceCharge
    ))
    return response;
  },
  fetchPlantTRWithMTD: async function(siteId) {
    const components = await componentPublic.find({siteId, deviceType:'chiller'});
    const componentIds = components.reduce((acc, curr) => {
          acc.push(curr.deviceId);
          return acc;
    },[]);
    const timezone = await sails.helpers.getSiteTimezone.with({ siteId, timezoneFormat: 'tz' });
    const now = moment().tz(timezone);
    const startTimestamp =  moment(now).tz(timezone).startOf('months').format('YYYY-MM-DDT00:00:00Z')
    const endTimestamp =  moment(now).tz(timezone).startOf('hour').format('YYYY-MM-DDTHH:00:00Z');
    const $assetAvgTR = componentIds.map((deviceId) =>  getAssetAvgTR(siteId, deviceId, {startTime:startTimestamp, endTime: endTimestamp}));
    const $assetAvgRunHr = componentIds.map((deviceId) =>  datadeviceService.calculateTotalRunMinutesInRange(siteId, deviceId, startTimestamp, endTimestamp));

    const assetAvgTRResults = await Promise.allSettled($assetAvgTR);
    const assetAvgRunHrResults = await Promise.allSettled($assetAvgRunHr);

    const componentData = componentIds.reduce((acc, deviceId, index) => {
      const totalRunMinutes = assetAvgRunHrResults[index].status == 'fulfilled' ? assetAvgRunHrResults[index].value : null;
      acc[deviceId] = {
        componentId: deviceId,
        avgTR: assetAvgTRResults[index].status == 'fulfilled' ? Number(Number(assetAvgTRResults[index].value).toFixed(3)) : null,
        runHour: totalRunMinutes &&  Math.floor(totalRunMinutes/60)
      };
      return acc;
    }, {});
    return Object.values(componentData);


    async function getAssetAvgTR(siteId, componentId, {startTime, endTime}) {
      try {
        const query = `
                SELECT MEAN(tr) AS tonnageDelivered
                FROM "device_component"."autogen"."components"
                WHERE 
                    time >= '${startTime}' AND time <= '${endTime}'
                    AND "siteId" = '${siteId}'
                    AND "componentId" = '${componentId}'
                    AND "status" = 1.0
        `;
        const record = await influxPublic.runInfluxQLQuery(query);
        return record?.[0]?.tonnageDelivered || 0;
      } catch(e) {
        sails.log('[getAssetAvgTR > Server Error]', e);
        throw e;
      }
    }
  },
  fetchSavings: async (siteId, unitPreference) => {
      let electricityCost = 9
      const site = await siteService.findOne({siteId});
      if (!site) {
        return null;
      }
      if (site?.unitCost) {
        electricityCost = site.unitCost;  
      }
      const userConsumptionUnit = unitPreference.cons || 'kvah';
      const timezone = await sails.helpers.getSiteTimezone.with({ siteId, timezoneFormat: 'tz' });
      const now = moment().tz(timezone);
      const currentStartMonthDate = now.clone().startOf('month');
      const currentDate = now.format('YYYY-MM-DD');
      const currentStartDay = now.clone().startOf('day');


      const baseline = await baselineService.getCurrentBaseline(siteId,currentDate);
      const dailyConsumptionUnit = await siteService.getSitesConsumptionUnit(siteId);

      const adjustments = await baselineService.Adjustment.find({
        siteId,
        timestamp: {
          in: [baseline.startDate, baseline.endDate],
        },
      });

      const previousAdjustment = await baselineService.Adjustment.findOne({
        siteId,
        timestamp: {
          '<': baseline.startDate,
        },
        sort: 'timestamp DESC',
      });


      const monthsBaselineAfterAdjustments = 
        baselineUtils.getMonthsBaselineAfterAdjustmentsInPrefferedUit(
          baseline,
          previousAdjustment,
          adjustments,
          userConsumptionUnit,
          dailyConsumptionUnit,
        );

      let actualConsumptionInBaselineDates = await
      dailyConsumptionService.getDayWiseConsumptionOfSiteBetween2TSInPreferredUnit(
          siteId,
          baseline.startDate,
          baseline.endDate,
          userConsumptionUnit,
      );
        
      const totalBaseline = Object.entries(monthsBaselineAfterAdjustments).reduce((acc, [time, val]) => {
        if (moment(time).isBetween(currentStartMonthDate, currentStartDay) || moment(time).isSame(currentStartMonthDate)) {
          acc += val;
        }
        return acc; 
      }, 0)

      const totalConsumption = actualConsumptionInBaselineDates.reduce((acc, {timestamp, actual}) => {
        if (
          moment(timestamp).isBetween(currentStartMonthDate, currentStartDay) || 
          moment(timestamp).isSame(currentStartMonthDate)
        ) {
          acc += actual;
        }
        return acc; 
      }, 0)


      return Math.round((totalBaseline - totalConsumption) * electricityCost);
    
  }
};



