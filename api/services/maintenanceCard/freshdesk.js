const axios = require('axios');
const flaverr = require('flaverr');

class FreshDesk {
  TICKET_API_ENDPOINT = '/api/v2/tickets';

  constructor() {
    this.axios = axios.create({
      baseURL: `https://${process.env.FRESHDESK_SERVICE_ACCOUNT}.freshdesk.com`,
      headers: {
        Authorization:
          'Basic ' +
          Buffer.from(process.env.FRESHDESK_API_KEY).toString('base64'),
      },
    });
  }

  async fetchTickets(filter) {
    const url = `/api/v2/search/tickets?query=${filter}`;
    return await this.axios.get(url);
  }

  async getTicketById(id) {
    try {
      const url = `${this.TICKET_API_ENDPOINT}/${id}?include=requester`;
      return await this.axios.get(url);
    } catch (e) {
      sails.log.error('FreshDesk > getTicketById');
      sails.log.error(e);
      throw flaverr('E_FRESHDESK_API_FAILURE', new Error(e?.message || 'Unable to fetch ticket'));
    }
  }

  async createTicket(data) {
    try {
      const {
        headers,
        formData
      } = data;
      return await this.axios.post(this.TICKET_API_ENDPOINT, formData, { headers });
    } catch (e) {
      sails.log.error('FreshDesk > createTicket');
      sails.log.error(e);
      throw flaverr('E_FRESHDESK_API_FAILURE', new Error(e?.message || 'Unable to create ticket'));
    }
  }

  async updateTicket(id, data) {
    try {
      const url = `${this.TICKET_API_ENDPOINT}/${id}`;
      const {
        headers,
        formData
      } = data;
      return await this.axios.put(url, formData, { headers });
    } catch (e) {
      sails.log.error('FreshDesk > updateTicket');
      sails.log.error(e);
      throw flaverr('E_FRESHDESK_API_FAILURE', new Error(e?.message || 'Unable to update ticket'));
    }
  }

  async deleteTicket(id) {
    try {
      const url = `${this.TICKET_API_ENDPOINT}/${id}`;
      return await this.axios.delete(url);
    } catch (e) {
      sails.log.error('FreshDesk > deleteTicket');
      sails.log.error(e);
      throw flaverr('E_FRESHDESK_API_FAILURE', new Error(e?.message || 'Unable to delete ticket'));
    }
  }

  async fetchConversationByTicketId(ticketId) {
    try {
      const url = `${this.TICKET_API_ENDPOINT}/${ticketId}/conversations`;
      return await this.axios.get(url);
    } catch (e) {
      sails.log.error('FreshDesk > fetchConversationByTicketId');
      sails.log.error(e);
      throw flaverr('E_FRESHDESK_API_FAILURE', new Error(e?.message || 'Unable to fetch ticket conversations'));
    }
  }

  async commentToConversation(ticketId, comment, userId) {
    try {
      const url = `${this.TICKET_API_ENDPOINT}/${ticketId}/reply`;
      const data = { body: `${userId}#&*&# ${comment}` };
      return await this.axios.post(url, data);
    } catch (e) {
      sails.log.error('FreshDesk > commentToConversation');
      sails.log.error(e);
      throw flaverr('E_FRESHDESK_API_FAILURE', new Error(e?.message || 'Unable to add comment to conversation'));
    }
  }

  async deleteTickets(ids = []) {
    const url = `${this.TICKET_API_ENDPOINT}/bulk_delete`;
    const data = { bulk_action: { ids } };
    return await this.axios.post(url, data);
  }
}

module.exports = FreshDesk;
