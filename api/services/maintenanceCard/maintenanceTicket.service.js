const FormData = require("form-data");
const fs = require("fs");

const createTicketFormData = async (maintenanceInfo, attachments) => {
  const formData = new FormData();

  const {
    priority,
    description,
    subject,
    email,
    status,
    comment,
    ccEmails,
    pageUrl,
    maintenanceType,
    devices,
    siteId,
  } = maintenanceInfo;

  formData.append("priority", priority);
  formData.append("description", description);
  formData.append("subject", subject);
  formData.append("email", email);
  formData.append("status", status);
  formData.append("custom_fields[cf_comment]", comment);

  const splitCcEmails = ccEmails ? ccEmails.split(",") : [];
  if (!_.isEmpty(splitCcEmails)) {
    for (let email of splitCcEmails) {
      formData.append("cc_emails[]", email);
    }
  }

  formData.append("custom_fields[cf_pageurl]", pageUrl);
  formData.append("custom_fields[cf_maintenancetype]", maintenanceType);
  formData.append("custom_fields[cf_site_id]", siteId);

  if (devices) {
    formData.append("custom_fields[cf_devices]", devices);
  }

  if (attachments && attachments.length > 0) {
    for (const attachment of attachments) {
      if (attachment && attachment.fd) {
        const filePath = attachment.fd;
        const buffer = await fs.readFileSync(filePath);

        await fs.unlinkSync(filePath);

        formData.append("attachments[]", buffer, {
          filename: attachment.filename,
          contentType: attachment.type,
          knownLength: 200,
        });
      }
    }
  }


  const headers = formData.getHeaders();
  return {
    formData,
    headers,
  };
};

module.exports = { createTicketFormData };
