/* eslint-disable no-undef */
const configurationHierarchy = require("./configurationHierarchy.private");
const Component = require("../component/component.service");
module.exports = {
  systems: configurationHierarchy.systems,
  siteSystemMapping: configurationHierarchy.siteSystemMapping,
  systemNodes: configurationHierarchy.systemNodes,
  nodes: configurationHierarchy.nodes,
  svgTaggingConfiguration: {
    /**
     * @description saveTaggingMapBykey function store the csv file uploaded from FE based on the key
     * @param {string} siteId | e.g be-hyd
     * @param {number} systemId | systemId from siteSystemMapping table
     * @param {string} key | Frontend rounter key for UI navigation
     * @param {array} svgMap | array of object [{svg_loc_id,type,device_id},...]
     * @returns
     */
    saveTaggingMapBykey: async function (siteId, systemId, key, svgMap) {
      let _existingSvgTagging = await SiteSystemMapping.findOne({
        where: {
          system_id: systemId,
          site_id: siteId,
        },
        select: ["id", "svg_tag_device_map"],
      });
      if (_.isEmpty(_existingSvgTagging)) {
        return {
          code: "E_SYSTEM_NOT_FIND",
          message: `system not found`,
        };
      }

      let checkComponentExistsPromiseHandler = [];
      let checkDeviceExistsPromiseHandler = [];
      for (let { device_id, type } of svgMap) {
        if (!device_id) {
          continue;
        }
        if (type == 'component') {
          checkComponentExistsPromiseHandler.push(
            isComponentExists(siteId, device_id)
          );
        }

        if (type == 'device') {
          checkDeviceExistsPromiseHandler.push(
            isDeviceExists(siteId, device_id)
          )
        }
      }
      let checkComponentExistsPromiseResponse = await Promise.all(
        checkComponentExistsPromiseHandler
      );
      let checkDeviceExistsPromiseResponse = await Promise.all(
        checkDeviceExistsPromiseHandler
      )

      let invalidComponents = checkComponentExistsPromiseResponse.filter(
        (it) => !it.componentExists
      );
      const invalidDevices = checkDeviceExistsPromiseResponse.filter(
        (it) => !it.deviceExists
      )
      if (!_.isEmpty(invalidComponents)) {
        return {
          code: "E_COMPONENTS_NOT_FOUND",
          message: `Component ids: ${invalidComponents
            .map((it) => it.componentId)
            .join(",")} not exists at siteId:${siteId}`,
        };
      }
      if (!_.isEmpty(invalidDevices)) {
        return {
          code: "E_DEVICES_NOT_FOUND",
          message: `Device ids: ${invalidDevices
            .map((it) => it.deviceId)
            .join(",")} not exists at siteId:${siteId}`,
        };
      }
      let _tagging = _existingSvgTagging.svg_tag_device_map || {};
      svgMap = svgMap.map(it=>{
        const {device_id,svg_loc_id,type} = it;
        return {
          device_id,
          svg_loc_id,
          type
        }
      })

      _tagging[key] = svgMap;
      async function isComponentExists(siteId, componentId) {
        let componentRow = await Component.findOne({ deviceId: componentId });
        if (!componentRow) {
          return { componentExists: false, componentId };
        }
        if (componentRow.siteId != siteId) {
          return { componentExists: false, componentId };
        }
        return { componentExists: true, componentId };
      }

      async function isDeviceExists(siteId, deviceId) {
        let deviceRow = await Devices.findOne({ deviceId, siteId });
        if (!deviceRow) {
          return { deviceExists: false, deviceId };
        }

        return { deviceExists: true, deviceId };
      } 

      await SiteSystemMapping.update({ id: _existingSvgTagging.id }).set({
        svg_tag_device_map: JSON.stringify(_tagging),
      });
      return;
    },

    /**
     * @description getSVGTaggingMapByKey function fetch the svgTaggingMap from siteSystemMapping table
     * @param {string} siteId | e.g be-hyd
     * @param {number} systemId | systemId from siteSystemMapping table
     * @param {string} key | Frontend rounter key for UI navigation
     * @returns {null || array}
     */
    getSVGTaggingMapByKey: async function (siteId, systemId, key) {
      let _existingSvgTagging = await SiteSystemMapping.findOne({
        where: {
          system_id: systemId,
          site_id: siteId,
        },
        select: ["id", "svg_tag_device_map"],
      });
      if (_.isEmpty(_existingSvgTagging)) return null;
      let svgTaggingMap = _existingSvgTagging.svg_tag_device_map;
      return svgTaggingMap && svgTaggingMap.hasOwnProperty(key)
        ? svgTaggingMap[key]
        : [];
    },

    /**
     * @description saveSVGTagingByLeafNode function for store the svgTaggingMap in node table.
     * @param {number} leafNodeId | leaf node id fro node table e.g floor 1 primary key for FAS system
     * @param {array} svgMap | array of object
     * @returns <Promise>
     */
    saveSVGTagingByLeafNode: async function (systemId, leafNodeId, svgMap) {
      let _leafNode = await Nodes.findOne({
        id: leafNodeId,
        system_id: systemId,
      });
      if (!_leafNode) {
        return {
          code: "E_LEAF_NODE_NOT_FOUND",
          message: `leaf node does not exist`,
        };
      }
      const deviceIds = [];
      const componentIds = []
      const allowedKeys = new Set(["device_id", "svg_loc_id", "type"]);
      svgMap = svgMap.map((it) => {
        const { device_id, svg_loc_id, type } = it;
        if (type == 'component' && !_.isEmpty(device_id)) {
          componentIds.push(device_id);
        }
        if (type == 'device' && !_.isEmpty(device_id)) {
          deviceIds.push(device_id);
        }
        return { device_id, svg_loc_id, type };
      });
      if (componentIds.length) {
        let invalidComponents = await this._validateComponentIdAtLeafNode(
          systemId,
          leafNodeId,
          componentIds
        );
        if (invalidComponents.length) {
          return {
            code: "E_INVALID_COMPONENT_ID_FOUND",
            message: `components don't exist at given leaf. Invalid components: ${invalidComponents.join(
              ","
            )}`,
          };
        }
      }

      if (deviceIds.length) {
        const invalidDevices = await this._validateDeviceIdAtLeafNode(
          systemId,
          leafNodeId,
          deviceIds
        )
        if (invalidDevices.length) {
          return {
            code: "E_INAVALID_DEVICE_ID_FOUND",
            message: `devices don't exist at given leaf. Invalid components: ${invalidDevices.join(
              ","
            )}`,
          };
        }
      }


      await Nodes.update({ id: leafNodeId }).set({
        svg_tag_device_map: JSON.stringify(svgMap),
      });
      return;
    },

    /**
     * @description getSVGTaggingMapByLeafNode fetches the svgTaggingMap by leafNodeId
     * @param {number} leafNodeId | leaf node id fro node table e.g floor 1 primary key for FAS system
     * @returns { null | array }
     */
    getSVGTaggingMapByLeafNode: async function (leafNodeId, systemId) {
      let _leafNode = await Nodes.findOne({
        id: leafNodeId,
        system_id: systemId,
      });
      if (!_leafNode) return null;
      return _leafNode.svg_tag_device_map || [];
    },
    _validateComponentIdAtLeafNode: async function (
      systemId,
      leafNodeId,
      components
    ) {
      let _existingComponents = await Nodes.find({
        where: {
          parent_id: leafNodeId,
          system_id: systemId,
          level_type: "component",
        },
        select: ["device_id"],
      });
      let _existingComponentsSet = new Set(
        _existingComponents.map((it) => it.device_id)
      );
      let invalidComponents = components.filter(
        (it) => !_existingComponentsSet.has(it)
      );
      return invalidComponents;
    },
    _validateDeviceIdAtLeafNode: async function(
      systemId,
      leafNodeId,
      devices
      ) {
        let _existingDevices = await Nodes.find({
          where: {
            parent_id: leafNodeId,
            system_id: systemId,
            level_type: "controller",
          },
          select: ["device_id"],
        });
        let _existingDevicesSet = new Set(
          _existingDevices.map((it) => it.device_id)
        );
        let invalidDevices = devices.filter(
          (it) => !_existingDevicesSet.has(it)
        );
        return invalidDevices;
      }
  },
};
