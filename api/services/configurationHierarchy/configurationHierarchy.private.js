/* eslint-disable no-undef */
module.exports = {
  systems : {
    /**
     * Systems module private functions
     */
    create: async (params) => {
      return Systems.create(params);
    },
    find: async (searchParams) => {
      // Systems.find
      return Systems.find(searchParams);
    },
    findOne: async (searchParams) => {
      // Systems.findone
      let systemss = await Systems.find(searchParams).limit(1);
      return systemss[0];
    },
    update: async (searchParams, updateValue) =>{
      return Systems.update(searchParams, updateValue);
    },
    delete: async (searchParams) => {
      return Systems.destroy(searchParams);
    },
  },

  siteSystemMapping : {
    /**
     * SiteSystemMapping module private functions
     */
    create: async (params) => {
      return SiteSystemMapping.create(params);
    },
    find: async (searchParams) => {
      // SiteSystemMapping.find
      return SiteSystemMapping.find(searchParams);
    },
    findOne: async (searchParams) => {
      // SiteSystemMapping.findone
      let SiteSystemMappings = await SiteSystemMapping.find(searchParams).limit(1);
      return SiteSystemMappings[0];
    },
    update: async (searchParams, updateValue) =>{
      return SiteSystemMapping.update(searchParams, updateValue);
    },
    delete: async (searchParams) => {
      return SiteSystemMapping.destroy(searchParams);
    }
  },

  systemNodes : {
    /**
     * SystemNodes module private functions
     */
    create: async (params) => {
      return SystemNodes.create(params);
    },
    find: async (searchParams) => {
      // SystemNodes.find
      return SystemNodes.find(searchParams);
    },
    findOne: async (searchParams) => {
      // SystemNodes.findone
      let SystemNodess = await SystemNodes.find(searchParams).limit(1);
      return SystemNodess[0];
    },
    update: async (searchParams, updateValue) =>{
      return SystemNodes.update(searchParams, updateValue);
    },
    delete: async (searchParams) => {
      return SystemNodes.destroy(searchParams);
    },
  },

  nodes: {
  /**
   * Nodes module private functions
   */
  create: async (params) => {
    return Nodes.create(params);
  },
  find: async (searchParams) => {
    // Nodes.find
    return Nodes.find(searchParams);
  },
  findOne: async (searchParams) => {
    // Nodes.findone
    let Nodess = await Nodes.find(searchParams).limit(1);
    return Nodess[0];
  },
  update: async (searchParams, updateValue) =>{
    return Nodes.update(searchParams, updateValue);
  },
  delete: async (searchParams) => {
    return Nodes.destroy(searchParams);
  },
}
};
