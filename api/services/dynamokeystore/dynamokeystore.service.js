const dynamokeystoreservice = require('./dynamokeystore.private');
const globalhelpers = require('../../utils/globalhelper');

  async function updateSitesMainMeterList (siteId, mainMeterList) {
    try {
      let value =  mainMeterList.join(',');
      let key = `${siteId}_mainMeter`;
      await dynamokeystoreservice.update(key, value);
      return true;
    } catch (e) {
      sails.log.error(e);
      return false;
    }
  }

module.exports = {

  find: dynamokeystoreservice.find,
  findOne: dynamokeystoreservice.findOne,
  update: dynamokeystoreservice.update,
  create: dynamokeystoreservice.create,

  updateConfigTs: async function (siteId, timestamp) {
    let key = `${siteId}_configTS`;
    let value = timestamp || globalhelpers.getCurrentUnixTs();
    try {
      await dynamokeystoreservice.create({ key, value });
      return true;
    } catch (e) {
      sails.log.error(e);
      return false;
    }
  },
  getConfigTs: async function (siteId) {
    let key = `${siteId}_configTS`;
    let value;

    try {
      value = await dynamokeystoreservice.findOne({ key });
    } catch (e) {
      sails.log.error(e);
      return false;
    }
    if (value) return value.value;
    else return false;
  },
  fetchEmList: async function(siteId){
    const dyanmoKeyStoreValue = await dynamokeystoreservice.findOne({ key: `${siteId}_em` });
    let emList;
    if (!dyanmoKeyStoreValue || !dyanmoKeyStoreValue.list || !dyanmoKeyStoreValue.list.values) return [];
    emList = [...dyanmoKeyStoreValue.list.values];
    if(Array.isArray(emList) && emList.length == 0) return [];
    return emList;
  },
  updateEMList: async function (siteId, emlist) {
    try {
      let key = `${siteId}_em`;
      let list = emlist;
      await dynamokeystoreservice.update({ key }, { list });
      return true;
    } catch (e) {
      sails.log.error(e);
      return false;
    }
  },
  getDeviceIDFromTotalDeviceCount: async function () {
    try {
      let _deviceCount = await dynamokeystoreservice.findOne({ 'key': 'totalDeviceCount' });
      let deviceCount = _deviceCount.value;
      if (!deviceCount) {
        return 0;
      } else {
        return parseInt(deviceCount);
      }
    } catch (e) {
      sails.log.error('[-]dynamoservice.getDeviceIDFromTotalDeviceCount ', e)
      throw new Error(e);
    }
  },
  updateTotalDeviceCount: async function (deviceCount) {
    try {
      let key = { 'key': 'totalDeviceCount' };
      let value = { 'value': deviceCount }
      await dynamokeystoreservice.update(key, value);

    } catch (e) {
      sails.log.error('[-]dynamoservice.updateTotalDeviceCount ', e)
      throw new Error(e);
    }
  },
  findSitesMainMeterSet: async function (siteId) {
    try {
      let mainMeterObj = await this.findOne({ key: `${siteId}_mainMeter` }); // will return {value:1} OR undefined
      let mainMeterSet = new Set();
      if (mainMeterObj !== undefined) {
        mainMeterSet = new Set(mainMeterObj.value.split(','));
      }
      return mainMeterSet;
    } catch (e) {
      sails.log.error('[-]dynampservice.findSitesMainMeterSet', e);
      throw new Error(e);
    }
  },
  addDeviceToSitesMainMeterList: async function (siteId, deviceId) {
    try {
      let mainMeterSet = await this.findSitesMainMeterSet(siteId); // will return {value:1} OR undefined
      mainMeterSet.add(deviceId);
      let updateResponse = await updateSitesMainMeterList(siteId, [...mainMeterSet]);
      if (updateResponse) {
        return true;
      } else {
        return false;
      }

    } catch (e) {
      sails.log.error('[-]dynampservice.addDeviceTOSitesMainMeterList', e);
      throw new Error(e);
    }
  },
  deleteDeviceFromSitesMainMeterList: async function (siteId, deviceId) {

    try {
      let mainMeterSet = await this.findSitesMainMeterSet(siteId); // will return {value:1} OR undefined
      let updateSet = mainMeterSet.delete(deviceId);
      let updateResponse = await updateSitesMainMeterList(siteId, [...updateSet]);
      if (updateResponse) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      sails.log.error('[-]dynamoservice.deleteDeviceFromSitesMainMeterList', e);
      throw new Error(e);
    }
  }

};
