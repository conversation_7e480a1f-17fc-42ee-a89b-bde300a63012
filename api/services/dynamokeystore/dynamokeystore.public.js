
const dynamokeystoreservice = require('./dynamokeystore.service');

module.exports = {
  find: dynamokeystoreservice.find,
  findOne : dynamokeystoreservice.findOne,
  getConfigTs : dynamokeystoreservice.getConfigTs,
  updateConfigTs : dynamokeystoreservice.updateConfigTs,
  update : dynamokeystoreservice.update,
  destroy : dynamokeystoreservice.destroy,

  fetchEmList: dynamokeystoreservice.fetchEmList,
  updateEMList : async function(siteId, emlist){
    if( Array.isArray(emlist) ){
      return dynamokeystoreservice.updateEMList(siteId, emlist);
    } else{
      sails.log('[dynamokeystore.public.js] emlist required array found string ');
      return false;
    }
  },

  findSitesMainMeterSet: dynamokeystoreservice.findSitesMainMeterSet,
  getDeviceIDFromTotalDeviceCount: dynamokeystoreservice.getDeviceIDFromTotalDeviceCount,
  updateTotalDeviceCount: dynamokeystoreservice.updateTotalDeviceCount,
  deleteDeviceFromSitesMainMeterList : dynamokeystoreservice.deleteDeviceFromSitesMainMeterList,
  addDeviceToSitesMainMeterList : dynamokeystoreservice.addDeviceToSitesMainMeterList
};
