/* eslint-disable no-undef */
module.exports = {

  /**
   * Store a key value pair in DyanmoKeyStore
   */
  find: async (searchParams) => {
    // DyanmoKeyStore.find
    return DyanmoKeyStores.find(searchParams);
  },
  findOne: async (searchParams) => {
    // DyanmoKeyStore.find
    return DyanmoKeyStores.findOne(searchParams);
  },
  update: async (searchParams, updateValue) =>{
    return DyanmoKeyStores.update(searchParams, updateValue);
  },
  create: async (searchParams) => {
    // DyanmoKeyStore.create
    return DyanmoKeyStores.create(searchParams);
  }
};
