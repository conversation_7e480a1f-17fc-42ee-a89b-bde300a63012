const globalHelpers = require('../../utils/globalhelper');
/* eslint-disable no-undef */
module.exports = {

  /**
   * Datadevice module private functions
   */
  create: async (params) => {
    return Datadevices.create(params);
  },
  find: async (searchParams, sortParams) => {
    // Datadevice.find
    let datadevices;
    if(!sortParams)
      datadevices =  await Datadevices.find(searchParams);
    else datadevices = await Datadevices.find(searchParams).sort(sortParams);
    datadevices.forEach(datadevice => {
      datadevice['data'] = globalHelpers.toJson(datadevice['data'])
    });
    return datadevices;
  },
  findOne: async (searchParams) => {
    // Datadevice.findone
    let datadevices = await Datadevices.find(searchParams).limit(1);
    datadevices.forEach(datadevice => {
      datadevice['data'] = globalHelpers.toJson(datadevice['data'])
    });
    return datadevices[0];
  },
  update: async (searchParams, updateValue) =>{
    return Datadevices.update(searchParams, updateValue);
  },
  delete: async (searchParams) => {
    return Datadevices.destroy(searchParams);
  },
};
