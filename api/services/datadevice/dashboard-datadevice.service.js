const cacheService = require("../cache/cache.service");
const dynamokeystorePublic = require("../dynamokeystore/dynamokeystore.public")
const influxEnterpriseService = require('../influx/enterprise/influx.public')
const dashboardUtils = require('../../utils/datadevice/dashboard');
const dailyConsumptionService = require('../dailyconsumption/dailyconsumption.public');
const dataDeviceDashboardUtils = require('../../utils/datadevice/dashboard'); 
const globalhelpers = require('../../../api/utils/globalhelper')
const moment = require('moment')

const BUCKET = 'device_component/autogen';
const MEASUREMENT = 'device';

 /**
     * @description Get the query raw data from the influxDB enterprise
     * @param {Object} params 
     * @return {Object} filterData
     * @description filter raw data
*/
const hourlyWiseLoadPattern = async function(params){
    try {
        const {
            startTime,
            endTime,
            siteId,
        } = params
        const timezoneOffset = await sails.helpers.getSiteTimezone.with({siteId, timezoneFormat:'utcOffsetInMinute'});
        let { deviceIds } = params;
        const query = `
        import "math"
        import "timezone"
        option location = timezone.fixed(offset: {{timezoneOffset}})
        from(bucket: "device_component/autogen")
        |> range(start: {{startTime}}, stop: {{endTime}})
        |> filter(fn: (r) => r["_measurement"] == "device")
        |> filter(fn: (r) => {{deviceId}})
        |> filter(fn: (r) => r["siteId"] == "{{siteId}}")
        |> filter(fn: (r) => r["_field"] == "kw")
        |> aggregateWindow(every: 1h, fn: mean,createEmpty: false,offset:{{timezoneOffset}},timeSrc:"_start")
        |> group(columns: ["_time"], mode:"by")
        |> sum()
        |> yield(name: "hourlyWiseLoadPattern")     
        `;
        deviceIds = deviceIds.map(id => `r["deviceId"] == "${id}"`).join(' or '); 
        const _consumptionRecord = await influxEnterpriseService.runQuery(query, {
            replacements: {
                bucket: BUCKET,
                startTime: startTime,
                endTime: endTime,
                measurement: MEASUREMENT,
                deviceId: deviceIds,
                siteId: siteId,
                timezoneOffset
            },
            debug: true,
        });
        const filterData = dataDeviceDashboardUtils.filterInfluxDbRawData(_consumptionRecord)
        return filterData;
      } catch(e) {
        sails.log('Influx Error: ', e);
        sails.log.error(e)
        throw e;
      }
}


const getCurrentLoad = async function(siteId, deviceIds) {
    try {
        const timezoneOffset = await sails.helpers.getSiteTimezone.with({siteId, timezoneFormat:'utcOffsetInMinute'});
        const query = `
        import "timezone"
        option location = timezone.fixed(offset: {{timezoneOffset}})
        from(bucket: "{{bucket}}")
        |> range(start:-1d)
        |> filter(fn: (r) => r["_measurement"] == "{{measurement}}")
        |> filter(fn: (r) => {{deviceId}})
        |> filter(fn: (r) => r["siteId"] == "{{siteId}}")
        |> filter(fn: (r) => r["_field"] == "kw")
        |> last()
        |> group(columns: ["_time"], mode:"by")
        |> sum()
        |> yield(name: "recentCurrentLoad")
        `;
        deviceIds = deviceIds.map(id => `r["deviceId"] == "${id}"`).join(' or '); 
        const _consumptionRecord = await influxEnterpriseService.runQuery(query, {
            replacements: {
                bucket: BUCKET,
                measurement: MEASUREMENT,
                deviceId: deviceIds,
                siteId: siteId,
                timezoneOffset
            },
            debug: true,
        });
        return _consumptionRecord[0] && Number(_consumptionRecord[0]._value) || 0;
      } catch(e) {
        sails.log('Influx Error: ', e);
        sails.log.error(e)
        throw e;
      }
}

/**
 * @description Get consumption last hour
 * @param {*} params 
 * @returns {Number} consumption last hour
 */
const consumptionLastHour = async function(params,fields, userPrefConsUnit) {
    let { deviceIds, siteId, timezoneOffset} = params;
    const query = `
    import "timezone"
    option location = timezone.fixed(offset: {{timezoneOffset}})
    from(bucket: "{{bucket}}")
    |> range(start: -1h)
    |> filter(fn: (r) => r["_measurement"] == "{{measurement}}")
    |> filter(fn: (r) => {{deviceId}})
    |> filter(fn: (r) => r["siteId"] == "{{siteId}}")
    |> filter(fn: (r) => {{fields}})
    |> spread(column: "_value")
    |> group(columns: ["_field"], mode:"by")
    |> reduce(fn: (r, accumulator) => ({ sum: r._value + accumulator.sum }), identity: {sum: 0.0})
    |> yield(name: "hourlyConsumption")
    `;
    deviceIds = deviceIds.map(id => `r["deviceId"] == "${id}"`).join(' or '); 
    let _fields = fields.map(f => `r["_field"] == "${f}"`).join(' or ')
    const _consumptionRecord = await influxEnterpriseService.runQuery(query, {
        replacements: {
            bucket: BUCKET,
            measurement: MEASUREMENT,
            deviceId: deviceIds,
            siteId: siteId,
            fields: _fields,
            timezoneOffset
        },
        debug: true,
    });
    const filterData = _consumptionRecord.reduce((acc, row) => {
        acc[row._field]= row.sum
        return acc
    }, {})
    const lastHourConsumption = globalhelpers.getConsumptionFromDeviceData(filterData, userPrefConsUnit)
    return lastHourConsumption;
}
module.exports = {
    /**
     * @description To get the emList from _mainMeter
     * @param {string} siteId
     * @return {Array} emList
     */
    getMainEMList: async function(siteId) {
        let emList = await dynamokeystorePublic.findOne({
            key: `${siteId}_mainMeter`
        })
        emList = emList && emList.value && emList.value.split(",");
        return emList;
    },
    /**
     * @description Get the last timestamp data form caches.
     * @param {String} siteId
     * @return {Integer} Number || null
     */
    cachedLastDataTime: async function(siteId) {
        const lastTimestamp = await cacheService.get(`${siteId}_lastDataTime`);
        return parseInt(lastTimestamp) || null;
    },
    /**
     * @description Group by hour the last day consumption data
     * @param {Array} emList 
     * @param {Object} siteConfig
     * @return {Object} consumptionByGroupBy
     */
    hourlyWiseConsumption: async function(devices, fields, siteConfig) {
        const {userPrefConsUnit, siteId} = siteConfig
        const [
            timezoneOffset,
            timezoneOffsetWithMinutes
        ] = await Promise.all([
                sails.helpers.getSiteTimezone.with({siteId, timezoneFormat:'Z'}),
                sails.helpers.getSiteTimezone.with({siteId, timezoneFormat:'utcOffsetInMinute'}),
        ])
        const lastDayTimestamp = moment().subtract(1, 'd').utcOffset(timezoneOffset).format('YYYY-MM-DDTHH:00:00Z')
        const currentDayTimestamp = moment().utcOffset(timezoneOffset).format('YYYY-MM-DDTHH:15:00Z');
        const [
            hourlyLoadPattern,
            hourlyCons,
            currentLoad
        ] = await Promise.all([
            hourlyWiseLoadPattern({
            siteId: siteId,
            startTime: lastDayTimestamp,
            endTime: currentDayTimestamp,
            sortByDesc: false,
            deviceIds: devices
          }),
            consumptionLastHour({
            siteId: siteId,
            deviceIds: devices,
            timezoneOffset: timezoneOffsetWithMinutes
            },
            fields, 
            userPrefConsUnit),
            getCurrentLoad(siteId, devices)
        ])
        const consumptionByGroupBy = dashboardUtils.hourlyWiseConsumption(hourlyLoadPattern, hourlyCons, currentLoad);
        return consumptionByGroupBy;
    },
    /**
     * @description Get weekly consumption data 
     * @param {Array} deviceList
     * @param {String} siteId
     * @param {String} userPrefConsUnit
     * @returns {Object}
     */
    getWeeklyConsumption: async function(deviceList, siteId, {userPrefConsUnit, type}) {
        const timezoneOffset = await sails.helpers.getSiteTimezone.with({siteId, timezoneFormat:'Z'})        
        const filter = {
            startTime: moment().subtract(7, 'days').utcOffset(timezoneOffset).format('YYYY-MM-DDT00:00:00Z'),
            endTime: moment().utcOffset(timezoneOffset).format('YYYY-MM-DDT00:00:00Z'), // Exclusive behavior influxDB
        }
        let weekConsRawData;
        if (type === 'expertPro') {
            weekConsRawData = await dailyConsumptionService.getWeeklyConsumptionExpertPro(deviceList, siteId, filter); 
        } else {
            weekConsRawData = await dailyConsumptionService.getWeeklyConsumptionNF29(deviceList, siteId, filter); 
        }
        const filteredConsumptionData = dataDeviceDashboardUtils.weeklyConsumptionData(weekConsRawData, userPrefConsUnit);
        return filteredConsumptionData;
    },
    lastDayConsumption: async function(deviceList, fields, {siteId, userPrefConsUnit}) {
    const timezoneOffsetMinute = await sails.helpers.getSiteTimezone.with({siteId, timezoneFormat:'utcOffsetInMinute'}) 
    const startTime = moment().startOf("D").utcOffset(timezoneOffsetMinute).format('YYYY-MM-DDTHH:mm:00Z')
    const query = `
    import "timezone"
    option location = timezone.fixed(offset: {{timezoneOffset}})
    from(bucket: "{{bucket}}")
    |> range(start: {{startTime}}, stop: now())
    |> filter(fn: (r) => r["_measurement"] == "{{measurement}}")
    |> filter(fn: (r) => {{deviceId}})
    |> filter(fn: (r) => r["siteId"] == "{{siteId}}")
    |> filter(fn: (r) => {{fields}})
    |> difference(nonNegative: false, columns: ["_value"])
    |> sum(column: "_value")
    |> group(columns: ["_time", "_field"], mode:"by")
    |> sum()
    |> yield(name: "lastDayConsumption")
    `;
    let deviceIds = deviceList.map(id => `r["deviceId"] == "${id}"`).join(' or '); 
    let _fields = fields.map(f => `r["_field"] == "${f}"`).join(' or ')
    const _consumptionRecord = await influxEnterpriseService.runQuery(query, {
        replacements: {
            bucket: BUCKET,
            measurement: MEASUREMENT,
            deviceId: deviceIds,
            siteId: siteId,
            fields: _fields,
            startTime,
            timezoneOffset: timezoneOffsetMinute
        },
        debug: true,
    });
    const lastDayConsumption = dashboardUtils.lastDayConsumption(_consumptionRecord, userPrefConsUnit)
    return lastDayConsumption
    }
  
}