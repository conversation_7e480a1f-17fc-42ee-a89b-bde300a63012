
const moment = require("moment");

const influx = require("../influx/enterprise/influx.service");
const INFLUX_BUCKET = "device_component/autogen";
const INFLUX_MEASUREMENT = "device";

module.exports = {
    getCurrentLoad: async function(emList){
        try {
            const query = `
                from(bucket: "{{bucket}}")
                    |> range(start: -5m)
                    |> filter(fn: (r) => r["_measurement"] == "{{measurement}}")
                    |> filter(fn: (r) => {{emList}})
                    |> filter(fn: (r) => r["_field"] == "kw")
                    |> last()
                    |> group(columns: ["_time"])
                    |> sum(column: "_value")
                    |> yield(name: "currentLoad")`;
            const queryDeviceIds = emList.map(deviceId => `r["deviceId"] == "${deviceId}"`).join(" or ");
            const currentLoadResult = await influx.runQuery(query, {
                replacements: {
                    bucket: INFLUX_BUCKET,
                    measurement: INFLUX_MEASUREMENT,
                    emList: queryDeviceIds,
                },
                debug: true,
            });
            return currentLoadResult[0] && Number(currentLoadResult[0]._value) || 0;
        } catch (error) {
            sails.log.error("[getCurrentLoad] >> Error fetching current load!");
            sails.log.error(error);
            return null;
        }
    },
    getLoadPatternGroupedByHour: async function(emList, startTime, endTime,siteId){
        try {
            const query = `
                from(bucket: "{{bucket}}")
                |> range(start: {{startTime}}, stop: {{endTime}})
                |> filter(fn: (r) => r["_measurement"] == "{{measurement}}")
                |> filter(fn: (r) => {{emList}})
                |> filter(fn: (r) => r["_field"] == "kw")
                |> aggregateWindow(every: 1h, fn: mean,createEmpty: false,offset:{{timezoneOffset}},timeSrc:"_start")
                |> group(columns: ["_time"], mode:"by")
                |> sum()
                |> yield(name: "loadPatternGroupedByHour") 
                `
            const queryDeviceIds = emList.map(deviceId => `r["deviceId"] == "${deviceId}"`).join(" or ");
            const timezoneOffset = await sails.helpers.getSiteTimezone.with({siteId, timezoneFormat:'utcOffsetInMinute'})
            const loadPattenRecords = await influx.runQuery(query, {
                replacements: {
                    bucket: INFLUX_BUCKET,
                    measurement: INFLUX_MEASUREMENT,
                    emList: queryDeviceIds,
                    startTime,
                    endTime,
                    timezoneOffset
                },
                debug: true,
            });
            return loadPattenRecords;
        } catch (error) {
            sails.log.error("[getLoadPatternGroupedByHour] >> Error fetching load pattern!");
            sails.log.error(error);
            return null;
        }
    }
}