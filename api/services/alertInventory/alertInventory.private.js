const flaverr = require('flaverr');
module.exports = {
  register: async (param) => {
    const {
      alert_name,
      observation_time_interval,
      running_interval,
      priority,
      message_template_id,
      observer_source,
      description,
      config,
      expression
    } = param;
    if(observer_source === "OTHER" && _.isEmpty(config)) throw new Error(`config key is required for Source ${observer_source}`);
    // if(observer_source  != "OTHER" && _.isEmpty(expression)) throw new Error(`expression is missing for Source ${observer_source}`);
    return await sails.getDatastore(process.env.SMART_ALERT_DB_NAME)
      .transaction(async db => {

        const alertTemplateObj= {
          name:alert_name,
          description,
          observer_source,
          priority
        }
        if(observer_source == "OTHER"){
          alertTemplateObj.config = JSON.parse(config)
        } else {
          alertTemplateObj.expression = expression;
        }
        //TODO need to implement Little language for flux or tick based observer_source. handle the deletion part also in case some error encounter
        let _Alert = await AlertTemplate.create(alertTemplateObj)
          .fetch()
          .usingConnection(db);
        if (!await MessageTemplate.findOne({
          id: message_template_id,
          status: 1
        })
          .usingConnection(db)) {
          throw flaverr('E_MESSAGE_TEMPLATE_NOT_FOUND', new Error('message template does not not exist.'));
        }
        await AlertMessageTemplateMapping.create({
          alert_id: _Alert.id,
          message_template_id
        })
          .usingConnection(db);
        return _Alert;
      });
  },
  delete: async function (alertId) {
    const softDeleteObject = {
      status: 0,
    };
    // noinspection JSUnresolvedVariable
    const alert = await AlertTemplate.findOne({ name: alertId })
    if(!alert) {
      throw flaverr('E_NOT_FOUND', new Error(`Alert(${alertId}) not exists`));
    }
    await AlertTemplate.update({ name: alertId }).set(softDeleteObject);
  },
  /**
   * Handles the deletion of an alert.
   * @param {string} alertId - The name of the alert to be deleted.
   * @throws {Error} If the alert does not exist or if it has already been deleted.
   * @returns {Promise<void>}
 */

  activate: async function (alertId) {
    if (!await AlertInventory.findOne({ id: alertId })) {
      throw flaverr('E_ALERT_NOT_FOUND', new Error('alert does not exist.'));
    }
    await AlertInventory.update({ id: alertId })
      .set({ status: 1 });
  },
  deactivate: async function (alertId) {
    // noinspection JSUnresolvedVariable
    if (!await AlertInventory.findOne({ id: alertId })) {
      throw flaverr('E_ALERT_NOT_FOUND', new Error('alert does not exist.'));
    }
    // noinspection JSUnresolvedVariable
    await AlertInventory.update({ id: alertId })
      .set({ status: 2 });
  },

  unsubscribe: async function (alertId, userId) {
    const softDeleteObject = {
      status: 0,
    };
    if(!alertId || _.isEmpty(alertId.trim())) {
      const userAlerts = await AlertSubscribers.find({ user_id: userId, status: { '!=': 0 } });
      if(userAlerts.length == 0) {
        throw flaverr('E_NOT_FOUND', new Error(`No active alert subscriptions found for user(${userId})`));
      }
      await AlertSubscribers.update({ user_id: userId }).set(softDeleteObject);
      return;
    }
    const alert = await AlertTemplate.findOne({ name: alertId })
    if(!alert) {
      throw flaverr('E_NOT_FOUND', new Error(`Alert(${alertId}) not exists`));
    }
    const userAlerts = await AlertSubscribers.find({alert_id: alert.id, user_id: userId})
    if(userAlerts.length == 0) {
      throw flaverr('E_NOT_FOUND', new Error(`Alert(${alertId}) not subscribed by user(${userId})`));
    }
    await AlertSubscribers.update({alert_id: alert.id, user_id: userId}).set(softDeleteObject);
  },

  subscribe: async function ({ userId, alertId, priority, subscriptionCriteria }) {
    const alert = await AlertTemplate.findOne({ name: alertId })
    if (alertId != '*' && !alert) {
      throw flaverr('E_NOT_FOUND', new Error(`Alert(${alertId}) not exists`));
    }
    return await sails.getDatastore(process.env.SMART_ALERT_DB_NAME)
      .transaction(async db => {
        const batchSubscriptionRecords = [];
        const subscribeObj = {
          user_id: userId,
          alert_id: alertId == '*' ? null : alert.id,
          priority
        }
        if (!Array.isArray(subscriptionCriteria)) {
          const existingSubscription = await AlertSubscribers.find({
            user_id: userId,
            alert_id: subscribeObj.alert_id,
            site_id: '*',
            device_id: '*'
          }).usingConnection(db);
          if(existingSubscription.length == 0) {
            const _subscribeObj = Object.assign({}, subscribeObj);
            _subscribeObj.site_id = '*';
            _subscribeObj.device_id = '*';
            batchSubscriptionRecords.push(_subscribeObj);
          }
        } else {
          for (let criteria of subscriptionCriteria) {
            const { siteId, deviceIds } = criteria
            if (deviceIds === '*') {
              const existingSubscription = await AlertSubscribers.find({
                user_id: userId,
                alert_id: subscribeObj.alert_id,
                site_id: ['*', siteId],
                device_id: '*',
                status: 1
              }).usingConnection(db);
              if (existingSubscription.length == 0) {
                const _subscribeObj = Object.assign({}, subscribeObj);
                _subscribeObj.site_id = siteId;
                _subscribeObj.device_id = deviceIds;
                batchSubscriptionRecords.push(_subscribeObj);
              }
            } else {
              for (let deviceId of deviceIds) {
                const existingSubscription = await AlertSubscribers.find({
                  user_id: userId,
                  alert_id: subscribeObj.alert_id,
                  site_id: ['*', siteId],
                  device_id: ['*', deviceId]
                }).usingConnection(db);
                if (existingSubscription.length == 0) {
                  const _subscribeObj = Object.assign({}, subscribeObj);
                  _subscribeObj.site_id = siteId;
                  _subscribeObj.device_id = deviceId;
                  batchSubscriptionRecords.push(_subscribeObj);
                }
              }
            }
          }
        }
        if(batchSubscriptionRecords.length > 0) {
          await AlertSubscribers.createEach(batchSubscriptionRecords)
            .usingConnection(db)
        }
      })
  }
};
