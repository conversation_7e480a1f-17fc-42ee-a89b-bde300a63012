const userSiteMapService = require('./userSiteMap.private');
const userSiteMapUtils = require('../../utils/usersitemap/utils');


module.exports = {
  'create': async function(params) {
    const {userId, siteId} = params;
    const record = await userSiteMapService.create(params);
    await userSiteMapService.addSiteIdInUserSiteCache(userId, siteId);
    return record;
  },
  'find': async function(searchParams){
    let userSiteMaps;
    try{
      userSiteMaps = await userSiteMapService.find(searchParams);
    }catch(e){
      throw e;
    }
    if(!userSiteMaps){
      return [];
    }
    userSiteMaps.forEach(userSiteMap=>{
      userSiteMap.unitPreference = userSiteMapUtils.addDefaultsToUnitPreference(userSiteMap.unitPreference);
    })
    return userSiteMaps;

  },
  'findOne': async function (searchParams) {
    let userSiteMap;
    try {
      userSiteMap = await userSiteMapService.findOne(searchParams);
    } catch (e) {
      throw e;
    }
    if (!userSiteMap) return undefined;
    const { userId, siteId } = userSiteMap
    await userSiteMapService.addSiteIdInUserSiteCache(userId, siteId);

    userSiteMap.unitPreference = userSiteMapUtils.addDefaultsToUnitPreference(userSiteMap.unitPreference);
    return userSiteMap;
  },
  'update': userSiteMapService.update,
  'delete': async function (searchParams) {
    const {userId, siteId} = searchParams;
    await userSiteMapService.delete(searchParams)
    await userSiteMapService.removeSiteIdInUserSiteCache(userId, siteId);
    return true;
  },
  'fetchEmList': async function(userId, siteId){
    const userPreferences = await this.findOne({ userId, siteId });
    if(!userPreferences || !userPreferences.consumptionPageEMList) return null;

    let emList;
    if(Array.isArray(userPreferences.consumptionPageEMList.values))
      emList = userPreferences.consumptionPageEMList.values;
    else if(Array.isArray(userPreferences.consumptionPageEMList))
      emList = userPreferences.consumptionPageEMList;

    if(typeof emList == "array" && emList.length == 0) return null;
    if(!emList) return null;
    return emList;
  },
  'updateEmList': async function(userId, siteId, emList){
    try {
      if(!emList || !Array.isArray(emList)) return false;
      return await userSiteMapService.update({ userId, siteId },{
        consumptionPageEMList: emList
      });
    } catch (error) {
      sails.log.error("Error updating em List in user preference!");
      sails.log.error(error);
      throw error;
    }
  },
  checkSiteIdInUserSite: async function(userId, siteId){
    if (await userSiteMapService.checkSiteIdInUserSiteCache(userId, siteId)) return true;
    const result=await this.findOne({ userId, siteId });
    return result ? true : false;
  },
  fetchUserSitePreferences: async (userId, siteId) => {
    const cacheUserSitePref = await userSiteMapService.getUserPrefCache(userId, siteId);
    if (!_.isEmpty(cacheUserSitePref)) return cacheUserSitePref;
    const userPref = await userSiteMapService.findOne({ userId, siteId });
    await userSiteMapService.setUserPrefCache(userId, siteId, userPref);
    return userPref;
  },
  getUserSiteIds: async (userId)=> {
    const cacheUserSitesMapId = await userSiteMapService.retrieveCachedUserSiteIds(userId);
    if(cacheUserSitesMapId) return cacheUserSitesMapId;
    const userSiteMap = await userSiteMapService.find({ userId, status:1 });
    const userSiteIds = userSiteMap.map((userSite)=> userSite?.siteId);
    await userSiteMapService.cacheUserSiteMapIds(userId, userSiteIds);
    return userSiteIds;
  },
  checkSiteIdInUserSiteCache: userSiteMapService.checkSiteIdInUserSiteCache,
  addSiteIdInUserSiteCache: userSiteMapService.addSiteIdInUserSiteCache,
  removeSiteIdInUserSiteCache: userSiteMapService.removeSiteIdInUserSiteCache,
  retrieveCachedUserSiteIds: userSiteMapService.retrieveCachedUserSiteIds,
  cacheUserSiteMapIds: userSiteMapService.cacheUserSiteMapIds,
  getUserPrefCache: userSiteMapService.getUserPrefCache,
  setUserPrefCache: userSiteMapService.setUserPrefCache,
  invalidateUserSiteIds: userSiteMapService.invalidateUserSiteIds,
};
