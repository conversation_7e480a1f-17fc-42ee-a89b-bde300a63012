const { getUserSiteMapCacheKey } = require("../../utils/usersitemap/utils");
const cacheService = require("../cache/cache.service");
const ttlSeconds = 60 * 60 * 24 * 15; //15 days

module.exports = {
  /**
   * UserSiteMaps module private functions
   */
  create: async (params) => {
    const { userId } = params;
    await invalidateUserSiteIds(userId);
    return UserSiteMaps.create(params);
  },
  find: async (searchParams) => {
    searchParams.status = 1;
    return await UserSiteMaps.find(searchParams);
  },
  findOne: async (searchParams) => {
    searchParams.status = 1;
    return UserSiteMaps.findOne(searchParams);
  },
  update: async (searchParams, updateValue) => {
    const { userId } = searchParams;
    await invalidateUserSiteIds(userId);
    await invalidateUserPrefCache(userId, searchParams?.siteId);
    return UserSiteMaps.update(searchParams, updateValue);
  },
  delete: async (searchParams) => {
    const { userId } = searchParams;
    await invalidateUserSiteIds(userId);
    await invalidateUserPrefCache(userId, searchParams?.siteId);
    return UserSiteMaps.destroy(searchParams);
  },
  /**Reading and writing to redis cache*/
  checkSiteIdInUserSiteCache: async function (userId, siteId) {
    const userSiteAccessCacheKey = getUserSiteMapCacheKey(userId);
    return cacheService.sismember(userSiteAccessCacheKey, siteId);
  },
  addSiteIdInUserSiteCache: async function (userId, siteId) {
    const userSiteAccessCacheKey = getUserSiteMapCacheKey(userId);
    await cacheService.sadd(userSiteAccessCacheKey, siteId);
    await cacheService.expire(userSiteAccessCacheKey, ttlSeconds);
    return true;
  },
  removeSiteIdInUserSiteCache: async function (userId, siteId) {
    const userSiteAccessCacheKey = getUserSiteMapCacheKey(userId);
    await cacheService.srem(userSiteAccessCacheKey, siteId);
    return true;
  },
  retrieveCachedUserSiteIds,
  cacheUserSiteMapIds,
  getUserPrefCache,
  setUserPrefCache,
  invalidateUserSiteIds
};

async function retrieveCachedUserSiteIds(userId) {
  try {
    const userSiteAccessCacheKey = _generateUserSiteMapCacheKey(userId);
    const cachedData = await cacheService.get(userSiteAccessCacheKey);
    return cachedData ? JSON.parse(cachedData) : null;
  } catch (error) {
    sails.log.error(`Failed to get user site map cache for userId: ${userId}`);
    sails.log.error(error);
    return null;
  }
}

async function cacheUserSiteMapIds(userId, userSiteIds) {
  try {
    if (!userId) return;
    const userSiteAccessCacheKey = _generateUserSiteMapCacheKey(userId);
    await cacheService.set(userSiteAccessCacheKey, JSON.stringify(userSiteIds));
    await cacheService.expire(userSiteAccessCacheKey, ttlSeconds);
  } catch (error) {
    console.error(`Failed to set user site map cache for userId: ${userId}`, error);
  }
}

async function invalidateUserSiteIds(userId) {
  try {
    if (!userId) return;
    const userSiteAccessCacheKey = _generateUserSiteMapCacheKey(userId);
    await cacheService.delete(userSiteAccessCacheKey);
  } catch (error) {
    console.error(`Failed to invalidate cache for userId: ${userId}`, error);
  }
}

function _generateUserSiteMapCacheKey(userId) {
  return `userSiteMapCacheKey:${userId}`;
}

function _getUserPrefCacheKey(userId, siteId) {
  return `site:${siteId}:user:preferences:${userId}`;
}

async function getUserPrefCache(userId, siteId) {
  if (!userId || !siteId) return null;
  const cacheKey = _getUserPrefCacheKey(userId, siteId);
  const userPrefCache = await cacheService.get(cacheKey);
  if (_.isEmpty(userPrefCache)) return null;
  return JSON.parse(userPrefCache);
}

async function setUserPrefCache(userId, siteId, userPref) {
  if (!userId || !siteId || _.isEmpty(userPref)) return;
  const cacheKey = _getUserPrefCacheKey(userId, siteId);
  await cacheService.set(cacheKey, JSON.stringify(userPref));
  await cacheService.expire(cacheKey, ttlSeconds);
}

async function invalidateUserPrefCache(userId, siteId) {
  if (!userId || !siteId) return;
  const cacheKey = _getUserPrefCacheKey(userId, siteId);
  await cacheService.delete(cacheKey);
}
