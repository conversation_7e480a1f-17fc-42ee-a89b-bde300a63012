const userSiteMapService = require("./userSiteMap.service");

module.exports = {
  findOne: userSiteMapService.findOne,
  find: userSiteMapService.find,
  create: userSiteMapService.create,
  update: userSiteMapService.update,
  delete: userSiteMapService.delete,
  fetchEmList: userSiteMapService.fetchEmList,
  updateEmList: userSiteMapService.updateEmList,
  checkSiteIdInUserSiteCache: userSiteMapService.checkSiteIdInUserSiteCache,
  addSiteIdInUserSiteCache: userSiteMapService.addSiteIdInUserSiteCache,
  removeSiteIdInUserSiteCache: userSiteMapService.removeSiteIdInUserSiteCache,
  getUserSiteIds: userSiteMapService.getUserSiteIds,
  fetchUserSitePreferences: userSiteMapService.fetchUserSitePreferences,
  invalidateUserSiteIds: userSiteMapService.invalidateUserSiteIds,
};
