/* eslint-disable no-undef */
module.exports = {

  /**
   * Action module private functions
   */
  create: async (params) => {
    return Actions.create(params);
  },
  find: async (searchParams) => {
    // Action.find
    return Actions.find(searchParams);
  },
  findOne: async (searchParams) => {
    // Action.findone
    let actions = await Actions.find(searchParams).limit(1);
    return actions[0];
  },
  update: async (searchParams, updateValue) =>{
    return Actions.update(searchParams, updateValue);
  },
  delete: async (searchParams) => {
    return Actions.destroy(searchParams);
  },
};
