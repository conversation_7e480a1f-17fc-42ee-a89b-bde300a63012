const globalHelpers = require('../../utils/globalhelper');
const moment = require('moment');
const Joi = require('joi');
module.exports = {
  create: async (params) => {
    const schema = Joi.object().keys({
      siteId:Joi.string().required(),
      product_name: Joi.string().required(),
      unit: Joi.string().required(),
      created_by: Joi.string().required()
    }).unknown()
    const { error } = schema.validate(params);
    if(error) throw new Error(`INVALID_INPUT`);

    const _productId = `Product-${params.product_name}`;
    const existingProduct = await ShiftProductionData.findOne({
      pk:params.siteId,
      sk:_productId
    })
    if(existingProduct){
      return null;
    }
    const payload = {
      pk:params.siteId,
      sk:_productId,
      product_name: params.product_name,
      unit: params.unit,
      createdAt: moment().format('YYYY-DD-MMTHH:mm:ssZ'),
      createdBy:params.created_by
    }
    // noinspection JSUnresolvedVariable
    const result = await ShiftProductionData.create(payload);
    return {
      ...params,
      productId:_productId
    };
  },
  find: async (searchParams) => {
    //TODO: implementation pendind
  },
  findOne: async (searchParams) => {
    //TODO: implementation pendind
  },
  update: async (searchParams, updateValue) =>{
    //TODO: implementation pendind
  },
  delete: async (param) => {
    //TODO: implementation pendind
  },
};
