const ProductService = require('./product.private')
const AWS = require('aws-sdk');
module.exports = {
  create:ProductService.create,
  getProductBySite:async (siteId)=>{
    const documentClient = new AWS.DynamoDB.DocumentClient({ });
    const params = {
      TableName:"ShiftProductionData",
      KeyConditionExpression:"#pk = :siteId and begins_with (#sk, :pattern)",
      ExpressionAttributeNames: {
        "#pk":"pk",
        "#sk":"sk"
      },
      ExpressionAttributeValues: {
        ":siteId":siteId,
        ":pattern": "Product-",
      }
    };
    const result = await documentClient.query(params).promise()
    return result.Items
  },

}
