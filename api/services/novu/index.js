const { Novu } = require('@novu/node');
const novu = new Novu(sails.config.NOVU_CONFIG.SECRET_KEY);

async function removeSubscriber(subscriberId) {
    try {
        const response = await novu.subscribers.delete(subscriberId);
        return response;
    } catch(e) {
        sails.log('[fn:removeSubscriber] Remove User Novo Subscription Error: ' + e.message);
    }
}

async function isSubscriberExistInNovu(subscriberId) {
    try {
        await novu.subscribers.get(subscriberId);
        return true
    } catch(e) {
        return false;
    }
}

module.exports = {
    removeSubscriber,
    isSubscriberExistInNovu,
}