const IotCoreCommunicationService = require("./IotCoreCommunication");
const JouleTrackMqttService = require("./JouleTrackMqttService");
const AWSIoTCoreService = require("./AWSIoTCoreService");

module.exports = {
  async sendDataToIoTCoreViaJouletrackMqtt(topic, data) {
    const iotCoreComInstance = new IotCoreCommunicationService(new JouleTrackMqttService());
    return iotCoreComInstance.sendDataToIotCore(topic, data);
  },
  async publish(topic, data) {
    const iotCoreComInstance = new IotCoreCommunicationService(new AWSIoTCoreService());
    return iotCoreComInstance.sendDataToIotCore(topic, data);
  },
};
