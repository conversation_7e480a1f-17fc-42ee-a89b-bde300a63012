const AWS = require('aws-sdk');
const fs = require('fs');
const https = require('https');
const Sentry = require('../logTransport/sentry.service');
const flaverr = require('flaverr');

class AWSIoTCoreService {
  constructor() {
    this.certPath = sails.config.IOT_CONFIG.certPath;
    this.iotClient = new AWS.IotData({
      endpoint: sails.config.IOT_CONFIG.host,
      apiVersion: '2015-05-28',
      region: sails.config.IOT_CONFIG.region,
      httpOptions: {
        agent: new https.Agent({ ca: fs.readFileSync(this.certPath) }),
      },
    });
  }

  async sendDataToIotCore(topic, data) {
    if (_.isEmpty(topic)) Error.throwsExceptionTopicIsRequired();
    if (_.isEmpty(data)) Error.throwsExceptionPacketIsRequired();

    const maxRetries = 6;
    const delayBetweenRetries = 500;
    let attempt = 0;

    const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

    while (attempt < maxRetries) {
      try {
        const payload =
          Object.getPrototypeOf(data) === Object.prototype || Array.isArray(data)
            ? JSON.stringify(data)
            : String(data);

        const publish = await this.iotClient.publish({
          topic,
          payload
        })
          .promise();
        sails.log.info(`Successfully Published to AWS IOT Core Topic=${topic} and Data=${payload}`);
        return publish;
      } catch (error) {
        attempt++;
        sails.log.error(`[AWS-IOT-CORE-SERVICE] Attempt ${attempt} failed:`, error);
        sails.log.error(error);
        if (attempt === maxRetries || (error.code !== 'ThrottlingException' || error.statusCode !== 429)) {
          Sentry.setTag('topic', topic);
          Sentry.setContext('Error Object', {
            data: JSON.stringify(data),
            error: JSON.stringify(error),
          });
          Sentry.captureException(error);
        }

        switch (error.statusCode || error.code) {
          case 400:
            sails.log.error('[AWS-IOT-CORE-SERVICE] Bad request while publishing to IoT Core.');
            throw flaverr('[AWS-IOT-CORE-SERVICE-BAD_REQUEST]', new Error('The request was invalid. Please check your input.'));

          case 401:
            sails.log.error('[AWS-IOT-CORE-SERVICE] Unauthorized access while publishing to IoT Core.');
            throw flaverr('[AWS-IOT-CORE-SERVICE-UNAUTHORIZED]', new Error('You are not authorized to perform this operation.'));

          case 403:
            sails.log.error('[AWS-IOT-CORE-SERVICE] Forbidden error while publishing to IoT Core.');
            throw flaverr('[AWS-IOT-CORE-SERVICE-FORBIDDEN]', new Error('Access to the requested resource is forbidden.'));

          case 404:
            sails.log.error('[AWS-IOT-CORE-SERVICE] Resource not found while publishing to IoT Core.');
            throw flaverr('[AWS-IOT-CORE-SERVICE-NOT_FOUND]', new Error('The requested resource was not found.'));

          case 409:
            sails.log.error('[AWS-IOT-CORE-SERVICE] Conflict error while publishing to IoT Core.');
            throw flaverr('[AWS-IOT-CORE-SERVICE-CONFLICT]', new Error('A conflict occurred while processing the request.'));

          case 413:
            sails.log.error('[AWS-IOT-CORE-SERVICE] Request too large error while publishing to IoT Core.');
            throw flaverr('[AWS-IOT-CORE-SERVICE-REQUEST_TOO_LARGE]', new Error('The request payload is too large.'));

          case 422:
            sails.log.error('[AWS-IOT-CORE-SERVICE] Failed to process request while publishing to IoT Core.');
            throw flaverr('[AWS-IOT-CORE-SERVICE-UNPROCESSABLE_ENTITY]', new Error('The request could not be processed.'));

          case 'ThrottlingException':
            if (attempt < maxRetries) {
              sails.log.info(`[AWS-IOT-CORE-SERVICE] ThrottlingException detected. Retrying after ${delayBetweenRetries}ms...`);
              await delay(attempt * delayBetweenRetries);
              continue;
            } else {
              sails.log.error('[AWS-IOT-CORE-SERVICE] Max retries reached for ThrottlingException.');
              throw flaverr('[AWS-IOT-CORE-SERVICE-THROTTLING]', new Error('Request throttled due to rate limiting after maximum retries.'));
            }

          case 429:
            if (attempt < maxRetries) {
              sails.log.info(`Retrying after ${delayBetweenRetries}ms due to Too Many Requests (429)...`);
              await delay(attempt * delayBetweenRetries);
              continue;
            } else {
              sails.log.error('[AWS-IOT-CORE-SERVICE] Max retries reached for Too Many Requests (429).');
              throw flaverr('[AWS-IOT-CORE-SERVICE-TOO_MANY_REQUESTS]', new Error('Request throttled after maximum retries.'));
            }

          case 500:
            sails.log.error('[AWS-IOT-CORE-SERVICE] Internal server error while publishing to IoT Core.');
            throw flaverr('[AWS-IOT-CORE-SERVICE-INTERNAL_SERVER_ERROR]', new Error('An internal server error occurred.'));

          case 503:
            sails.log.error('[AWS-IOT-CORE-SERVICE] Service unavailable while publishing to IoT Core.');
            throw flaverr('[AWS-IOT-CORE-SERVICE-SERVICE_UNAVAILABLE]', new Error('The service is currently unavailable. Please try again later.'));

          default:
            sails.log.error('[AWS-IOT-CORE-SERVICE] Unknown error occurred while publishing to IoT Core.');
            throw flaverr('[AWS-IOT-CORE-SERVICE]', new Error(error));
        }
      }
    }
  }

}

module.exports = AWSIoTCoreService;
