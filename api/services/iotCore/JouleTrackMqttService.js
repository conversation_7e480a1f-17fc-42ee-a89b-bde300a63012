const Axios = require('axios');
const Error = require('../../utils/iotCore/Errors');

class JouleTrackMqttService {
  constructor(config) {
    // eslint-disable-next-line no-mixed-operators
    this.MQTT_HOST = config && config.MQTT_HOST || process.env.MQTT_HOST;
  }

  async sendDataDataIotCore(topic, data) {
    if (_.isEmpty(topic)) Error.throwsExceptionTopicIsRequired();
    if (_.isEmpty(data)) Error.throwsExceptionPacketIsRequired();
    try {
      const {
        err,
        status
      } = await Axios.post(`${this.MQTT_HOST}/v1/event/publish`, {
        topic: topic,
        msg: JSON.stringify(data)
      });
      if (status != 200) {
        Error.throwsExceptionErrorCodeReceivedFromJouleTrackMqttService(status, err);
      }
    } catch (e) {
      if (e.code === "ECONNREFUSED") {
        Error.throwsExceptionUnableToConnectToService(e.message);
      }
      Error.throwsExceptionUnhandledJouletrackMqttCall()
    }
  }
}

module.exports = JouleTrackMqttService;
