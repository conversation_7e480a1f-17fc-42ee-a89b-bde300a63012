const redis = require("redis");
const EventEmitter = require("events");

// noinspection JSUnresolvedReference
/**
 * @class
 * @extends EventEmitter
 */
class RedisEventHandler extends EventEmitter {
  static channels = ["__keyevent@0__:expired"];

  constructor() {
    super();
    this.redisClient = redis.createClient(sails.config.cachePort, sails.config.cacheHost, {
      retry_strategy: function (options) {
        /**Log and reconnect after a delay*/
        sails.log.error(
          `[Redis-Notifier]Reconnecting to Redis,message="${options?.error?.message}" and errorCode="${options?.error?.code}"`
        );
        /**Retry after 30 seconds*/
        return 1000 * 30;
      },
    });
    this.redisClient.on("ready", function () {
      sails.log.info("[Redis-Notifier] Redis connection established successfully");
    });

    // Event listener for errors
    this.redisClient.on("error", function (err) {
      sails.log.error("[Redis-Notifier] Redis error:", err);
    });
    RedisEventHandler.channels.forEach((channel) => {
      this.redisClient.psubscribe(channel);
      sails.log(`[Redis-Notifier] Subscribed ${channel} in redis KEYSPACE Notification`);
    });
    this.redisClient.on("pmessage", (pattern, channel, expiredKey) => {
      this.emit("message", pattern, channel, expiredKey);
    });
  }

  static getInstance() {
    if (!RedisEventHandler.instance) {
      RedisEventHandler.instance = new RedisEventHandler();
    }
    return RedisEventHandler.instance;
  }
}

module.exports = RedisEventHandler.getInstance();
