module.exports = {
  create: async (params) => {
    return ConfiguratorSystemCategory.create(params);
    ;
  },
  find: async (searchParams) => {
    return ConfiguratorSystemCategory.find(searchParams);
  },
  update: async (searchParams, updateValue) => {
    return ConfiguratorSystemCategory.update(searchParams, updateValue);
  },
  findOne: async (searchParams) => {
    return ConfiguratorSystemCategory.findOne(searchParams);
  },
  delete: async (searchParams) => {
    return ConfiguratorSystemCategory.destroy(searchParams);
  },
};
