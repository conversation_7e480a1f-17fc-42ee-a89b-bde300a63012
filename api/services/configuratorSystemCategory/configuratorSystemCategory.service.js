const flaverr = require("flaverr");
const systemCategoryService = require("./configuratorSystemCategory.private");
module.exports = {
  create: systemCategoryService.create,
  find: systemCategoryService.find,
  update: systemCategoryService.update,
  delete: systemCategoryService.delete,
  findOne: systemCategoryService.findOne,
  registerNewSystemCategory: async function (name, userId) {
    const _SystemCategory = await ConfiguratorSystemCategory.create({
      name: name,
      created_by: userId,
    }).fetch();
    return _SystemCategory;
  },
  fetchSystemCategories: async function () {
    const systemCategories = await ConfiguratorSystemCategory.find({
      status: 1,
    }).sort("name ASC");
    return systemCategories;
  },
  isValidSystemCategory: async function (systemCategoryId) {
    const system = await this.findOne({
      id: systemCategoryId,
      status: 1,
    });
    if (!system) {
      return false;
    }
    return true;
  },
  isSystemCategoryExist: async function (systemId) {
    const system = await this.findOne({
      id: systemId,
      status: { "!=": 0 },
    });
    if (!system) {
      return false;
    }
    return true;
  },
  deleteSystemCategory: async function (systemCategoryId, userId) {
    if (!(await this.isSystemCategoryExist(systemCategoryId))) {
      throw flaverr("E_SYSTEM_CATEGORY_NOT_FOUND", new Error("System category not found"));
    }

    if (await this.checkSystemCategoryHasAnySystem(systemCategoryId)) {
      throw flaverr(
        "E_SYSTEM_CATEGORY_DELETION_CONDITION",
        new Error(
          "This system category contains atleast one system please delete system first please delete system first then delete system Category",
        ),
      );
    }

    return this.update(
      {
        id: systemCategoryId,
        last_updated_by: userId,
      },
      {
        status: 0,
      },
    );
  },
  checkSystemCategoryHasAnySystem: async function (systemCategoryId) {
    let query = `
      SELECT COUNT(*)
      FROM configurator_system_category AS csc
      RIGHT JOIN configurator_systems AS cs ON cs.system_id = csc.id
      WHERE csc.id = $1 AND cs.status != 0
    `;
    let systemCategoryDetailRaw = await sails
      .getDatastore("postgres")
      .sendNativeQuery(query, [systemCategoryId]);
    return systemCategoryDetailRaw.rows[0].count > 0;
  },
};
