const kafka = require('./kafka.connection.service');

class KafkaProducerService {
  constructor() {
    this.producer = sails.config?.kafka?.kafkaEnabled ? kafka.producer() : null;
  }

  static getInstance() {
    if (!KafkaProducerService.instance) {
      sails.log.info('[KafkaProducerService] Instance created successfully.');
      KafkaProducerService.instance = new KafkaProducerService();
    }
    return KafkaProducerService.instance;
  }

  async initialize() {
    if (!this.producer) {
      sails.log.info('[KafkaProducerService] Kafka is disabled. Skipping initialization.');
      return;
    }
    try {
      await this.producer.connect();
      sails.log.info('[KafkaProducerService] Producer connected successfully.');
    } catch (error) {
      sails.log.error('[KafkaProducerService] Failed to connect producer:', error);
    }
  }

  async send(topic, messages) {
    try {
      const result = await this.producer.send({
        topic,
        messages
      });
      sails.log.info(`[KafkaProducerService] Messages sent to topic: ${topic}`);
      return result;
    } catch (error) {
      sails.log.error(`[KafkaProducerService] Failed to send messages to topic: ${topic}`, error);
      throw error;
    }
  }

  async shutdown() {
    if (!this.producer) {
      sails.log.info('[KafkaProducerService] Kafka is disabled. No producer attached');
      return;
    }
    try {
      await this.producer.disconnect();
      sails.log.info('[KafkaProducerService] Producer disconnected successfully.');
    } catch (error) {
      sails.log.error('[KafkaProducerService] Failed to disconnect producer:', error);
    }
  }
}

const kafkaProducerService = KafkaProducerService.getInstance();
Object.freeze(kafkaProducerService);
module.exports = kafkaProducerService;
