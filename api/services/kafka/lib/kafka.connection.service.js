const { Kafka } = require("kafkajs");
const { v4: uuidv4 } = require("uuid");

const KafkaConfig = {
  clientId: `${sails.config.kafka.clientId}-${uuidv4()}`,
  brokers: sails.config.kafka.brokers,
  ssl: sails.config.kafka.securityProtocol === "SASL_SSL",
  sasl: sails.config.kafka.username
    ? {
        mechanism: sails.config.kafka.securityProtocol === "SASL_SSL" ? "scram-sha-512" : "plain",
        username: sails.config.kafka.username,
        password: sails.config.kafka.password,
      }
    : undefined,
  connectionTimeout: sails.config.kafka.connectionTimeout,
};

const kafka = new Kafka(KafkaConfig);

module.exports = kafka;
