const kafka = require('./kafka.connection.service');

class KafkaConsumerService {
  constructor(groupId = sails.config.kafka.groupId) {
    this.consumer = sails.config?.kafka?.kafkaEnabled ? kafka.consumer({ groupId }) : null;
  }

  async connect() {
    if (!this.consumer) {
      sails.log.info('[KafkaConsumerService] Kafka is disabled. Skipping connection.');
      return;
    }
    try {
      await this.consumer.connect();
      sails.log.info('[KafkaConsumerService] Consumer connected.');
    } catch (error) {
      sails.log.error(`[KafkaConsumerService] Error: ${JSON.stringify(error)}`);
    }
  }

  async subscribe(topic) {
    try {
      await this.consumer.subscribe({
        topic,
        fromBeginning: true
      });
      sails.log.info(`[KafkaConsumerService] Subscribed to topic: ${topic}`);
    } catch (error) {
      sails.log.error(`[KafkaConsumerService] Subscription error: ${error}`);
    }
  }

  async run(onMessage) {
    try {
      await this.consumer.run({
        eachMessage: async (payload) => {
          try {
            await onMessage(payload);
          } catch (error) {
            sails.log.error(
              '[KafkaConsumerService] Error processing message: ' + JSON.stringify(error)
            );
          }
        },
      });
    } catch (error) {
      sails.log.error(`[KafkaConsumerService] Run error: ${error}`);
    }
  }

  async disconnect() {
    if (!this.consumer) {
      sails.log.info('[KafkaConsumerService] Kafka is disabled. No consumer attached');
      return;
    }
    try {
      await this.consumer.disconnect();
      sails.log.info('[KafkaConsumerService] Consumer disconnected.');
    } catch (error) {
      sails.log.error(`[KafkaConsumerService] Disconnection error: ${error}`);
    }
  }
}

module.exports = KafkaConsumerService;
