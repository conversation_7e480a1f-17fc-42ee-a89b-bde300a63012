const errorHandler = require('../../utils/configuratorGraph/graphErrorHandler');
const graphUtils = require('../../utils/configuratorGraph/graphUtils');
const SankeyGraph = require('./SankeyGraph');

class ConfiguratorGraphFactory {
  constructor({
    pageId,
    graphId,
    type,
    graphProperty,
    siteId,
    userId
  }) {
    this.newGraphData = graphProperty || {};
    this.pageId = pageId;
    this.graphPk = graphId;
    this.instance = null;
    this.siteId = siteId || null;
    this.userId = userId
    switch (graphUtils.graphTypeMap[type]) {
      case 'Sankey':
        this.instance = new SankeyGraph({
          graphId: this.graphPk,
          pageId: this.pageId,
          siteId: siteId,
          userId: userId
        });
        break;
      default:
        return errorHandler.invalidGraphType();
    }
  }

  async validateGraphProperty() {
    return await this.instance.validateGraphProperty(this.newGraphData);
  }

  setSiteId(siteId) {
    this.instance.siteId = this.siteId = siteId;
  }
  setUserId(userId) {
    this.instance.userId = userId;
  }

  async save() {
    this.instance.buildGraphProperty({
      pageId: this.pageId,
      siteId: this.siteId,
      graphPk: this.graphPk,
      graphProperty: this.newGraphData
    });
    const graphProperty = await this.instance.save();
    return graphProperty;

  }

  async fetch() {
    return await this.instance.fetch(this.graphPk);
  }
}

module.exports = ConfiguratorGraphFactory;
