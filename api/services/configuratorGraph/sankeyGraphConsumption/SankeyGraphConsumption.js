const dynamokeystoreService = require('../../dynamokeystore/dynamokeystore.service');
const ElectricalPower = require('../../energyConsumption/src/ElectricalPower');
const errorHandler = require('../../../utils/configuratorGraph/graphErrorHandler');

class SankeyGraphConsumptionFactory {
  constructor(type) {
    this.type = type;
    switch (type) {
      case 'Electrical Consumption': {
        this.instance = ElectricalPower;
        break;
      }
      default: {
        throw new Error('Invalid sankey graph type.');
      }
    }
  }

  static async getGraphTypeSankeyConfig(type) {
    const flowParamsRecord = await dynamokeystoreService.findOne('sankey_flow_params_config');
    const flowParamsConfig = JSON.parse(flowParamsRecord?.value) || {};
    for (const node of flowParamsConfig) {
      const label = node.label;
      if (label === type) return node.deviceDriverDetails;
    }
    return null;
  }

  /**
   * @example [{
   deviceId,
   deviceClass,
   startTime,
   endTime,
   siteId,
   driverType,
   deviceType
   }]
   * @param {Array{Object}} nodes
   */
  async fetchConsumption(nodes) {
    const sankeyConfig = await SankeyGraphConsumptionFactory.getGraphTypeSankeyConfig(this.type);
    if (!sankeyConfig) errorHandler.throwExceptionInvalidSankeyConfig();
    const deviceDriverConfigParams = _buildSankeyConfigMap(sankeyConfig);
    const nodeWithAbbr = _fetchNodeWithAbbr(nodes, deviceDriverConfigParams);
    //TBD Validate if there exist empty parameter
    return this.instance.fetchConsumption(nodeWithAbbr);

    function _buildSankeyConfigMap(sankeyConfig) {
      return sankeyConfig.reduce((acc, curr) => {
        const {
          driverType,
          deviceType,
          abbr,
          class: className
        } = curr;
        const key = `${className}_${deviceType}_${driverType}`;
        if (acc.hasOwnProperty(key)) {
          acc[key].push(abbr);
        } else {
          acc[key] = [abbr];
        }
        return acc;
      }, {});
    }

    function _fetchNodeWithAbbr(nodes, deviceDriverConfigParams) {
      return nodes.map((node) => {
        const {
          driverType,
          deviceType,
          deviceClass
        } = node;
        node.abbr = deviceDriverConfigParams[`${deviceClass}_${deviceType}_${driverType}`] || ['NA'];
        delete node.driverType;
        delete node.deviceType;
        delete node.class;
        return node;
      });
    }

  }

}

module.exports = SankeyGraphConsumptionFactory
