const errorHandler = require('../../utils/configuratorGraph/graphErrorHandler');
const utils = require('../../utils/configuratorGraph/graphRequestValidator');
const componentService = require('../component/component.public');
const deviceService = require('../device/device.service');
const dynamoKeystoreService = require('../dynamokeystore/dynamokeystore.public');
const SankeyGraphConsumptionFactory = require('./sankeyGraphConsumption/SankeyGraphConsumption');
const moment = require('moment');

class SankeyGraph {
  constructor(obj) {
    this.nodes = obj.nodes || null;
    this.links = obj.links || null;
    this.graphId = obj.graphId || null;
    this.pageId = obj.pageId || null;
    this.siteId = obj.siteId || null;
    this.parameterLabel = obj.parameterLabel || null;
    this.userId = obj.userId;
  }

  async validateGraphProperty(graphProperty) {
    utils.validateSankeyGraph(graphProperty);
    const isValidLabel = await this._validateGraphLabel(graphProperty.parameterLabel);
    if (!isValidLabel) return errorHandler.throwExceptionInvalidLabel(graphProperty.parameterLabel);
    const invalidDevices = await this._getInvalidNodes(graphProperty.nodes);
    if (invalidDevices.length) return errorHandler.throwExceptionInvalidDevices(invalidDevices);
  }

  async _validateGraphLabel(parameterLabel) {
    const flowParamsRecord = await dynamoKeystoreService.findOne('sankey_flow_params_config');
    const flowParamsConfig = JSON.parse(flowParamsRecord?.value) || {};
    return flowParamsConfig.filter((flow) => flow.label === parameterLabel).length;
  }

  async buildGraphProperty({
    pageId,
    siteId,
    graphPk,
    graphProperty
  }) {
    this.pageId = pageId;
    this.nodes = graphProperty.nodes;
    this.links = graphProperty.data;
    this.graphId = graphPk;
    this.site = siteId;
    this.parameterLabel = graphProperty.parameterLabel;
  }

  async _getInvalidNodes(deviceIds) {
    const invalidDevices = [];
    const $getInvalidDevices = deviceIds.map(({
      deviceId,
      deviceClass,
      deviceType
    }) => {
      if (deviceClass == 'device') {
        return deviceService.isDeviceExist(
          deviceId,
          this.siteId,
          deviceType
        );
      } else if (deviceClass == 'component') {
        return componentService.isComponentExistByDriverType(
          deviceId,
          this.siteId,
          deviceType,
        );
      }
    })
      .filter(Boolean);

    const getInvalidDevices = await Promise.all($getInvalidDevices);

    getInvalidDevices.forEach((isDeviceExist, index) => {
      if (!isDeviceExist) {
        const { deviceId } = deviceIds[index];
        invalidDevices.push(deviceId);
      }
    });
    return invalidDevices;

  }

  async save() {
    return sails.getDatastore('postgres')
      .transaction(async (dbTransactionObj) => {
        await ConfiguratorGraphs.updateOne({
          id: this.graphId,
        }, {
          graphProperty: { parameterLabel: this.parameterLabel, lastUpdatedBy:this.userId }
        })
          .usingConnection(dbTransactionObj);

        await SankeyGraphNodes.update({
          graphRefId: this.graphId,
          status: 1
        }, {
          status: 0,
          lastUpdatedBy: this.userId
        })
          .usingConnection(dbTransactionObj);

        await ConfiguratorGraphsLinks.update({
          graphRefId: this.graphId,
          status: 1
        }, {
          status: 0,
          lastUpdatedBy: this.userId
        })
          .usingConnection(dbTransactionObj);

        const batchNodesRecords = this.nodes.map((node) => {
          const {
            deviceId,
            deviceClass,
            deviceType,
            driverType,
            offset,
            color,
            column
          } = node;
          const createNodeObj = {
            deviceId,
            deviceClass,
            driverType,
            deviceType,
            graphRefId: this.graphId,
            level: column || 1,
            createdBy: this.userId,
          };
          if (offset) createNodeObj.offset = offset;
          // if (color) createNodeObj.color = color;
          return createNodeObj;
        });
        const graphNodes = await SankeyGraphNodes.createEach(batchNodesRecords)
          .fetch()
          .usingConnection(dbTransactionObj);

        const graphNodePkMap = new Map();
        graphNodes.forEach((node) => {
          const {
            deviceId,
            id
          } = node;
          graphNodePkMap.set(deviceId, Number(id));
        });

        const nodeLinksRecords = this.links.map((link, order) => {
          const [sourceDeviceId, targetDeviceId] = link;
          return {
            sourceId: Number(graphNodePkMap.get(sourceDeviceId.toString())),
            targetId: graphNodePkMap.get(targetDeviceId.toString()) || null,
            order: order + 1,
            graphRefId: this.graphId,
            createdBy: this.userId
          };
        });
        await ConfiguratorGraphsLinks.createEach(nodeLinksRecords)
          .usingConnection(dbTransactionObj);
      });
  }

  async fetch(graphId) {
    const query = `select
	    cg.id as id,
	    cg.name as name,
	    cg.type as type,
	    cg.graph_property as "graphProperty",
      sgn.id as "nodeId",
      sgn.device_id as "deviceId",
      sgn.device_class as "deviceClass",
      sgn.device_type as "deviceType",
      sgn.driver_type as "driverType",
      sgn.level as column,
      sgn.color as color,
      sgn.offset as offset,
      sgl.source_id as "sourceId",
      sgl.target_id as "targetId",
      sgl.order as order,
      ssp.is_published as status
    from sub_system_pages ssp
    join configurator_graphs cg on ssp.id = cg.sub_system_page_id
    left join sankey_graph_nodes sgn on cg.id = sgn.graph_ref_id and sgn.status = 1
    left join sankey_graph_links sgl on cg.id = sgl.graph_ref_id and sgl.status = 1
    	where ssp.id = $1
      and ssp.status = 1
      and cg.id = $2
      and cg.status = 1
    `;
    const [
      result,
      graphNodes
    ] = await Promise.all([
      sails.getDatastore('postgres')
        .sendNativeQuery(query, [this.pageId, graphId]),
      SankeyGraphNodes.find({
        graphRefId: graphId,
        status: 1
      })
    ]);
    let allDevicesConsumptionRecords = {};
    if (graphNodes.length && result.rows[0].status === 1) {
      const  graphParameterLabel = result.rows[0].graphProperty.parameterLabel;
      const sankeyGraphConsumption = new SankeyGraphConsumptionFactory(graphParameterLabel);
      const timezoneOffset = await sails.helpers.getSiteTimezone.with({siteId: this.siteId, timezoneFormat:'Z'})        
      allDevicesConsumptionRecords = await sankeyGraphConsumption.fetchConsumption(_buildGraphNodeForConsumption(this.siteId, result.rows, timezoneOffset));
    }
    return this._buildGraphDetail(result.rows, allDevicesConsumptionRecords);

    function _buildGraphNodeForConsumption(siteId, node, timezoneOffset) { 
      const uniqueDevices = {};      
      node.forEach((row) => {
          const {
            deviceId,
            deviceClass,
            driverType,
            deviceType,
          } = row;
          if (uniqueDevices.hasOwnProperty(deviceId)) {
            return;
          }
          uniqueDevices[deviceId] = {
            deviceId,
            deviceClass,
            driverType,
            deviceType,
            startTime: moment()
              .subtract(30, 'days')
              .utcOffset(timezoneOffset)
              .format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
            endTime: moment()
              .utcOffset(timezoneOffset)
              .format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
            siteId
          };
        }
      );

     return Object.values(uniqueDevices);
    }
  }

  async _buildGraphDetail(rows, allDevicesConsumptionRecords) {
    const nodes = new Map();
    const links = new Map();
    const firstLevelNodeConsumption = new Map();
    let graphProperty = {};
    let type = '';
    let name = '';
    let status = 0;
    let id = 0;
    if (rows.length === 0) {
      return {
        nodes: [],
        data: [],
        graphProperty,
        type,
        name,
        status,
        id
      };
    }
    graphProperty = rows[0].graphProperty || {};
    type = rows[0].type;
    name = rows[0].name;
    status = rows[0].status;
    id = rows[0].id;
    const linkDeviceIdMap = rows.reduce((acm, curr) => {
      if (curr.nodeId) {
        acm[curr.nodeId] = {
          deviceId: curr.deviceId
        };
      }
      return acm;
    }, {});

    for (const row of rows) {
      if (row.deviceId && !nodes.has(row.deviceId)) {
        nodes.set(row.deviceId, {
          deviceId: row.deviceId,
          deviceClass: row.deviceClass,
          deviceType: row.deviceType,
          driverType: row.driverType,
          column: row.column,
          color: row.color,
          offset: row.offset
        });
      }
      if (row.column == 1 && !firstLevelNodeConsumption.has(row.deviceId)) {
        firstLevelNodeConsumption.set(row.deviceId, [row.deviceId, '', allDevicesConsumptionRecords[row.deviceId]]);
      }
      if (row.sourceId && row.targetId) {
        const src = linkDeviceIdMap[row.sourceId];
        const tar = linkDeviceIdMap[row.targetId];

        if (src && tar) {
          const linkKey = `${src.deviceId}-${tar.deviceId}`;
          if (!links.has(linkKey)) {
            links.set(linkKey, {
              sourceId: src.deviceId,
              targetId: tar.deviceId,
              order: row.order
            });
          }
        }
      }
    }

    const linksData = Array.from(links.values())
      .sort((a, b) => a.order - b.order)
      .map(link => [
        link.sourceId,
        link.targetId,
        allDevicesConsumptionRecords[link.targetId]
      ]);
    const data = [
      ...Array.from(firstLevelNodeConsumption.values()),
      ...linksData
    ];
    return {
      nodes: Array.from(nodes.values())
        .sort((a, b) => a.column - b.column),
      data,
      graphProperty,
      type,
      name,
      status,
      id
    };
  }

}

module.exports = SankeyGraph;
