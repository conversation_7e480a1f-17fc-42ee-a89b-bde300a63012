const versionService = require("./version.private");
const utils = require("../../utils/site/utils");
// const globalHelpers = require("../../utils/globalhelper");
// const selfutils = require("../../utils/site/add-new-site.util");

// const site service variable

module.exports = {
  /**
   *
   * @param {*} searchParams
   * @returns
   */
  async findOne(searchParams) {
    try {
      const versionDetails = await versionService.findOne(searchParams);
      if (!versionDetails) return;
      return versionDetails;
    } catch (e) {
      throw new Error(e);
    }
  },
};
