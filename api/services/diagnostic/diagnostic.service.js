const eventService = require("../event/event.public");
const globalHelpers = require("../../utils/globalhelper");
const deviceService = require("../device/device.public");
const versionService = require("./version.public");
const diagnosticUtils = require("../../utils/diagnostic/utils");
const rabbitMQService = require("../rabbitmq/rabbitmq.public");
const hardwareInventoryService = require("./hardwareInventory.public");

const DIAGNOSTIC_QUEUE = new rabbitMQService.consumerWithAckQueue(
  "DIAGNOSTIC_QUEUE"
);

exports = {
  /**
   *
   * @param {String} data
   * @todo fetch version from device -> fetch version from version table
   * based on device's version -> publish version details to IOT core via
   * mqtt service
   */
  // eslint-disable-next-line consistent-return
  async eventHandler(data) {
    try {
      let { topic, message } = globalHelpers.toJson(data.content.toString());
      if (message === undefined) {
        sails.log.error(
          "Diagnostic.service::eventHandler message cannot be undefined: ",
          topic,
          message
        );
        return false;
      }

      switch (message.operation) {
        case "versioncheck":
          (() => {
            const deviceId = topic.split("/")[2];
            versionControlOperation(deviceId, "versioncheck");
          })();
          break;
        case "hardwareProvisioning":
          hardwareInventoryService.hardwareProvisioningRouter(topic, message);
          break;
        default:
          (() => {
            sails.log.error(
              "Diagnostic.service::eventHandler illegal operation: ",
              topic,
              message
            );
            return false;
          })();
      }

      return true;
    } catch (err) {
      sails.log.error("Diagnostic.service::fetchVersionsUsingDeviceId ", err);
      return false;
    }
  },
  DIAGNOSTIC_QUEUE,
};

async function versionControlOperation(deviceId, operation) {
  const device = await deviceService.findOne({ deviceId });
  if (!device) {
    sails.log.error(
      "fetchVersionsUsingDeviceId:diagnostic.service device cannot be undefined:",
      device
    );
    return false;
  }

  const query = {
    deviceType: "controller",
    version: device.currentVersion,
  };

  let versionDetails = await versionService.findOne(query);
  if (!versionDetails) {
    sails.log.error(
      "fetchVersionsUsingDeviceId:diagnostic.service version cannot be undefined:",
      device
    );
    return false;
  }

  versionDetails = diagnosticUtils.formatVersions(versionDetails, operation);
  eventService.publish(
    diagnosticUtils.preparePublishTopic(deviceId),
    JSON.stringify(versionDetails)
  );
}

module.exports = exports;
