
/* eslint-disable no-undef */
const hardwareInventory = require('./hardwareInventory.private');
const hardwareInventoryUtils = require("../../utils/diagnostic/hardwareInventoryUtils");
const eventService = require("../event/event.public");
const moment = require("moment");

module.exports = {
  create: hardwareInventory.create,
  find: hardwareInventory.find,
  findOne: hardwareInventory.findOne,
  update: hardwareInventory.update,
  delete: hardwareInventory.delete,

  /**
   * @param {object} message Payload message. Example: //TODOI
   * @description Router function being called incase message found in DIAGNOSTIC_QUEUE with the operation "hardwareProvisioning"
   */
  hardwareProvisioningRouter: async function(topic, message){
    const { method, controllerId, hardwareId } = message;
    let { source } = message;
    if (source !== "jouleone") source = "dejoule"; // To make this compatible with JouleOne Pipeline.
    const siteId = topic.split('/')[0];
    const inputCheckResult = hardwareInventoryUtils.checkInput(method, controllerId, hardwareId);
    if (!inputCheckResult.status){
      sails.log.error("HardwareInventoryService::hardwareProvisioningRouter >> Error in MQTT payload! Problems:", inputCheckResult.problems);
      return false;
    }

    switch(method) {
      case "create":
        // Checking if hardwareId already exists. Sending error incase it does.
        let hardwareConfigs = await hardwareInventory.find({ hardwareId });
        if (hardwareConfigs.length != 0){
          // TODOI: Log into sentry
          sails.log.error(`HardwareInventoryService::hardwareProvisioningRouter >> Can not create hardwareId config as it already exists! hardwareId: ${hardwareId}`);
          publishHardwareProvisioningResponse(controllerId, siteId, method, false);
          return false;
        } else {
          createHardwareInventoryEntry(hardwareId, controllerId, siteId, method, source);
        }
        break;
      case "update":
        createHardwareInventoryEntry(hardwareId, controllerId, siteId, method, source);
        break;
      default:
        sails.log.error( "HardwareInventoryService::hardwareProvisioningRouter >> Illegal 'method': ", message );
        break;
    }
  },
};

// Helper Functions
/**
 * @description Creates an entry in the hardwareInventory table. Incase of any error,
 * publishes back to MQTT.
 * @param {string} hardwareId hardwareId generated by the controller
 * @param {string} controllerId deviceId of the controller
 */
async function createHardwareInventoryEntry(hardwareId, controllerId, siteId, method, source){
  const timestamp = moment().unix();
  try {
    await hardwareInventory.create({ hardwareId, controllerId, timestamp, source });
    publishHardwareProvisioningResponse(controllerId, siteId, method, true);
  } catch (error) {
    // TODO: Log into sentry.
    sails.log.error(`HardwareInventoryService::hardwareProvisioningRouter >> Error creating entry in hardwareinventory table. Inputs: ${JSON.stringify({hardwareId, controllerId, timestamp})}`);
    publishHardwareProvisioningResponse(controllerId, siteId, method, false);
    return false;
  }
}

/**
 * @description Sends response back to the same controller in case it was a success or a failure
 * @param {string} controllerId deviceId of the controller
 * @param {string} siteId siteId dervied from the topic received
 * @param {string} method create/update
 * @param {boolean} executionStatus To init the "success" key
 */
async function publishHardwareProvisioningResponse(controllerId, siteId, method, executionStatus){
  const topic = `${siteId}/response/${controllerId}/inventory`;
  const success = executionStatus ? "true" : "false";
  const payload = {
    operation: "hardwareProvisioning",
    method,
    success,
  };
  eventService.publish(topic, payload);
}