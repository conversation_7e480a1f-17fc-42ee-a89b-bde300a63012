/* eslint-disable no-undef */
module.exports = {

  /**
   * HardwareInventory module private functions
   */
  create: async (params) => {
    return HardwareInventory.create(params);
  },
  find: async (searchParams) => {
    // HardwareInventory.find
    return HardwareInventory.find(searchParams);
  },
  findOne: async (searchParams) => {
    // HardwareInventory.findone
    let HardwareInventorys = await HardwareInventory.find(searchParams).limit(1);
    return HardwareInventorys[0];
  },
  update: async (searchParams, updateValue) =>{
    return HardwareInventory.update(searchParams, updateValue);
  },
  delete: async (searchParams) => {
    return HardwareInventory.destroy(searchParams);
  },
};
